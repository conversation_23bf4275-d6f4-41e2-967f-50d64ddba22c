<?php
function export_candidate_csv_button_shortcode() {
    ob_start();
    ?>
    <button id="export-candidate-csv" class="button">Exportar CSV de Candidatos e Mulheres Vulneráveis</button>
    <script>
    jQuery(document).ready(function($) {
        var ajaxurl = '<?php echo admin_url('admin-ajax.php'); ?>';
        
        $('#export-candidate-csv').click(function() {
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'export_candidate_csv'
                },
                success: function(response) {
                    if (response.success) {
                        var blob = new Blob([response.data], { type: 'text/csv;charset=utf-8;' });
                        var link = document.createElement("a");
                        if (link.download !== undefined) {
                            var url = URL.createObjectURL(blob);
                            link.setAttribute("href", url);
                            link.setAttribute("download", "candidatos_e_mulheres_vulneraveis.csv");
                            link.style.visibility = 'hidden';
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                        }
                    } else {
                        alert('Erro ao exportar CSV: ' + response.data);
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    alert('Erro na requisição AJAX: ' + textStatus + ' - ' + errorThrown);
                }
            });
        });
    });
    </script>
    <?php
    return ob_get_clean();
}
add_shortcode('export_candidate_csv_button', 'export_candidate_csv_button_shortcode');

function export_candidate_csv_ajax_handler() {
    global $wpdb;
    
    $users = get_users(array('role__in' => array('candidato', 'mulher_vulneravel')));
    
    $csv_data = array();
    $header = array('CPF', 'Nome', 'Sobrenome', 'Data de Nascimento (Idade)', 'Endereço', 'Bairro', 'Escola', 'Email', 'Telefone', 'Status de Candidatura', 'Papel');
    
    // Array para armazenar todas as perguntas únicas do quiz 3
    $all_questions = array();
    
    foreach ($users as $user) {
        $user_id = $user->ID;
        
        // Início do código para obter dados básicos do usuário
        $cpf = get_user_meta($user_id, 'cpf', true);
        $nome = get_user_meta($user_id, 'nome', true);
        $sobrenome = get_user_meta($user_id, 'sobrenome', true);
        $data_nascimento = get_user_meta($user_id, 'data_nascimento', true);
        $endereco = get_user_meta($user_id, 'endereco', true);
        $bairro = get_user_meta($user_id, 'bairro', true);
        $escola = get_user_meta($user_id, 'escola', true);
        $telefone = get_user_meta($user_id, 'telefone', true);
        
        $email = $user->user_email;
        
        // Calcula a idade
        $idade = '';
        if ($data_nascimento) {
            $nascimento = DateTime::createFromFormat('d/m/Y', $data_nascimento);
            if ($nascimento) {
                $hoje = new DateTime();
                $idade = $hoje->diff($nascimento)->y;
                $data_nascimento .= " ($idade anos)";
            }
        }
        
        // Verifica o status da candidatura
        $quiz_3_completed = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}mlw_results WHERE user = %d AND quiz_id = 3",
            $user_id
        ));
        
        $quiz_2_completed = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}mlw_results WHERE user = %d AND quiz_id = 2",
            $user_id
        ));
        
        $next_quiz_id = get_user_meta($user_id, 'next_quiz', true);
        $next_quiz_completed = $next_quiz_id ? $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}mlw_results WHERE user = %d AND quiz_id = %d",
            $user_id,
            $next_quiz_id
        )) : 1;
        
        $is_complete = ($quiz_3_completed > 0 && $quiz_2_completed > 0 && $next_quiz_completed > 0);
        $status = $is_complete ? 'Completa' : 'Incompleta';
        
        // Determina o papel do usuário
        $papel = in_array('candidato', $user->roles) ? 'Candidato' : 'Mulher Vulnerável';
        // Fim do código para obter dados básicos do usuário
        
        $user_data = array($cpf, $nome, $sobrenome, $data_nascimento, $endereco, $bairro, $escola, $email, $telefone, $status, $papel);
        
        // Busca os resultados apenas do quiz 3
        $quiz_3_result = $wpdb->get_var($wpdb->prepare(
            "SELECT quiz_results FROM {$wpdb->prefix}mlw_results WHERE user = %d AND quiz_id = 3",
            $user_id
        ));
        
        $user_answers = array();
        
        if ($quiz_3_result) {
            $quiz_data = maybe_unserialize($quiz_3_result);
            if (is_array($quiz_data) && isset($quiz_data[1])) {
                foreach ($quiz_data[1] as $question) {
                    if (isset($question['question_title']) && isset($question['user_answer'])) {
                        $question_title = $question['question_title'];
                        $answer = is_array($question['user_answer']) ? implode(', ', $question['user_answer']) : $question['user_answer'];
                        $user_answers[$question_title] = $answer;
                        
                        // Adiciona a pergunta ao array de todas as perguntas se ainda não existir
                        if (!in_array($question_title, $all_questions)) {
                            $all_questions[] = $question_title;
                        }
                    }
                }
            }
        }
        
        // Adiciona as respostas do usuário na ordem correta
        foreach ($all_questions as $question) {
            $user_data[] = isset($user_answers[$question]) ? $user_answers[$question] : '';
        }
        
        $csv_data[] = $user_data;
    }
    
    // Adiciona todas as perguntas do quiz 3 ao cabeçalho
    $header = array_merge($header, $all_questions);
    array_unshift($csv_data, $header);
    
    // Gera o CSV
    $output = fopen('php://output', 'w');
    foreach ($csv_data as $row) {
        fputcsv($output, $row);
    }
    fclose($output);
    
    $csv_content = ob_get_clean();
    
    if (!empty($csv_content)) {
        wp_send_json_success($csv_content);
    } else {
        wp_send_json_error('Nenhuma informação encontrada.');
    }
}
add_action('wp_ajax_export_candidate_csv', 'export_candidate_csv_ajax_handler');
add_action('wp_ajax_nopriv_export_candidate_csv', 'export_candidate_csv_ajax_handler');