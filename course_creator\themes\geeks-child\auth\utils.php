<?php
function custom_login_error_message( $user, $username, $password ) {
    global $login_error_message;

    if ( is_wp_error( $user ) ) {
        $login_error_message = $user->get_error_message();
    }

    return $user;
}
add_filter( 'authenticate', 'custom_login_error_message', 30, 3 );
add_shortcode('nav-header', 'geeks_navbar_right');

// Função para criar o shortcode de logout
function custom_logout_shortcode($atts) {
    // Verifica se o usuário está logado
    if (is_user_logged_in()) {
        // URL de logout com redirecionamento para a página principal
        $logout_url = wp_logout_url(home_url());
        
        // Gera o link de logout com o texto "Sair"
        return '<a href="' . esc_url($logout_url) . '">Sair</a>';
    }
    
    // Se o usuário não estiver logado, não exibe nada
    return '';
}
// Adiciona o shortcode [custom_logout]
add_shortcode('custom_logout', 'custom_logout_shortcode');

// Função para garantir que todos os logouts redirecionem para a página principal
function custom_logout_redirect() {
    wp_safe_redirect(home_url());
    exit();
}
add_action('wp_logout', 'custom_logout_redirect');

function redirecionar_apos_login($user_login, $user) {
    $allowed_roles = array('candidato', 'mulher_vulnervel');
    $student_roles = array('alunos_ia', 'aluno_wordpress', 'aluno_programacao', 'aluno_marketing', 'aluno_edio_de_vdeo', 'aluno_design', 'aprendiz');

    // Verifica se o usuário possui as funções permitidas
    if (array_intersect($allowed_roles, $user->roles)) {
        // Verifica se o usuário já completou o quiz 3
        if(has_user_completed_quiz(3, $user->ID)){
            wp_redirect(home_url('/painel-inscricao'));
        }else{
            wp_redirect(home_url('/formulario-de-inscricao'));
        }
        exit;
    }
    // Verifica se o usuário possui uma das funções de estudante
    elseif (array_intersect($student_roles, $user->roles)) {
        wp_redirect(home_url('/minha-formacao'));
        exit;
    }
}
add_action('wp_login', 'redirecionar_apos_login', 10, 2);

// Função para redirecionar usuários não logados da página /dashboard para /login
function redirect_non_logged_users_from_dashboard() {
    if (!is_user_logged_in() && is_page('dashboard')) {
        wp_safe_redirect(home_url('/login'));
        exit();
    }
}
add_action('template_redirect', 'redirect_non_logged_users_from_dashboard');

//redireciona para página de login usuário não logado da página Minha Formação
function redirecionar_para_login_se_nao_autenticado() {
    // Verifica se a página atual é 'minha-formacao' e se o usuário não está logado
    if (is_page('minha-formacao') && !is_user_logged_in()) {
        // Obtém a URL atual
        $current_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
        
        // Codifica a URL atual para usar como parâmetro de redirecionamento
        $redirect_url = urlencode($current_url);
        
        // Redireciona para a página de login com o parâmetro de redirecionamento
        wp_redirect(home_url('/login') . '?redirect_to=' . $redirect_url);
        exit;
    }
}
add_action('template_redirect', 'redirecionar_para_login_se_nao_autenticado');
