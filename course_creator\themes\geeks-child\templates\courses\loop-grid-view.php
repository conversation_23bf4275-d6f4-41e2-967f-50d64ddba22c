<?php
/**
 * Geeks Course Loop Grid View
 */
 
$savedPrerequisitesIDS = maybe_unserialize(get_post_meta(get_the_ID(), '_tutor_course_prerequisites_ids', true));
$user_id = get_current_user_id();
$all_completed = true;

if($savedPrerequisitesIDS){
	foreach($savedPrerequisitesIDS as $course_id){
		if(!tutor_utils()->is_completed_course($course_id)){
			$all_completed = false;
		}
	}
}


?><div class="card mb-4 card-hover">
	
	<a href="<?php the_permalink(); ?>" class="card-img-top">
		<?php if(!$all_completed && is_user_logged_in()): ?>
    		<?php if ( has_post_thumbnail() ) : ?>
    			<?php the_post_thumbnail( 'full', array( 'class' => 'card-img-top rounded-top-md', 'style' => 'filter: grayscale(1)' ) ); ?>
    		<?php endif; ?>
    		<div class="rounded-lock">
    			<svg fill="#000000" height="24px" width="24px" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" 
        			 viewBox="0 0 330 330" xml:space="preserve">
            		<g id="XMLID_509_">
            			<path id="XMLID_510_" d="M65,330h200c8.284,0,15-6.716,15-15V145c0-8.284-6.716-15-15-15h-15V85c0-46.869-38.131-85-85-85
            				S80,38.131,80,85v45H65c-8.284,0-15,6.716-15,15v170C50,323.284,56.716,330,65,330z M180,234.986V255c0,8.284-6.716,15-15,15
            				s-15-6.716-15-15v-20.014c-6.068-4.565-10-11.824-10-19.986c0-13.785,11.215-25,25-25s25,11.215,25,25
            				C190,223.162,186.068,230.421,180,234.986z M110,85c0-30.327,24.673-55,55-55s55,24.673,55,55v45H110V85z"/>
            		</g>
        		</svg>
    		</div>
		<?php else: ?>
    		<?php if ( has_post_thumbnail() ) : ?>
    			<?php the_post_thumbnail( 'full', array( 'class' => 'card-img-top rounded-top-md' ) ); ?>
    		<?php endif; ?>
		<?php endif; ?>

	</a>
	<!-- Card body -->
	<div class="card-body">

		<?php do_action( 'geeks_courses_card_body_top' ); ?>
		
		<h4 class="mb-2 text-truncate-line-2 ">
			<a href="<?php the_permalink(); ?>" class="text-inherit"><?php the_title(); ?></a>
		</h4>
		
		<?php if ( isset( $post_meta_items ) ) : ?>
		<!-- List inline -->
		<ul class="mb-3 list-inline">
			<?php 
			foreach ( $post_meta_items as $post_meta_item ) :
				
				ob_start();
				call_user_func( $post_meta_item );
				$list_item = ob_get_clean();
				
				if ( ! empty( $list_item ) ) :
			?>
			<li class="list-inline-item"><?php echo wp_kses( $list_item, 'post-svg' ); ?></li>
			<?php 
				endif;
			endforeach; 
			?>
		</ul>
		<?php endif; ?>
		
		<?php if ( isset( $post_average_rating ) && isset( $post_rating_count ) ) : ?>
		<div class="lh-1">
			<?php geeks_display_course_rating( $post_average_rating, $post_rating_count ); ?>
		</div>
		<?php endif; ?>

		<?php if ( isset( $price_html ) ) : ?>
		<div class="lh-1 mt-3 text-dark fw-bold">
		<?php echo wp_kses( $price_html, array( 'span' => array( 'class' => true ), 'del' => array( 'class' => true ), 'ins' => array( 'class' => true ) ) ); ?>
        </div>
    	<?php endif; ?>

		<?php do_action( 'geeks_courses_card_body_bottom' ); ?>
	
	</div>

	<!-- Card footer -->
	<div class="card-footer">
		<!-- Row -->
		<div class="row align-items-center g-0">
			<?php do_action( 'geeks_courses_loop_footer' ); ?>
		</div>
	</div>
</div>