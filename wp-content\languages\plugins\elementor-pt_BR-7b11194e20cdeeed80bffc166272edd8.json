{"translation-revision-date": "2025-06-27 03:46:37+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n > 1;", "lang": "pt_BR"}, "Inheritance item: %s": ["Item herdado: %s"], "Style origin": ["Origem do estilo"], "Base": ["Base"], "Inherited from base styles": ["Herdado dos estilos base"], "With your current role,": ["Com sua função atual,"], "%s created": ["%s criada"], "Class": ["Classe"], "With your current role, you can only use existing states.": ["Com sua função atual, você pode usar apenas os estados existentes."], "Column gap": ["Espaçamento entre colunas"], "you can only use existing classes.": ["você pode usar apenas as classes existentes."], "class %s applied": ["classe %s aplicada"], "With your current role, you can use existing classes but can’t modify them.": ["Com sua função atual, você pode usar as classes existentes, mas não pode modificá-las."], "class %s removed": ["classe %s removida"], "Dynamic tags": ["Tags dinâmicas"], "You’ll need Elementor Pro to use this feature.": ["Você vai precisar do Elementor Pro, para usar este recurso."], "Streamline your workflow with dynamic tags": ["Simplifique seu fluxo de trabalho com tags dinâmicas"], "Word spacing": ["Espaçamento entre palavras"], "Text transform": ["Transformação do texto"], "Text stroke": ["Traço do texto"], "Line decoration": ["Decoração da linha"], "Overline": ["Sobrelinhado"], "Line-through": ["<PERSON><PERSON> (tachado)"], "Underline": ["<PERSON><PERSON><PERSON><PERSON>"], "Text color": ["Cor do  texto"], "Text align": ["Alinhamento do texto"], "Line height": ["<PERSON>ura da linha"], "Letter spacing": ["Espaçamento entre letras"], "900 - Black": ["900 - Preto"], "800 - Extra bold": ["800 - Extra negrito"], "700 - Bold": ["700 - <PERSON>eg<PERSON>"], "600 - Semi bold": ["600 - Semi negrito"], "500 - Medium": ["500 - M<PERSON><PERSON>"], "400 - Normal": ["400 - Normal"], "300 - Light": ["300 - <PERSON><PERSON>"], "200 - Extra light": ["200 - Extra leve"], "100 - Thin": ["100 - <PERSON><PERSON>"], "Font style": ["<PERSON><PERSON><PERSON>"], "Font family": ["<PERSON><PERSON><PERSON><PERSON> da fonte"], "Max width": ["<PERSON><PERSON><PERSON> m<PERSON>xi<PERSON>"], "Min width": ["<PERSON><PERSON><PERSON>"], "Object position": ["Posição do objeto"], "Object fit": ["Ajuste do objeto"], "Scale down": ["Reduzir a escala"], "Z-index": ["Z-Index"], "Anchor offset": ["Deslocamento da âncora"], "Inline-flex": ["Flexível em linha"], "In-flx": ["In-flx"], "Inline-block": ["Bloco em linha"], "In-blk": ["In-blk"], "Align content": ["<PERSON><PERSON><PERSON>"], "Adjust corners": ["Ajustar cantos"], "Border radius": ["Raio da borda"], "Adjust borders": ["Ajustar bordas"], "Border width": ["<PERSON><PERSON><PERSON> da borda"], "Border type": ["<PERSON><PERSON><PERSON> <PERSON> b<PERSON>a"], "Border color": ["<PERSON><PERSON> <PERSON> b<PERSON>a"], "This has value from another style": ["Este valor vem de outro estilo"], "This value is overridden by another style": ["Este valor foi substituído por outro estilo"], "This is the final value": ["Este é o valor final"], "Style edited": ["<PERSON><PERSON><PERSON>"], "Type class name": ["Digite o nome da classe"], "Classes": ["Classes"], "You’ve reached the limit of 50 classes. Please remove an existing one to create a new class.": ["Você atingiu o limite de 50 classes. Remova uma existente, para criar uma nova classe."], "Has style": ["Tem estilo"], "States": ["Estados"], "Radial Gradient": ["Gradiente radial"], "Linear Gradient": ["Gradiente linear"], "Bottom right": ["Inferior à direita"], "Bottom left": ["Inferior à esquerda"], "Bottom center": ["Inferior ao centro"], "Top right": ["Superior à direita"], "Top left": ["Superior à esquerda"], "Top center": ["Superior ao centro"], "Center right": ["Centro à direita"], "Center left": ["Centro à esquerda"], "Center center": ["Centro ao centro"], "Clear & try again": ["Limpar e tentar novamente"], "Try something else.": ["Tente outra coisa."], "Open CSS Class Menu": ["Abrir menu de classes CSS"], "Flex child": ["<PERSON><PERSON><PERSON> (descendente) flexível"], "Reversed wrap": ["Envoltório invertido"], "No wrap": ["Sem envolver"], "Justify content": ["Justificar con<PERSON>"], "Space evenly": ["Espaço uniforme"], "Space around": ["Espaço ao redor"], "Basis": ["Base"], "Custom order": ["Ordem personalizada"], "Last": ["Último"], "First": ["<PERSON><PERSON>"], "Reversed column": ["Coluna invertida"], "Reversed row": ["<PERSON><PERSON> invertida"], "Flex": ["Flexível"], "Block": ["Bloco"], "Align self": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Align items": ["<PERSON><PERSON><PERSON>"], "local": ["local"], "Ridge": ["Saliência"], "Effects": ["Efeitos"], "Relative": ["Relativo"], "Static": ["Está<PERSON><PERSON>"], "Visible": ["Visível"], "Outset": ["Externo"], "Inset": ["Interno"], "Remove dynamic value": ["Remover valor dinâ<PERSON>o"], "Sorry, nothing matched": ["<PERSON>da corresponde"], "Search dynamic tags…": ["<PERSON><PERSON><PERSON><PERSON> tags dinâmicas..."], "Show more": ["<PERSON><PERSON> mais"], "Show less": ["<PERSON><PERSON> menos"], "Font weight": ["<PERSON><PERSON><PERSON> da fonte"], "Font size": ["<PERSON><PERSON><PERSON>"], "Max height": ["Altura máxima"], "Min height": ["<PERSON><PERSON> mínima"], "Space between": ["Espaço entre"], "Sticky": ["Fixo"], "Wrap": ["Envolver"], "Shrink": ["<PERSON><PERSON><PERSON><PERSON>"], "Grow": ["<PERSON><PERSON><PERSON>"], "Groove": ["Sulco"], "Rename": ["Renomear"], "Order": ["Ordem"], "Gaps": ["Espaçamentos"], "Row": ["<PERSON><PERSON>"], "Google Fonts": ["Fontes do Google"], "Fill": ["<PERSON><PERSON><PERSON>"], "Contain": ["<PERSON><PERSON>"], "Cover": ["Cobertura"], "Auto": ["Automático"], "Fixed": ["Fixo"], "Absolute": ["Absoluto"], "Hidden": ["Oculto"], "Overflow": ["Transbordar"], "Position": ["Posição"], "Custom Fonts": ["<PERSON><PERSON>s personaliza<PERSON>"], "Remove": ["Remover"], "End": ["Fim"], "Start": ["Início"], "Edit %s": ["Editar %s"], "Background": ["Plano de fundo"], "General": ["G<PERSON>"], "Spacing": ["Espaçamento"], "Left to right": ["Esquerda para direita"], "Direction": ["Direção"], "Display": ["<PERSON><PERSON><PERSON>"], "Settings": ["Configurações"], "Aspect Ratio": ["Proporção da tela"], "Right to left": ["Direita para esquerda"], "Justify": ["Justificar"], "Border": ["<PERSON><PERSON>"], "Size": ["<PERSON><PERSON><PERSON>"], "Stretch": ["Esticar"], "Height": ["Altura"], "Width": ["<PERSON><PERSON><PERSON>"], "Layout": ["Layout"], "Padding": ["Preenchimento"], "Margin": ["Margem"], "Center": ["Centro"], "Typography": ["Tipografia"], "Column": ["Coluna"], "Style": ["<PERSON><PERSON><PERSON>"], "Columns": ["Colunas"], "Custom": ["Personalizado"], "Italic": ["Itálico"], "Normal": ["Normal"], "Capitalize": ["Capitalizar"], "Lowercase": ["Minúsculas"], "Uppercase": ["Mai<PERSON><PERSON><PERSON>"], "Dashed": ["<PERSON><PERSON><PERSON>"], "Dotted": ["Pontil<PERSON><PERSON>"], "Double": ["<PERSON><PERSON><PERSON>"], "Solid": ["<PERSON><PERSON><PERSON><PERSON>"], "None": ["<PERSON><PERSON><PERSON>"], "System": ["Sistema"], "Left": ["E<PERSON>rda"], "Bottom": ["Inferior"], "Right": ["<PERSON><PERSON><PERSON>"], "Top": ["Superior"], "Clear": ["Limpar"]}}, "comment": {"reference": "assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js"}}