!function(){"use strict";var e={5338:function(e,t,r){var n=r(3617);t.H=n.createRoot,n.hydrateRoot},3617:function(e){e.exports=window.ReactDOM}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={exports:{}};return e[n](i,i.exports,r),i.exports}r.d=function(e,t){for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};r.r(n),r.d(n,{injectIntoLogic:function(){return p},injectIntoTop:function(){return f},start:function(){return E}});var o=window.elementorV2.locations,i=window.React,c=r(3617),l=r(5338),a=window.elementorV2.editorCurrentUser,u=window.elementorV2.editorV1Adapters,d=window.elementorV2.query,m=window.elementorV2.store,w=window.elementorV2.ui,{Slot:s,inject:f}=(0,o.createLocation)(),{Slot:y,inject:p}=(0,o.createLocation)();function v(){return i.createElement(i.Fragment,null,i.createElement(s,null),i.createElement("div",{style:{display:"none"}},i.createElement(y,null)))}function E(e){const t=(0,m.__createStore)(),r=(0,d.createQueryClient)();(0,u.__privateDispatchReadyEvent)(),(0,a.ensureCurrentUser)({queryClient:r}),function(e,t){let r;try{const n=(0,l.H)(t);r=()=>{n.render(e)}}catch{r=()=>{c.render(e,t)}}r()}(i.createElement(m.__StoreProvider,{store:t},i.createElement(d.QueryClientProvider,{client:r},i.createElement(w.DirectionProvider,{rtl:"rtl"===window.document.dir},i.createElement(w.ThemeProvider,null,i.createElement(v,null))))),e)}(window.elementorV2=window.elementorV2||{}).editor=n}(),window.elementorV2.editor?.init?.();