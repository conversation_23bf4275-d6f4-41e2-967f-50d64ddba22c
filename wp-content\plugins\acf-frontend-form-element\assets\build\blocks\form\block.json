{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 2, "name": "frontend-admin/form", "title": "Frontend Form", "description": "Displays a form that allows users to edit and add content from the frontend.", "icon": "feedback", "category": "frontend-admin", "textdomain": "frontend-admin", "supports": {"align": ["wide"]}, "providesContext": {"frontend-admin/form/form_settings": "form_settings"}, "attributes": {"form_key": {"type": "string", "default": ""}, "form_title": {"type": "string", "default": ""}, "form_settings": {"type": "object", "default": {"post_type": ["post"], "post_status": "draft", "custom_fields_save": "post", "post_to_edit": "current_post", "url_query_post": "post_id", "select_post": 0, "hide_if_no_post": 0}}}, "editorScript": "file:../../form/index.js"}