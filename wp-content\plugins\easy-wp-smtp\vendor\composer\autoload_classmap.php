<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Composer\\Installers\\AglInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AglInstaller.php',
    'Composer\\Installers\\AimeosInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AimeosInstaller.php',
    'Composer\\Installers\\AnnotateCmsInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AnnotateCmsInstaller.php',
    'Composer\\Installers\\AsgardInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AsgardInstaller.php',
    'Composer\\Installers\\AttogramInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AttogramInstaller.php',
    'Composer\\Installers\\BaseInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/BaseInstaller.php',
    'Composer\\Installers\\BitrixInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/BitrixInstaller.php',
    'Composer\\Installers\\BonefishInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/BonefishInstaller.php',
    'Composer\\Installers\\CakePHPInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CakePHPInstaller.php',
    'Composer\\Installers\\ChefInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ChefInstaller.php',
    'Composer\\Installers\\CiviCrmInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CiviCrmInstaller.php',
    'Composer\\Installers\\ClanCatsFrameworkInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ClanCatsFrameworkInstaller.php',
    'Composer\\Installers\\CockpitInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CockpitInstaller.php',
    'Composer\\Installers\\CodeIgniterInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CodeIgniterInstaller.php',
    'Composer\\Installers\\Concrete5Installer' => $vendorDir . '/composer/installers/src/Composer/Installers/Concrete5Installer.php',
    'Composer\\Installers\\CraftInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CraftInstaller.php',
    'Composer\\Installers\\CroogoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CroogoInstaller.php',
    'Composer\\Installers\\DecibelInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DecibelInstaller.php',
    'Composer\\Installers\\DframeInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DframeInstaller.php',
    'Composer\\Installers\\DokuWikiInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DokuWikiInstaller.php',
    'Composer\\Installers\\DolibarrInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DolibarrInstaller.php',
    'Composer\\Installers\\DrupalInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DrupalInstaller.php',
    'Composer\\Installers\\ElggInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ElggInstaller.php',
    'Composer\\Installers\\EliasisInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/EliasisInstaller.php',
    'Composer\\Installers\\ExpressionEngineInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ExpressionEngineInstaller.php',
    'Composer\\Installers\\EzPlatformInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/EzPlatformInstaller.php',
    'Composer\\Installers\\FuelInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/FuelInstaller.php',
    'Composer\\Installers\\FuelphpInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/FuelphpInstaller.php',
    'Composer\\Installers\\GravInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/GravInstaller.php',
    'Composer\\Installers\\HuradInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/HuradInstaller.php',
    'Composer\\Installers\\ImageCMSInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ImageCMSInstaller.php',
    'Composer\\Installers\\Installer' => $vendorDir . '/composer/installers/src/Composer/Installers/Installer.php',
    'Composer\\Installers\\ItopInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ItopInstaller.php',
    'Composer\\Installers\\JoomlaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/JoomlaInstaller.php',
    'Composer\\Installers\\KanboardInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/KanboardInstaller.php',
    'Composer\\Installers\\KirbyInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/KirbyInstaller.php',
    'Composer\\Installers\\KnownInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/KnownInstaller.php',
    'Composer\\Installers\\KodiCMSInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/KodiCMSInstaller.php',
    'Composer\\Installers\\KohanaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/KohanaInstaller.php',
    'Composer\\Installers\\LanManagementSystemInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/LanManagementSystemInstaller.php',
    'Composer\\Installers\\LaravelInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/LaravelInstaller.php',
    'Composer\\Installers\\LavaLiteInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/LavaLiteInstaller.php',
    'Composer\\Installers\\LithiumInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/LithiumInstaller.php',
    'Composer\\Installers\\MODULEWorkInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MODULEWorkInstaller.php',
    'Composer\\Installers\\MODXEvoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MODXEvoInstaller.php',
    'Composer\\Installers\\MagentoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MagentoInstaller.php',
    'Composer\\Installers\\MajimaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MajimaInstaller.php',
    'Composer\\Installers\\MakoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MakoInstaller.php',
    'Composer\\Installers\\MantisBTInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MantisBTInstaller.php',
    'Composer\\Installers\\MauticInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MauticInstaller.php',
    'Composer\\Installers\\MayaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MayaInstaller.php',
    'Composer\\Installers\\MediaWikiInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MediaWikiInstaller.php',
    'Composer\\Installers\\MiaoxingInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MiaoxingInstaller.php',
    'Composer\\Installers\\MicroweberInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MicroweberInstaller.php',
    'Composer\\Installers\\ModxInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ModxInstaller.php',
    'Composer\\Installers\\MoodleInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MoodleInstaller.php',
    'Composer\\Installers\\OctoberInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/OctoberInstaller.php',
    'Composer\\Installers\\OntoWikiInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/OntoWikiInstaller.php',
    'Composer\\Installers\\OsclassInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/OsclassInstaller.php',
    'Composer\\Installers\\OxidInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/OxidInstaller.php',
    'Composer\\Installers\\PPIInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PPIInstaller.php',
    'Composer\\Installers\\PantheonInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PantheonInstaller.php',
    'Composer\\Installers\\PhiftyInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PhiftyInstaller.php',
    'Composer\\Installers\\PhpBBInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PhpBBInstaller.php',
    'Composer\\Installers\\PimcoreInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PimcoreInstaller.php',
    'Composer\\Installers\\PiwikInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PiwikInstaller.php',
    'Composer\\Installers\\PlentymarketsInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PlentymarketsInstaller.php',
    'Composer\\Installers\\Plugin' => $vendorDir . '/composer/installers/src/Composer/Installers/Plugin.php',
    'Composer\\Installers\\PortoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PortoInstaller.php',
    'Composer\\Installers\\PrestashopInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PrestashopInstaller.php',
    'Composer\\Installers\\ProcessWireInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ProcessWireInstaller.php',
    'Composer\\Installers\\PuppetInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PuppetInstaller.php',
    'Composer\\Installers\\PxcmsInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PxcmsInstaller.php',
    'Composer\\Installers\\RadPHPInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/RadPHPInstaller.php',
    'Composer\\Installers\\ReIndexInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ReIndexInstaller.php',
    'Composer\\Installers\\Redaxo5Installer' => $vendorDir . '/composer/installers/src/Composer/Installers/Redaxo5Installer.php',
    'Composer\\Installers\\RedaxoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/RedaxoInstaller.php',
    'Composer\\Installers\\RoundcubeInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/RoundcubeInstaller.php',
    'Composer\\Installers\\SMFInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SMFInstaller.php',
    'Composer\\Installers\\ShopwareInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ShopwareInstaller.php',
    'Composer\\Installers\\SilverStripeInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SilverStripeInstaller.php',
    'Composer\\Installers\\SiteDirectInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SiteDirectInstaller.php',
    'Composer\\Installers\\StarbugInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/StarbugInstaller.php',
    'Composer\\Installers\\SyDESInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SyDESInstaller.php',
    'Composer\\Installers\\SyliusInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SyliusInstaller.php',
    'Composer\\Installers\\Symfony1Installer' => $vendorDir . '/composer/installers/src/Composer/Installers/Symfony1Installer.php',
    'Composer\\Installers\\TYPO3CmsInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TYPO3CmsInstaller.php',
    'Composer\\Installers\\TYPO3FlowInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TYPO3FlowInstaller.php',
    'Composer\\Installers\\TaoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TaoInstaller.php',
    'Composer\\Installers\\TastyIgniterInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TastyIgniterInstaller.php',
    'Composer\\Installers\\TheliaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TheliaInstaller.php',
    'Composer\\Installers\\TuskInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TuskInstaller.php',
    'Composer\\Installers\\UserFrostingInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/UserFrostingInstaller.php',
    'Composer\\Installers\\VanillaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/VanillaInstaller.php',
    'Composer\\Installers\\VgmcpInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/VgmcpInstaller.php',
    'Composer\\Installers\\WHMCSInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/WHMCSInstaller.php',
    'Composer\\Installers\\WinterInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/WinterInstaller.php',
    'Composer\\Installers\\WolfCMSInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/WolfCMSInstaller.php',
    'Composer\\Installers\\WordPressInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/WordPressInstaller.php',
    'Composer\\Installers\\YawikInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/YawikInstaller.php',
    'Composer\\Installers\\ZendInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ZendInstaller.php',
    'Composer\\Installers\\ZikulaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ZikulaInstaller.php',
    'EasyWPSMTP\\AbstractConnection' => $baseDir . '/src/AbstractConnection.php',
    'EasyWPSMTP\\Admin\\Area' => $baseDir . '/src/Admin/Area.php',
    'EasyWPSMTP\\Admin\\ConnectionSettings' => $baseDir . '/src/Admin/ConnectionSettings.php',
    'EasyWPSMTP\\Admin\\DashboardWidget' => $baseDir . '/src/Admin/DashboardWidget.php',
    'EasyWPSMTP\\Admin\\DebugEvents\\DebugEvents' => $baseDir . '/src/Admin/DebugEvents/DebugEvents.php',
    'EasyWPSMTP\\Admin\\DebugEvents\\Event' => $baseDir . '/src/Admin/DebugEvents/Event.php',
    'EasyWPSMTP\\Admin\\DebugEvents\\EventsCollection' => $baseDir . '/src/Admin/DebugEvents/EventsCollection.php',
    'EasyWPSMTP\\Admin\\DebugEvents\\Migration' => $baseDir . '/src/Admin/DebugEvents/Migration.php',
    'EasyWPSMTP\\Admin\\DebugEvents\\Table' => $baseDir . '/src/Admin/DebugEvents/Table.php',
    'EasyWPSMTP\\Admin\\DomainChecker' => $baseDir . '/src/Admin/DomainChecker.php',
    'EasyWPSMTP\\Admin\\Notifications' => $baseDir . '/src/Admin/Notifications.php',
    'EasyWPSMTP\\Admin\\PageAbstract' => $baseDir . '/src/Admin/PageAbstract.php',
    'EasyWPSMTP\\Admin\\PageInterface' => $baseDir . '/src/Admin/PageInterface.php',
    'EasyWPSMTP\\Admin\\Pages\\ActionSchedulerTab' => $baseDir . '/src/Admin/Pages/ActionSchedulerTab.php',
    'EasyWPSMTP\\Admin\\Pages\\AdditionalConnectionsTab' => $baseDir . '/src/Admin/Pages/AdditionalConnectionsTab.php',
    'EasyWPSMTP\\Admin\\Pages\\AlertsTab' => $baseDir . '/src/Admin/Pages/AlertsTab.php',
    'EasyWPSMTP\\Admin\\Pages\\AuthTab' => $baseDir . '/src/Admin/Pages/AuthTab.php',
    'EasyWPSMTP\\Admin\\Pages\\ControlTab' => $baseDir . '/src/Admin/Pages/ControlTab.php',
    'EasyWPSMTP\\Admin\\Pages\\DebugEventsTab' => $baseDir . '/src/Admin/Pages/DebugEventsTab.php',
    'EasyWPSMTP\\Admin\\Pages\\EmailReports' => $baseDir . '/src/Admin/Pages/EmailReports.php',
    'EasyWPSMTP\\Admin\\Pages\\EmailReportsTab' => $baseDir . '/src/Admin/Pages/EmailReportsTab.php',
    'EasyWPSMTP\\Admin\\Pages\\ExportTab' => $baseDir . '/src/Admin/Pages/ExportTab.php',
    'EasyWPSMTP\\Admin\\Pages\\Logs' => $baseDir . '/src/Admin/Pages/Logs.php',
    'EasyWPSMTP\\Admin\\Pages\\LogsTab' => $baseDir . '/src/Admin/Pages/LogsTab.php',
    'EasyWPSMTP\\Admin\\Pages\\MiscTab' => $baseDir . '/src/Admin/Pages/MiscTab.php',
    'EasyWPSMTP\\Admin\\Pages\\SettingsTab' => $baseDir . '/src/Admin/Pages/SettingsTab.php',
    'EasyWPSMTP\\Admin\\Pages\\SmartRoutingTab' => $baseDir . '/src/Admin/Pages/SmartRoutingTab.php',
    'EasyWPSMTP\\Admin\\Pages\\TestTab' => $baseDir . '/src/Admin/Pages/TestTab.php',
    'EasyWPSMTP\\Admin\\Pages\\Tools' => $baseDir . '/src/Admin/Pages/Tools.php',
    'EasyWPSMTP\\Admin\\ParentPageAbstract' => $baseDir . '/src/Admin/ParentPageAbstract.php',
    'EasyWPSMTP\\Admin\\PluginsInstallSkin' => $baseDir . '/src/Admin/PluginsInstallSkin.php',
    'EasyWPSMTP\\Admin\\SetupWizard' => $baseDir . '/src/Admin/SetupWizard.php',
    'EasyWPSMTP\\Admin\\UserFeedback' => $baseDir . '/src/Admin/UserFeedback.php',
    'EasyWPSMTP\\Compatibility\\Compatibility' => $baseDir . '/src/Compatibility/Compatibility.php',
    'EasyWPSMTP\\Compatibility\\Plugin\\Admin2020' => $baseDir . '/src/Compatibility/Plugin/Admin2020.php',
    'EasyWPSMTP\\Compatibility\\Plugin\\PluginAbstract' => $baseDir . '/src/Compatibility/Plugin/PluginAbstract.php',
    'EasyWPSMTP\\Compatibility\\Plugin\\PluginInterface' => $baseDir . '/src/Compatibility/Plugin/PluginInterface.php',
    'EasyWPSMTP\\Compatibility\\Plugin\\WPForms' => $baseDir . '/src/Compatibility/Plugin/WPForms.php',
    'EasyWPSMTP\\Compatibility\\Plugin\\WPFormsLite' => $baseDir . '/src/Compatibility/Plugin/WPFormsLite.php',
    'EasyWPSMTP\\Compatibility\\Plugin\\WooCommerce' => $baseDir . '/src/Compatibility/Plugin/WooCommerce.php',
    'EasyWPSMTP\\Conflicts' => $baseDir . '/src/Conflicts.php',
    'EasyWPSMTP\\Connect' => $baseDir . '/src/Connect.php',
    'EasyWPSMTP\\Connection' => $baseDir . '/src/Connection.php',
    'EasyWPSMTP\\ConnectionInterface' => $baseDir . '/src/ConnectionInterface.php',
    'EasyWPSMTP\\ConnectionsManager' => $baseDir . '/src/ConnectionsManager.php',
    'EasyWPSMTP\\Core' => $baseDir . '/src/Core.php',
    'EasyWPSMTP\\DBRepair' => $baseDir . '/src/DBRepair.php',
    'EasyWPSMTP\\Debug' => $baseDir . '/src/Debug.php',
    'EasyWPSMTP\\Geo' => $baseDir . '/src/Geo.php',
    'EasyWPSMTP\\Helpers\\Crypto' => $baseDir . '/src/Helpers/Crypto.php',
    'EasyWPSMTP\\Helpers\\DB' => $baseDir . '/src/Helpers/DB.php',
    'EasyWPSMTP\\Helpers\\Geo' => $baseDir . '/src/Helpers/Geo.php',
    'EasyWPSMTP\\Helpers\\Helpers' => $baseDir . '/src/Helpers/Helpers.php',
    'EasyWPSMTP\\Helpers\\UI' => $baseDir . '/src/Helpers/UI.php',
    'EasyWPSMTP\\MailCatcher' => $baseDir . '/src/MailCatcher.php',
    'EasyWPSMTP\\MailCatcherInterface' => $baseDir . '/src/MailCatcherInterface.php',
    'EasyWPSMTP\\MailCatcherTrait' => $baseDir . '/src/MailCatcherTrait.php',
    'EasyWPSMTP\\MailCatcherV6' => $baseDir . '/src/MailCatcherV6.php',
    'EasyWPSMTP\\Migrations\\DeprecatedOptionsConverter' => $baseDir . '/src/Migrations/DeprecatedOptionsConverter.php',
    'EasyWPSMTP\\Migrations\\DeprecatedOptionsMigration' => $baseDir . '/src/Migrations/DeprecatedOptionsMigration.php',
    'EasyWPSMTP\\Migrations\\GeneralMigration' => $baseDir . '/src/Migrations/GeneralMigration.php',
    'EasyWPSMTP\\Migrations\\MigrationAbstract' => $baseDir . '/src/Migrations/MigrationAbstract.php',
    'EasyWPSMTP\\Migrations\\Migrations' => $baseDir . '/src/Migrations/Migrations.php',
    'EasyWPSMTP\\OptimizedEmailSending' => $baseDir . '/src/OptimizedEmailSending.php',
    'EasyWPSMTP\\Options' => $baseDir . '/src/Options.php',
    'EasyWPSMTP\\Pro\\AdditionalConnections\\AdditionalConnections' => $baseDir . '/src/Pro/AdditionalConnections/AdditionalConnections.php',
    'EasyWPSMTP\\Pro\\AdditionalConnections\\Admin\\SettingsTab' => $baseDir . '/src/Pro/AdditionalConnections/Admin/SettingsTab.php',
    'EasyWPSMTP\\Pro\\AdditionalConnections\\Admin\\TestTab' => $baseDir . '/src/Pro/AdditionalConnections/Admin/TestTab.php',
    'EasyWPSMTP\\Pro\\AdditionalConnections\\Connection' => $baseDir . '/src/Pro/AdditionalConnections/Connection.php',
    'EasyWPSMTP\\Pro\\AdditionalConnections\\ConnectionOptions' => $baseDir . '/src/Pro/AdditionalConnections/ConnectionOptions.php',
    'EasyWPSMTP\\Pro\\Admin\\Area' => $baseDir . '/src/Pro/Admin/Area.php',
    'EasyWPSMTP\\Pro\\Admin\\DashboardWidget' => $baseDir . '/src/Pro/Admin/DashboardWidget.php',
    'EasyWPSMTP\\Pro\\Admin\\Pages\\MiscTab' => $baseDir . '/src/Pro/Admin/Pages/MiscTab.php',
    'EasyWPSMTP\\Pro\\Admin\\PluginsList' => $baseDir . '/src/Pro/Admin/PluginsList.php',
    'EasyWPSMTP\\Pro\\Alerts\\AbstractOptions' => $baseDir . '/src/Pro/Alerts/AbstractOptions.php',
    'EasyWPSMTP\\Pro\\Alerts\\Admin\\SettingsTab' => $baseDir . '/src/Pro/Alerts/Admin/SettingsTab.php',
    'EasyWPSMTP\\Pro\\Alerts\\Alert' => $baseDir . '/src/Pro/Alerts/Alert.php',
    'EasyWPSMTP\\Pro\\Alerts\\Alerts' => $baseDir . '/src/Pro/Alerts/Alerts.php',
    'EasyWPSMTP\\Pro\\Alerts\\Handlers\\HandlerInterface' => $baseDir . '/src/Pro/Alerts/Handlers/HandlerInterface.php',
    'EasyWPSMTP\\Pro\\Alerts\\Loader' => $baseDir . '/src/Pro/Alerts/Loader.php',
    'EasyWPSMTP\\Pro\\Alerts\\Notifier' => $baseDir . '/src/Pro/Alerts/Notifier.php',
    'EasyWPSMTP\\Pro\\Alerts\\OptionsInterface' => $baseDir . '/src/Pro/Alerts/OptionsInterface.php',
    'EasyWPSMTP\\Pro\\Alerts\\Providers\\CustomWebhook\\Handler' => $baseDir . '/src/Pro/Alerts/Providers/CustomWebhook/Handler.php',
    'EasyWPSMTP\\Pro\\Alerts\\Providers\\CustomWebhook\\Options' => $baseDir . '/src/Pro/Alerts/Providers/CustomWebhook/Options.php',
    'EasyWPSMTP\\Pro\\Alerts\\Providers\\DiscordWebhook\\Handler' => $baseDir . '/src/Pro/Alerts/Providers/DiscordWebhook/Handler.php',
    'EasyWPSMTP\\Pro\\Alerts\\Providers\\DiscordWebhook\\Options' => $baseDir . '/src/Pro/Alerts/Providers/DiscordWebhook/Options.php',
    'EasyWPSMTP\\Pro\\Alerts\\Providers\\Email\\Handler' => $baseDir . '/src/Pro/Alerts/Providers/Email/Handler.php',
    'EasyWPSMTP\\Pro\\Alerts\\Providers\\Email\\Options' => $baseDir . '/src/Pro/Alerts/Providers/Email/Options.php',
    'EasyWPSMTP\\Pro\\Alerts\\Providers\\Push\\Handler' => $baseDir . '/src/Pro/Alerts/Providers/Push/Handler.php',
    'EasyWPSMTP\\Pro\\Alerts\\Providers\\Push\\Options' => $baseDir . '/src/Pro/Alerts/Providers/Push/Options.php',
    'EasyWPSMTP\\Pro\\Alerts\\Providers\\Push\\Provider' => $baseDir . '/src/Pro/Alerts/Providers/Push/Provider.php',
    'EasyWPSMTP\\Pro\\Alerts\\Providers\\SlackWebhook\\Handler' => $baseDir . '/src/Pro/Alerts/Providers/SlackWebhook/Handler.php',
    'EasyWPSMTP\\Pro\\Alerts\\Providers\\SlackWebhook\\Options' => $baseDir . '/src/Pro/Alerts/Providers/SlackWebhook/Options.php',
    'EasyWPSMTP\\Pro\\Alerts\\Providers\\TeamsWebhook\\Handler' => $baseDir . '/src/Pro/Alerts/Providers/TeamsWebhook/Handler.php',
    'EasyWPSMTP\\Pro\\Alerts\\Providers\\TeamsWebhook\\Options' => $baseDir . '/src/Pro/Alerts/Providers/TeamsWebhook/Options.php',
    'EasyWPSMTP\\Pro\\Alerts\\Providers\\TwilioSMS\\Handler' => $baseDir . '/src/Pro/Alerts/Providers/TwilioSMS/Handler.php',
    'EasyWPSMTP\\Pro\\Alerts\\Providers\\TwilioSMS\\Options' => $baseDir . '/src/Pro/Alerts/Providers/TwilioSMS/Options.php',
    'EasyWPSMTP\\Pro\\BackupConnections\\Admin\\SettingsTab' => $baseDir . '/src/Pro/BackupConnections/Admin/SettingsTab.php',
    'EasyWPSMTP\\Pro\\BackupConnections\\BackupConnections' => $baseDir . '/src/Pro/BackupConnections/BackupConnections.php',
    'EasyWPSMTP\\Pro\\ConditionalLogic\\CanProcessConditionalLogicTrait' => $baseDir . '/src/Pro/ConditionalLogic/CanProcessConditionalLogicTrait.php',
    'EasyWPSMTP\\Pro\\ConditionalLogic\\ConditionalLogicSettings' => $baseDir . '/src/Pro/ConditionalLogic/ConditionalLogicSettings.php',
    'EasyWPSMTP\\Pro\\ConnectionsManager' => $baseDir . '/src/Pro/ConnectionsManager.php',
    'EasyWPSMTP\\Pro\\DBRepair' => $baseDir . '/src/Pro/DBRepair.php',
    'EasyWPSMTP\\Pro\\Emails\\Control\\Admin\\SettingsTab' => $baseDir . '/src/Pro/Emails/Control/Admin/SettingsTab.php',
    'EasyWPSMTP\\Pro\\Emails\\Control\\Control' => $baseDir . '/src/Pro/Emails/Control/Control.php',
    'EasyWPSMTP\\Pro\\Emails\\Control\\Reload' => $baseDir . '/src/Pro/Emails/Control/Reload.php',
    'EasyWPSMTP\\Pro\\Emails\\Control\\Switcher' => $baseDir . '/src/Pro/Emails/Control/Switcher.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Admin\\ArchivePage' => $baseDir . '/src/Pro/Emails/Logs/Admin/ArchivePage.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Admin\\PageAbstract' => $baseDir . '/src/Pro/Emails/Logs/Admin/PageAbstract.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Admin\\PrintPreview' => $baseDir . '/src/Pro/Emails/Logs/Admin/PrintPreview.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Admin\\SettingsTab' => $baseDir . '/src/Pro/Emails/Logs/Admin/SettingsTab.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Admin\\SinglePage' => $baseDir . '/src/Pro/Emails/Logs/Admin/SinglePage.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Admin\\Table' => $baseDir . '/src/Pro/Emails/Logs/Admin/Table.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Attachments\\Attachment' => $baseDir . '/src/Pro/Emails/Logs/Attachments/Attachment.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Attachments\\Attachments' => $baseDir . '/src/Pro/Emails/Logs/Attachments/Attachments.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Attachments\\Migration' => $baseDir . '/src/Pro/Emails/Logs/Attachments/Migration.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\CanResendEmailTrait' => $baseDir . '/src/Pro/Emails/Logs/CanResendEmailTrait.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\AbstractDeliveryVerifier' => $baseDir . '/src/Pro/Emails/Logs/DeliveryVerification/AbstractDeliveryVerifier.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\DeliveryStatus' => $baseDir . '/src/Pro/Emails/Logs/DeliveryVerification/DeliveryStatus.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\DeliveryVerification' => $baseDir . '/src/Pro/Emails/Logs/DeliveryVerification/DeliveryVerification.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\ElasticEmail\\DeliveryVerifier' => $baseDir . '/src/Pro/Emails/Logs/DeliveryVerification/ElasticEmail/DeliveryVerifier.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\MailerSend\\DeliveryVerifier' => $baseDir . '/src/Pro/Emails/Logs/DeliveryVerification/MailerSend/DeliveryVerifier.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\Mailgun\\DeliveryVerifier' => $baseDir . '/src/Pro/Emails/Logs/DeliveryVerification/Mailgun/DeliveryVerifier.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\Mailjet\\DeliveryVerifier' => $baseDir . '/src/Pro/Emails/Logs/DeliveryVerification/Mailjet/DeliveryVerifier.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\Postmark\\DeliveryVerifier' => $baseDir . '/src/Pro/Emails/Logs/DeliveryVerification/Postmark/DeliveryVerifier.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\SMTP2GO\\DeliveryVerifier' => $baseDir . '/src/Pro/Emails/Logs/DeliveryVerification/SMTP2GO/DeliveryVerifier.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\SMTPcom\\DeliveryVerifier' => $baseDir . '/src/Pro/Emails/Logs/DeliveryVerification/SMTPcom/DeliveryVerifier.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\Sendinblue\\DeliveryVerifier' => $baseDir . '/src/Pro/Emails/Logs/DeliveryVerification/Sendinblue/DeliveryVerifier.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\Sendlayer\\DeliveryVerifier' => $baseDir . '/src/Pro/Emails/Logs/DeliveryVerification/Sendlayer/DeliveryVerifier.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\DeliveryVerification\\SparkPost\\DeliveryVerifier' => $baseDir . '/src/Pro/Emails/Logs/DeliveryVerification/SparkPost/DeliveryVerifier.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Email' => $baseDir . '/src/Pro/Emails/Logs/Email.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\EmailsCollection' => $baseDir . '/src/Pro/Emails/Logs/EmailsCollection.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Export\\AbstractData' => $baseDir . '/src/Pro/Emails/Logs/Export/AbstractData.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Export\\Admin' => $baseDir . '/src/Pro/Emails/Logs/Export/Admin.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Export\\CanRemoveExportFileTrait' => $baseDir . '/src/Pro/Emails/Logs/Export/CanRemoveExportFileTrait.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Export\\EMLData' => $baseDir . '/src/Pro/Emails/Logs/Export/EMLData.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Export\\Export' => $baseDir . '/src/Pro/Emails/Logs/Export/Export.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Export\\File' => $baseDir . '/src/Pro/Emails/Logs/Export/File.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Export\\Handler' => $baseDir . '/src/Pro/Emails/Logs/Export/Handler.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Export\\Request' => $baseDir . '/src/Pro/Emails/Logs/Export/Request.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Export\\TableData' => $baseDir . '/src/Pro/Emails/Logs/Export/TableData.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Logs' => $baseDir . '/src/Pro/Emails/Logs/Logs.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Migration' => $baseDir . '/src/Pro/Emails/Logs/Migration.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Providers\\Common' => $baseDir . '/src/Pro/Emails/Logs/Providers/Common.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Providers\\SMTP' => $baseDir . '/src/Pro/Emails/Logs/Providers/SMTP.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\RecheckDeliveryStatus' => $baseDir . '/src/Pro/Emails/Logs/RecheckDeliveryStatus.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Reports\\Admin' => $baseDir . '/src/Pro/Emails/Logs/Reports/Admin.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Reports\\Emails\\Summary' => $baseDir . '/src/Pro/Emails/Logs/Reports/Emails/Summary.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Reports\\Report' => $baseDir . '/src/Pro/Emails/Logs/Reports/Report.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Reports\\Reports' => $baseDir . '/src/Pro/Emails/Logs/Reports/Reports.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Reports\\Table' => $baseDir . '/src/Pro/Emails/Logs/Reports/Table.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Resend' => $baseDir . '/src/Pro/Emails/Logs/Resend.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Tracking\\Events\\AbstractEvent' => $baseDir . '/src/Pro/Emails/Logs/Tracking/Events/AbstractEvent.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Tracking\\Events\\EventFactory' => $baseDir . '/src/Pro/Emails/Logs/Tracking/Events/EventFactory.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Tracking\\Events\\EventInterface' => $baseDir . '/src/Pro/Emails/Logs/Tracking/Events/EventInterface.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Tracking\\Events\\Events' => $baseDir . '/src/Pro/Emails/Logs/Tracking/Events/Events.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Tracking\\Events\\Injectable\\AbstractInjectableEvent' => $baseDir . '/src/Pro/Emails/Logs/Tracking/Events/Injectable/AbstractInjectableEvent.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Tracking\\Events\\Injectable\\ClickLinkEvent' => $baseDir . '/src/Pro/Emails/Logs/Tracking/Events/Injectable/ClickLinkEvent.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Tracking\\Events\\Injectable\\OpenEmailEvent' => $baseDir . '/src/Pro/Emails/Logs/Tracking/Events/Injectable/OpenEmailEvent.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Tracking\\Migration' => $baseDir . '/src/Pro/Emails/Logs/Tracking/Migration.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Tracking\\Tracking' => $baseDir . '/src/Pro/Emails/Logs/Tracking/Tracking.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\AbstractProcessor' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/AbstractProcessor.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\AbstractProvider' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/AbstractProvider.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\AbstractSubscriber' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/AbstractSubscriber.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Events\\Delivered' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Events/Delivered.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Events\\EventInterface' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Events/EventInterface.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Events\\Failed' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Events/Failed.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\ProcessorInterface' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/ProcessorInterface.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\ProviderInterface' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/ProviderInterface.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\MailerSend\\Events\\Failed' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/MailerSend/Events/Failed.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\MailerSend\\Processor' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/MailerSend/Processor.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\MailerSend\\Provider' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/MailerSend/Provider.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\MailerSend\\Subscriber' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/MailerSend/Subscriber.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Mailgun\\Events\\Failed' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Mailgun/Events/Failed.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Mailgun\\Processor' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Mailgun/Processor.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Mailgun\\Provider' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Mailgun/Provider.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Mailgun\\Subscriber' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Mailgun/Subscriber.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Mailjet\\Events\\Failed' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Mailjet/Events/Failed.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Mailjet\\Processor' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Mailjet/Processor.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Mailjet\\Provider' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Mailjet/Provider.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Mailjet\\Subscriber' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Mailjet/Subscriber.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Postmark\\Events\\Failed' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Postmark/Events/Failed.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Postmark\\Processor' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Postmark/Processor.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Postmark\\Provider' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Postmark/Provider.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Postmark\\Subscriber' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Postmark/Subscriber.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SMTP2GO\\Events\\Failed' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/SMTP2GO/Events/Failed.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SMTP2GO\\Processor' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/SMTP2GO/Processor.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SMTP2GO\\Provider' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/SMTP2GO/Provider.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SMTP2GO\\Subscriber' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/SMTP2GO/Subscriber.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SMTPcom\\Events\\Failed' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/SMTPcom/Events/Failed.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SMTPcom\\Processor' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/SMTPcom/Processor.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SMTPcom\\Provider' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/SMTPcom/Provider.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SMTPcom\\Subscriber' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/SMTPcom/Subscriber.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Sendinblue\\Events\\Failed' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Sendinblue/Events/Failed.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Sendinblue\\Processor' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Sendinblue/Processor.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Sendinblue\\Provider' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Sendinblue/Provider.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Sendinblue\\Subscriber' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Sendinblue/Subscriber.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Sendlayer\\Events\\Failed' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Sendlayer/Events/Failed.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Sendlayer\\Processor' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Sendlayer/Processor.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Sendlayer\\Provider' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Sendlayer/Provider.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\Sendlayer\\Subscriber' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/Sendlayer/Subscriber.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SparkPost\\Events\\Failed' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/SparkPost/Events/Failed.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SparkPost\\Processor' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/SparkPost/Processor.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SparkPost\\Provider' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/SparkPost/Provider.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Providers\\SparkPost\\Subscriber' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Providers/SparkPost/Subscriber.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\SubscriberInterface' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/SubscriberInterface.php',
    'EasyWPSMTP\\Pro\\Emails\\Logs\\Webhooks\\Webhooks' => $baseDir . '/src/Pro/Emails/Logs/Webhooks/Webhooks.php',
    'EasyWPSMTP\\Pro\\Emails\\RateLimiting\\RateLimiting' => $baseDir . '/src/Pro/Emails/RateLimiting/RateLimiting.php',
    'EasyWPSMTP\\Pro\\License\\License' => $baseDir . '/src/Pro/License/License.php',
    'EasyWPSMTP\\Pro\\License\\Updater' => $baseDir . '/src/Pro/License/Updater.php',
    'EasyWPSMTP\\Pro\\MailCatcher' => $baseDir . '/src/Pro/MailCatcher.php',
    'EasyWPSMTP\\Pro\\MailCatcherTrait' => $baseDir . '/src/Pro/MailCatcherTrait.php',
    'EasyWPSMTP\\Pro\\MailCatcherV6' => $baseDir . '/src/Pro/MailCatcherV6.php',
    'EasyWPSMTP\\Pro\\Multisite' => $baseDir . '/src/Pro/Multisite.php',
    'EasyWPSMTP\\Pro\\Pro' => $baseDir . '/src/Pro/Pro.php',
    'EasyWPSMTP\\Pro\\ProductApi\\Client' => $baseDir . '/src/Pro/ProductApi/Client.php',
    'EasyWPSMTP\\Pro\\ProductApi\\Credentials' => $baseDir . '/src/Pro/ProductApi/Credentials.php',
    'EasyWPSMTP\\Pro\\ProductApi\\CredentialsGenerationNonce' => $baseDir . '/src/Pro/ProductApi/CredentialsGenerationNonce.php',
    'EasyWPSMTP\\Pro\\ProductApi\\CredentialsGenerator' => $baseDir . '/src/Pro/ProductApi/CredentialsGenerator.php',
    'EasyWPSMTP\\Pro\\ProductApi\\CredentialsRepository' => $baseDir . '/src/Pro/ProductApi/CredentialsRepository.php',
    'EasyWPSMTP\\Pro\\ProductApi\\ProductApi' => $baseDir . '/src/Pro/ProductApi/ProductApi.php',
    'EasyWPSMTP\\Pro\\ProductApi\\Response' => $baseDir . '/src/Pro/ProductApi/Response.php',
    'EasyWPSMTP\\Pro\\Providers\\AmazonSES\\Auth' => $baseDir . '/src/Pro/Providers/AmazonSES/Auth.php',
    'EasyWPSMTP\\Pro\\Providers\\AmazonSES\\IdentitiesTable' => $baseDir . '/src/Pro/Providers/AmazonSES/IdentitiesTable.php',
    'EasyWPSMTP\\Pro\\Providers\\AmazonSES\\Identity' => $baseDir . '/src/Pro/Providers/AmazonSES/Identity.php',
    'EasyWPSMTP\\Pro\\Providers\\AmazonSES\\Mailer' => $baseDir . '/src/Pro/Providers/AmazonSES/Mailer.php',
    'EasyWPSMTP\\Pro\\Providers\\AmazonSES\\Options' => $baseDir . '/src/Pro/Providers/AmazonSES/Options.php',
    'EasyWPSMTP\\Pro\\Providers\\Gmail\\Api\\Client' => $baseDir . '/src/Pro/Providers/Gmail/Api/Client.php',
    'EasyWPSMTP\\Pro\\Providers\\Gmail\\Api\\OneTimeToken' => $baseDir . '/src/Pro/Providers/Gmail/Api/OneTimeToken.php',
    'EasyWPSMTP\\Pro\\Providers\\Gmail\\Api\\Response' => $baseDir . '/src/Pro/Providers/Gmail/Api/Response.php',
    'EasyWPSMTP\\Pro\\Providers\\Gmail\\Api\\SiteId' => $baseDir . '/src/Pro/Providers/Gmail/Api/SiteId.php',
    'EasyWPSMTP\\Pro\\Providers\\Gmail\\Auth' => $baseDir . '/src/Pro/Providers/Gmail/Auth.php',
    'EasyWPSMTP\\Pro\\Providers\\Gmail\\Mailer' => $baseDir . '/src/Pro/Providers/Gmail/Mailer.php',
    'EasyWPSMTP\\Pro\\Providers\\Gmail\\Options' => $baseDir . '/src/Pro/Providers/Gmail/Options.php',
    'EasyWPSMTP\\Pro\\Providers\\Outlook\\AttachmentsUploader' => $baseDir . '/src/Pro/Providers/Outlook/AttachmentsUploader.php',
    'EasyWPSMTP\\Pro\\Providers\\Outlook\\Auth' => $baseDir . '/src/Pro/Providers/Outlook/Auth.php',
    'EasyWPSMTP\\Pro\\Providers\\Outlook\\Mailer' => $baseDir . '/src/Pro/Providers/Outlook/Mailer.php',
    'EasyWPSMTP\\Pro\\Providers\\Outlook\\OneClick\\Auth' => $baseDir . '/src/Pro/Providers/Outlook/OneClick/Auth.php',
    'EasyWPSMTP\\Pro\\Providers\\Outlook\\OneClick\\Auth\\Client' => $baseDir . '/src/Pro/Providers/Outlook/OneClick/Auth/Client.php',
    'EasyWPSMTP\\Pro\\Providers\\Outlook\\OneClick\\Auth\\Response' => $baseDir . '/src/Pro/Providers/Outlook/OneClick/Auth/Response.php',
    'EasyWPSMTP\\Pro\\Providers\\Outlook\\OneClick\\Options' => $baseDir . '/src/Pro/Providers/Outlook/OneClick/Options.php',
    'EasyWPSMTP\\Pro\\Providers\\Outlook\\Options' => $baseDir . '/src/Pro/Providers/Outlook/Options.php',
    'EasyWPSMTP\\Pro\\Providers\\Outlook\\Provider' => $baseDir . '/src/Pro/Providers/Outlook/Provider.php',
    'EasyWPSMTP\\Pro\\Providers\\Providers' => $baseDir . '/src/Pro/Providers/Providers.php',
    'EasyWPSMTP\\Pro\\Providers\\Zoho\\Auth' => $baseDir . '/src/Pro/Providers/Zoho/Auth.php',
    'EasyWPSMTP\\Pro\\Providers\\Zoho\\Auth\\Zoho' => $baseDir . '/src/Pro/Providers/Zoho/Auth/Zoho.php',
    'EasyWPSMTP\\Pro\\Providers\\Zoho\\Auth\\ZohoUser' => $baseDir . '/src/Pro/Providers/Zoho/Auth/ZohoUser.php',
    'EasyWPSMTP\\Pro\\Providers\\Zoho\\Mailer' => $baseDir . '/src/Pro/Providers/Zoho/Mailer.php',
    'EasyWPSMTP\\Pro\\Providers\\Zoho\\Options' => $baseDir . '/src/Pro/Providers/Zoho/Options.php',
    'EasyWPSMTP\\Pro\\SiteHealth' => $baseDir . '/src/Pro/SiteHealth.php',
    'EasyWPSMTP\\Pro\\SmartRouting\\Admin\\SettingsTab' => $baseDir . '/src/Pro/SmartRouting/Admin/SettingsTab.php',
    'EasyWPSMTP\\Pro\\SmartRouting\\ConditionalLogic' => $baseDir . '/src/Pro/SmartRouting/ConditionalLogic.php',
    'EasyWPSMTP\\Pro\\SmartRouting\\SmartRouting' => $baseDir . '/src/Pro/SmartRouting/SmartRouting.php',
    'EasyWPSMTP\\Pro\\Tasks\\EmailLogCleanupTask' => $baseDir . '/src/Pro/Tasks/EmailLogCleanupTask.php',
    'EasyWPSMTP\\Pro\\Tasks\\LicenseCheckTask' => $baseDir . '/src/Pro/Tasks/LicenseCheckTask.php',
    'EasyWPSMTP\\Pro\\Tasks\\Logs\\BulkVerifySentStatusTask' => $baseDir . '/src/Pro/Tasks/Logs/BulkVerifySentStatusTask.php',
    'EasyWPSMTP\\Pro\\Tasks\\Logs\\ElasticEmail\\VerifySentStatusTask' => $baseDir . '/src/Pro/Tasks/Logs/ElasticEmail/VerifySentStatusTask.php',
    'EasyWPSMTP\\Pro\\Tasks\\Logs\\ExportCleanupTask' => $baseDir . '/src/Pro/Tasks/Logs/ExportCleanupTask.php',
    'EasyWPSMTP\\Pro\\Tasks\\Logs\\MailerSend\\VerifySentStatusTask' => $baseDir . '/src/Pro/Tasks/Logs/MailerSend/VerifySentStatusTask.php',
    'EasyWPSMTP\\Pro\\Tasks\\Logs\\Mailgun\\VerifySentStatusTask' => $baseDir . '/src/Pro/Tasks/Logs/Mailgun/VerifySentStatusTask.php',
    'EasyWPSMTP\\Pro\\Tasks\\Logs\\Mailjet\\VerifySentStatusTask' => $baseDir . '/src/Pro/Tasks/Logs/Mailjet/VerifySentStatusTask.php',
    'EasyWPSMTP\\Pro\\Tasks\\Logs\\Postmark\\VerifySentStatusTask' => $baseDir . '/src/Pro/Tasks/Logs/Postmark/VerifySentStatusTask.php',
    'EasyWPSMTP\\Pro\\Tasks\\Logs\\ResendTask' => $baseDir . '/src/Pro/Tasks/Logs/ResendTask.php',
    'EasyWPSMTP\\Pro\\Tasks\\Logs\\SMTP2GO\\VerifySentStatusTask' => $baseDir . '/src/Pro/Tasks/Logs/SMTP2GO/VerifySentStatusTask.php',
    'EasyWPSMTP\\Pro\\Tasks\\Logs\\SMTPcom\\VerifySentStatusTask' => $baseDir . '/src/Pro/Tasks/Logs/SMTPcom/VerifySentStatusTask.php',
    'EasyWPSMTP\\Pro\\Tasks\\Logs\\Sendinblue\\VerifySentStatusTask' => $baseDir . '/src/Pro/Tasks/Logs/Sendinblue/VerifySentStatusTask.php',
    'EasyWPSMTP\\Pro\\Tasks\\Logs\\Sendlayer\\VerifySentStatusTask' => $baseDir . '/src/Pro/Tasks/Logs/Sendlayer/VerifySentStatusTask.php',
    'EasyWPSMTP\\Pro\\Tasks\\Logs\\SparkPost\\VerifySentStatusTask' => $baseDir . '/src/Pro/Tasks/Logs/SparkPost/VerifySentStatusTask.php',
    'EasyWPSMTP\\Pro\\Tasks\\Logs\\VerifySentStatusTaskAbstract' => $baseDir . '/src/Pro/Tasks/Logs/VerifySentStatusTaskAbstract.php',
    'EasyWPSMTP\\Pro\\Tasks\\NotifierTask' => $baseDir . '/src/Pro/Tasks/NotifierTask.php',
    'EasyWPSMTP\\Pro\\Translations' => $baseDir . '/src/Pro/Translations.php',
    'EasyWPSMTP\\Pro\\Upgrade' => $baseDir . '/src/Pro/Upgrade.php',
    'EasyWPSMTP\\Processor' => $baseDir . '/src/Processor.php',
    'EasyWPSMTP\\Providers\\AmazonSES\\Options' => $baseDir . '/src/Providers/AmazonSES/Options.php',
    'EasyWPSMTP\\Providers\\AuthAbstract' => $baseDir . '/src/Providers/AuthAbstract.php',
    'EasyWPSMTP\\Providers\\AuthInterface' => $baseDir . '/src/Providers/AuthInterface.php',
    'EasyWPSMTP\\Providers\\ElasticEmail\\Mailer' => $baseDir . '/src/Providers/ElasticEmail/Mailer.php',
    'EasyWPSMTP\\Providers\\ElasticEmail\\Options' => $baseDir . '/src/Providers/ElasticEmail/Options.php',
    'EasyWPSMTP\\Providers\\Gmail\\Options' => $baseDir . '/src/Providers/Gmail/Options.php',
    'EasyWPSMTP\\Providers\\Loader' => $baseDir . '/src/Providers/Loader.php',
    'EasyWPSMTP\\Providers\\Mail\\Mailer' => $baseDir . '/src/Providers/Mail/Mailer.php',
    'EasyWPSMTP\\Providers\\Mail\\Options' => $baseDir . '/src/Providers/Mail/Options.php',
    'EasyWPSMTP\\Providers\\MailerAbstract' => $baseDir . '/src/Providers/MailerAbstract.php',
    'EasyWPSMTP\\Providers\\MailerInterface' => $baseDir . '/src/Providers/MailerInterface.php',
    'EasyWPSMTP\\Providers\\MailerSend\\Mailer' => $baseDir . '/src/Providers/MailerSend/Mailer.php',
    'EasyWPSMTP\\Providers\\MailerSend\\Options' => $baseDir . '/src/Providers/MailerSend/Options.php',
    'EasyWPSMTP\\Providers\\Mailgun\\Mailer' => $baseDir . '/src/Providers/Mailgun/Mailer.php',
    'EasyWPSMTP\\Providers\\Mailgun\\Options' => $baseDir . '/src/Providers/Mailgun/Options.php',
    'EasyWPSMTP\\Providers\\Mailjet\\Mailer' => $baseDir . '/src/Providers/Mailjet/Mailer.php',
    'EasyWPSMTP\\Providers\\Mailjet\\Options' => $baseDir . '/src/Providers/Mailjet/Options.php',
    'EasyWPSMTP\\Providers\\OptionsAbstract' => $baseDir . '/src/Providers/OptionsAbstract.php',
    'EasyWPSMTP\\Providers\\OptionsInterface' => $baseDir . '/src/Providers/OptionsInterface.php',
    'EasyWPSMTP\\Providers\\Outlook\\Options' => $baseDir . '/src/Providers/Outlook/Options.php',
    'EasyWPSMTP\\Providers\\Outlook\\Provider' => $baseDir . '/src/Providers/Outlook/Provider.php',
    'EasyWPSMTP\\Providers\\Postmark\\Mailer' => $baseDir . '/src/Providers/Postmark/Mailer.php',
    'EasyWPSMTP\\Providers\\Postmark\\Options' => $baseDir . '/src/Providers/Postmark/Options.php',
    'EasyWPSMTP\\Providers\\SMTP2GO\\Mailer' => $baseDir . '/src/Providers/SMTP2GO/Mailer.php',
    'EasyWPSMTP\\Providers\\SMTP2GO\\Options' => $baseDir . '/src/Providers/SMTP2GO/Options.php',
    'EasyWPSMTP\\Providers\\SMTP\\Mailer' => $baseDir . '/src/Providers/SMTP/Mailer.php',
    'EasyWPSMTP\\Providers\\SMTP\\Options' => $baseDir . '/src/Providers/SMTP/Options.php',
    'EasyWPSMTP\\Providers\\SMTPcom\\Mailer' => $baseDir . '/src/Providers/SMTPcom/Mailer.php',
    'EasyWPSMTP\\Providers\\SMTPcom\\Options' => $baseDir . '/src/Providers/SMTPcom/Options.php',
    'EasyWPSMTP\\Providers\\Sendgrid\\Mailer' => $baseDir . '/src/Providers/Sendgrid/Mailer.php',
    'EasyWPSMTP\\Providers\\Sendgrid\\Options' => $baseDir . '/src/Providers/Sendgrid/Options.php',
    'EasyWPSMTP\\Providers\\Sendinblue\\Api' => $baseDir . '/src/Providers/Sendinblue/Api.php',
    'EasyWPSMTP\\Providers\\Sendinblue\\Mailer' => $baseDir . '/src/Providers/Sendinblue/Mailer.php',
    'EasyWPSMTP\\Providers\\Sendinblue\\Options' => $baseDir . '/src/Providers/Sendinblue/Options.php',
    'EasyWPSMTP\\Providers\\Sendlayer\\Mailer' => $baseDir . '/src/Providers/Sendlayer/Mailer.php',
    'EasyWPSMTP\\Providers\\Sendlayer\\Options' => $baseDir . '/src/Providers/Sendlayer/Options.php',
    'EasyWPSMTP\\Providers\\SparkPost\\Mailer' => $baseDir . '/src/Providers/SparkPost/Mailer.php',
    'EasyWPSMTP\\Providers\\SparkPost\\Options' => $baseDir . '/src/Providers/SparkPost/Options.php',
    'EasyWPSMTP\\Providers\\Zoho\\Options' => $baseDir . '/src/Providers/Zoho/Options.php',
    'EasyWPSMTP\\Queue\\Attachments' => $baseDir . '/src/Queue/Attachments.php',
    'EasyWPSMTP\\Queue\\Email' => $baseDir . '/src/Queue/Email.php',
    'EasyWPSMTP\\Queue\\Migration' => $baseDir . '/src/Queue/Migration.php',
    'EasyWPSMTP\\Queue\\Queue' => $baseDir . '/src/Queue/Queue.php',
    'EasyWPSMTP\\Reports\\Emails\\Summary' => $baseDir . '/src/Reports/Emails/Summary.php',
    'EasyWPSMTP\\Reports\\Reports' => $baseDir . '/src/Reports/Reports.php',
    'EasyWPSMTP\\SiteHealth' => $baseDir . '/src/SiteHealth.php',
    'EasyWPSMTP\\Tasks\\DebugEventsCleanupTask' => $baseDir . '/src/Tasks/DebugEventsCleanupTask.php',
    'EasyWPSMTP\\Tasks\\Meta' => $baseDir . '/src/Tasks/Meta.php',
    'EasyWPSMTP\\Tasks\\NotificationsUpdateTask' => $baseDir . '/src/Tasks/NotificationsUpdateTask.php',
    'EasyWPSMTP\\Tasks\\Queue\\CleanupQueueTask' => $baseDir . '/src/Tasks/Queue/CleanupQueueTask.php',
    'EasyWPSMTP\\Tasks\\Queue\\ProcessQueueTask' => $baseDir . '/src/Tasks/Queue/ProcessQueueTask.php',
    'EasyWPSMTP\\Tasks\\Queue\\SendEnqueuedEmailTask' => $baseDir . '/src/Tasks/Queue/SendEnqueuedEmailTask.php',
    'EasyWPSMTP\\Tasks\\Reports\\SummaryEmailTask' => $baseDir . '/src/Tasks/Reports/SummaryEmailTask.php',
    'EasyWPSMTP\\Tasks\\Task' => $baseDir . '/src/Tasks/Task.php',
    'EasyWPSMTP\\Tasks\\Tasks' => $baseDir . '/src/Tasks/Tasks.php',
    'EasyWPSMTP\\Uploads' => $baseDir . '/src/Uploads.php',
    'EasyWPSMTP\\UsageTracking\\SendUsageTask' => $baseDir . '/src/UsageTracking/SendUsageTask.php',
    'EasyWPSMTP\\UsageTracking\\UsageTracking' => $baseDir . '/src/UsageTracking/UsageTracking.php',
    'EasyWPSMTP\\Vendor\\Normalizer' => $baseDir . '/vendor_prefixed/symfony/polyfill-intl-normalizer/Resources/stubs/Normalizer.php',
    'EasyWPSMTP\\Vendor\\Psr\\Http\\Message\\MessageInterface' => $baseDir . '/vendor_prefixed/psr/http-message/src/MessageInterface.php',
    'EasyWPSMTP\\Vendor\\Psr\\Http\\Message\\RequestInterface' => $baseDir . '/vendor_prefixed/psr/http-message/src/RequestInterface.php',
    'EasyWPSMTP\\Vendor\\Psr\\Http\\Message\\ResponseInterface' => $baseDir . '/vendor_prefixed/psr/http-message/src/ResponseInterface.php',
    'EasyWPSMTP\\Vendor\\Psr\\Http\\Message\\ServerRequestInterface' => $baseDir . '/vendor_prefixed/psr/http-message/src/ServerRequestInterface.php',
    'EasyWPSMTP\\Vendor\\Psr\\Http\\Message\\StreamInterface' => $baseDir . '/vendor_prefixed/psr/http-message/src/StreamInterface.php',
    'EasyWPSMTP\\Vendor\\Psr\\Http\\Message\\UploadedFileInterface' => $baseDir . '/vendor_prefixed/psr/http-message/src/UploadedFileInterface.php',
    'EasyWPSMTP\\Vendor\\Psr\\Http\\Message\\UriInterface' => $baseDir . '/vendor_prefixed/psr/http-message/src/UriInterface.php',
    'EasyWPSMTP\\Vendor\\Symfony\\Polyfill\\Intl\\Idn\\Idn' => $baseDir . '/vendor_prefixed/symfony/polyfill-intl-idn/Idn.php',
    'EasyWPSMTP\\Vendor\\Symfony\\Polyfill\\Intl\\Idn\\Info' => $baseDir . '/vendor_prefixed/symfony/polyfill-intl-idn/Info.php',
    'EasyWPSMTP\\Vendor\\Symfony\\Polyfill\\Intl\\Idn\\Resources\\unidata\\DisallowedRanges' => $baseDir . '/vendor_prefixed/symfony/polyfill-intl-idn/Resources/unidata/DisallowedRanges.php',
    'EasyWPSMTP\\Vendor\\Symfony\\Polyfill\\Intl\\Idn\\Resources\\unidata\\Regex' => $baseDir . '/vendor_prefixed/symfony/polyfill-intl-idn/Resources/unidata/Regex.php',
    'EasyWPSMTP\\Vendor\\Symfony\\Polyfill\\Intl\\Normalizer\\Normalizer' => $baseDir . '/vendor_prefixed/symfony/polyfill-intl-normalizer/Normalizer.php',
    'EasyWPSMTP\\Vendor\\Symfony\\Polyfill\\Mbstring\\Mbstring' => $baseDir . '/vendor_prefixed/symfony/polyfill-mbstring/Mbstring.php',
    'EasyWPSMTP\\WP' => $baseDir . '/src/WP.php',
    'EasyWPSMTP\\WPMailArgs' => $baseDir . '/src/WPMailArgs.php',
    'EasyWPSMTP\\WPMailInitiator' => $baseDir . '/src/WPMailInitiator.php',
);
