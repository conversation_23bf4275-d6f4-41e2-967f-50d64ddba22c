<?php

function shortcode_based_on_role() {
    
    if ( is_user_logged_in() && get_post_type() == 'lesson') {
        $user = wp_get_current_user();

        if ( in_array( 'aluno_wordpress', (array) $user->roles ) ) {
            return do_shortcode('[wpaicg_chatgpt id=19257]');
        } elseif ( in_array( 'aluno_programacao', (array) $user->roles ) ) {
            return do_shortcode('[wpaicg_chatgpt id=12683]');
        } elseif ( in_array( 'aluno_design', (array) $user->roles ) ) {
            return do_shortcode('[wpaicg_chatgpt id=19254]');
        } elseif ( in_array( 'aluno_edio_de_vdeo', (array) $user->roles ) ) {
            return do_shortcode('[wpaicg_chatgpt id=19255]');
        } elseif ( in_array( 'aluno_marketing', (array) $user->roles ) ) {
            return do_shortcode('[wpaicg_chatgpt id=19256]');
        }
    }

    return do_shortcode('[wpaicg_chatgpt]');
}
add_shortcode('custom_chatgpt_shortcode', 'shortcode_based_on_role');