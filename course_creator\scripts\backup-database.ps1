# Database Backup Script for Course Creator Project
# Usage: .\backup-database.ps1 [-WordPressPath ".."] [-BackupName "custom_name"]

param(
    [string]$WordPressPath = "..",
    [string]$BackupName = ""
)

# Colors for output
$Green = "Green"
$Red = "Red"
$Yellow = "Yellow"
$Cyan = "Cyan"

Write-Host "🗄️  Course Creator - Database Backup Script" -ForegroundColor $Cyan
Write-Host "=============================================" -ForegroundColor $Cyan

# Resolve WordPress path
$WordPressFullPath = Resolve-Path $WordPressPath -ErrorAction SilentlyContinue
if (-not $WordPressFullPath) {
    Write-Host "❌ WordPress path not found: $WordPressPath" -ForegroundColor $Red
    exit 1
}

Write-Host "📁 WordPress Path: $WordPressFullPath" -ForegroundColor $Green

# Check if wp-config.php exists
$WpConfigPath = Join-Path $WordPressFullPath "wp-config.php"
if (-not (Test-Path $WpConfigPath)) {
    Write-Host "❌ wp-config.php not found at: $WpConfigPath" -ForegroundColor $Red
    exit 1
}

# Extract database configuration from wp-config.php
Write-Host "🔍 Reading database configuration..." -ForegroundColor $Yellow

try {
    $WpConfigContent = Get-Content $WpConfigPath -Raw
    
    # Extract database settings using regex
    $DbName = [regex]::Match($WpConfigContent, "define\s*\(\s*['\"]DB_NAME['\"]\s*,\s*['\"]([^'\"]+)['\"]").Groups[1].Value
    $DbUser = [regex]::Match($WpConfigContent, "define\s*\(\s*['\"]DB_USER['\"]\s*,\s*['\"]([^'\"]+)['\"]").Groups[1].Value
    $DbPassword = [regex]::Match($WpConfigContent, "define\s*\(\s*['\"]DB_PASSWORD['\"]\s*,\s*['\"]([^'\"]+)['\"]").Groups[1].Value
    $DbHost = [regex]::Match($WpConfigContent, "define\s*\(\s*['\"]DB_HOST['\"]\s*,\s*['\"]([^'\"]+)['\"]").Groups[1].Value
    
    if (-not $DbName) {
        Write-Host "❌ Could not extract database name from wp-config.php" -ForegroundColor $Red
        exit 1
    }
    
    Write-Host "✅ Database: $DbName" -ForegroundColor $Green
    Write-Host "✅ User: $DbUser" -ForegroundColor $Green
    Write-Host "✅ Host: $DbHost" -ForegroundColor $Green
    
} catch {
    Write-Host "❌ Error reading wp-config.php: $($_.Exception.Message)" -ForegroundColor $Red
    exit 1
}

# Generate backup filename
if (-not $BackupName) {
    $Timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"
    $BackupName = "backup_${DbName}_${Timestamp}"
}

$BackupDir = "backups"
$BackupPath = Join-Path $BackupDir "${BackupName}.sql"

# Create backups directory if it doesn't exist
if (-not (Test-Path $BackupDir)) {
    New-Item -ItemType Directory -Path $BackupDir | Out-Null
    Write-Host "📁 Created backups directory" -ForegroundColor $Green
}

# Check if mysqldump is available
try {
    $null = Get-Command mysqldump -ErrorAction Stop
    Write-Host "✅ mysqldump found" -ForegroundColor $Green
} catch {
    Write-Host "❌ mysqldump not found. Please ensure MySQL is installed and in PATH." -ForegroundColor $Red
    Write-Host "💡 For XAMPP: Add C:\xampp\mysql\bin to your PATH" -ForegroundColor $Yellow
    exit 1
}

# Create backup
Write-Host "🔄 Creating database backup..." -ForegroundColor $Yellow
Write-Host "📄 Backup file: $BackupPath" -ForegroundColor $Cyan

try {
    # Build mysqldump command
    $MysqlDumpArgs = @(
        "-h", $DbHost,
        "-u", $DbUser
    )
    
    # Add password if not empty
    if ($DbPassword) {
        $MysqlDumpArgs += "-p$DbPassword"
    }
    
    # Add database name and output redirection
    $MysqlDumpArgs += @(
        "--single-transaction",
        "--routines",
        "--triggers",
        $DbName
    )
    
    # Execute mysqldump
    $BackupContent = & mysqldump @MysqlDumpArgs 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        # Save to file
        $BackupContent | Out-File -FilePath $BackupPath -Encoding UTF8
        
        $BackupSize = (Get-Item $BackupPath).Length
        $BackupSizeKB = [math]::Round($BackupSize / 1024, 2)
        
        Write-Host "✅ Database backup completed successfully!" -ForegroundColor $Green
        Write-Host "📊 Backup size: $BackupSizeKB KB" -ForegroundColor $Green
        Write-Host "📁 Backup location: $BackupPath" -ForegroundColor $Green
        
        # Show backup info
        Write-Host "`n📋 Backup Information:" -ForegroundColor $Cyan
        Write-Host "   Database: $DbName" -ForegroundColor $White
        Write-Host "   Timestamp: $(Get-Date)" -ForegroundColor $White
        Write-Host "   File: $BackupPath" -ForegroundColor $White
        
        # Show restore command
        Write-Host "`n🔄 To restore this backup on another PC:" -ForegroundColor $Yellow
        Write-Host "   mysql -u $DbUser -p $DbName < $BackupPath" -ForegroundColor $White
        
    } else {
        Write-Host "❌ Database backup failed!" -ForegroundColor $Red
        Write-Host "Error output: $BackupContent" -ForegroundColor $Red
        exit 1
    }
    
} catch {
    Write-Host "❌ Error during backup: $($_.Exception.Message)" -ForegroundColor $Red
    exit 1
}

Write-Host "`n🎉 Backup process completed!" -ForegroundColor $Green
