/*
Theme Name: <PERSON><PERSON> Child
Theme URI: http://cursos.institutopanapana.org.br/
Description: <PERSON><PERSON>.
Author: Dbs web
Author URI: http://dbsweb.com.br
Template: geeks
Version: 1.0.0
*/

#chat-btn{
    z-index: 999!important;
}

/* --- Start Tutor LMS Quiz Input Fix --- */

/* Override theme/plugin rule hiding native inputs specifically for <PERSON><PERSON> quizzes */
.tutor-quiz-single-wrap .quiz-question-ans-choice input[type="radio"].tutor-form-check-input,
.tutor-quiz-single-wrap .quiz-question-ans-choice input[type="checkbox"].tutor-form-check-input {
    position: static !important; /* Override absolute positioning */
    left: auto !important;
    opacity: 1 !important; /* Make it visible */
    pointer-events: auto !important; /* Allow clicks */
    width: auto !important; /* Use browser default or theme default size */
    height: auto !important;
    margin-right: 8px !important; /* Add some space */
    vertical-align: middle; /* Align nicely with text */
}

/* Optional: Add custom orange styling if desired (otherwise they'll use browser/theme defaults) */
/*
.tutor-quiz-single-wrap .quiz-question-ans-choice input[type="radio"].tutor-form-check-input,
.tutor-quiz-single-wrap .quiz-question-ans-choice input[type="checkbox"].tutor-form-check-input {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    width: 18px;
    height: 18px;
    border: 2px solid #ccc;
    border-radius: 0; 
    cursor: pointer;
}

.tutor-quiz-single-wrap .quiz-question-ans-choice input[type="radio"].tutor-form-check-input:checked,
.tutor-quiz-single-wrap .quiz-question-ans-choice input[type="checkbox"].tutor-form-check-input:checked {
    background-color: #ff7300; 
    border-color: #ff7300;
    position: relative; 
}

.tutor-quiz-single-wrap .quiz-question-ans-choice input[type="radio"].tutor-form-check-input:checked::after,
.tutor-quiz-single-wrap .quiz-question-ans-choice input[type="checkbox"].tutor-form-check-input:checked::after {
    content: '\2713'; 
    color: white;
    font-size: 12px;
    line-height: 15px;
    text-align: center;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
*/

/* --- End Tutor LMS Quiz Input Fix --- */

/* --- Force Tutor Quiz Input Visibility --- */
.tutor-quiz-wrap .tutor-form-check-input[type="checkbox"],
.tutor-quiz-wrap .tutor-form-check-input[type="radio"] {
    position: static !important; /* Override absolute positioning */
    left: auto !important;       /* Override negative left */
    opacity: 1 !important;        /* Override opacity 0 */
    pointer-events: auto !important; /* Make it clickable */
    appearance: auto !important;   /* Restore default browser appearance */
    width: 16px !important;       /* Set a sensible default size */
    height: 16px !important;      /* Set a sensible default size */
    margin-right: 8px !important; /* Add some space between input and label */
    vertical-align: middle;     /* Align nicely with text */
		/* Remove potentially conflicting custom styles from child theme */
		border: revert !important; 
		box-shadow: none !important;
}
/* Optional: Adjust label padding if needed now that input is visible */
.tutor-quiz-wrap .quiz-question-ans-choice label {
	 display: flex; /* Helps with alignment */
	 align-items: center; /* Vertically center items */
}

.tutor-quiz-wrap .quiz-question-ans-choice label span {
	 flex-grow: 1; /* Allow text label to take remaining space */
}
/* --- End Force Tutor Quiz Input Visibility --- */