# 🎓 Tutor LMS Course Creator Automation

A comprehensive WordPress automation system for creating courses, lessons, and quizzes in Tutor LMS using CSV files. This system enables rapid course development and deployment for educational platforms.

## 🚀 Features

- **📊 CSV-Based Course Creation**: Create complete courses from structured CSV files
- **🎥 Video Lesson Support**: Automatic YouTube video integration with VTT subtitles
- **📚 E-book Lesson Support**: PDF integration with Google Docs viewer
- **❓ Quiz System**: Multiple choice and true/false questions with configurable settings
- **🔐 Role-Based Access**: Secure access control for course creators
- **📤 Course Export**: Export course video links and structure
- **🧪 Testing Tools**: Built-in debugging and testing utilities

## 📋 Requirements

- WordPress 5.0+
- Tutor LMS Plugin
- PHP 7.4+
- MySQL 5.7+
- Geeks Theme (or compatible theme)

## 🏗️ Architecture

### System Components

1. **Core Modules**:
   - `capabilities.php` - User permissions and access control
   - `csv-parser.php` - CSV file processing and validation
   - `course-creator.php` - Course, topic, and lesson creation
   - `quiz-creator.php` - Quiz and question management
   - `course-exporter.php` - Course data export functionality

2. **User Interface**:
   - `course-shortcodes.php` - WordPress shortcodes for frontend access
   - Responsive forms and interfaces

3. **System Loader**:
   - `course-automation-loader.php` - Main system initialization and file loading

## 📊 CSV Format

The system uses a structured CSV format with the following columns:

```csv
row_type,course_title,topic_title,item_title,lesson_type,content,description,order,video_url,vtt_path,pdf_url,quiz_settings,question_type,question_points,answer_text,is_correct,answer_order
```

### Row Types:
- `course` - Course definition
- `topic` - Course module/section
- `lesson` - Individual lesson (video or ebook)
- `quiz` - Quiz definition
- `question` - Quiz question
- `answer` - Question answer option

## 🛠️ Installation

### 1. Clone Repository
```bash
git clone https://github.com/dbs-web/course_creator.git
cd course_creator
```

### 2. Deploy to WordPress
```bash
# Copy child theme to WordPress
cp -r themes/geeks-child/ /path/to/wordpress/wp-content/themes/

# Activate child theme in WordPress admin
```

### 3. Configure Permissions
The system automatically adds the `export_course_data` capability to:
- Users with `gestor` role
- Users with `manage_options` capability (administrators)

## 🎯 Usage

### Available Shortcodes

1. **`[panapana_csv_course_creator]`** - CSV course upload interface
2. **`[tutor_course_exporter]`** - Course export functionality  
3. **`[panapana_hello_world_course]`** - Test course creator
4. **`[panapana_master_debug]`** - System debugging information

### Creating Courses from CSV

1. Prepare your CSV file following the format specification
2. Use the `[panapana_csv_course_creator]` shortcode on any page
3. Upload your CSV file
4. Click "Criar Cursos" to process

### Example CSV Structure
```csv
row_type,course_title,topic_title,item_title,lesson_type,video_url,pdf_url
course,"Git Basics","","","","",""
topic,"Git Basics","Module 1","","","",""
lesson,"Git Basics","Module 1","Introduction",video,"https://youtube.com/watch?v=abc123",""
lesson,"Git Basics","Module 1","Manual",ebook,"","https://example.com/manual.pdf"
```

## 🧪 Testing

### Test Files Available:
- `tests/debug-shortcodes.php` - Shortcode functionality testing
- `tests/test-csv-automation.php` - CSV processing testing
- `backlog/sample-course.csv` - Example course data

### Running Tests:
1. Access test files via browser: `http://yoursite.com/debug-shortcodes.php`
2. Check system status and function availability
3. Test CSV processing with sample data

## 🔧 Development

### Multi-Device Workflow:
1. **Work PC**: Make changes, commit, push to GitHub
2. **Home PC**: Pull changes, sync database if needed
3. Both environments use the same child theme structure

### Key Development Principles:
- **Modular Architecture**: Separated by functionality
- **Single Responsibility**: Each file has a specific purpose
- **WordPress Standards**: Follows WordPress coding standards
- **Security First**: Proper sanitization and capability checks

## 📚 Documentation

- `docs/ARCHITECTURE.md` - Detailed system architecture
- `docs/CSV-FORMAT.md` - Complete CSV format specification
- `docs/DEPLOYMENT.md` - Deployment and setup guide
- `docs/TROUBLESHOOTING.md` - Common issues and solutions

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues and questions:
1. Check the troubleshooting guide
2. Review existing GitHub issues
3. Create a new issue with detailed information

---

**Built with ❤️ for educational content creators**
