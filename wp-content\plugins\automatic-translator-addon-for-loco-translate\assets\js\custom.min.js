const AutoTranslator=function(window,$){const locoConf=window.locoConf,configData=window.extradata,{ajax_url:ajaxUrl,nonce:nonce,ATLT_URL:ATLT_URL,extra_class:rtlClass}=configData;function onLoad(){if(locoConf&&locoConf.conf){const{conf:conf}=locoConf,allStrings=locoConf.conf.podata;allStrings.shift();const{locale:locale,project:project}=conf,projectId=generateProjectId(project,locale),widgetType="yandex";createStringsModal(projectId,widgetType),addStringsInModal(allStrings)}}function initialize(){const{conf:conf}=locoConf,{locale:locale,project:project}=conf;0===$("#loco-editor nav").find("#cool-auto-translate-btn").length&&addAutoTranslationBtn(),settingsModel(),$("#cool-auto-translate-btn").on("click",openSettingsModel),$("button.icon-robot[data-loco='auto']").on("click",onAutoTranslateClick),$("#atlt_yandex_translate_btn").on("click",(function(){onYandexTranslateClick(locale)})),$(".atlt_save_strings").on("click",onSaveClick),$(".atlt-translater").on("click",e=>{["BUTTON","IMG","A"].includes(e.target.tagName)||jQuery(e.delegateTarget).find(".inputGroup button.button[id]").trigger("click")})}function destroyYandexTranslator(){$(".yt-button__icon.yt-button__icon_type_right").trigger("click"),$(".atlt_custom_model.yandex-widget-container").find(".atlt_string_container").scrollTop(0);const progressContainer=$(".modal-body.yandex-widget-body").find(".atlt_translate_progress");progressContainer.hide(),progressContainer.find(".progress-wrapper").hide(),progressContainer.find("#myProgressBar").css("width","0"),progressContainer.find("#progressText").text("0%")}function addStringsInModal(allStrings){const plainStrArr=filterRawObject(allStrings,"plain");plainStrArr.length>0?printStringsInPopup(plainStrArr,type="yandex"):($("#ytWidget").hide(),$(".notice-container").addClass("notice inline notice-warning").html("There is no plain string available for translations."),$(".atlt_string_container, .choose-lang, .translator-widget, .notice-info, .is-dismissible").hide())}function generateProjectId(project,locale){const{domain:domain}=project||{},{lang:lang,region:region}=locale;return project?`${domain}-${lang}-${region}`:`temp-${lang}-${region}`}function onYandexTranslateClick(locale){const defaultcode=locale.lang||null;let defaultlang="";const langMapping={bel:"be",he:"iw",snd:"sd",jv:"jv",nb:"no",nn:"no"};defaultlang=langMapping[defaultcode]||defaultcode;let modelContainer=$("div#atlt_strings_model.yandex-widget-container");modelContainer.find(".atlt_actions > .atlt_save_strings").prop("disabled",!0),modelContainer.find(".atlt_stats").hide(),localStorage.setItem("lang",defaultlang);const supportedLanguages=["af","jv","no","am","ar","az","ba","be","bg","bn","bs","ca","ceb","cs","cy","da","de","el","en","eo","es","et","eu","fa","fi","fr","ga","gd","gl","gu","he","hi","hr","ht","hu","hy","id","is","it","ja","jv","ka","kk","km","kn","ko","ky","la","lb","lo","lt","lv","mg","mhr","mi","mk","ml","mn","mr","mrj","ms","mt","my","ne","nl","no","pa","pap","pl","pt","ro","ru","si","sk","sl","sq","sr","su","sv","sw","ta","te","tg","th","tl","tr","tt","udm","uk","ur","uz","vi","xh","yi","zh"];supportedLanguages.includes(defaultlang)?($("#atlt-dialog").dialog("close"),modelContainer.fadeIn("slow")):($("#atlt-dialog").dialog("close"),modelContainer.find(".notice-container").addClass("notice inline notice-warning").html("Yandex Automatic Translator Does not support this language."),modelContainer.find(".atlt_string_container, .choose-lang, .atlt_save_strings, #ytWidget, .translator-widget, .notice-info, .is-dismissible").hide(),modelContainer.fadeIn("slow"))}function onSaveClick(){let pluginOrTheme="",pluginOrThemeName="";if(locoConf&&locoConf.conf&&locoConf.conf.project&&locoConf.conf.project.bundle)if(pluginOrTheme=locoConf.conf.project.bundle.split(".")[0],"theme"===pluginOrTheme)pluginOrThemeName=locoConf.conf.project.domain||"";else{const match=locoConf.conf.project.bundle.match(/^[^.]+\.(.*?)(?=\/)/);pluginOrThemeName=match?match[1]:""}let translatedObj=[];const rpl={'"% s"':'"%s"','"% d"':'"%d"','"% S"':'"%s"','"% D"':'"%d"',"% s":" %s ","% S":" %s ","% d":" %d ","% D":" %d ","٪ s":" %s ","٪ S":" %s ","٪ d":" %d ","٪ D":" %d ","٪ س":" %s ","%S":" %s ","%D":" %d ","% %":"%%"},regex=/(\%\s*\d+\s*\$?\s*[a-z0-9])/gi;$(".atlt_strings_table tbody tr").each((function(){const source=$(this).find("td.source").text(),target=$(this).find("td.target").text(),improvedTarget1=strtr(target,rpl),improvedSource1=strtr(source,rpl),improvedTarget=improvedTarget1.replace(regex,(function(match){return match.replace(/\s/g,"").toLowerCase()})),improvedSource=improvedSource1.replace(regex,(function(match){return match.replace(/\s/g,"").toLowerCase()}));translatedObj.push({source:improvedSource,target:improvedTarget})}));const container=$(this).closest(".atlt_custom_model"),time_taken=container.data("translation-time")||0,translation_provider=container.data("translation-provider"),{lang:lang,region:region}=locoConf.conf.locale,target_language=region?`${lang}_${region}`:lang,totalCharacters=translatedObj.reduce((sum,item)=>sum+item.source.length,0),totalStrings=translatedObj.length,translationData={time_taken:time_taken,translation_provider:translation_provider,pluginORthemeName:pluginOrThemeName,target_language:target_language,total_characters:totalCharacters,total_strings:totalStrings};var projectId=$(this).parents("#atlt_strings_model").find("#project_id").val();saveTranslatedStrings(translatedObj,projectId,translationData),$(".atlt_custom_model").fadeOut("slow"),$("html").addClass("merge-translations"),updateLocoModel()}function onAutoTranslateClick(e){if(void 0!==e.originalEvent)var checkModal=setInterval((function(){var locoModal=$(".loco-modal"),locoBatch=locoModal.find("#loco-apis-batch"),locoTitle=locoModal.find(".ui-dialog-titlebar .ui-dialog-title"),opt;locoBatch.length&&!locoModal.is(":hidden")&&(locoModal.removeClass("addtranslations"),locoBatch.find("select#auto-api").show(),locoBatch.find("a.icon-help, a.icon-group").show(),locoBatch.find("#loco-job-progress").show(),locoTitle.html("Auto-translate this file"),locoBatch.find("button.button-primary span").html("Translate"),1===locoBatch.find("select#auto-api option").length&&(locoBatch.find(".noapiadded").remove(),locoBatch.removeClass("loco-alert"),locoBatch.find("form").hide(),locoBatch.addClass("loco-alert"),locoTitle.html("No translation APIs configured"),locoBatch.append("<div class='noapiadded'>\n                            <p>Add automatic translation services in the plugin settings.<br>or<br>Use <strong>Auto Translate</strong> addon button.</p>\n                            <nav>\n                                <a href='http://locotranslate.local/wp-admin/admin.php?page=loco-config&amp;action=apis' class='button button-link has-icon icon-cog'>Settings</a>\n                                <a href='https://localise.biz/wordpress/plugin/manual/providers' class='button button-link has-icon icon-help' target='_blank'>Help</a>\n                                <a href='https://localise.biz/wordpress/translation?l=de-DE' class='button button-link has-icon icon-group' target='_blank'>Need a human?</a>\n                            </nav>\n                        </div>")),clearInterval(checkModal))}),100)}function updateLocoModel(){var checkModal=setInterval((function(){var locoModel=$(".loco-modal"),locoModelApisBatch=$(".loco-modal #loco-apis-batch");if(locoModel.length&&locoModel.attr("style").indexOf("none")<=-1&&locoModel.find("#loco-job-progress").length){$("html").removeClass("merge-translations"),locoModelApisBatch.find("a.icon-help, a.icon-group, #loco-job-progress").hide(),locoModelApisBatch.find("select#auto-api").hide();var currentState=$("select#auto-api option[value='loco_auto']").prop("selected","selected");locoModelApisBatch.find("select#auto-api").val(currentState.val()),locoModel.find(".ui-dialog-titlebar .ui-dialog-title").html("Step 3 - Add Translations into Editor and Save"),locoModelApisBatch.find("button.button-primary span").html("Start Adding Process"),locoModelApisBatch.find("button.button-primary").on("click",(function(){$(this).find("span").html("Adding...")})),locoModel.addClass("addtranslations"),$(".noapiadded").remove(),locoModelApisBatch.find("form").show(),locoModelApisBatch.removeClass("loco-alert"),clearInterval(checkModal)}}),200)}function filterRawObject(rawArray,filterType){return rawArray.filter(item=>!(!item.source||item.target)&&(!(ValidURL(item.source)||isHTML(item.source)||isSpecialChars(item.source)||isEmoji(item.source)||item.source.includes("#"))&&(isPlacehodersChars(item.source),!0)))}function ValidURL(str){var pattern;return/(ftp|http|https):\/\/(\w+:{0,1}\w*@)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?/.test(str)}function isHTML(str){var rgex;return/<(?=.*? .*?\/ ?>|br|hr|input|!--|wbr)[a-z]+.*?>|<([a-z]+).*?<\/\1>/i.test(str)}function isSpecialChars(str){var rgex;return/[@^{}|<>]/g.test(str)}function isEmoji(str){var ranges=["(?:[✀-➿]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff]|[#-9]️?⃣|㊙|㊗|〽|〰|Ⓜ|\ud83c[\udd70-\udd71]|\ud83c[\udd7e-\udd7f]|🆎|\ud83c[\udd91-\udd9a]|\ud83c[\udde6-\uddff]|[\ud83c[\ude01-\ude02]|🈚|🈯|[\ud83c[\ude32-\ude3a]|[\ud83c[\ude50-\ude51]|‼|⁉|[▪-▫]|▶|◀|[◻-◾]|©|®|™|ℹ|🀄|[☀-⛿]|⬅|⬆|⬇|⬛|⬜|⭐|⭕|⌚|⌛|⌨|⏏|[⏩-⏳]|[⏸-⏺]|🃏|⤴|⤵|[←-⇿])"];return str.match(ranges.join("|"))}function isPlacehodersChars(str){var rgex;return/%s|%d/g.test(str)}function strtr(s,p,r){return!!s&&{2:function(){for(var i in p)s=strtr(s,i,p[i]);return s},3:function(){return s.replace(RegExp(p,"g"),r)},0:function(){}}[arguments.length]()}function saveTranslatedStrings(translatedStrings,projectId,translationData){if(translatedStrings&&translatedStrings.length>0){const batchSize=2500;for(let i=0;i<translatedStrings.length;i+=batchSize){const batch=translatedStrings.slice(i,i+batchSize),part=`-part-${Math.ceil(i/batchSize)}`;sendBatchRequest(batch,projectId,part,translationData)}}}function sendBatchRequest(stringData,projectId,part,translationData){const data={action:"save_all_translations",data:JSON.stringify(stringData),part:part,"project-id":projectId,wpnonce:nonce,translation_data:JSON.stringify(translationData)};jQuery.post(ajaxUrl,data,(function(response){$("#loco-editor nav").find("button").each((function(i,el){var id;"auto"==el.getAttribute("data-loco")&&($(el).hasClass("model-opened")&&$(el).removeClass("model-opened"),$(el).addClass("model-opened"),$(el).trigger("click"))}))}))}function addAutoTranslationBtn(){const existingBtn=$("#loco-editor nav").find("#cool-auto-translate-btn");existingBtn.length>0&&existingBtn.remove();const locoActions=$("#loco-editor nav").find("#loco-actions"),autoTranslateBtn=$('<fieldset><button id="cool-auto-translate-btn" class="button has-icon icon-translate">Auto Translate</button></fieldset>');locoActions.append(autoTranslateBtn)}function openSettingsModel(){$("#atlt-dialog").dialog({dialogClass:rtlClass,resizable:!1,height:"auto",draggable:!1,width:400,modal:!0,buttons:{Cancel:function(){$(this).dialog("close")}}})}onLoad();var gModal=document.getElementById("atlt_strings_model");function encodeHtmlEntity(str){for(var buf=[],i=str.length-1;i>=0;i--)buf.unshift(["&#",str[i].charCodeAt(),";"].join(""));return buf.join("")}function printStringsInPopup(jsonObj,type){let html="",totalTChars=0,index=1;if(jsonObj){let wordCount=0;for(const key in jsonObj)if(jsonObj.hasOwnProperty(key)){const element=jsonObj[key],sourceText=element.source.trim();wordCount+=sourceText.trim().split(/\s+/).length,""!==sourceText&&("yandex"===type||key<=2500)&&(html+=`<tr id="${key}"><td>${index}</td><td class="notranslate source">${"yandex"===type?encodeHtmlEntity(sourceText):sourceText}</td>`,html+="yandex"===type?`<td translate="yes" class="target translate">${sourceText}</td></tr>`:'<td class="target translate"></td></tr>',index++,totalTChars+=sourceText.length)}$(".atlt_stats").each((function(){$(this).find(".totalChars").html(totalTChars)}))}$(".atlt_strings_table > tbody.atlt_strings_body").html(html)}function settingsModel(){const ytPreviewImg=ATLT_URL+"assets/images/"+extradata.yt_preview,gtPreviewImg=ATLT_URL+"assets/images/"+extradata.gt_preview,dplPreviewImg=ATLT_URL+"assets/images/"+extradata.dpl_preview,chatGPTPreviewImg=ATLT_URL+"assets/images/"+extradata.chatGPT_preview,geminiAIPreviewImg=ATLT_URL+"assets/images/"+extradata.geminiAI_preview,chromeAiPreviewImg=ATLT_URL+"assets/images/"+extradata.chromeAi_preview,documentPreviewImg=ATLT_URL+"assets/images/"+extradata.document_preview,informationPreviewImg=ATLT_URL+"assets/images/"+extradata.information_preview,modelHTML=`\n            <div id="atlt-dialog" title="Step 1 - Select Translation Provider" style="display:none;">\n                <div class="atlt-settings">\n                    <div class="atlt-translater-row">\n                        <div class="atlt-translater">\n                            <div class="atlt-translater-icon">\n                                <a href="https://locoaddon.com/docs/translate-plugin-theme-via-yandex-translate/?utm_source=atlt_plugin&utm_medium=inside&utm_campaign=docs&utm_content=popup_yandex" target="_blank"><img src="${documentPreviewImg}" alt="Documentation"></a>\n                                <a href="https://translate.yandex.com/" target="_blank"><img src="${informationPreviewImg}" alt="Information"></a>\n                            </div>\n                            <strong class="atlt-heading">Translate Using Yandex Page Translate Widget</strong>\n                            <div class="inputGroup">\n                                <img  class="pro-features-img" src="${ytPreviewImg}" alt="powered by Yandex Translate Widget">\n                                <br/><button id="atlt_yandex_translate_btn" class="notranslate button button-primary">Yandex Translate</button>\n                                <span class="proonly-button alsofree">✔ Available</span>\n                            </div>\n                        </div>\n\n                        <div class="atlt-translater">\n                            <div class="atlt-translater-icon">\n                                <a href="https://locoaddon.com/docs/how-to-use-chrome-ai-auto-translations/?utm_source=atlt_plugin&utm_medium=inside&utm_campaign=docs&utm_content=popup_chrome" target="_blank"><img src="${documentPreviewImg}" alt="Documentation"></a>\n                                <a href="https://developer.chrome.com/docs/ai/translator-api" target="_blank"><img src="${informationPreviewImg}" alt="Information"></a>\n                            </div>\n                            <strong class="atlt-heading">Translate Using Chrome Built-in AI</strong>\n                            <div class="inputGroup">\n                                <img  class="pro-features-img" src="${chromeAiPreviewImg}" width="100" alt="powered by Chrome built-in API">\n                                <br/><button id="ChromeAiTranslator_settings_btn" disabled="disabled" class="notranslate button button-primary">Chrome AI Translator (Beta)</button>\n                                <span class="proonly-button"><a href="https://locoaddon.com/pricing/?utm_source=atlt_plugin&utm_medium=inside&utm_campaign=get_pro&utm_content=popup_chrome" target="_blank" title="Buy Pro">PRO Only</a></span>\n                            </div>\n                        </div>\n\n                        <div class="atlt-translater">\n                            <div class="atlt-translater-icon">\n                                <a href="https://locoaddon.com/docs/auto-translations-via-google-translate/?utm_source=atlt_plugin&utm_medium=inside&utm_campaign=docs&utm_content=popup_google" target="_blank"><img src="${documentPreviewImg}" alt="Documentation"></a>\n                                <a href="https://translate.google.com/" target="_blank"><img src="${informationPreviewImg}" alt="Information"></a>\n                            </div>\n                            <strong class="atlt-heading">Translate Using Google Page Translate Widget</strong>\n                            <div class="inputGroup">\n                                <img  class="pro-features-img" src="${gtPreviewImg}" alt="powered by Google Translate Widget">\n                                <br/><button id="atlt_gtranslate_btn" disabled="disabled" class="notranslate button button-primary">Google Translate</button>\n                                <span class="proonly-button"><a href="https://locoaddon.com/pricing/?utm_source=atlt_plugin&utm_medium=inside&utm_campaign=get_pro&utm_content=popup_google" target="_blank" title="Buy Pro">PRO Only</a></span>\n                            </div>\n                        </div>\n                    </div>\n                        \n                    <div class="atlt-translater-row">\n                        <div class="atlt-translater">\n                            <div class="atlt-translater-icon">\n                                <a href="https://locoaddon.com/docs/gemini-ai-translations-wordpress/?utm_source=atlt_plugin&utm_medium=inside&utm_campaign=docs&utm_content=popup_gemini" target="_blank"><img src="${documentPreviewImg}" alt="Documentation"></a>\n                                <a href="https://locoaddon.com/docs/pro-plugin/how-to-use-gemini-ai-to-translate-plugins-or-themes/?utm_source=atlt_plugin&utm_medium=popup&utm_campaign=docs&utm_content=gemini_ai_translation" target="_blank"><img src="${informationPreviewImg}" alt="Information"></a>\n                            </div>\n                            <strong class="atlt-heading">Translate Using AI APIs</strong>\n                            <div class="inputGroup">\n                                <img  class="pro-features-img" src="${geminiAIPreviewImg}" alt="powered by GeminiAI Translate">\n                                <br/><button id="atlt_openAI_btn" disabled="disabled" class="notranslate button button-primary">AI Translate (GeminiAI / OpenAI)</button>\n                                <span class="proonly-button"><a href="https://locoaddon.com/pricing/?utm_source=atlt_plugin&utm_medium=inside&utm_campaign=get_pro&utm_content=popup_gemini" target="_blank" title="Buy Pro">PRO Only</a></span>\n                            </div>\n                        </div>\n                        \n                        <div class="atlt-translater">\n                            <div class="atlt-translater-icon">\n                                <a href="https://locoaddon.com/docs/chatgpt-ai-translations-wordpress/?utm_source=atlt_plugin&utm_medium=inside&utm_campaign=docs&utm_content=popup_chatgpt" target="_blank"><img src="${documentPreviewImg}" alt="Documentation"></a>\n                                <a href="https://chat.openai.com/" target="_blank"><img src="${informationPreviewImg}" alt="Information"></a>\n                            </div>\n                            <strong class="atlt-heading">Translate Using ChatGPT Translator</strong>\n                            <div class="inputGroup">\n                                <img  class="pro-features-img" src="${chatGPTPreviewImg}" alt="powered by ChatGPT Translate">\n                                <br/><button id="atlt_chatGPT_btn" disabled="disabled" class="notranslate button button-primary">ChatGPT Translate</button>\n                                <span class="proonly-button"><a href="https://locoaddon.com/pricing/?utm_source=atlt_plugin&utm_medium=inside&utm_campaign=get_pro&utm_content=popup_chatgpt" target="_blank" title="Buy Pro">PRO Only</a></span>\n                            </div>\n                        </div>\n\n                        <div class="atlt-translater">\n                            <div class="atlt-translater-icon">\n                                <a href="https://locoaddon.com/docs/translate-via-deepl-doc-translator/?utm_source=atlt_plugin&utm_medium=inside&utm_campaign=docs&utm_content=popup_deepl" target="_blank"><img src="${documentPreviewImg}" alt="Documentation"></a>\n                                <a href="https://www.deepl.com/en/translator" target="_blank"><img src="${informationPreviewImg}" alt="Information"></a>\n                            </div>\n                            <strong class="atlt-heading">Translate Using Deepl Doc Translator</strong>\n                            <div class="inputGroup">\n                                <img class="pro-features-img" src="${dplPreviewImg}" alt="powered by DeepL Translate">\n                                <br/><button  disabled="disabled" id="atlt_deepl_btn" class="notranslate button button-primary">DeepL Translate</button>\n                                <span class="proonly-button"><a href="https://locoaddon.com/pricing/?utm_source=atlt_plugin&utm_medium=inside&utm_campaign=get_pro&utm_content=popup_deepl" target="_blank" title="Buy Pro">PRO Only</a></span>\n                            </div>\n                        </div>\n                    </div> \n                </div>\n            </div>\n        `;$("body").append(modelHTML)}function createStringsModal(projectId,widgetType){let{wrapperCls:wrapperCls,headerCls:headerCls,bodyCls:bodyCls,footerCls:footerCls}=getWidgetClasses("yandex"),modelHTML=`\n            <div id="atlt_strings_model" class="modal atlt_custom_model  ${wrapperCls} ${rtlClass}">\n                <div class="modal-content">\n                    <input type="hidden" id="project_id" value="${projectId}"> \n                    ${modelHeaderHTML(widgetType,headerCls)}   \n                    ${modelBodyHTML(widgetType,bodyCls)}   \n                    ${modelFooterHTML(widgetType,footerCls)}   \n            </div></div>`;$("body").append(modelHTML)}function getWidgetClasses(widgetType){let wrapperCls="",headerCls="",bodyCls="",footerCls="";switch(widgetType){case"yandex":default:wrapperCls="yandex-widget-container",headerCls="yandex-widget-header",bodyCls="yandex-widget-body",footerCls="yandex-widget-footer"}return{wrapperCls:wrapperCls,headerCls:headerCls,bodyCls:bodyCls,footerCls:footerCls}}function modelBodyHTML(widgetType,bodyCls){const HTML=`\n        <div class="modal-body  ${bodyCls}">\n            <div class="atlt_translate_progress">\n                Automatic translation is in progress....<br/>\n                It will take a few minutes, enjoy ☕ coffee in this time!<br/><br/>\n                Please do not leave this window or browser tab while the translation is in progress...\n\n                 <div class="progress-wrapper">\n                    <div class="progress-container">\n                        <div class="progress-bar" id="myProgressBar">\n                            <span id="progressText">0%</span>\n                        </div>\n                    </div>\n                </div>\n            </div>\n            ${translatorWidget(widgetType)}\n            <div class="atlt_string_container">\n                <table class="scrolldown atlt_strings_table">\n                    <thead>\n                        <th class="notranslate">S.No</th>\n                        <th class="notranslate">Source Text</th>\n                        <th class="notranslate">Translation</th>\n                    </thead>\n                    <tbody class="atlt_strings_body">\n                    </tbody>\n                </table>\n            </div>\n            <div class="notice-container"></div>\n        </div>`;return HTML}function modelHeaderHTML(widgetType,headerCls){const HTML=`\n        <div class="modal-header  ${headerCls}">\n                        <span class="close">&times;</span>\n                        <h2 class="notranslate">Step 2 - Start Automatic Translation Process</h2>\n                        <div class="atlt_actions">\n                            <button class="notranslate atlt_save_strings button button-primary" disabled="true">Merge Translation</button>\n                        </div>\n                        <div style="display:none" class="atlt_stats hidden">\n                            Wahooo! You have saved your valuable time via auto translating \n                            <strong class="totalChars"></strong> characters  using \n                            <strong>\n                                <a href="https://wordpress.org/support/plugin/automatic-translator-addon-for-loco-translate/reviews/#new-post" target="_new">\n                                    Loco Automatic Translate Addon\n                                </a>\n                            </strong>\n                        </div>\n                    </div>\n                    <div class="notice inline notice-info is-dismissible">\n                        Plugin will not translate any strings with HTML or special characters because Yandex Translator currently does not support HTML and special characters translations.\n                        You can edit translated strings inside Loco Translate Editor after merging the translations. Only special characters (%s, %d) fixed at the time of merging of the translations.\n                    </div>\n                    <div class="notice inline notice-info is-dismissible">\n                        Machine translations are not 100% correct.\n                        Please verify strings before using on the production website.\n                    </div>`;return HTML}function modelFooterHTML(widgetType,footerCls){const HTML=` <div class="modal-footer ${footerCls}">\n        <div class="atlt_actions">\n            <button class="notranslate atlt_save_strings button button-primary" disabled="true">Merge Translation</button>\n        </div>\n        <div style="display:none" class="atlt_stats">\n            Wahooo! You have saved your valuable time via auto translating \n            <strong class="totalChars"></strong> characters  using \n            <strong>\n                <a href="https://wordpress.org/support/plugin/automatic-translator-addon-for-loco-translate/reviews/#new-post" target="_new">\n                    Loco Automatic Translate Addon\n                </a>\n            </strong>\n        </div>\n    </div>`;return HTML}function translatorWidget(widgetType){if("yandex"===widgetType){const widgetPlaceholder='<div id="ytWidget">..Loading</div>';return`\n                <div class="translator-widget">\n                    <h3 class="choose-lang">Choose language <span class="dashicons-before dashicons-translation"></span></h3>\n                    ${widgetPlaceholder}\n                </div>`}return""}$(window).click((function(event){event.target.closest(".modal-content")||destroyYandexTranslator(),event.target==gModal&&(gModal.style.display="none")})),$("#atlt_strings_model").find(".close").on("click",(function(){destroyYandexTranslator(),$("#atlt_strings_model").fadeOut("slow")})),$(document).ready((function(){initialize()}))}(window,jQuery);