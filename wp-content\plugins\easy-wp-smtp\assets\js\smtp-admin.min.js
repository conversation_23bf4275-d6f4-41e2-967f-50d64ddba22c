"use strict";var EasyWPSMTP=window.EasyWPSMTP||{};EasyWPSMTP.Admin=EasyWPSMTP.Admin||{},EasyWPSMTP.Admin.Settings=EasyWPSMTP.Admin.Settings||function(a,n,i){var o={pluginSettingsChanged:!1,init:function(){i(o.ready)},ready:function(){o.pageHolder=i(".easy-wp-smtp-tab-settings"),o.settingsForm=i(".easy-wp-smtp-connection-settings-form"),0<i("#screen-meta-links").length&&(i("#screen-meta-links, #screen-meta").prependTo("#easy-wp-smtp-header-temp"),i("#screen-meta-links").show()),o.bindActions(),o.setJQueryConfirmDefaults(),i("#easy-wp-smtp-clean-debug-log").click(function(e){e.preventDefault();var a=i(this);i.confirm({backgroundDismiss:!1,escapeKey:!0,animationBounce:1,type:"orange",icon:o.getModalIcon("exclamation-triangle-orange"),title:easy_wp_smtp.heads_up_title,content:easy_wp_smtp.clear_debug_log,buttons:{confirm:{text:easy_wp_smtp.yes_text,btnClass:"btn-confirm",keys:["enter"],action:function(){a.addClass("easy-wp-smtp-btn--loading"),jQuery.ajax({url:ajaxurl,type:"post",data:{action:"swpsmtp_clear_log",nonce:easy_wp_smtp.nonce}}).done(function(e){var t,s,e="1"===e?(t=easy_wp_smtp.debug_log_cleared,s="check-circle-green","green"):(t=easy_wp_smtp.error_occurred+" "+e,s="times-circle-red","red");o.displayAlertModal(t,s,e),a.removeClass("easy-wp-smtp-btn--loading")})}},cancel:{text:easy_wp_smtp.cancel_text,btnClass:"btn-cancel"}}})})},bindActions:function(){o.mailers.smtp.bindActions(),o.triggerExitNotice(),o.beforeSaveChecks(),i(".easy-wp-smtp-meta-box__header").on("click",function(e){"A"!==e.target.tagName&&"BUTTON"!==e.target.tagName&&i(this).closest(".easy-wp-smtp-meta-box").toggleClass("easy-wp-smtp-meta-box--closed")}),i(".easy-wp-smtp-mailers-picker__input",o.settingsForm).on("change",function(){i(".easy-wp-smtp-mailer-options",o.settingsForm).removeClass("easy-wp-smtp-mailer-options--active"),i('.easy-wp-smtp-mailer-options[data-mailer="'+i(this).val()+'"]',o.settingsForm).addClass("easy-wp-smtp-mailer-options--active")}),i(".easy-wp-smtp-mailers-picker__mailer--disabled",o.settingsForm).on("click",function(){var e=i(this).prev(".easy-wp-smtp-mailers-picker__input");e.hasClass("easy-wp-smtp-educate")&&o.education.upgradeMailer(e)}),i(".easy-wp-smtp-mailers-picker__input",o.settingsForm).on("change",this.processMailerSettingsOnChange),i("#easy-wp-smtp-setting-advanced",o.settingsForm).on("change",function(){i(this).closest(".easy-wp-smtp-row").nextAll(".easy-wp-smtp-row")[i(this).is(":checked")?"removeClass":"addClass"]("easy-wp-smtp-hidden")}),i("#easy-wp-smtp-setting-test_email_custom").on("change",function(){i("#easy-wp-smtp-setting-row-test_email_subject, #easy-wp-smtp-setting-row-test_email_message").toggle(i(this).is(":checked")),i("#easy-wp-smtp-setting-test_email_subject, #easy-wp-smtp-setting-test_email_message").prop("required",i(this).is(":checked")),i("#easy-wp-smtp-setting-test_email_html").prop("disabled",i(this).is(":checked"));var e=i("#easy-wp-smtp-setting-test_email_html");i(this).is(":checked")?(e.data("value",e.is(":checked")),e.prop("checked",!1).prop("disabled",!0)):e.prop("checked",e.data("value")).prop("disabled",!1)}),i(".js-easy-wp-smtp-pro-banner-dismiss",o.pageHolder).on("click",function(e){e.preventDefault(),i.ajax({url:ajaxurl,dataType:"json",type:"POST",data:{action:"easy_wp_smtp_ajax",task:"pro_banner_dismiss",nonce:easy_wp_smtp.nonce}}).always(function(){i(".easy-wp-smtp-pro-banner",o.pageHolder).fadeOut("fast")})}),i(".js-easy-wp-smtp-mailer-notice-dismiss",o.settingsForm).on("click",function(e){e.preventDefault();var t=i(this),s=t.parents(".easy-wp-smtp-notice");if(t.hasClass("disabled"))return!1;i.ajax({url:ajaxurl,dataType:"json",type:"POST",data:{action:"easy_wp_smtp_ajax",nonce:easy_wp_smtp.nonce,task:"notice_dismiss",notice:s.data("notice"),mailer:s.data("mailer")},beforeSend:function(){t.addClass("disabled")}}).always(function(){s.fadeOut("fast",function(){t.removeClass("disabled")})})}),i(".easy-wp-smtp-test-email-debug .easy-wp-smtp-error-log-toggle").on("click",function(e){e.preventDefault(),i(".easy-wp-smtp-test-email-debug .easy-wp-smtp-error-log").slideToggle()}),i(".easy-wp-smtp-test-email-debug .easy-wp-smtp-error-log-copy").on("click",function(e){e.preventDefault();var t=i(this),e=i(".easy-wp-smtp-test-email-debug .easy-wp-smtp-error-log"),s=(e.is(":visible")||e.addClass("easy-wp-smtp-error-log-selection"),a.createRange());s.selectNode(e[0]),n.getSelection().removeAllRanges(),n.getSelection().addRange(s),a.execCommand("Copy"),n.getSelection().removeAllRanges(),e.removeClass("easy-wp-smtp-error-log-selection"),t.addClass("easy-wp-smtp-error-log-copy-copied"),setTimeout(function(){t.removeClass("easy-wp-smtp-error-log-copy-copied")},1500)}),i(".js-easy-wp-smtp-provider-remove",o.settingsForm).on("click",function(){return confirm(easy_wp_smtp.text_provider_remove)}),i(".easy-wp-smtp-setting-copy",o.settingsForm).on("click",function(e){e.preventDefault();i("#"+i(this).data("source_id")).get(0).select(),a.execCommand("Copy");var t=i(this).find("svg:first-child"),e=i(this).find("svg:last-child");t.hide(),e.show().fadeOut(1e3,"swing",function(){t.fadeIn(200)})}),i(".easy-wp-smtp-tab-tools-test #easy-wp-smtp-email-test-form").on("submit",function(){var e=i(this).find(".easy-wp-smtp-btn");e.attr("disabled",!0),e.addClass("easy-wp-smtp-btn--loading")}),i("#easy-wp-smtp-setting-domain_check").on("change",function(){i("#easy-wp-smtp-setting-domain_check_allowed_domains, #easy-wp-smtp-setting-domain_check_do_not_send").prop("disabled",!i(this).is(":checked"))}),i(".easy-wp-smtp-btn[data-clear-field]").on("click",function(e){var t=i(this),s=t.attr("data-clear-field"),s=i("#"+s);s.prop("disabled",!1),s.attr("name",s.attr("data-name")),s.removeAttr("value"),s.focus(),t.remove()}),i("#easy-wp-smtp-setting-rate_limit-lite").on("click",function(e){e.preventDefault(),o.education.rateLimitUpgrade()})},education:{upgradeModal:function(e,t,s){i.alert({backgroundDismiss:!0,escapeKey:!0,animationBounce:1,type:"blue",closeIcon:!0,title:e,icon:'"></i>'+easy_wp_smtp.education.upgrade_icon_lock+'<i class="',content:t,boxWidth:"550px",onOpenBefore:function(){this.$btnc.after('<div class="easy-wp-smtp-already-purchased">'+easy_wp_smtp.education.upgrade_doc+"</div>"),this.$body.addClass("easy-wp-smtp-upgrade-mailer-education-modal")},buttons:{confirm:{text:easy_wp_smtp.education.upgrade_button,btnClass:"easy-wp-smtp-btn easy-wp-smtp-btn--green",keys:["enter"],action:function(){var e=/(\?)/.test(easy_wp_smtp.education.upgrade_url)?"&":"?",e=easy_wp_smtp.education.upgrade_url+e+"utm_content="+encodeURIComponent(s);n.open(e,"_blank")}}}})},upgradeMailer:function(e){this.upgradeModal(easy_wp_smtp.education.upgrade_title.replace(/%name%/g,e.data("title")),easy_wp_smtp.education.upgrade_content.replace(/%name%/g,e.data("title"))+easy_wp_smtp.education.upgrade_bonus,e.val())},rateLimitUpgrade:function(){this.upgradeModal(easy_wp_smtp.education.rate_limit.upgrade_title,easy_wp_smtp.education.rate_limit.upgrade_content+easy_wp_smtp.education.upgrade_bonus,"rate-limit-setting")}},mailers:{smtp:{bindActions:function(){i("#easy-wp-smtp-setting-smtp-auth").on("change",function(){i("#easy-wp-smtp-setting-row-smtp-user, #easy-wp-smtp-setting-row-smtp-pass").toggleClass("easy-wp-smtp-hidden")}),i("#easy-wp-smtp-setting-row-smtp-encryption input").on("change",function(){var e=i(this),t=i("#easy-wp-smtp-setting-smtp-port",o.settingsForm);"tls"===e.val()?(t.val("587"),i("#easy-wp-smtp-setting-row-smtp-autotls").addClass("easy-wp-smtp-hidden")):("ssl"===e.val()?t.val("465"):t.val("25"),i("#easy-wp-smtp-setting-row-smtp-autotls").removeClass("easy-wp-smtp-hidden"))})}}},triggerExitNotice:function(){var e=i(".easy-wp-smtp-page-general");i(n).on("beforeunload",function(){if(o.pluginSettingsChanged)return easy_wp_smtp.text_settings_not_saved}),i(":input:not( #easy-wp-smtp-setting-license-key, .easy-wp-smtp-not-form-input, #easy-wp-smtp-setting-outlook-one_click_setup_enabled )",e).on("change",function(){o.pluginSettingsChanged=!0}),i("form",e).on("submit",function(){o.pluginSettingsChanged=!1})},beforeSaveChecks:function(){o.settingsForm.on("submit",function(){var e;if("mail"===i(".easy-wp-smtp-mailers-picker__input:checked",o.settingsForm).val())return e=i(this),i.alert({backgroundDismiss:!1,escapeKey:!1,animationBounce:1,type:"orange",icon:o.getModalIcon("exclamation-triangle-orange"),title:easy_wp_smtp.default_mailer_notice.title,content:easy_wp_smtp.default_mailer_notice.content,boxWidth:"550px",buttons:{confirm:{text:easy_wp_smtp.default_mailer_notice.save_button,btnClass:"btn-confirm",keys:["enter"],action:function(){e.off("submit").trigger("submit")}},cancel:{text:easy_wp_smtp.default_mailer_notice.cancel_button,btnClass:"btn-cancel"}}}),!1})},processMailerSettingsOnChange:function(){var e,t=easy_wp_smtp.all_mailers_supports[i(this).val()];for(e in t)t.hasOwnProperty(e)&&i(".js-easy-wp-smtp-setting-"+e,o.settingsForm).toggle(t[e]);var s=i(".js-easy-wp-smtp-setting-from_email");s.closest(".easy-wp-smtp-setting-row").toggle(t.from_email||t.from_email_force),(s=i(".js-easy-wp-smtp-setting-from_name")).closest(".easy-wp-smtp-setting-row").toggle(t.from_name||t.from_name_force)},setJQueryConfirmDefaults:function(){jconfirm.defaults={typeAnimated:!1,draggable:!1,animateFromElement:!1,theme:"modern",boxWidth:"450px",useBootstrap:!1}},displayAlertModal:function(e,t,s,a=void 0){a=a||function(){},i.alert({backgroundDismiss:!0,escapeKey:!0,animationBounce:1,type:s=s||"default",closeIcon:!0,title:!1,icon:t?o.getModalIcon(t):"",content:e,buttons:{confirm:{text:easy_wp_smtp.ok_text,btnClass:"easy-wp-smtp-btn easy-wp-smtp-btn-md",keys:["enter"],action:a}}})},getModalIcon:function(e){return'"></i><img src="'+easy_wp_smtp.plugin_url+"/assets/images/icons/"+e+'.svg" alt=""><i class="'}};return o}(document,window,jQuery),EasyWPSMTP.Admin.Settings.init();