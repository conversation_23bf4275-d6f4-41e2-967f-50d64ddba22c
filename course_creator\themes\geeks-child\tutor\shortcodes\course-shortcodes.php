<?php
/**
 * <PERSON>tor LMS Course Automation - Course Shortcodes
 *
 * This file contains all shortcodes for course creation and export functionality.
 *
 * @package TutorLMS_CourseAutomation
 * @subpackage Shortcodes
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Shortcode for course export interface.
 * Usage: [tutor_course_exporter]
 *
 * @return string HTML output for course export interface.
 */
function panapana_course_exporter_shortcode() {
	// Check permissions
	$permission_error = panapana_check_user_permissions();
	if ( $permission_error ) {
		return $permission_error;
	}

	$courses = panapana_get_courses_for_export();

	if ( empty( $courses ) ) {
		return '<p>Nenhum curso encontrado.</p>';
	}

	ob_start();
	?>
	<div class="tutor-course-exporter">
		<h3>Exportar Dados dos Cursos</h3>
		<p>Clique em "Exportar Links das Aulas" para baixar os dados de um curso específico.</p>
		
		<ul style="list-style: none; padding: 0; border: 1px solid #ddd; border-radius: 5px; max-width: 600px;">
			<?php foreach ( $courses as $course ) : ?>
				<li style="padding: 10px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center;">
					<span><?php echo esc_html( $course->post_title ); ?></span>
					<a href="<?php echo esc_url( add_query_arg( 'export_tutor_course', $course->ID ) ); ?>" 
					   class="tutor-btn tutor-btn-primary tutor-btn-sm" target="_blank">
						Exportar Links das Aulas
					</a>
				</li>
			<?php endforeach; ?>
		</ul>
	</div>
	<?php
	return ob_get_clean();
}
add_shortcode( 'tutor_course_exporter', 'panapana_course_exporter_shortcode' );

/**
 * Shortcode for CSV course creation interface.
 * Usage: [panapana_csv_course_creator]
 *
 * @return string HTML output for CSV course creation interface.
 */
function panapana_csv_course_creator_shortcode() {
	// Check permissions
	$permission_error = panapana_check_user_permissions();
	if ( $permission_error ) {
		return $permission_error;
	}

	$message = '';
	$message_type = '';

	// Handle form submission
	if ( isset( $_POST['panapana_csv_upload'] ) && wp_verify_nonce( $_POST['panapana_csv_nonce'], 'panapana_csv_upload_action' ) ) {
		$result = panapana_process_csv_upload();
		
		if ( is_wp_error( $result ) ) {
			$message = $result->get_error_message();
			$message_type = 'error';
		} else {
			$message = $result;
			$message_type = 'success';
		}
	}

	ob_start();
	?>
	<div class="panapana-csv-course-creator">
		<h3>Criador de Cursos via CSV</h3>
		<p>Faça upload de um arquivo CSV para criar cursos automaticamente no sistema.</p>

		<?php if ( ! empty( $message ) ) : ?>
			<div class="panapana-message panapana-message-<?php echo esc_attr( $message_type ); ?>" 
				 style="padding: 10px; margin: 10px 0; border-radius: 5px; 
						<?php echo $message_type === 'error' ? 'background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24;' : 'background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724;'; ?>">
				<?php echo esc_html( $message ); ?>
			</div>
		<?php endif; ?>

		<form method="post" enctype="multipart/form-data" style="max-width: 500px;">
			<?php wp_nonce_field( 'panapana_csv_upload_action', 'panapana_csv_nonce' ); ?>
			
			<div style="margin-bottom: 15px;">
				<label for="csv_file" style="display: block; margin-bottom: 5px; font-weight: bold;">
					Arquivo CSV:
				</label>
				<input type="file" 
					   id="csv_file" 
					   name="csv_file" 
					   accept=".csv" 
					   required 
					   style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px;">
			</div>

			<div style="margin-bottom: 15px;">
				<input type="submit" 
					   name="panapana_csv_upload" 
					   value="Criar Cursos" 
					   class="button button-primary" 
					   style="background-color: #0073aa; border-color: #0073aa; color: white; padding: 10px 20px; border-radius: 3px; cursor: pointer;">
			</div>
		</form>

		<div class="panapana-csv-help" style="margin-top: 20px; padding: 15px; background-color: #f9f9f9; border-radius: 5px;">
			<h4>Formato do CSV:</h4>
			<p>O arquivo CSV deve conter as seguintes colunas:</p>
			<ul>
				<li><strong>row_type:</strong> course, topic, lesson, quiz, question, answer</li>
				<li><strong>course_title:</strong> Título do curso</li>
				<li><strong>topic_title:</strong> Título do módulo/tópico</li>
				<li><strong>item_title:</strong> Título da aula/quiz/pergunta</li>
				<li><strong>lesson_type:</strong> video, ebook</li>
				<li><strong>video_url:</strong> URL do YouTube (para aulas de vídeo)</li>
				<li><strong>pdf_url:</strong> URL do PDF (para aulas de e-book)</li>
				<li><strong>quiz_settings:</strong> Configurações do quiz (formato: key:value,key:value)</li>
				<li><strong>question_text:</strong> Texto da pergunta</li>
				<li><strong>question_type:</strong> single_choice, true_false</li>
				<li><strong>answer_text:</strong> Texto da resposta</li>
				<li><strong>is_correct:</strong> 1 para resposta correta, 0 para incorreta</li>
			</ul>
		</div>
	</div>
	<?php
	return ob_get_clean();
}
add_shortcode( 'panapana_csv_course_creator', 'panapana_csv_course_creator_shortcode' );

/**
 * Shortcode for creating a simple test course.
 * Usage: [panapana_hello_world_course]
 *
 * @return string HTML output with course creation result.
 */
function panapana_hello_world_course_shortcode() {
	// Check permissions
	$permission_error = panapana_check_user_permissions();
	if ( $permission_error ) {
		return $permission_error;
	}

	$message = '';
	$message_type = '';

	// Handle form submission
	if ( isset( $_POST['create_hello_world'] ) && wp_verify_nonce( $_POST['hello_world_nonce'], 'create_hello_world_action' ) ) {
		$result = panapana_create_hello_world_course();
		
		if ( is_wp_error( $result ) ) {
			$message = 'Erro ao criar curso: ' . $result->get_error_message();
			$message_type = 'error';
		} else {
			$course_edit_url = admin_url( 'post.php?post=' . $result . '&action=edit' );
			$message = 'Curso "Hello World" criado com sucesso! <a href="' . esc_url( $course_edit_url ) . '" target="_blank">Ver curso</a>';
			$message_type = 'success';
		}
	}

	ob_start();
	?>
	<div class="panapana-hello-world-creator">
		<h3>Criador de Curso de Teste</h3>
		<p>Crie um curso de exemplo para testar o funcionamento do sistema.</p>

		<?php if ( ! empty( $message ) ) : ?>
			<div class="panapana-message panapana-message-<?php echo esc_attr( $message_type ); ?>" 
				 style="padding: 10px; margin: 10px 0; border-radius: 5px; 
						<?php echo $message_type === 'error' ? 'background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24;' : 'background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724;'; ?>">
				<?php echo wp_kses_post( $message ); ?>
			</div>
		<?php endif; ?>

		<form method="post">
			<?php wp_nonce_field( 'create_hello_world_action', 'hello_world_nonce' ); ?>
			
			<p>Este curso de teste incluirá:</p>
			<ul>
				<li>1 módulo com 2 aulas (1 vídeo + 1 e-book)</li>
				<li>1 quiz com 1 pergunta de múltipla escolha</li>
				<li>Configurações básicas de curso</li>
			</ul>

			<input type="submit" 
				   name="create_hello_world" 
				   value="Criar Curso de Teste" 
				   class="button button-primary" 
				   style="background-color: #0073aa; border-color: #0073aa; color: white; padding: 10px 20px; border-radius: 3px; cursor: pointer;">
		</form>
	</div>
	<?php
	return ob_get_clean();
}
add_shortcode( 'panapana_hello_world_course', 'panapana_hello_world_course_shortcode' );

/**
 * Shortcode for debugging and system information.
 * Usage: [panapana_master_debug]
 *
 * @return string HTML output with debug information.
 */
function panapana_master_debug_shortcode() {
	// Check permissions
	$permission_error = panapana_check_user_permissions();
	if ( $permission_error ) {
		return $permission_error;
	}

	ob_start();
	?>
	<div class="panapana-debug-info">
		<h3>Informações de Debug do Sistema</h3>

		<h4>Verificação de Capacidades para ESTE Usuário</h4>
		<ul>
			<li>Possui capacidade '<strong>export_course_data</strong>'? 
				<strong style="color: <?php echo current_user_can( 'export_course_data' ) ? 'green' : 'red'; ?>;">
					<?php echo current_user_can( 'export_course_data' ) ? 'SIM' : 'NÃO'; ?>
				</strong>
			</li>
			<li>Possui capacidade '<strong>manage_options</strong>' (Admin)? 
				<strong style="color: <?php echo current_user_can( 'manage_options' ) ? 'green' : 'red'; ?>;">
					<?php echo current_user_can( 'manage_options' ) ? 'SIM' : 'NÃO'; ?>
				</strong>
			</li>
		</ul>

		<h4>Verificação do Role 'gestor'</h4>
		<?php $role_object = get_role( 'gestor' ); ?>
		<?php if ( ! $role_object ) : ?>
			<p style="color: red;"><strong>ERRO CRÍTICO: O role 'gestor' não existe! Verifique o slug do role.</strong></p>
		<?php else : ?>
			<ul>
				<li>O role '<strong>gestor</strong>' possui a capacidade '<strong>export_course_data</strong>'?
					<strong style="color: <?php echo $role_object->has_cap( 'export_course_data' ) ? 'green' : 'red'; ?>;">
						<?php echo $role_object->has_cap( 'export_course_data' ) ? 'SIM' : 'NÃO'; ?>
					</strong>
				</li>
			</ul>
		<?php endif; ?>

		<h4>Informações do Sistema</h4>
		<ul>
			<li><strong>WordPress Version:</strong> <?php echo get_bloginfo( 'version' ); ?></li>
			<li><strong>Usuário Atual:</strong> <?php echo wp_get_current_user()->display_name; ?> (ID: <?php echo get_current_user_id(); ?>)</li>
			<li><strong>Roles do Usuário:</strong> <?php echo implode( ', ', wp_get_current_user()->roles ); ?></li>
		</ul>

		<h4>Shortcodes Disponíveis</h4>
		<ul>
			<li>[tutor_course_exporter] - Interface de exportação de cursos</li>
			<li>[panapana_csv_course_creator] - Criador de cursos via CSV</li>
			<li>[panapana_hello_world_course] - Criador de curso de teste</li>
			<li>[panapana_master_debug] - Esta página de debug</li>
		</ul>
	</div>
	<?php
	return ob_get_clean();
}
add_shortcode( 'panapana_master_debug', 'panapana_master_debug_shortcode' );
