# Translation of Plugins - <PERSON><PERSON> LMS &#8211; eLearning and online course solution - Stable (latest release) in Portuguese (Brazil)
# This file is distributed under the same license as the Plugins - Tutor LMS &#8211; eLearning and online course solution - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-09-09 11:05:40+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: pt_BR\n"
"Project-Id-Version: Plugins - Tutor LMS &#8211; eLearning and online course solution - Stable (latest release)\n"

#: restapi/REST_Topic.php:81
msgid "Topic not found for given course ID"
msgstr "Tópico não encontrado para determinado ID do curso"

#: restapi/REST_Topic.php:58
msgid "course_id is required"
msgstr "course_id é obrigatório"

#: restapi/REST_Lesson.php:61
msgid "topic_id is required"
msgstr "topic_id é obrigatório"

#. translators: %s: available balance
#: templates/dashboard/withdraw.php:79
msgid "You have %s ready to withdraw now"
msgstr "Você tem %s pronto para sacar agora"

#: restapi/REST_Course.php:338
msgid "Contents for this course with the given course id not found"
msgstr "O conteúdo para este curso com o ID fornecido não foi encontrado."

#: restapi/REST_Course.php:330
msgid "Course contents retrieved successfully"
msgstr "Os conteúdos do curso foram recuperados com sucesso."

#: helpers/ValidationHelper.php:155
msgid " record not found"
msgstr " registro não encontrado"

#. translators: %s is anchor link for signin
#: classes/Shortcode.php:113
msgid "Please %s to view this page"
msgstr "%s para visualizar esta página."

#: classes/Shortcode.php:111
msgid "Sign-In"
msgstr "Entrar"

#: views/pages/tools/manage-tokens.php:143
msgid "Update API"
msgstr "Atualizar API"

#: helpers/ValidationHelper.php:164
msgid " user does not exist"
msgstr " usuário não existe"

#: helpers/ValidationHelper.php:144
msgid " invalid date format"
msgstr " formato de data inválido"

#: helpers/ValidationHelper.php:137
msgid " is not an array"
msgstr " não é um array"

#: helpers/ValidationHelper.php:131
msgid " is not boolean"
msgstr " não é booleano"

#: helpers/ValidationHelper.php:125
msgid " string is not valid"
msgstr " string não é válida"

#: helpers/ValidationHelper.php:118
msgid " extension is not valid"
msgstr " extensão não é válida"

#: helpers/ValidationHelper.php:74
msgid " is not numeric"
msgstr " não é numérico"

#: helpers/ValidationHelper.php:67
msgid " is required"
msgstr " é obrigatório"

#: classes/Options_V2.php:1737
msgid "Quiz Permalink"
msgstr "Link permanente do questionário"

#: classes/Options_V2.php:1730
msgid "Lesson Permalink"
msgstr "Link Permanente da lição"

#: classes/Options_V2.php:1723
msgid "Course Permalink"
msgstr "Link Permanente do curso"

#: classes/Options_V2.php:1716
msgid "Base Permalink"
msgstr "Link permanente base"

#: views/pages/feature-promotion.php:20
msgid "Send automated email notifications to students, instructors, and admins based on their actions and events on your eLearning website. Customize email templates, send manual emails, and do much more."
msgstr "Envie notificações de email automatizadas para estudantes, instrutores e administradores com base em suas ações e eventos em seu site de eLearning.Personalize modelos de email, envie e -mails manuais e faça muito mais."

#: views/pages/feature-promotion.php:19
msgid "Built-in email notification and email marketing tool!"
msgstr "Ferramenta de notificação por e-mail interno e marketing por e-mail!"

#: templates/dashboard/instructor/logged-in.php:20
msgid "You have been rejected from being an instructor."
msgstr "Você foi rejeitado para ser instrutor."

#: views/pages/tutor-pro-addons.php:45 views/pages/welcome.php:305
msgid "Compare Free vs Pro"
msgstr "Compare Grátis com Pro"

#: templates/single/quiz/parts/open-ended.php:24
#: templates/single/quiz/parts/short-answer.php:24
msgid "Character Remaining: "
msgstr "Caracteres restantes    "

#: templates/loop/course-continue.php:45
#: templates/single/course/course-entry-box.php:161
msgid "Review Progress"
msgstr "Revise o progresso"

#: views/pages/course-list.php:145
msgid "Schedule"
msgstr "Agendar"

#: views/options/template/common/reset-button-template.php:21
msgid "WARNING! This will overwrite all customized settings of this section and reset them to default. Proceed with caution."
msgstr "AVISO!Isso substituirá todas as configurações personalizadas desta seção e as redefinirá para padrão.Prossiga com cuidado."

#: views/options/template/common/reset-button-template.php:20
msgid "Reset to Default Settings?"
msgstr "Redefinir para configurações padrão?"

#. translators: %s: total pending withdrawal
#: templates/dashboard/withdraw.php:90
msgid "Total Pending Withdrawal %s"
msgstr "Retirada total pendente %s"

#. translators: 1: total pending withdraw request 2: available for withdraw
#: classes/Withdraw.php:246
msgid "You have total %1$s pending withdraw request. You can't make more than %2$s withdraw request at a time"
msgstr "Você tem um total de %1$s de solicitação de saque pendente. Você não pode fazer mais de %2$s solicitações de saque por vez"

#: classes/Course_List.php:169
msgid "Scheduled"
msgstr "Agendado"

#: classes/WhatsNew.php:39 classes/WhatsNew.php:46
msgid "What's New"
msgstr "O que há de novo"

#: classes/Template.php:482
msgid "Profile Page"
msgstr "Página do perfil"

#: views/pages/tools/manage-tokens.php:129
msgid "Generate"
msgstr "Gerar"

#: views/pages/tools/manage-tokens.php:78
msgid "Generate API Key, Secret"
msgstr "Gerar a chave da API, segredo"

#: views/pages/tools/manage-tokens.php:61
msgid "No record available"
msgstr "Nenhum registro disponível"

#: views/pages/tools/manage-tokens.php:46
#: views/pages/tools/manage-tokens.php:101
#: views/pages/tools/manage-tokens.php:167
msgid "Permission"
msgstr "Permissão"

#: views/pages/tools/manage-tokens.php:45
msgid "Secret"
msgstr "Segredo"

#: views/pages/tools/manage-tokens.php:44
msgid "API Key"
msgstr "Chave API"

#: views/pages/tools/manage-tokens.php:43
#: views/pages/tools/manage-tokens.php:91
#: views/pages/tools/manage-tokens.php:157
msgid "User"
msgstr "Do utilizador"

#: restapi/RestAuth.php:387
msgid "Revoke"
msgstr "Revogar"

#: restapi/RestAuth.php:254
msgid "API keys revoke failed, please try again."
msgstr "Falha na revogação das chaves de API, por favor, tentar novamente."

#: restapi/RestAuth.php:252
msgid "API keys permanently revoked"
msgstr "API Keys Revogada permanentemente"

#: restapi/RestAuth.php:244
msgid "Invalid meta id"
msgstr "ID da meta inválida"

#: classes/Tools_V2.php:332
msgid "Token List"
msgstr "Lista de token"

#: classes/Tools_V2.php:330
msgid "Rest API"
msgstr "Rest API"

#: classes/Utils.php:9343
msgid "Create Bundle"
msgstr "Crie um pacote"

#: classes/Addons.php:236
msgid "Group multiple courses to sell together."
msgstr "Grupo vários cursos para vender juntos."

#: classes/Addons.php:235
msgid "Course Bundle"
msgstr "Pacote de curso"

#: templates/dashboard/settings/withdraw-settings.php:158
msgid "There's no Withdrawal method selected yet! To select a Withdraw method, please contact the Site Admin."
msgstr "Ainda não existe método de saque. Para efetuar o saque, por favor, contate o administrador do site."

#: templates/dashboard/my-courses.php:107
msgid "Co-author"
msgstr "Co-autor"

#: classes/Utils.php:10178
msgid "You are not authorzied to perform this action"
msgstr "Você não está autorizado a executar esta ação"

#: classes/Utils.php:10173
msgid "Something went wrong"
msgstr "Algo deu errado"

#: classes/Options_V2.php:1321
msgid "Enable to show course level"
msgstr "Habilite para mostrar o nível do curso"

#: classes/Addons.php:244
msgid "Let users register & login through social network like Facebook, Google, etc."
msgstr "Deixe os usuários se registrarem e fazer o login através da rede social como o Facebook, Google, etc."

#: classes/Addons.php:243
msgid "Social Login"
msgstr "Login social"

#: templates/single/assignment/content.php:280
msgctxt "Assignment attachment"
msgid " file)"
msgstr "\tarquivo)"

#: templates/single/assignment/content.php:279
msgctxt "Assignment attachment"
msgid "Attach assignment files (Max: "
msgstr "Anexar arquivos de tarefa "

#: includes/translate-text.php:248 includes/translate-text.php:284
msgctxt "Month name"
msgid "December"
msgstr "dezembro"

#: includes/translate-text.php:245 includes/translate-text.php:281
msgctxt "Month name"
msgid "November"
msgstr "Novembro"

#: includes/translate-text.php:242 includes/translate-text.php:278
msgctxt "Month name"
msgid "October"
msgstr "Outubro"

#: includes/translate-text.php:239 includes/translate-text.php:275
msgctxt "Month name"
msgid "September"
msgstr "Setembro"

#: includes/translate-text.php:236 includes/translate-text.php:272
msgctxt "Month name"
msgid "August"
msgstr "Agosto"

#: includes/translate-text.php:233 includes/translate-text.php:269
msgctxt "Month name"
msgid "July"
msgstr "Julho"

#: includes/translate-text.php:230 includes/translate-text.php:266
msgctxt "Month name"
msgid "June"
msgstr "Junho"

#: includes/translate-text.php:227 includes/translate-text.php:263
msgctxt "Month name"
msgid "May"
msgstr "Maio"

#: includes/translate-text.php:224 includes/translate-text.php:260
msgctxt "Month name"
msgid "April"
msgstr "Abril"

#: includes/translate-text.php:221 includes/translate-text.php:257
msgctxt "Month name"
msgid "March"
msgstr "Março"

#: includes/translate-text.php:218 includes/translate-text.php:254
msgctxt "Month name"
msgid "February"
msgstr "Fevereiro"

#: includes/translate-text.php:215 includes/translate-text.php:251
msgctxt "Month name"
msgid "January"
msgstr "Janeiro"

#: includes/translate-text.php:210
msgctxt "Week name"
msgid "Sunday"
msgstr "Domingo"

#: includes/translate-text.php:207
msgctxt "Week name"
msgid "Saturday"
msgstr "Sábado"

#: includes/translate-text.php:204
msgctxt "Week name"
msgid "Friday"
msgstr "Sexta-feira"

#: includes/translate-text.php:201
msgctxt "Week name"
msgid "Thursday"
msgstr "Quinta-feira"

#: includes/translate-text.php:198
msgctxt "Week name"
msgid "Wednesday"
msgstr "Quarta-feira"

#: includes/translate-text.php:195
msgctxt "Week name"
msgid "Tuesday"
msgstr "Terça-feira"

#: includes/translate-text.php:192
msgctxt "Week name"
msgid "Monday"
msgstr "Segunda-feira"

#: views/maintenance.php:77
msgid "Sorry for the inconvenience."
msgstr "Desculpe pela inconveniência."

#: views/maintenance.php:76
msgid "Under Maintenance"
msgstr "Em manutenção"

#: classes/Addons.php:332
msgid "Translate & manage multilingual courses for global reach with full edit control."
msgstr "Traduzir e gerenciar cursos multilíngues para alcance global com controle de edição total."

#: includes/tutor-template-functions.php:53
msgid "The file you are trying to load does not exist in your theme or Tutor LMS plugin location. If you are extending the Tutor LMS plugin, please create a php file here: "
msgstr "O arquivo que você está tentando carregar não existe no seu tema ou tutor LMS Plugin Local. Se você estiver estendendo o plug-in Tutor LMS, crie um arquivo PHP aqui: "

#: includes/tutor-general-functions.php:411
#: includes/tutor-general-functions.php:418
msgid "file not exists"
msgstr "arquivo não existe"

#: classes/Course.php:2069
msgid "Only main instructor can delete this course"
msgstr "Somente o instrutor principal pode excluir este curso"

#: views/qna/qna-single.php:127
msgid "ago"
msgstr "atrás"

#: views/pages/instructors.php:320
msgid "Enter Your Email"
msgstr "Digite seu e-mail"

#: views/pages/instructors.php:309
msgid "Enter Phone Number"
msgstr "Insira o número de telefone"

#: views/pages/instructors.php:298
msgid "Enter Username"
msgstr "Insira nome de usuário"

#: views/pages/instructors.php:288
msgid "Enter Last Name"
msgstr "Insira o último nome"

#: views/pages/instructors.php:280
msgid "Enter First Name"
msgstr "Introduza o primeiro nome"

#: views/options/template/common/reset-button-template.php:23
#: views/options/template/google_classroom.php:19
#: views/options/template/gradebook.php:19
msgid "Reset to Default"
msgstr "Restaurar ao padrão"

#: views/fragments/thumbnail-uploader.php:54
msgctxt "tutor-supported-image-type"
msgid "jpg, .jpeg,. gif, or .png"
msgstr "jpg, .jpeg,.gif, ou .png"

#: classes/Tutor_Setup.php:680
msgctxt "tutor-wizard-greeting"
msgid "Hello "
msgstr "Olá"

#: classes/Tutor_Setup.php:579
msgctxt "tutor-setup-assistance"
msgid "contact form."
msgstr "formulário de contato."

#: classes/Tutor_Setup.php:577
msgctxt "tutor-setup-assistance"
msgid "If you need further assistance, please don’t hesitate to contact us via our "
msgstr "Se você precisar de mais assistência, não hesite em entrar em contato conosco por meio de nosso "

#: classes/Tutor_Setup.php:574
msgctxt "tutor setup content"
msgid "that covers everything!"
msgstr "isso cobre tudo!"

#: classes/Tutor_Setup.php:572
msgctxt "tutor setup content"
msgid "documentation"
msgstr "documentação"

#: classes/Tutor_Setup.php:570
msgctxt "tutor setup content"
msgid "Tutor LMS is up and running on your website! If you really want to become a Tutor LMS genius, read our "
msgstr "O Tutor LMS está em funcionamento no seu site! Se você realmente quer se tornar um gênio do tutor LMS, leia nosso "

#: classes/Ajax.php:408
msgid "Nonce verification failed"
msgstr "A verificação de Nonce falhou"

#: classes/Quiz.php:1654
msgid "Invalid quiz question ID"
msgstr "ID da pergunta do teste inválido"

#: classes/Post_types.php:441
msgid "Course scheduled for:"
msgstr "Curso programado para:"

#: views/quiz/attempt-table.php:206
msgid "Would you like to delete Quiz Attempt permanently? We suggest you proceed with caution."
msgstr "Gostaria de excluir permanentemente a tentativa do questionário? Sugerimos que prossiga com cautela."

#: views/pages/addons.php:60
msgctxt "addon-last-checked"
msgid "from now"
msgstr "a partir de agora"

#: views/pages/addons.php:58
msgctxt "addon-last-checked"
msgid "ago, It will check again after"
msgstr "atrás, ele vai verificar novamente depois"

#: views/pages/addons.php:56
msgctxt "addon-last-checked"
msgid "Last checked"
msgstr "Última verificação"

#. translators: %s: rating count
#: templates/single/course/reviews.php:112
msgid "%s Rating"
msgid_plural "%s Ratings"
msgstr[0] "%s avaliação"
msgstr[1] "% avaliações"

#. translators: %s: current balance
#: templates/dashboard/withdraw.php:72
msgid "Current Balance is %s"
msgstr "O valor atual é %s"

#: templates/dashboard/settings/reset-password.php:46
#: templates/dashboard/settings/reset-password.php:76
msgid "Type Password"
msgstr "Digite a senha"

#: models/OrderModel.php:1658 templates/dashboard/purchase_history.php:276
msgid "Pay"
msgstr "Pagar"

#: templates/dashboard/my-courses.php:260
msgid "Cancel Submission"
msgstr "Cancelar envio"

#: templates/dashboard/my-courses.php:241
msgid "Move to Draft"
msgstr "Mover para o rascunho"

#: templates/dashboard/my-courses.php:222
msgid "Duplicate"
msgstr "Duplicar"

#: classes/Quiz.php:2144
msgid "Invalid attempt ID"
msgstr "ID de tentativa inválida"

#: classes/Quiz.php:2152
msgid "Attempt deleted successfully!"
msgstr "Lista excluída com sucesso."

#: classes/Course.php:2838
msgid "Invalid course ID"
msgstr "O ID de curso é inválido"

#: classes/Course.php:2835
msgid "Enrollment failed, please try again!"
msgstr "Falha no pagamento. Tente novamente."

#: classes/Course.php:2833
msgid "Enrollment successfully done!"
msgstr "Inscrição realizada com sucesso!"

#: classes/Course.php:1491
msgid "One product can not be added to multiple course!"
msgstr "Um produto não pode ser adicionado à múltiplos cursos!"

#: classes/Addons.php:267
msgid "Tutor Google Meet Integration"
msgstr "Integração do Tutor com Google Meet"

#: templates/course-embed.php:124
#: templates/email/order_new_email_to_admin.php:32
#: templates/email/order_new_email_to_students.php:32
#: templates/email/order_new_email_to_teachers.php:33
#: templates/email/order_updated_email_to_admin.php:33
#: templates/email/order_updated_email_to_students.php:33
#: templates/email/order_updated_email_to_teachers.php:33
msgid "View Details"
msgstr "Ver detalhes"

#: views/pages/course-list.php:420 views/pages/ecommerce/coupon-list.php:213
#: views/pages/ecommerce/order-list.php:226
msgid "Deletion of the course will erase all its topics, lessons, quizzes, events, and other information. Please confirm your choice."
msgstr "A exclusão do curso apagará todos os seus tópicos, lições, testes, eventos e outras informações.Por favor, confirme sua escolha."

#: views/elements/bulk-confirm-popup.php:24
msgid "Before You Proceed!"
msgstr "Antes de prosseguir!"

#: templates/single/course/reviews.php:57
msgid "Student Ratings & Reviews"
msgstr "Classificações e revisões de estudantes"

#: includes/translate-text.php:37
msgid "Archived"
msgstr "Arquivado"

#: includes/translate-text.php:31
msgid "Unread"
msgstr "Não lido"

#: includes/translate-text.php:28 restapi/RestAuth.php:408
msgid "Read"
msgstr "Ler"

#: classes/Options_V2.php:1291
msgid "You can keep the following features active or inactive as per the need of your business model"
msgstr "Você pode manter os seguintes recursos ativos ou inativos conforme a necessidade do seu modelo de negócios"

#: classes/Options_V2.php:1290
msgid "Page Features"
msgstr "Recursos de página"

#: classes/Options_V2.php:908
msgid "Put the answer display time in seconds"
msgstr "Coloque o tempo de exibição da resposta em segundos"

#. translators: %1$s: number of quiz/assignment pass required; %2$s:
#. quiz/assignment string
#: classes/Course.php:2682 classes/Course.php:2687
msgid "You have to pass %1$s %2$s to complete this course."
msgstr "Você precisa passar %1$s %2$s para concluir este curso."

#: templates/dashboard/withdraw.php:152
msgid "Withdrawable Balance"
msgstr "Saldo retirado"

#: classes/Utils.php:1816
msgid " left"
msgstr " esquerda"

#: classes/Options_V2.php:1188
msgid "If enabled, the courses will be sortable by Course Name or Creation Date in either Ascending or Descending order"
msgstr "Se ativado, os cursos serão classificados por nome do curso ou data de criação em ordem ascendente ou descendente"

#: classes/Options_V2.php:1185
msgid "Course Sorting"
msgstr "Classificação do curso"

#: classes/Options_V2.php:822
msgid "If enabled, an Enrolled Course will be automatically completed if all its Lessons, Quizzes, and Assignments are already completed by the Student"
msgstr "Se ativado, um curso inscrito será concluído automaticamente se todas as suas aulas, testes e tarefas já estiverem concluídas pelo aluno"

#: classes/Course.php:2678
msgid "assignment"
msgid_plural "assignments"
msgstr[0] "atribuição"
msgstr[1] "atribuições"

#: classes/Course.php:2677
msgid "quiz"
msgid_plural "quizzes"
msgstr[0] "questionário"
msgstr[1] "questionários"

#: classes/Course.php:1503
msgid "Product not found"
msgstr "Produto não encontrado"

#: views/pages/welcome.php:301
msgid "Let's Start Building"
msgstr "Vamos começar a construir"

#: templates/dashboard/dashboard.php:123
msgid "Click Here"
msgstr "Clique aqui"

#: templates/dashboard.php:98
msgid "Hello"
msgstr "Olá"

#: classes/Reviews.php:112
msgid "Only admin can change review status"
msgstr "Somente o administrador pode alterar o status de revisão"

#: classes/Options_V2.php:850
msgid "Enable to publish/re-publish Course Review after the approval of Site Admin"
msgstr "Ativar publicar/republicar a revisão do curso após a aprovação do admin do site"

#: classes/Options_V2.php:847
msgid "Publish Course Review on Admin's Approval"
msgstr "Publique a revisão do curso sobre a aprovação do administrador"

#: views/qna/qna-new.php:18
msgid "Do you have any questions?"
msgstr "Você tem alguma pergunta?"

#: templates/single/lesson/comment.php:84
#: templates/single/lesson/comments-loop.php:82
msgid "Write your comment here…"
msgstr "Escreva o seu comentário aqui..."

#: templates/single/course/instructors.php:21
msgid "A course by"
msgstr "Um curso de"

#: templates/single/course/course-topics.php:38
#: templates/single/lesson/lesson_sidebar.php:42
msgid "Course Content"
msgstr "Conteúdo do curso"

#: templates/dashboard/instructor/registration.php:160
#: templates/dashboard/registration.php:161
msgid "Terms and Conditions"
msgstr "Termos e condições"

#: templates/dashboard/instructor/registration.php:160
#: templates/dashboard/registration.php:161
msgid "By signing up, I agree with the website's"
msgstr "Ao me inscrever, concordo com os termos do site"

#: classes/Options_V2.php:997
msgid "If enabled, in the case of Courses, WooCommerce Orders will get the \"Completed\" status ."
msgstr "Se ativado, no caso de Cursos, os Pedidos WooCommerce terão o status \"Concluído\"."

#: classes/Options_V2.php:994
msgid "Automatically Complete WooCommerce Orders"
msgstr "Completar automaticamente os pedidos de wooCommerce"

#: classes/Options_V2.php:916
msgid "The highest number of attempts allowed for students to participate a quiz. 0 means unlimited. This will work as the default Quiz Attempt limit in case of Quiz Retry Mode."
msgstr "O maior número de tentativas permitidas para os alunos participarem de um quiz. 0 significa ilimitado. Isso funcionará como o limite de tentativa de teste padrão no caso do modo de repetição de teste."

#: classes/Options_V2.php:695
msgid "This page will be used as the Terms and Conditions page"
msgstr "Esta página será usada como a página de Termos e Condições"

#: classes/Options_V2.php:692
msgid "Terms and Conditions Page"
msgstr "Termos e Condições"

#: classes/Quiz.php:228
msgid "Reattempt quiz any number of times. Define Attempts Allowed below."
msgstr "Reattempt questionário várias vezes.Definir tentativas permitidas abaixo."

#: classes/Options_V2.php:798
msgid "Enabling this feature will show a course content summary on the Course Details page."
msgstr "A ativação desse recurso mostrará um resumo do conteúdo do curso na página de detalhes do curso."

#: classes/Options_V2.php:796
msgid "Content Summary"
msgstr "Resumo do conteúdo"

#: templates/permission-denied.php:28
msgid "You don't have permission to access this page"
msgstr "Você não tem permissão para acessar esta página"

#: templates/login-form.php:101
msgid "Register Now"
msgstr "Registrar agora"

#: classes/Utils.php:7625
msgid "Set Withdraw Method"
msgstr "Definir método de retirada"

#: classes/Utils.php:7620
msgid "Set Your Bio"
msgstr "Defina sua biografia"

#: classes/Utils.php:7619
msgid "Set Your Profile Photo"
msgstr "Defina sua foto de perfil"

#: classes/Options_V2.php:1166
msgid "Set the number of courses to display per page on the Course List page."
msgstr "Defina o número de cursos a serem exibidos por página na página da lista de cursos."

#: classes/Options_V2.php:728
msgid "Set the number of rows to be displayed per page"
msgstr "Defina o número de linhas a serem exibidas por página"

#: views/qna/qna-single.php:195
msgid "Do You Want to Delete This Question?"
msgstr "Deseja excluir esta pergunta?"

#: views/options/field-types/upload_half.php:35
msgid "Image size ratio: 4:1"
msgstr "Proporção do tamanho da imagem: 4:1"

#: views/options/field-types/upload_half.php:33
msgid "jpg, .jpeg, .png"
msgstr ".jpg, .jpeg, .png"

#: views/options/field-types/checkbox.php:15
msgid "Logged Only"
msgstr "Somente registrado"

#: views/elements/common-confirm-popup.php:31
msgid "Are you sure you want to delete this permanently from the site? Please confirm your choice."
msgstr "Tem certeza de que deseja excluir isso permanentemente do site? Por favor, confirme sua escolha."

#: views/elements/common-confirm-popup.php:30
msgid "Do You Want to Delete This?"
msgstr "Você deseja excluir isso?"

#: templates/single/quiz/top.php:143
msgid "Do You Want to Skip This Quiz?"
msgstr "Você quer pular este teste?"

#: templates/single/lesson/complete_form.php:29
msgid "Mark as Complete"
msgstr "Marcar como concluído"

#: templates/single/course/lead-info.php:78
msgid "Categories:"
msgstr "Categorias:"

#: templates/modal/alert.php:37
msgid "Ok"
msgstr "Ok"

#: templates/instructor/cover.php:16
msgid "Instructor Cover Photo"
msgstr "Foto de capa do instrutor"

#: templates/dashboard/reviews/given-reviews.php:137
msgid "Do You Want to Delete This Review?"
msgstr "Você quer excluir esta revisão?"

#: templates/dashboard/dashboard.php:63
msgid "Thanks for completing your profile"
msgstr "Obrigado por completar seu perfil"

#: templates/dashboard/dashboard.php:61
msgid "You are almost done"
msgstr "Você está quase pronto"

#: templates/dashboard/dashboard.php:59
msgid "Please complete profile"
msgstr "Por favor, preencha o perfil"

#: templates/dashboard/assignments/review.php:141
msgid "Evaluate mark can not be greater than total mark"
msgstr "Avaliar mark não pode ser maior que a marca total"

#: includes/translate-text.php:163
msgctxt "true/false question options"
msgid "False"
msgstr "Falso"

#: includes/translate-text.php:160
msgctxt "true/false question options"
msgid "True"
msgstr "verdadeiro/falso"

#: classes/Options_V2.php:1609
msgid "Choose a color for elements like table, card etc"
msgstr "Escolha uma cor para elementos para tabelas, cartões, etc."

#: classes/Options_V2.php:1607
msgid "Gray"
msgstr "Cinza"

#: classes/Ajax.php:566
msgid "Something Went Wrong!"
msgstr "Algo deu errado!"

#: views/options/template/tutor_pages.php:22
msgid "All Pages"
msgstr "Todas as páginas"

#: templates/single/lesson/sidebar_question_and_answer.php:48
msgid "Describe what you're trying to achieve and where you're getting stuck"
msgstr "Descreva o que você está tentando alcançar e onde está ficando preso"

#: templates/single/course/course-benefits.php:23
msgid "What Will You Learn?"
msgstr "O que você aprenderá?"

#: classes/Lesson.php:369 templates/dashboard/assignments.php:34
msgid "Assignment"
msgstr "Atribuição"

#: classes/Course_List.php:419
msgid "Course delete failed "
msgstr "O delete do curso falhou "

#: classes/Course_List.php:417
msgid "Course has been deleted "
msgstr "Curso foi excluído "

#: views/quiz/header-context/course-single-previous-attempts.php:50
msgid "Passing Marks"
msgstr "Marcas de passagem"

#: views/quiz/attempt-details.php:30
msgid "Attempt not found or access permission denied"
msgstr "Tentativa falha ou acesso negado"

#: views/qna/qna-table.php:144
msgid "Mark as read"
msgstr "Marcar como lido"

#: views/qna/qna-table.php:141 views/qna/qna-table.php:144
msgid "Mark as Unread"
msgstr "Marcar como não lido"

#: views/qna/qna-table.php:141
msgid "Mark as Read"
msgstr "Marcar como Lido"

#: views/qna/qna-table.php:132 views/qna/qna-table.php:135
msgid "Un-archive"
msgstr "Desarquivar"

#: views/qna/qna-table.php:116
msgid "Unresolved Yet"
msgstr "Ainda não resolvido"

#: views/qna/qna-table.php:67
msgid "Mark this conversation as important"
msgstr "Marcar conversa como importante"

#: views/qna/qna-table.php:67
msgid "This conversation is important"
msgstr "Essa conversa é importante"

#: views/pages/tools/manage-tokens.php:116
#: views/pages/tools/manage-tokens.php:182 views/qna/qna-single.php:174
msgid "Write here..."
msgstr "Escreva aqui..."

#: views/qna/qna-single.php:74 views/qna/qna-single.php:76
msgid "Un-Archive"
msgstr "Desarquivar"

#: includes/translate-text.php:34 views/qna/qna-single.php:71
msgid "Important"
msgstr "Importante"

#: views/qna/qna-single.php:66 views/qna/qna-table.php:116
msgid "Solved"
msgstr "Resolvido"

#: views/qna/qna-single.php:196 views/qna/qna-table.php:172
msgid "All the replies also will be deleted."
msgstr "Todas as respostas também serão deletadas."

#: views/qna/qna-new.php:49
msgid "Ask Question"
msgstr "Faça uma Pergunta"

#: views/qna/contexts.php:19
msgid "Waiting Since"
msgstr "Esperando Desde"

#: views/pages/withdraw_requests.php:433
msgid "Yes, Reject Withdrawal"
msgstr "Sim, Rejeitar Saque"

#: views/pages/withdraw_requests.php:421 views/pages/withdraw_requests.php:422
msgid "Other"
msgstr "Outro"

#: views/pages/withdraw_requests.php:418 views/pages/withdraw_requests.php:419
msgid "Invalid Request"
msgstr "Pedido Inválido"

#: views/pages/withdraw_requests.php:415 views/pages/withdraw_requests.php:416
msgid "Invalid Payment Details"
msgstr "Detalhes de Pagamento Inválidos"

#: views/pages/withdraw_requests.php:375
msgid "Yes, Approve Withdrawal"
msgstr "Sim, Aceitar Saque"

#: views/pages/withdraw_requests.php:367
msgid "Approve Withdrawal?"
msgstr "Aprovar Saque?"

#: views/pages/withdraw_requests.php:308
msgid "No request found"
msgstr "Nenhum pedido encontrado"

#: views/pages/withdraw_requests.php:217
msgid "BIC/SWIFT:"
msgstr "BIC/SWIFT:"

#: views/pages/withdraw_requests.php:198
msgid "IBAN:"
msgstr "IBAN:"

#: views/pages/withdraw_requests.php:190
msgid "Bank Name:"
msgstr "Nome do Banco:"

#: views/options/field-types/webhook_url.php:29
#: views/pages/withdraw_requests.php:183 views/pages/withdraw_requests.php:210
msgid "Copy"
msgstr "Copiar"

#: views/pages/withdraw_requests.php:171
msgid "A/C Number:"
msgstr "Número da Conta:"

#: views/pages/withdraw_requests.php:163
msgid "Name:"
msgstr "Nome:"

#: views/pages/withdraw_requests.php:94
msgid "Withdraw Details"
msgstr "Detalhes do Saque"

#: views/pages/withdraw_requests.php:84
msgid "Request Date"
msgstr "Data do Pedido"

#: views/pages/tutor-pro-addons.php:58
msgid "Search…"
msgstr "Procurar..."

#: views/pages/tutor-pro-addons.php:41
msgid "Advanced customization options for each add-on"
msgstr "Opções avançadas de personalização para cada complemento"

#: views/pages/tutor-pro-addons.php:36
msgid "Easy enable options for the features you need"
msgstr "Opções de ativação fácil para os recursos de que você precisa"

#: views/pages/tutor-pro-addons.php:31
msgid "Enjoy all popular add-ons under a single price"
msgstr "Aproveite todos os complementos populares por um preço único"

#: views/pages/tutor-pro-addons.php:28
msgid "Get All of Our Addons for a Single Price"
msgstr "Obtenha todos os nossos complementos por um preço único"

#: views/pages/instructors.php:471
msgid "Invalid instructor"
msgstr "Instrutor Inválido"

#: views/pages/instructors.php:460
msgid "Attempted invalid action"
msgstr "Ação inválida"

#: views/pages/instructors.php:444 views/pages/instructors.php:448
msgid "Reject the Application"
msgstr "Rejeitar Aplicação"

#: views/pages/instructors.php:441 views/pages/instructors.php:452
msgid "Approve the Instructor"
msgstr "Aprovar Instrutor"

#: views/pages/instructors.php:430
msgid "Email:"
msgstr "E-mail:"

#: views/pages/instructors.php:423
msgid "Username:"
msgstr "Nome de usuário:"

#: views/pages/instructors.php:404
msgid "You can either accept or reject the application. The applicant will be notified via email either way."
msgstr "Você pode aceitar ou rejeitar o pedido. O candidato será notificado por e-mail de qualquer maneira."

#: views/pages/instructors.php:401
msgid "A New Instructor Just Signed Up"
msgstr "Um Novo Professor se Inscreveu"

#: views/pages/instructors.php:370
msgid "Add Instructor"
msgstr "Adicionar Professor"

#: views/pages/instructors.php:305 views/pages/instructors.php:353
msgid "(Optional)"
msgstr "(Opcional)"

#: views/pages/instructors.php:337
msgid "Retype Password"
msgstr "Digite a senha novamente"

#: views/pages/instructors.php:309
msgid "Only number is allowed"
msgstr "Só números são permitidos"

#: views/pages/instructors.php:298
msgid "Only alphanumeric and underscore are allowed"
msgstr "Apenas alfanumérico e sublinhado são permitidos"

#: views/pages/instructors.php:280 views/pages/instructors.php:288
msgid "Only alphanumeric & space are allowed"
msgstr "Apenas alfanuméricos e espaço são permitidos"

#: views/pages/course-list.php:338
msgid "Update course status"
msgstr "Atualizar status do curso"

#: views/pages/course-list.php:255
msgid "Lesson:"
msgstr "Aula:"

#: views/pages/course-list.php:248
msgid "Topic:"
msgstr "Módulo:"

#: views/options/template/import_export.php:154
msgid "Reset All Settings"
msgstr "Redefinir Todas as Configurações"

#: views/options/template/import_export.php:153
msgid "WARNING! This will reset all settings to default, please proceed with caution."
msgstr "ATENÇÃO! Isto vai reiniciar todas as configurações para o padrão, prossiga com cuidado."

#: views/options/template/import_export.php:152
msgid "Reset All Settings?"
msgstr "Redefinir todas as configurações?"

#: views/options/template/import_export.php:151
msgid "Yes, Reset Settings"
msgstr "Sim, redefinir configurações"

#: views/options/template/import_export.php:145
msgid "It will revert all settings to initial setup."
msgstr "Ele reverterá todas as configurações para a configuração inicial."

#: views/options/template/import_export.php:143
msgid "Reset Everything to Default"
msgstr "Redefinir Tudo para o Padrão"

#: views/options/template/import_export.php:138
msgid "Reset Settings"
msgstr "Redefinir Configurações"

#: views/options/template/import_export.php:129
msgid "No settings data found."
msgstr "Nenhuma configuração encontrada."

#: views/options/template/import_export.php:116
msgid "WARNING! This will remove the settings history data from your system, please proceed with caution."
msgstr "AVISO! Isso removerá os dados do histórico de configurações do seu sistema, prossiga com cuidado."

#: views/options/template/import_export.php:116
msgid "Delete This Settings?"
msgstr "Deletar Estas Configurações?"

#: views/options/template/import_export.php:116
msgid "Yes, Delete Settings"
msgstr "Sim, Deletar Configurações"

#: views/options/template/import_export.php:112
msgid "Download"
msgstr "Baixar"

#: views/options/template/import_export.php:103
msgid "Restore Previous Settings?"
msgstr "Recuperar Configurações Anteriores?"

#: views/options/template/import_export.php:103
msgid "Yes, Restore Settings"
msgstr "Sim, tenho certeza"

#: views/options/template/import_export.php:79
msgid "Settings History"
msgstr "Histórico de Configurações"

#: views/options/template/import_export.php:71
msgid "Update Settings"
msgstr "Atualizar Configurações"

#: views/options/template/import_export.php:70
#: views/options/template/import_export.php:103
msgid "WARNING! This will overwrite all existing settings, please proceed with caution."
msgstr "AVISO! Isso substituirá todas as configurações existentes, prossiga com cuidado."

#: views/options/template/import_export.php:69
msgid "Import from Previous Settings?"
msgstr "Importar de Configurações Anteriores?"

#: views/options/template/import_export.php:68
msgid "Yes, Import Settings"
msgstr "Sim, tenho certeza"

#: views/options/template/import_export.php:57
msgid "Or"
msgstr "Ou"

#: views/options/template/import_export.php:57
msgid "File Format"
msgstr "Formato de Arquivo"

#: views/options/template/import_export.php:56
msgid "Drag &amp; Drop your JSON File here"
msgstr "Arrastar & amp;Solte seu arquivo json aqui"

#: views/options/template/import_export.php:48
msgid "Import Settings"
msgstr "Importar Configurações"

#: views/options/template/import_export.php:40
msgid "Export Settings"
msgstr "Exportar Configurações"

#: views/options/template/import_export.php:34
msgid "Current Settings"
msgstr "Configurações Atuais"

#: views/options/template/import_export.php:28
msgid "Export"
msgstr "Exportar"

#: views/options/template/import_export.php:21
msgid "Warning: Importing, Restoring, or Resetting will overwrite ALL existing settings. Please proceed with caution."
msgstr "Aviso: Importar, Restaurar ou Redefinir substituirá TODAS as configurações existentes. Por favor, prossiga com cautela."

#: views/options/settings.php:33
msgid "Save Changes"
msgstr "Salvar Mudanças"

#: views/options/settings.php:26
msgid "Search ...⌃⌥ + S or Alt+S for shortcut"
msgstr "Pesquise ... use os atalhos Ctrl + S ou Alt+S "

#: views/modal/topic-form.php:44
msgid "Add a summary of short text to prepare students for the activities for the topic. The text is shown on the course page beside the tooltip beside the topic name."
msgstr "Adicione um resumo para preparar os estudantes para as atividades do módulo. Este texto é exibido ao lado do nome do módulo."

#: views/modal/question_form.php:41
msgid "Select your question type"
msgstr "Escolher tipo de pergunta"

#: templates/login-form.php:99
msgid "Don't have an account?"
msgstr "Ainda não tem uma conta?"

#: templates/login-form.php:86
msgid "Sign In"
msgstr "Entrar"

#: templates/login-form.php:80
msgid "Forgot?"
msgstr "Esqueceu?"

#: templates/login-form.php:76
msgid "Keep me signed in"
msgstr "Manter logado"

#: templates/login.php:34 views/modal/login.php:24
msgid "Hi, Welcome back!"
msgstr "Olá, bem-vindo de volta!"

#: views/modal/edit_quiz.php:193
msgid "Advance Settings"
msgstr "Configurações Avançadas"

#: views/modal/edit_quiz.php:17
msgid "Quiz Title"
msgstr "Título do Questionário"

#: views/modal/edit-lesson.php:43
msgid "WP Editor"
msgstr "Editor WordPress"

#: views/modal/edit-lesson.php:34
msgid "Lesson Content"
msgstr "Conteúdo da Aula"

#: views/modal/edit-lesson.php:27
msgid "Lesson titles are displayed publicly wherever required."
msgstr "O nome será exibido publicamente sempre que solicitado."

#: views/modal/edit-lesson.php:23
msgid "Lesson Name"
msgstr "Nome da Aula"

#: views/options/template/import_export.php:60
msgid "Browse File"
msgstr "Procurar Arquivo"

#: views/options/field-types/upload_half.php:32
msgid "File Support"
msgstr "Suporte ao arquivo"

#: views/fragments/thumbnail-uploader.php:42
msgid "700x430 pixels"
msgstr "700x430 pixels"

#: views/fragments/announcement-list.php:337
msgid "Edit Announcement"
msgstr "Editar recado"

#: views/pages/ecommerce/order-list.php:112
#: views/pages/tools/manage-tokens.php:47 views/qna/contexts.php:21
msgid "Action"
msgstr "Ação"

#: views/fragments/announcement-list.php:214
msgid "Are you sure you want to delete this Announcement permanently from the site? Please confirm your choice."
msgstr "Tem certeza que deseja deletar este recado permanentemente? Por favor confirme a escolha."

#: views/fragments/announcement-list.php:213
msgid "Delete This Announcement?"
msgstr "Apagar este recado?"

#: views/fragments/announcement-list.php:153
msgid "Published Date"
msgstr "Data de publicação"

#: views/elements/filters.php:132
msgid "No record found"
msgstr "Nenhum registro encontrado"

#: views/elements/filters.php:124
msgid "All Category"
msgstr "Todas as Categorias"

#: views/elements/filters.php:85
msgid "Reset"
msgstr "Recomeçar"

#: views/elements/bulk-confirm-popup.php:25
msgid "Are you sure you would like to perform this action? We suggest you proceed with caution."
msgstr "Tem certeza que quer fazer isto? Sugerimos que tenha cuidado."

#: views/course-share.php:45
msgid "Share On Social Media"
msgstr "Compartilhar nas mídias"

#: views/course-share.php:38
msgid "Page Link"
msgstr "Link da Página"

#: views/course-share.php:35
msgid "Share Course"
msgstr "Compartilhar Curso"

#: templates/single/quiz/top.php:146
msgid "Yes, Skip This"
msgstr "Sim, pular isto"

#: templates/single/quiz/top.php:144
msgid "Are you sure you want to skip this quiz? Please confirm your choice."
msgstr "Você tem certeza que quer pular o quiz? Confirme sua escolha."

#: templates/single/quiz/top.php:134
msgid "Skip Quiz"
msgstr "Pular Questionário"

#: templates/single/quiz/parts/meta.php:33 templates/single/quiz/top.php:100
msgid "Total Attempted"
msgstr "Total de Tentativas"

#: templates/single/quiz/top.php:77
msgid "Week"
msgstr "Semana"

#: includes/translate-text.php:169 templates/single/quiz/top.php:76
msgid "Day"
msgstr "Dia"

#: includes/translate-text.php:175 templates/single/quiz/top.php:75
msgid "Hour"
msgstr "Hora"

#: includes/translate-text.php:181 templates/single/quiz/top.php:74
msgid "Minute"
msgstr "Minuto"

#: includes/translate-text.php:187 templates/single/quiz/top.php:73
msgid "Second"
msgstr "Segundo"

#: templates/single/quiz/parts/question.php:220
msgid "Skip Question"
msgstr "Pular Questão"

#: templates/single/quiz/parts/question.php:216
msgid "Submit &amp; Next"
msgstr "Enviar &amp; Próximo"

#: templates/single/quiz/parts/meta.php:22
msgid "Questions No"
msgstr "Nº Questões"

#: templates/single/quiz/parts/image-matching.php:60
#: templates/single/quiz/parts/matching.php:87
msgid "Drag your answer"
msgstr "Arraste sua resposta"

#: templates/single/lesson/content.php:162
msgid "About Lesson"
msgstr "Sobre a Aula"

#: templates/single/lesson/content.php:149
msgid "Comments"
msgstr "Comentários"

#: templates/single/lesson/content.php:136
#: templates/single/lesson/content.php:198
msgid "Exercise Files"
msgstr "Arquivos"

#: templates/single/lesson/comment.php:73
msgid "Join the conversation"
msgstr "Entrar na conversa"

#: templates/single/course/enrolled/question_and_answer.php:67
#: templates/single/course/reviews.php:152
#: templates/single/lesson/comment.php:39
msgid "Load More"
msgstr "Carregar Mais"

#: templates/single/course/reviews.php:82
msgid " Rating"
msgid_plural " Ratings"
msgstr[0] " Avaliação"
msgstr[1] " Avaliações"

#: templates/single/course/reviews.php:80
msgid "Total "
msgstr "Total"

#: templates/single/course/reviews.php:63
msgid "No Review Yet"
msgstr "Sem avaliações ainda"

#: templates/single/course/lead-info.php:92
msgid "Uncategorized"
msgstr "Não categorizado"

#: templates/dashboard/enrolled-courses.php:69
#: templates/dashboard/settings/nav-bar.php:54
#: templates/single/course/enrolled/nav.php:34 views/elements/navbar.php:67
#: views/options/template/tab.php:42
msgid "More"
msgstr "Mais"

#: templates/single/course/course-target-audience.php:24
msgid "Audience"
msgstr "Público"

#: templates/single/course/course-entry-box.php:271
msgid "Free access this course"
msgstr "Acesse o curso gratuitamente"

#: templates/single/course/course-entry-box.php:239
msgid "This course is full right now. We limit the number of students to create an optimized and productive group dynamic."
msgstr "Este curso está cheio neste momento. Nós limitamos o número de estudantes para criar um grupo mais dinâmico e produtivo."

#: templates/single/course/course-entry-box.php:205
msgid "You enrolled in this course on"
msgstr "Você se matriculou no curso em"

#: templates/single/course/course-entry-box.php:89
msgid "Course Progress"
msgstr "Progresso do Curso"

#: templates/single/course/course-entry-box.php:43
#: templates/single/course/course-entry-box.php:44
msgid "Last Updated"
msgstr "Atualizado"

#: templates/single/assignment/content.php:599
msgid "Continue Lesson"
msgstr "Continuar Aula"

#: templates/single/assignment/content.php:513
msgid "Your Assignment"
msgstr "Tua tarefa"

#: templates/single/assignment/content.php:386
#: templates/single/assignment/content.php:617
msgid "Skip To Next"
msgstr "Pular para a Próxima"

#: templates/single/assignment/content.php:309
msgid "MB"
msgstr "MB"

#: templates/single/assignment/content.php:306
msgid "Total File Size: Max"
msgstr "Tam. Total de Arquivo: Máx"

#: templates/single/assignment/content.php:302
msgid "Any standard Image, Document, Presentation, Sheet, PDF or Text file is allowed"
msgstr "Qualquer Imagem, Documento, Apresentação, Arquivo de Texto ou PDF são permitidos."

#: templates/single/assignment/content.php:300
#: views/fragments/thumbnail-uploader.php:47
#: views/fragments/thumbnail-uploader.php:52
msgid "File Support: "
msgstr "Arquivos Suportados: "

#: templates/single/assignment/content.php:291
msgid "Choose file"
msgstr "Escolha o arquivo"

#: templates/single/assignment/content.php:250
msgid "Assignment Submission"
msgstr "Enviar Tarefa"

#: templates/single/assignment/content.php:224
#: views/fragments/thumbnail-uploader.php:35
#: views/fragments/thumbnail-uploader.php:40
msgid "Size: "
msgstr "Tamanho: "

#: templates/single/assignment/content.php:160
msgid "Passing Mark:"
msgstr "Nota mínima para aprovação:"

#: templates/single/assignment/content.php:148
msgid "N\\A"
msgstr "N.R.A."

#: templates/single/assignment/content.php:138
msgid "Deadline:"
msgstr "Prazo:"

#: templates/single/common/header.php:73
msgid "of "
msgstr "de "

#: templates/single/common/header.php:68
msgid "Your Progress:"
msgstr "Seu Progresso:"

#: templates/shortcode/instructor-filter.php:114
msgid "Sort by"
msgstr "Ordenar por"

#: templates/shortcode/instructor-filter.php:78
#: templates/single/assignment/content.php:377
#: templates/single/assignment/content.php:588
#: templates/single/course/course-content.php:41
#: views/options/template/color_picker.php:106
msgid "Show More"
msgstr "Exibir Mais"

#: templates/shortcode/instructor-filter.php:51
msgid "Clear"
msgstr "Limpar"

#: templates/shortcode/instructor-filter.php:45
msgid "Filters"
msgstr "Filtros"

#: templates/shortcode/instructor-filter.php:20
msgid "Popular"
msgstr "Popular"

#: templates/shortcode/instructor-filter.php:19
msgid "New"
msgstr "Novo"

#: templates/shortcode/instructor-filter.php:18
msgid "Relevant"
msgstr "Relevante"

#: templates/loop/course-price-edd.php:57
#: templates/loop/course-price-tutor.php:66
#: templates/loop/course-price-woocommerce.php:65
#: templates/loop/course-price.php:59
msgid "% Booked"
msgstr "% Reservado"

#: templates/loop/course-continue.php:38 templates/loop/course-continue.php:42
#: templates/loop/course-continue.php:47
#: templates/single/course/course-entry-box.php:148
msgid "Continue Learning"
msgstr "Continue a Estudar"

#: templates/course-embed.php:95 templates/loop/course-author.php:32
#: templates/loop/meta.php:53
#: templates/single/course/enrolled/announcements.php:34
#: templates/single/course/lead-info.php:72
msgid "By"
msgstr "Por"

#: templates/global/attachments.php:45
msgid "No Attachment Found"
msgstr "Nenhum anexo encontrado"

#: templates/global/alert.php:27
msgid "Please define alert class"
msgstr "Por favor defina o alerta da aula"

#: templates/dashboard/settings/social-profile.php:21
msgid "Social Profile Link"
msgstr "Link de Mídia Social"

#: templates/dashboard/settings/reset-password.php:68
msgid "Re-type New Password"
msgstr "Reescreva a Nova Senha"

#: templates/dashboard/settings/profile.php:160
msgid "UX Designer"
msgstr "UX Designer"

#: templates/dashboard/settings/nav-bar.php:35
msgid "Social Profile"
msgstr "Mídias Sociais"

#: templates/dashboard/reviews/given-reviews.php:138
msgid "Are you sure you want to delete this review permanently from the site? Please confirm your choice."
msgstr "Tem certeza que deseja deletar permanentemente esta avaliação? Confirme."

#: templates/dashboard/purchase_history.php:57
msgid "Yearly"
msgstr "Ano à Ano"

#: templates/dashboard/purchase_history.php:52
msgid "Monthly"
msgstr "Mês à Mês"

#: templates/dashboard/my-profile.php:34
#: templates/dashboard/settings/profile.php:158
msgid "Skill/Occupation"
msgstr "Skills/Profissão"

#: templates/dashboard/my-profile.php:30
msgid "-"
msgstr "-"

#: templates/dashboard/my-courses.php:297
msgid "Are you sure you want to delete this course permanently from the site? Please confirm your choice."
msgstr "Tem certeza que quer apagar este curso permanentemente? Confirme."

#: templates/dashboard/instructor/logged-in.php:53
msgid "Instructor Application Received"
msgstr "Aplicação de Professor Recebida"

#: templates/dashboard/instructor/apply_for_instructor.php:41
msgid "Apply Now"
msgstr "Aplique-se"

#: templates/dashboard/instructor/apply_for_instructor.php:35
msgid "Tell us your qualifications, show us your passion, and begin teaching with us!"
msgstr "Diga -nos suas qualificações, mostre -nos sua paixão e comece a ensinar conosco!"

#: templates/dashboard/instructor/apply_for_instructor.php:29
msgid "Do you want to start your career as an instructor?"
msgstr "Quer começar sua carreira como professor?"

#: templates/dashboard/instructor/apply_for_instructor.php:22
msgid "Instructor Application"
msgstr "Aplicação de Professor"

#: templates/dashboard/elements/pagination.php:53
#: views/elements/pagination.php:16
msgid "Page"
msgstr "Página"

#: templates/dashboard/dashboard.php:340
msgid "View All"
msgstr "Ver Tudo"

#: templates/dashboard/dashboard.php:299
msgid "lesson"
msgid_plural "lessons"
msgstr[0] "aula"
msgstr[1] "aulas"

#: templates/dashboard/dashboard.php:295
#: templates/dashboard/elements/pagination.php:57
#: views/elements/pagination.php:20
msgid "of"
msgstr "de"

#: templates/dashboard/dashboard.php:256
msgid "In Progress Courses"
msgstr "Cursos em Progresso"

#: templates/dashboard/assignments/review.php:151
#: templates/dashboard/reviews.php:60
msgid "Feedback"
msgstr "Opinião"

#: templates/dashboard/assignments/review.php:110
#: templates/global/attachments.php:30
#: templates/single/assignment/content.php:550
#: views/fragments/attachments.php:30
msgid "Size"
msgstr "Tamanho"

#: templates/dashboard/assignments.php:77
msgid "Assignment Name"
msgstr "Nome da Tarefa"

#: templates/dashboard.php:60
msgid "Menu"
msgstr "Menu"

#: templates/course-filter/filters.php:145
msgid "Clear All Filters"
msgstr "Limpar todos os filtros"

#: includes/tutor-template-functions.php:1366
msgid "second"
msgstr "segundo"

#: includes/tutor-template-functions.php:1366
msgid "s"
msgstr "s"

#: includes/tutor-template-functions.php:1365
msgid "minute"
msgstr "minuto"

#: includes/tutor-template-functions.php:1365
msgid "m"
msgstr "m"

#: includes/tutor-template-functions.php:1364
msgid "hour"
msgstr "hora"

#: includes/tutor-template-functions.php:1364
msgid "h"
msgstr "h"

#: classes/Withdraw_Requests_List.php:47
msgid "Withdraw Request"
msgstr "Solicitação de Saque"

#: classes/Withdraw.php:219
msgid "Withdrawal information saved!"
msgstr "Informação de saque salva!"

#: classes/Utils.php:9414 templates/dashboard/purchase_history.php:77
#: templates/dashboard/purchase_history.php:98
msgid "Order History"
msgstr "Histórico de Pedidos"

#: classes/Utils.php:9313
msgid "No Data Found from your Search/Filter"
msgstr "Nenhum dado da Busca ou Filtro foi encontrado "

#: classes/Utils.php:9312
#: templates/single/lesson/sidebar_question_and_answer.php:43
msgid "No Data Available in this Section"
msgstr "Sem Dados Disponíveis"

#: classes/Utils.php:8948
msgid "Custom"
msgstr "Personalizado"

#: classes/Utils.php:8947
msgid "Last 365 Days"
msgstr "Últimos 365 Dias"

#: classes/Utils.php:8946
msgid "Last 90 Days"
msgstr "Últimos 90 Dias"

#: classes/Utils.php:8945
msgid "Last 30 Days"
msgstr "Últimos 30 Dias"

#: classes/Utils.php:8944 templates/dashboard/purchase_history.php:47
msgid "Today"
msgstr "Hoje"

#: classes/Course_List.php:175 includes/translate-text.php:149
#: views/pages/course-list.php:136 views/pages/ecommerce/order-list.php:60
msgid "Private"
msgstr "Privado"

#: includes/translate-text.php:81 includes/translate-text.php:85
#: includes/translate-text.php:97 models/OrderModel.php:172
#: templates/dashboard/purchase_history.php:221
msgid "Cancelled"
msgstr "Cancelado"

#: includes/translate-text.php:57
msgid "Wrong"
msgstr "Errado"

#: classes/Utils.php:5880
msgid "Github"
msgstr "Github"

#: classes/Utils.php:5875
msgid "Website"
msgstr "Website"

#: classes/Utils.php:5842 classes/Utils.php:5870
msgid "Linkedin"
msgstr "Linkedin"

#: classes/Utils.php:5836 classes/Utils.php:5865
msgid "Twitter"
msgstr "Twitter"

#: classes/Utils.php:5830 classes/Utils.php:5860
msgid "Facebook"
msgstr "Facebook"

#: classes/Utils.php:5139
msgid "Open Ended"
msgstr "Discursiva"

#: classes/Utils.php:3872 templates/dashboard/dashboard.php:356
msgid "Rating"
msgstr "Avaliação"

#: classes/Utils.php:3872 templates/shortcode/instructor-filter.php:86
msgid "Ratings"
msgstr "Avaliações"

#: classes/Utils.php:10179
msgid "Nonce not matched. Action failed!"
msgstr "Nonce não correspondente.Ação: falhou!"

#: classes/Tutor_Setup.php:653
msgid "Finish Setup"
msgstr "Concluir configuração"

#: classes/Tutor_Setup.php:593
msgid "Explore Addons"
msgstr "Explorar complementos"

#: classes/Tutor_Setup.php:590 templates/dashboard.php:144
msgid "Create a New Course"
msgstr "Criar Novo Curso"

#. translators: %s: sample permalink
#: classes/Tutor_Setup.php:443 classes/Tutor_Setup.php:450
msgid "Example:  %s"
msgstr "Exemplo:  %s"

#: classes/Tutor_Setup.php:448
msgid "Lesson permalink"
msgstr "Link da aula"

#: classes/Tools_V2.php:321
msgid "Tutor Pages Settings"
msgstr "Configuração de páginas de professores"

#: classes/Tools_V2.php:311
msgid "Import/Export Settings"
msgstr "Importar/Exportar Configurações"

#: classes/Tools_V2.php:309 views/options/template/import_export.php:15
msgid "Import/Export"
msgstr "Importar/Exportar"

#: classes/Tools_V2.php:189
msgid "WordPress Cron"
msgstr "WordPress"

#: classes/Tools_V2.php:173
msgid "Tutor version"
msgstr "Versão"

#: classes/Tools_V2.php:105
msgid "Status Settings"
msgstr "Configurações de status"

#: classes/Student.php:304
msgid "Password Changed"
msgstr "Senha Alterada"

#: classes/Student.php:220
msgid "Profile Updated"
msgstr "Perfil Atualizado"

#: classes/Q_And_A.php:325
msgid "Permission Denied!"
msgstr "Autorização Negada!"

#: classes/Q_And_A.php:100
msgid "Empty Content Not Allowed!"
msgstr "Conteúdo Vazio Não Permitido!"

#: classes/Quiz.php:1694
msgid "Please make sure the question has answer"
msgstr "Por favor certifique-se de que a questão foi respondida"

#: classes/Quiz.php:1253
msgid "Quiz Permission Denied"
msgstr "Quiz não autorizado"

#: classes/Quiz.php:322
msgid "Invalid quiz info"
msgstr "Informação de questionário inválida"

#: classes/Options_V2.php:1762
msgid "Enable to use the tutor login modal instead of the default WordPress login page"
msgstr "Habilite para usar o modal de login do Tutor em vez da página de login padrão do WordPress"

#: classes/Options_V2.php:1759
msgid "Enable Tutor Login"
msgstr "Permitir login de professor"

#: classes/Options_V2.php:1690
msgid "Choose the page for instructor registration."
msgstr "Escolha a página para registro de professor."

#: classes/Options_V2.php:1673
msgid "Enable to hide course products on shop page."
msgstr "Permitir esconder curso na página de loja."

#: classes/Options_V2.php:1666
msgid "Enable this to create courses using the Gutenberg Editor."
msgstr "Habilite isso para criar cursos usando o Editor Gutenberg."

#: classes/Options_V2.php:1652 ecommerce/Settings.php:175
#: ecommerce/Settings.php:202
msgid "Advanced Settings"
msgstr "Configurações Avançadas"

#: classes/Options_V2.php:1650
msgid "Advanced"
msgstr "Avançado"

#: classes/Options_V2.php:1643
msgid "Enable this option to use Tutor LMS video player for Vimeo."
msgstr "Ative esta opção para usar o player de vídeo do Vimeo."

#: classes/Options_V2.php:1640
msgid "Use Tutor Player for Vimeo"
msgstr "Use o player do Vimeo"

#: classes/Options_V2.php:1635
msgid "Enable this option to use Tutor LMS video player for YouTube."
msgstr "Ative esta opção para usar o player de vídeo do YouTube."

#: classes/Options_V2.php:1632
msgid "Use Tutor Player for YouTube"
msgstr "Usar o player do YouTube"

#: classes/Options_V2.php:1618
msgid "Choose a border color for your website"
msgstr "Escolha um esquema de cores de bordas"

#: classes/Options_V2.php:1616
msgid "Border"
msgstr "Borda"

#: classes/Options_V2.php:1600
msgid "Choose a text color for your website"
msgstr "Escolha um esquema de cores de textos"

#: classes/Options_V2.php:1591
msgid "Choose a primary hover color"
msgstr "Escolha uma cor primária"

#: classes/Options_V2.php:1582
msgid "Choose a primary color"
msgstr "Escolha uma cor primária"

#: classes/Options_V2.php:1440
msgid "These colors will be used throughout your website. Choose between these presets or create your own custom palette."
msgstr "Essas cores serão usadas em todo o seu site. Escolha entre essas predefinições ou crie sua própria paleta personalizada."

#: classes/Options_V2.php:1439 classes/Options_V2.php:1573
msgid "Preset Colors"
msgstr "Cores predefinidas"

#: classes/Options_V2.php:1432
msgid "Colors"
msgstr "Cores"

#: classes/Options_V2.php:1425
msgid "Enable to show course review section"
msgstr "Ativar para mostrar a seção de revisão do curso"

#: classes/Options_V2.php:1417
msgid "Enable to show course announcements section"
msgstr "Ativar para mostrar a seção de anúncios do curso"

#: classes/Options_V2.php:1409
msgid "Enable to show course target audience section"
msgstr "Ativar para mostrar a seção de público-alvo do curso"

#: classes/Options_V2.php:1401
msgid "Enable to show courses requirements setion"
msgstr "Ativar para mostrar a seção de requisitos dos cursos"

#: classes/Options_V2.php:1393
msgid "Enable to show course benefits section"
msgstr "Ativar para mostrar a seção de benefícios do curso"

#: classes/Options_V2.php:1390
msgid "Benefits"
msgstr "Benefícios "

#: classes/Options_V2.php:1385
msgid "Enable to show course description"
msgstr "Ative para mostrar a descrição do curso"

#: classes/Options_V2.php:1377
msgid "Enable to show course about section"
msgstr "Ativar para mostrar o curso sobre a seção"

#: classes/Options_V2.php:1374
msgid "About"
msgstr "Sobre"

#: classes/Options_V2.php:1369
msgid "Enable to show course materials"
msgstr "Ativar para mostrar os materiais do curso"

#: classes/Options_V2.php:1366
msgid "Material"
msgstr "Material"

#: classes/Options_V2.php:1361
msgid "Enable to show course progress for Students"
msgstr "Ativar para mostrar o progresso do curso para alunos"

#: classes/Options_V2.php:1358
msgid "Progress Bar"
msgstr "Barra de progresso"

#: classes/Options_V2.php:1353
msgid "Enable to show course update information"
msgstr "Ativar para mostrar informações de atualização do curso"

#: classes/Options_V2.php:1350
msgid "Update Date"
msgstr "Atualizar Data"

#: classes/Options_V2.php:1345
msgid "Enable to show total enrolled students"
msgstr "Ative para mostrar o total de alunos matriculados"

#: classes/Options_V2.php:1337
msgid "Enable to show course duration"
msgstr "Ativar a duração do curso"

#: classes/Options_V2.php:1329
msgid "Toggle to enable course social share"
msgstr "Alternar para ativar a participação social do curso"

#: classes/Options_V2.php:1326
msgid "Social Share"
msgstr "Compartilhamento"

#: classes/Options_V2.php:1313
msgid "Enable to show course author name"
msgstr "Ativar o nome do autor do curso"

#: classes/Options_V2.php:1305
msgid "Enable to add a Q&A section"
msgstr "Ativar adicionar uma seção de perguntas e respostas"

#: classes/Options_V2.php:1298
msgid "Toggle to show instructor info"
msgstr "Alternar para mostrar as informações do instrutor"

#: classes/Options_V2.php:1296
msgid "Instructor Info"
msgstr "Informação de Professor"

#: classes/Options_V2.php:1283
msgid "Course Details"
msgstr "Detalhes do Curso"

#: classes/Options_V2.php:1259
msgid "Choose a layout design for a student’s public profile"
msgstr "Escolha um design de layout para o perfil público de um aluno"

#: classes/Options_V2.php:1258
msgid "Student Public Profile Layout"
msgstr "Layout de perfil público estudantil"

#: classes/Options_V2.php:1234
msgid "Choose a layout design for a instructor’s public profile"
msgstr "Escolha um design de layout para o perfil público de um instrutor"

#: classes/Options_V2.php:1233
msgid "Instructor Public Profile Layout"
msgstr "Layout do perfil público do instrutor"

#: classes/Options_V2.php:1201
msgid "Choose a layout for the list of instructors inside a course page. You can change this at any time."
msgstr "Escolha um layout para a lista de instrutores dentro de uma página do curso.Você pode mudar isso a qualquer momento."

#: classes/Options_V2.php:1193
msgid "Layout"
msgstr "Apresentação"

#: classes/Options_V2.php:1128
msgid "Design"
msgstr "Design"

#: classes/Options_V2.php:1121
msgid "Write bank instructions for the instructors to conduct withdrawals."
msgstr "Escreva instruções bancárias para os instrutores realizarem saques."

#: classes/Options_V2.php:1120
msgid "Write the up to date bank informations of your instructor here."
msgstr "Escreva aqui as informações bancárias atualizadas do seu instrutor."

#: classes/Options_V2.php:1119
msgid "Bank Instructions"
msgstr "Instruções bancárias"

#: classes/Options_V2.php:1114
msgid "Set how you would like to withdraw money from the website."
msgstr "Defina como você gostaria de sacar dinheiro do site."

#: classes/Options_V2.php:1111
msgid "Enable Withdraw Method"
msgstr "Ativar método de saque"

#: classes/Options_V2.php:1106
msgid "Any income has to remain this many days in the platform before it is available for withdrawal."
msgstr "Qualquer renda deve permanecer tantos dias na plataforma antes de estar disponível para retirada."

#: classes/Options_V2.php:1103
msgid "Minimum Days Before Balance is Available"
msgstr "Mínimo de dias antes que o saldo esteja disponível"

#: classes/Options_V2.php:1068
msgid "Select the fee type and add fee amount/percentage"
msgstr "Selecione o tipo de taxa e adicione o valor/porcentagem da taxa"

#: classes/Options_V2.php:1067
msgid "Fee Amount & Type"
msgstr "Valor e tipo de Taxa"

#: classes/Options_V2.php:1059
msgid "Set a description for the fee that you are deducting. Make sure to give a reasonable explanation to maintain transparency with your site’s instructors."
msgstr "Defina uma descrição para a taxa que você está deduzindo. Certifique-se de dar uma explicação razoável para manter a transparência com os instrutores do seu site"

#: classes/Options_V2.php:1057 classes/Options_V2.php:1058
msgid "Fee Description"
msgstr "Descrição da taxa"

#: classes/Options_V2.php:1041
msgid "Fees"
msgstr "Taxas"

#: classes/Options_V2.php:1012 classes/Tutor_Setup.php:476
msgid "Allow revenue generated from selling courses to be shared with course creators."
msgstr "Permitir que a receita gerada pela venda de cursos seja compartilhada com os criadores de cursos."

#: classes/Options_V2.php:1009
msgid "Enable Revenue Sharing"
msgstr "Ativar participação nos lucros"

#: classes/Options_V2.php:1036
msgid "Set how the sales revenue will be shared among admins and instructors."
msgstr "Defina como a receita de vendas será compartilhada entre administradores e instrutores."

#: classes/Options_V2.php:1019 classes/Tutor_Setup.php:480
msgid "Sharing Percentage"
msgstr "Porcentagem de Compartilhamento"

#: classes/WooCommerce.php:461
msgid "Allow customers to place orders without an account."
msgstr "Permita que os clientes façam pedidos sem uma conta."

#: classes/WooCommerce.php:458
msgid "Enable Guest Mode"
msgstr "Ativar modo convidado"

#: classes/Options_V2.php:973
msgid "Select eCommerce Engine"
msgstr "Selecione o motor do Marketplace"

#: classes/Options_V2.php:966 classes/Options_V2.php:1744
msgid "Options"
msgstr "Opções"

#: classes/Options_V2.php:921
msgid "Show Quiz Previous Button"
msgstr "Botão Mostrar anterior do questionário"

#: classes/Options_V2.php:881
msgid "Enable this feature to allow students to post comments on lessons."
msgstr "Ative esse recurso para permitir que os alunos postem comentários nas aulas."

#: classes/Options_V2.php:878
msgid "Enable Lesson Comment"
msgstr "Permitir Comentário na Aula"

#: classes/Options_V2.php:873
msgid "Enable this feature to automatically load the next course content after the current one is finished."
msgstr "Ative este recurso para carregar automaticamente o próximo conteúdo do curso após o término do atual."

#: classes/Options_V2.php:865
msgid "Enable classic editor to edit lesson."
msgstr "Ative o editor clássico para editar a lição."

#: classes/Options_V2.php:862
msgid "WP Editor for Lesson"
msgstr "Editor WP para Lição"

#: classes/Options_V2.php:834
msgid "Choose when a user can click on the <strong>“Complete Course”</strong> button"
msgstr "Escolha quando um usuário pode clicar no botão <strong>\"Concluir curso\"</strong>"

#: classes/Options_V2.php:832
msgid "Students have to complete, pass all the lessons and quizzes (if any) to mark a course as complete."
msgstr "Estudantes devem completar e passar em todas as lições e quis para marcar o curso como completo."

#: classes/Options_V2.php:831
msgid "Students can complete courses anytime in the Flexible mode"
msgstr "Os alunos podem concluir os cursos a qualquer momento no modo Flexível"

#: classes/Options_V2.php:759
msgid "Enable the option to display this button on the student dashboard."
msgstr "Habilite a opção de exibir este botão no painel do aluno."

#: classes/Options_V2.php:743
msgid "Enable instructors to publish the course directly. If disabled, admins will be able to review course content before publishing."
msgstr "Permita que os instrutores publiquem o curso diretamente. Se desativado, os administradores poderão revisar o conteúdo do curso antes de publicar."

#: classes/Options_V2.php:717
msgid "Enable Marketplace"
msgstr "Ativar Marketplace"

#: classes/Options_V2.php:710
msgid "Others"
msgstr "Outros"

#: classes/Instructor.php:122 classes/Instructor.php:252
#: classes/Instructor.php:267 classes/Student.php:93
#: views/pages/instructors.php:341
msgid "Your passwords should match each other. Please recheck."
msgstr "Suas senhas devem corresponder umas às outras. Por favor, cheque novamente."

#: classes/Course_List.php:310
msgid "Could not delete selected courses"
msgstr "Não foi possível excluir os cursos selecionados"

#: classes/Course_List.php:299 ecommerce/OrderController.php:876
msgid "Please select appropriate action"
msgstr "Selecione a ação apropriada"

#: classes/Course_List.php:145
msgid "Mine"
msgstr "Minha"

#: classes/Course.php:2282
msgid "Course difficulty level"
msgstr "Nível de Dificuldade do Curso"

#: classes/Course.php:1809
msgid "Topic title is required!"
msgstr "Título do módulo necessário!"

#: classes/Course.php:1522 classes/Frontend.php:107
msgid "Access Denied!"
msgstr "Acesso Negado!"

#: classes/Backend_Page_Trait.php:132 views/pages/course-list.php:374
#: views/pages/ecommerce/coupon-list.php:167
msgid "Delete Permanently"
msgstr "Deletar Permanentemente"

#: classes/Backend_Page_Trait.php:119 includes/translate-text.php:77
#: templates/dashboard/purchase_history.php:209
#: templates/dashboard/purchase_history.php:217
msgid "Processing"
msgstr "Processando"

#: classes/Backend_Page_Trait.php:93 includes/translate-text.php:101
#: includes/translate-text.php:105 includes/translate-text.php:109
#: includes/translate-text.php:113 templates/dashboard/purchase_history.php:213
msgid "On Hold"
msgstr "Em Espera"

#: classes/Backend_Page_Trait.php:80 classes/Course_List.php:157
#: includes/translate-text.php:145 templates/dashboard/my-courses.php:69
#: views/pages/course-list.php:135 views/pages/ecommerce/order-list.php:59
msgid "Draft"
msgstr "Rascunho"

#: classes/Backend_Page_Trait.php:41
msgid "Bulk Action"
msgstr "Ação em massa"

#: classes/Ajax.php:548
msgid "All fields required!"
msgstr "Todos os campos obrigatórios!"

#: classes/Addons.php:143
msgid "SSL certificate"
msgstr "Certificado SSL"

#: classes/Addons.php:142
msgid "PHP 7.2.5 or greater is required"
msgstr "PHP 7.2.5 ou superior é necessário"

#: classes/Ajax.php:294
msgid "Permissioned Denied!"
msgstr "Permissão negada!"

#: classes/Utils.php:9519
msgid "Shortcode"
msgstr "Código curto"

#: views/pages/students.php:96
msgid "Course Taken"
msgstr "Curso realizado"

#: views/fragments/thumbnail-uploader.php:24
msgid "Use this media"
msgstr "Usar esta mídia"

#: views/fragments/thumbnail-uploader.php:24
msgid "Select or Upload Media Of Your Chosen Persuasion"
msgstr "Selecione ou faça upload de mídia de sua persuasão escolhida"

#: templates/dashboard/my-courses.php:202
#: templates/single/lesson/comment.php:90
#: templates/single/password-protected.php:53
#: views/pages/tools/manage-tokens.php:194 views/qna/qna-new.php:36
msgid "Submit"
msgstr "Enviar"

#: views/modal/review.php:35
msgid "Tell us about your own personal experience taking this course. Was it a good match for you?"
msgstr "Conte-nos sobre sua experiência ao fazer este curso. Foi uma boa escolha para você?"

#: templates/dashboard/reviews/given-reviews.php:103 views/modal/review.php:23
msgid "Select Rating"
msgstr "Selecione a classificação"

#: templates/dashboard/reviews/given-reviews.php:102 views/modal/review.php:22
msgid "How would you rate this course?"
msgstr "Como você classificaria este curso?"

#: templates/dashboard/withdraw.php:180
msgid "Minimum withdraw amount is"
msgstr "Valor mínimo para saques"

#: templates/dashboard/instructor/registration.php:22
#: templates/dashboard/registration.php:21
msgid "Go to Home"
msgstr "Ir para página inicial"

#: templates/dashboard/instructor/registration.php:20
#: templates/dashboard/registration.php:19
msgid "You do not have access to this area of the application. Please refer to your system  administrator."
msgstr "Você não tem acesso a esta área do site. Consulte o administrador do sistema."

#: templates/dashboard/instructor/registration.php:19
#: templates/dashboard/registration.php:18
msgid "Oooh! Access Denied"
msgstr "Oooh! Acesso Negado"

#: classes/Backend_Page_Trait.php:183 classes/Backend_Page_Trait.php:266
#: classes/Course_List.php:181 includes/translate-text.php:141
#: models/CouponModel.php:204 models/OrderModel.php:173
#: templates/dashboard/dashboard.php:154 views/pages/course-list.php:134
#: views/pages/ecommerce/order-list.php:58
msgid "Trash"
msgstr "Lixeira"

#: includes/tinymce_translate.php:26
msgid "Order :"
msgstr "Pedido #"

#: includes/tinymce_translate.php:25
msgctxt "tinyMCE button order by"
msgid "Order By :"
msgstr "Ordenar por:"

#: includes/tinymce_translate.php:24
msgid "Category IDS"
msgstr "IDs das Categorias"

#: includes/tinymce_translate.php:23
msgid "Exclude Course IDS"
msgstr "Excluir IDs de Cursos"

#: includes/tinymce_translate.php:22
msgid "Course id, separate by (,) comma"
msgstr "ID de Curso, separado por (,) vírgula"

#: includes/tinymce_translate.php:21
msgid "Courses Shortcode"
msgstr "Shortcode de Cursos"

#: includes/tinymce_translate.php:20
msgctxt "tinyMCE button courses"
msgid "Courses"
msgstr "Cursos"

#: includes/tinymce_translate.php:19
msgid "Instructor Registration Form"
msgstr "Cadastro de instrutores"

#: includes/tinymce_translate.php:18
msgid "Student Registration Form"
msgstr "Cadastro de alunos"

#: includes/tinymce_translate.php:17
msgid "Tutor ShortCode"
msgstr "Shortcode do Tutor"

#: classes/Tutor_Setup.php:251
msgid "weeks"
msgstr "semanas"

#: classes/Tutor_Setup.php:250
msgid "days"
msgstr "dias"

#: classes/Tutor_Setup.php:249 includes/tutor-template-functions.php:1364
msgid "hours"
msgstr "horas"

#: classes/Tutor_Setup.php:248 classes/Tutor_Setup.php:256
#: includes/tutor-template-functions.php:1365
msgid "minutes"
msgstr "minutos"

#: classes/Tutor_Setup.php:247 includes/tutor-template-functions.php:1366
msgid "seconds"
msgstr "segundos"

#: classes/Post_types.php:372
msgctxt "tutor assignment add"
msgid "Add New"
msgstr "Adicionar novo"

#: classes/Post_types.php:293
msgctxt "tutor quiz add"
msgid "Add New"
msgstr "Adicionar Novo"

#: classes/Post_types.php:226
msgctxt "tutor lesson add"
msgid "Add New"
msgstr "Adicionar novo"

#: classes/Post_types.php:94
msgctxt "tutor course add"
msgid "Add New"
msgstr "Adicionar novo"

#: classes/Options_V2.php:923
msgid "Choose whether to show or hide the previous button for each question."
msgstr "Escolha se deve mostrar ou ocultar o botão anterior para cada pergunta."

#: templates/dashboard/my-courses.php:304
#: templates/dashboard/reviews/given-reviews.php:140
#: views/fragments/announcement-list.php:220 views/qna/qna-single.php:198
#: views/qna/qna-table.php:179
msgid "Yes, Delete This"
msgstr "Sim, exclua isso"

#: templates/single/quiz/parts/meta.php:106
msgid "Reattempt"
msgstr "Reattempt"

#: templates/single/lesson/required-enroll.php:16
msgid "Please enroll in this course to view course content."
msgstr "Por favor, inscreva -se neste curso para visualizar o conteúdo do curso."

#: templates/permission-denied.php:29
msgid "Please make sure you are logged in to correct account if the content needs authorization."
msgstr "Verifique se você está conectado para corrigir a conta se o conteúdo precisar de autorização."

#: includes/tutor-template-functions.php:1603
msgid "Please make sure you are logged in to correct account"
msgstr "Por favor tenha certeza que entrou com a conta correta"

#: includes/tutor-template-functions.php:1602
msgid "You don't have the right to edit this course"
msgstr "Você não tem permissão para editar este curso"

#: classes/User.php:434
msgid "Dismiss"
msgstr "Liberar"

#: classes/Reviews.php:56 classes/Student.php:307
msgid "Something went wrong!"
msgstr "Algo deu errado!"

#: templates/single/course/course-entry-box.php:131
msgid "Retake This Course"
msgstr "Retomar este curso"

#: classes/Options_V2.php:842
msgid "Enabling this feature will allow students to reset course progress and start over."
msgstr "A habilitação desse recurso permitirá que os alunos redefinam o progresso do curso e comecem de novo."

#: classes/Options_V2.php:839
msgid "Course Retake"
msgstr "Curso retomar"

#: classes/Course.php:2789
msgid "Invalid Course ID or Access Denied."
msgstr "ID ou acesso ao curso inválido negado."

#: classes/Addons.php:336
msgid "Create multilingual courses, lessons, dashboard and more for a global audience."
msgstr "Crie cursos multilíngues, lições, painel e muito mais para um público global."

#: classes/Addons.php:335
msgid "WPML Multilingual CMS"
msgstr "WPML Multilingual CMS"

#: includes/tutor-general-functions.php:169
msgid "Select a tag"
msgstr "Selecione uma tag"

#: includes/tutor-general-functions.php:168
msgid "Search Course Tags. ex. Design, Development, Business"
msgstr "Selecione Tag do Curso ex. Design, Desenvolvimento, Negócios"

#: views/modal/question_form.php:35
msgid "Type your question here"
msgstr "Digite sua pergunta aqui"

#: templates/loop/course-continue.php:20 templates/loop/course-continue.php:40
#: templates/single/course/course-entry-box.php:145
#: templates/single/course/course-entry-box.php:224
msgid "Start Learning"
msgstr "Comece a estudar"

#: classes/Options_V2.php:806
msgid "When a user's WooCommerce order is auto-completed, they will be redirected to enrolled courses"
msgstr "Quando a ordem do wooCommerce de um usuário for concluída automaticamente, eles serão redirecionados para cursos inscritos"

#: classes/Backend_Page_Trait.php:196 views/pages/instructors.php:62
#: views/pages/withdraw_requests.php:264
msgid "Reject"
msgstr "Rejeitar"

#: classes/Course.php:1552 templates/loop/course-price-edd.php:80
#: templates/loop/course-price-tutor.php:85
#: templates/loop/course-price-woocommerce.php:77
#: templates/loop/course-price.php:72
msgid "Fully Booked"
msgstr "Totalmente reservado"

#: templates/dashboard/assignments/review.php:132
msgid "Assignment evaluated"
msgstr "Atribuição avaliada"

#: templates/dashboard/withdraw.php:101 templates/dashboard/withdraw.php:147
msgid "Withdrawal Request"
msgstr "Pedido de retirada"

#: templates/shortcode/instructor-filter.php:109
msgid "Search any instructor..."
msgstr "Pesquise qualquer instrutor ..."

#: templates/dashboard/assignments.php:65
#: templates/dashboard/elements/filters.php:51
msgid "Create Date"
msgstr "Data de criação"

#. translators: %s: max mark
#: templates/dashboard/assignments/review.php:145
msgid "Evaluate this assignment out of %s"
msgstr "Pontue esta tarefa em até %s"

#: templates/dashboard/assignments/review.php:138
msgid "Your Points"
msgstr "Sua pontuação"

#: templates/dashboard/assignments/review.php:69
msgid "Submitted Date"
msgstr "Data de submissão"

#: templates/dashboard/assignments/submitted.php:66
msgid "Oldest"
msgstr "Mais antigo"

#: templates/dashboard/assignments/submitted.php:65
msgid "Latest"
msgstr "Mais recentes"

#: templates/dashboard/assignments/submitted.php:63
msgid "Sort By:"
msgstr "Ordenar por:"

#: templates/dashboard/assignments/submitted.php:55
msgid "Pass Points"
msgstr "Pontos de passagem"

#: templates/dashboard/assignments/submitted.php:51
#: templates/dashboard/assignments/submitted.php:83
msgid "Total Points"
msgstr "Total de pontos"

#: templates/dashboard/assignments/submitted.php:47
msgid "Submission Deadline"
msgstr "Data limite para envio"

#: templates/dashboard/assignments/submitted.php:26
msgid "No Limit"
msgstr "Sem limite"

#: templates/dashboard/announcements/update.php:18
msgid "Update Announcement"
msgstr "Atualizar anúncio"

#: views/quiz/instructor-feedback.php:37
msgid "Updated"
msgstr "Atualizado"

#: classes/Options_V2.php:543
msgid "Settings Saved"
msgstr "Configurações salvas"

#: templates/dashboard/announcements/details.php:35
msgid "Publish Date"
msgstr "Data de publicação"

#: templates/dashboard/announcements.php:111
#: templates/dashboard/assignments.php:60
#: templates/dashboard/elements/filters.php:47 views/elements/filters.php:188
msgid "DESC"
msgstr "Decresc."

#: templates/dashboard/announcements.php:110
#: templates/dashboard/assignments.php:59
#: templates/dashboard/elements/filters.php:46 views/elements/filters.php:191
msgid "ASC"
msgstr "Cresc."

#: templates/dashboard/announcements.php:82 views/pages/announcements.php:112
msgid "Add New Announcement"
msgstr "Adicionar anúncio"

#: templates/dashboard/announcements.php:76 views/pages/announcements.php:106
msgid "Notify all students of your course"
msgstr "Notifique todos os alunos do seu curso"

#: classes/Backend_Page_Trait.php:67
#: templates/dashboard/announcements/create.php:64
#: templates/dashboard/my-courses.php:59 templates/dashboard/my-courses.php:200
#: views/fragments/announcement-list.php:91 views/pages/course-list.php:132
#: views/pages/course-list.php:144 views/pages/ecommerce/order-list.php:56
msgid "Publish"
msgstr "Publicar"

#: templates/dashboard/announcements/create.php:55
#: templates/dashboard/announcements/update.php:56
#: views/fragments/announcement-list.php:80
msgid "Summary..."
msgstr "Resumo..."

#: templates/dashboard/announcements/create.php:53
#: templates/dashboard/announcements/update.php:54
#: views/fragments/announcement-list.php:78 views/modal/edit_quiz.php:23
msgid "Summary"
msgstr "Resumo"

#: templates/dashboard/announcements/create.php:49
#: templates/dashboard/announcements/update.php:50
#: views/fragments/announcement-list.php:73
msgid "Announcement title"
msgstr "Título do anúncio"

#: templates/dashboard/announcements/create.php:47
#: templates/dashboard/announcements/update.php:48
#: views/fragments/announcement-list.php:71
msgid "Announcement Title"
msgstr "Título do anúncio"

#: templates/dashboard/announcements/create.php:18
msgid "Create New Announcement"
msgstr "Criar anúncio"

#: templates/dashboard/announcements.php:108
#: templates/dashboard/assignments.php:57
#: templates/dashboard/elements/filters.php:44
#: templates/dashboard/question-answer.php:72 views/elements/filters.php:184
msgid "Sort By"
msgstr "Ordenar por"

#: templates/dashboard/announcements.php:102
#: templates/dashboard/announcements/create.php:41
#: templates/dashboard/announcements/update.php:42
#: templates/dashboard/assignments.php:51
#: templates/dashboard/elements/filters.php:39 views/elements/filters.php:111
#: views/fragments/announcement-list.php:64
msgid "No course found"
msgstr "Nenhum curso encontrado."

#: classes/Course_List.php:139 classes/Instructors_List.php:92
#: classes/Quiz_Attempts_List.php:214 classes/Withdraw_Requests_List.php:73
#: ecommerce/CouponController.php:386 ecommerce/OrderController.php:762
#: includes/translate-text.php:25 templates/dashboard/announcements.php:94
#: templates/dashboard/assignments.php:43
#: templates/dashboard/elements/filters.php:30
msgid "All"
msgstr "Todos"

#: classes/Ajax.php:589
msgid "Announcement delete failed"
msgstr "Falha em apagar anúncio"

#: classes/Ajax.php:586
msgid "Announcement deleted successfully"
msgstr "Anúncio apagado"

#: classes/Ajax.php:535 classes/Ajax.php:540
msgid "Announcement summary required"
msgstr "Resumo do anúncio obrigatório"

#: classes/Ajax.php:531
msgid "Announcement title required"
msgstr "Título do anúncio obrigatório"

#: classes/Ajax.php:526
msgid "Course name required"
msgstr "Nome do curso necessário"

#: classes/Ajax.php:562
msgid "Announcement updated successfully"
msgstr "Anúncio atualizado com sucesso"

#: classes/Ajax.php:562
msgid "Announcement created successfully"
msgstr "Anúncio criado com sucesso"

#: classes/Quiz.php:2071
msgid "Access Denied."
msgstr "Acesso negado."

#: templates/dashboard/settings/profile.php:86
msgid "Saving..."
msgstr "Salvando..."

#: templates/dashboard/settings/profile.php:85
msgid "700x430"
msgstr "700x430"

#: templates/dashboard/settings/profile.php:85
msgid "Cover Photo Size"
msgstr "Tamanho da foto da capa"

#: templates/dashboard/settings/profile.php:84
#: templates/dashboard/settings/profile.php:85
msgid "pixels"
msgstr "píxeis"

#: templates/dashboard/settings/profile.php:84
msgid "200x200"
msgstr "200x200"

#: templates/dashboard/settings/profile.php:84
msgid "Profile Photo Size"
msgstr "Tamanho da foto do perfil"

#: classes/Course_List.php:151 templates/dashboard/dashboard.php:152
msgid "Published"
msgstr "Publicado"

#: classes/Addons.php:183 classes/Ajax.php:86 classes/Ajax.php:165
#: classes/Ajax.php:502 classes/Ajax.php:581 classes/Course.php:1826
#: classes/Course.php:2060 classes/Instructor.php:325 classes/Quiz.php:358
#: classes/Quiz.php:966 classes/Quiz.php:1320 classes/Quiz.php:1470
#: classes/Quiz.php:1558 classes/Quiz.php:1986 classes/Quiz.php:2023
#: classes/Q_And_A.php:205
msgid "Access Denied"
msgstr "Acesso negado"

#: classes/Quiz.php:223
msgid "Show result after the attempt."
msgstr "Mostra o resultado após a tentativa."

#: classes/Quiz.php:218
msgid "Answers shown after quiz is finished"
msgstr "Respostas mostradas após o término do questionário"

#: views/modal/question_answer_form.php:13
#: views/modal/question_answer_list.php:16
msgid "No option is necessary for this answer type"
msgstr "Nenhuma opção é necessária para este tipo de resposta"

#: classes/Withdraw_Requests_List.php:91 includes/translate-text.php:65
msgid "Rejected"
msgstr "Rejeitado"

#: classes/Options_V2.php:1178
msgid "Price Type"
msgstr "Tipo de preço"

#: classes/Options_V2.php:1174
msgid "Keyword Search"
msgstr "Pesquisa por palavra-chave"

#: classes/Backend_Page_Trait.php:171 classes/Instructors_List.php:110
msgid "Block"
msgstr "Bloquear"

#: classes/Backend_Page_Trait.php:158 classes/Instructors_List.php:98
#: views/pages/withdraw_requests.php:261
msgid "Approve"
msgstr "Aprovar"

#: classes/Tutor_Setup.php:197
msgid "ON"
msgstr "LIGADO"

#: classes/Tutor_Setup.php:192
msgid "OFF"
msgstr "DESLIGADO"

#: templates/dashboard/my-profile.php:35 templates/public-profile.php:179
msgid "Biography"
msgstr "Biografia"

#: templates/public-profile.php:154
msgid "Course Completed"
msgstr "Curso Concluído"

#: templates/public-profile.php:154
msgid "Courses Completed"
msgstr "Cursos concluídos"

#: templates/public-profile.php:149
msgid "Courses Enrolled"
msgstr "Curso matriculado"

#. translators: %s: Withdraw Method Name
#: templates/dashboard/withdraw.php:118
msgid "The preferred payment method is selected as %s. "
msgstr "O método de pagamento preferido é selecionado como %s."

#: templates/dashboard/settings/profile.php:99
msgid "Upload Photo"
msgstr "Enviar Foto"

#: templates/dashboard/settings/profile.php:78
msgid "Upload Cover Photo"
msgstr "Enviar foto de capa"

#: templates/dashboard/settings/profile.php:78
msgid "Update Cover Photo"
msgstr "Atualizar foto de capa"

#: templates/profile/courses_taken.php:49
msgid "No course yet."
msgstr "Nem curso ainda!"

#: templates/loop/add-to-cart-tutor.php:31 templates/loop/course-in-cart.php:18
#: templates/single/course/add-to-cart-tutor.php:73
#: templates/single/course/add-to-cart-woocommerce.php:26
msgid "View Cart"
msgstr "Ver carrinho"

#: classes/Options_V2.php:1200
msgid "Instructor List Layout"
msgstr "Layout da lista do instrutor"

#: views/pages/instructors.php:232
msgid "No instructor found"
msgstr "Nenhum instrutor encontrado"

#: templates/modal/confirm.php:19
msgid "Yes"
msgstr "Sim"

#: views/quiz/contexts.php:95
msgid "No"
msgstr "Não"

#: templates/dashboard/assignments/submitted.php:100
msgid "Evaluate"
msgstr "Avalie"

#: templates/dashboard/withdraw.php:215
msgid "Withdrawal History"
msgstr "Histórico de saques"

#: templates/dashboard/withdraw.php:197
msgid "Submit Request"
msgstr "Enviar pedido"

#: templates/dashboard/withdraw.php:157
msgid "Selected Payment Method"
msgstr "Método de pagamento selecionado"

#: templates/dashboard/withdraw.php:148
msgid "Please check your transaction notification on your connected withdrawal method"
msgstr "Verifique sua notificação de transação no seu método de retirada conectada"

#: templates/dashboard/withdraw.php:39
msgid "Withdrawal request is pending for approval, please hold tight."
msgstr "O pedido de retirada está pendente para aprovação, por favor, aguarde."

#: templates/dashboard/withdraw.php:38
msgid "Please contact the site administrator for more information."
msgstr "Entre em contato com o administrador do site para obter mais informações."

#: classes/Options_V2.php:1180
msgid "Choose preferred filter options you'd like to show on the course archive page."
msgstr "Escolha as opções de filtro preferidas que você gostaria de mostrar na página de arquivo do curso."

#: classes/Options_V2.php:1171
msgid "Preferred Course Filters"
msgstr "Filtros de curso preferidos"

#: classes/Withdraw.php:269
msgid "Insufficient balance."
msgstr "Saldo insuficiente."

#: classes/Options_V2.php:1158
msgid "Show sorting and filtering options on course archive page"
msgstr "Mostrar opções de classificação e filtragem na página de arquivo do curso"

#: classes/Options_V2.php:1155
msgid "Course Filter"
msgstr "Filtro do curso"

#: classes/Options_V2.php:949 classes/Options_V2.php:950
msgid "Preferred Video Source"
msgstr "Fonte de vídeo preferida"

#: classes/Withdraw.php:108
msgid "We will use this email address to send the money to your Paypal account"
msgstr "Usaremos este endereço de e-mail para enviar o dinheiro para sua conta Paypal"

#: classes/Options_V2.php:1318 templates/course-filter/filters.php:83
#: templates/single/course/course-entry-box.php:54
msgid "Level"
msgstr "Nível"

#: classes/Options_V2.php:1176 templates/course-filter/filters.php:69
msgid "Tag"
msgstr "Tag"

#: classes/Course_Widget.php:175 classes/Options_V2.php:1175
#: templates/course-filter/filters.php:55
#: templates/shortcode/instructor-filter.php:58 views/elements/filters.php:119
msgid "Category"
msgstr "Categoria"

#: views/elements/filters.php:210
msgid "Search..."
msgstr "Pesquisar..."

#: templates/single/assignment/content.php:179
#: templates/single/assignment/content.php:500
msgid "You have missed the submission deadline. Please contact the instructor for more information."
msgstr "Você perdeu o prazo de envio. Entre em contato com o instrutor para obter mais informações."

#: templates/dashboard/instructor/logged-in.php:86
msgid "Go to Dashboard"
msgstr "Ir para o painel"

#: templates/dashboard/instructor/logged-in.php:79
msgid "Please contact the site administrator for further information."
msgstr "Entre em contato com o administrador do site para obter mais informações."

#: templates/dashboard/instructor/logged-in.php:77
msgid "Start building your first course today and let your eLearning journey begin."
msgstr "Comece a construir seu primeiro curso hoje e deixe sua jornada de eLearning começar."

#: templates/dashboard/instructor/logged-in.php:75
msgid "We've received your application, and we will review it soon. Please hang tight!"
msgstr "Recebemos sua inscrição e a analisaremos em breve. Por favor, aguente firme!"

#: templates/dashboard/instructor/logged-in.php:66
msgid "Unfortunately, your instructor status has been removed."
msgstr "Infelizmente, seu status de instrutor foi removido."

#: templates/dashboard/instructor/logged-in.php:64
msgid "Congratulations! You are now registered as an instructor."
msgstr "Parabéns! Agora você está registrado como instrutor."

#: templates/dashboard/instructor/logged-in.php:62
msgid "Thank you for registering as an instructor! "
msgstr "Obrigado por se registrar como instrutor! "

#: templates/dashboard/instructor/logged-in.php:42
msgid "You have been blocked from being an instructor."
msgstr "Você foi impedido de ser um instrutor."

#: templates/dashboard/instructor/logged-in.php:40
msgid "Your application has been accepted. Further necessary details have been sent to your registered email account."
msgstr "Seu aplicativo foi aceito.Mais detalhes necessários foram enviados para sua conta de email registrada."

#: templates/dashboard/instructor/logged-in.php:38
msgid "Your application will be reviewed and the results will be sent to you by email."
msgstr "Seu aplicativo será revisado e os resultados serão enviados a você por e -mail."

#: templates/dashboard/withdraw.php:224
msgid "Withdrawal Method"
msgstr "Método de retirada"

#: templates/dashboard.php:153
msgid "Your Application is pending as of"
msgstr "Seu aplicativo está pendente a partir de"

#: ecommerce/Settings.php:479 templates/single/course/course-topics.php:116
msgid "Live"
msgstr "Ao vivo"

#: includes/translate-text.php:89 templates/single/assignment/content.php:143
#: templates/single/course/course-topics.php:114
msgid "Expired"
msgstr "Expirado"

#: restapi/REST_Topic.php:73
msgid "Topic retrieved successfully"
msgstr "Tópico recuperado com sucesso"

#: restapi/REST_Course_Announcement.php:73
msgid "Announcement not found for given ID"
msgstr "Anúncio não encontrado para determinado ID"

#: restapi/REST_Course_Announcement.php:64
msgid "Announcement retrieved successfully"
msgstr "Anúncio recuperado com sucesso"

#: restapi/REST_Lesson.php:118
msgid "Lesson not found for the given topic ID"
msgstr "Aula não encontrada para determinado tópico ID"

#: restapi/REST_Lesson.php:109
msgid "Lesson retrieved successfully"
msgstr "Aula recuperada com sucesso"

#: restapi/REST_Author.php:70
msgid "Author not found"
msgstr "Autor não encontrado"

#: restapi/REST_Author.php:61
msgid "Author details retrieved successfully"
msgstr "Detalhes do autor recuperados com sucesso"

#: restapi/REST_Quiz.php:339
msgid "Quiz attempts not found for given ID"
msgstr "Tentativas de Quiz não encontradas para determinado ID"

#: restapi/REST_Quiz.php:330
msgid "Quiz attempts retrieved successfully"
msgstr "Tentativas de Quiz recuperadas com sucesso"

#: restapi/REST_Quiz.php:270
msgid "Question not found for given ID"
msgstr "Pergunta não encontrada para determinado ID"

#: restapi/REST_Quiz.php:261
msgid "Question retrieved successfully"
msgstr "Questão recuperada com sucesso"

#: restapi/REST_Quiz.php:106 restapi/REST_Quiz.php:196
msgid "Quiz not found for given ID"
msgstr "Quiz não encontrado para determinado ID"

#: restapi/REST_Quiz.php:140 restapi/REST_Quiz.php:187
msgid "Quiz retrieved successfully"
msgstr "Quiz recuperado com sucesso"

#: restapi/REST_Course.php:286
msgid "Tags field is not an array"
msgstr "Campo de Tags não é uma array"

#: restapi/REST_Course.php:282
msgid "Categories field is not an array"
msgstr "Campo de categorias não é uma array"

#: restapi/REST_Course.php:223
msgid "Detail not found for given ID"
msgstr "Detalhe não encontrado para determinado ID"

#: restapi/REST_Course.php:216
msgid "Course detail retrieved successfully"
msgstr "Detalhes do curso recuperados com sucesso"

#: restapi/REST_Course.php:193
msgid "Course not found"
msgstr "Curso não encontrado"

#: restapi/REST_Course.php:184
msgid "Course retrieved successfully"
msgstr "Curso recuperado com sucesso"

#: restapi/REST_Rating.php:84
msgid "Rating not found for given ID"
msgstr "Classificação não encontrada para determinada identificação"

#: restapi/REST_Rating.php:75
msgid "Course rating retrieved successfully"
msgstr "Classificação do curso recuperado com sucesso"

#: classes/Addons.php:287
msgid "Google Classroom Integration"
msgstr "Integração do Google Classroom"

#: classes/Addons.php:292
msgid "Connect Tutor LMS with Zoom to host live online classes. Students can attend live classes right from the lesson page."
msgstr "Conecte o Tutor LMS com o Zoom para hospedar aulas on -line ao vivo.Os alunos podem participar das aulas ao vivo desde a página da lição."

#: classes/Addons.php:291
msgid "Tutor Zoom Integration"
msgstr "Integração ao Zoom"

#: classes/Options_V2.php:791
msgid "Allow instructors and admins to view the course content without enrolling"
msgstr "Permitir que instrutores e administradores visualizem o conteúdo do curso sem se inscrever"

#: classes/Options_V2.php:788
msgid "Course Content Access"
msgstr "Acesso ao conteúdo do curso"

#: classes/Withdraw.php:315
msgid "Withdrawal Request Sent!"
msgstr "Solicitação de retirada enviada!"

#: classes/Options_V2.php:1754
msgid "Enabling this feature will show a notification bar to students and instructors to complete their profile information"
msgstr "Ativar este recurso mostrará uma barra de notificação para alunos e instrutores completarem suas informações de perfil"

#: classes/Options_V2.php:1751
msgid "Profile Completion"
msgstr "Preenchimento do Perfil"

#: classes/Utils.php:9363 templates/dashboard/withdraw.php:57
msgid "Withdrawals"
msgstr "Saques"

#: templates/dashboard/notifications/profile-completion.php:42
msgid "You are almost done!"
msgstr "Você está quase lá!"

#: templates/dashboard/notifications/profile-completion.php:40
msgid "% Complete"
msgstr "% Completo"

#: templates/dashboard/notifications/profile-completion.php:27
msgid "Set Your"
msgstr "Defina seu"

#: templates/dashboard/notifications/profile-completion.php:22
msgid "Complete your profile so people can know more about you! Go to Profile"
msgstr "Complete seu perfil para que as pessoas possam saber mais sobre você! Ir para o perfil"

#: templates/dashboard/dashboard.php:36
#: templates/dashboard/notifications/profile-completion.php:20
msgid "Complete Your Profile"
msgstr "Complete seu perfil."

#: templates/dashboard/question-answer/answers.php:107
#: templates/single/lesson/comments-loop.php:38
#: templates/single/lesson/comments-loop.php:88 views/qna/contexts.php:18
#: views/qna/qna-single.php:144 views/qna/qna-single.php:179
#: views/qna/qna-table.php:122
msgid "Reply"
msgstr "Responder"

#: views/qna/qna-table.php:171
msgid "Delete This Question?"
msgstr "Apagar a Questão?"

#: views/pages/course-list.php:262
msgid "Quiz:"
msgstr "Questionário:"

#: templates/dashboard/assignments.php:116
#: templates/dashboard/assignments/submitted.php:104
#: templates/instructor/cover.php:43 templates/instructor/default.php:37
#: templates/instructor/minimal-horizontal.php:35
#: templates/instructor/minimal.php:39
#: templates/instructor/portrait-horizontal.php:40
#: views/fragments/announcement-list.php:310 views/pages/students.php:152
#: views/quiz/attempt-table.php:174 views/quiz/contexts.php:25
msgid "Details"
msgstr "Detalhes"

#: templates/single/assignment/content.php:156
msgid "Total Marks:"
msgstr "Total de marcas:"

#: views/quiz/contexts.php:22 views/quiz/contexts.php:73
msgid "Incorrect Answer"
msgstr "Resposta incorreta"

#: views/quiz/contexts.php:21 views/quiz/contexts.php:72
#: views/quiz/contexts.php:99
msgid "Correct Answer"
msgstr "Resposta correta"

#: classes/Quiz.php:222
msgid "Reveal Mode"
msgstr "Modo de Revelação"

#: classes/Quiz.php:227
msgid "Retry Mode"
msgstr "Modo de Repetição"

#: classes/Quiz.php:217
msgid "Default"
msgstr "Padrão"

#: views/modal/edit_quiz.php:128
msgid "Pick the quiz system\"s behaviour on choice based questions."
msgstr "Escolha o comportamento do sistema de teste em perguntas baseadas em escolha."

#: views/modal/edit_quiz.php:124
msgid "Quiz Feedback Mode"
msgstr "Modo Comentário de Questionário"

#: templates/dashboard/announcements/update.php:63
#: views/pages/withdraw_requests.php:103 views/pages/withdraw_requests.php:257
#: views/quiz/instructor-feedback.php:38
msgid "Update"
msgstr "Atualizar"

#: views/quiz/instructor-feedback.php:23
msgid "Instructor Feedback"
msgstr "Comentários do Instrutor"

#: views/modal/edit_quiz.php:271
msgid "Students will place the answer in the Open-Ended/Essay question type within this character limit."
msgstr "Os alunos colocarão a resposta no tipo de pergunta aberta/ensaio dentro desse limite de caracteres."

#: views/modal/edit_quiz.php:267
msgid "Open-Ended/Essay questions answer character limit"
msgstr "Perguntas abertas/ensaios Answar o limite do personagem"

#: templates/dashboard/settings/profile.php:200
msgid "The display name is shown in all public fields, such as the author name, instructor name, student name, and name that will be printed on the certificate."
msgstr "O nome apresentado é exibido em todos os campos públicos, assim como o nome de autor, nome de instrutor, nome de estudante e nome que será impresso no certificado."

#: templates/dashboard/settings/profile.php:187
msgid "Display name publicly as"
msgstr "Exibir nome publicamente como"

#: classes/Options_V2.php:827
msgid "Course Completion Process"
msgstr "Processo de conclusão de curso"

#. translators: %1$s: number of quiz pass required; %2$s: quiz string; %3$s:
#. number of assignment pass required; %4$s: assignment string
#: classes/Course.php:2692
msgid "You have to pass %1$s %2$s and %3$s %4$s to complete this course."
msgstr "Você tem que passar em %1$s %2$s e %3$s %4$s dos questionários para concluir este curso."

#: classes/Course.php:2612
msgid "Complete all lessons to mark this course as complete"
msgstr "Conclua todas as lições para marcar este curso como concluído"

#: classes/Addons.php:296
msgid "Save time by exporting/importing quiz data with easy options."
msgstr "Economize tempo exportando/importando dados do questionário com opções fáceis."

#: classes/Addons.php:295
msgid "Quiz Export/Import"
msgstr "Exportação/Importação de questionários"

#: classes/Withdraw.php:102 ecommerce/Ecommerce.php:217
msgid "PayPal"
msgstr "PayPal"

#: classes/Tutor_Setup.php:801
msgid "Tutor &rsaquo; Setup Wizard"
msgstr "Assistente de configuração do Tutor"

#: classes/Tutor_Setup.php:761
msgid "Marketplace"
msgstr "Marketplace"

#: classes/Tutor_Setup.php:746
msgid "Individual"
msgstr "Individual"

#: classes/Tutor_Setup.php:707
msgid "I already know, skip it!"
msgstr "Eu já sei, pule isso!"

#: classes/Tutor_Setup.php:700
msgid "Let’s Start"
msgstr "Vamos começar"

#: classes/Tutor_Setup.php:621 classes/Tutor_Setup.php:650
msgid "Skip this step"
msgstr "Pular esta etapa"

#: classes/Tutor_Setup.php:567
msgid "Congratulations, you’re all set!"
msgstr "Parabéns, está tudo pronto!"

#: classes/Tutor_Setup.php:491
msgid "Payment Withdrawal Method"
msgstr "Método de retirada de pagamento"

#: views/pages/instructors.php:131 views/pages/instructors.php:182
msgid "Commission Rate"
msgstr "Taxa de comissão"

#: classes/Options_V2.php:928
msgid "Final Grade Calculation"
msgstr "Cálculo da nota final"

#: classes/Tutor_Setup.php:465
msgid "How many courses per page on the archive pages."
msgstr "Quantos cursos por página nas páginas de arquivo."

#: classes/Tutor_Setup.php:460
msgid "How many courses per row on the archive pages."
msgstr "Quantos cursos por linha nas páginas de arquivo."

#: classes/Tutor_Setup.php:459
msgid "Courses Per Row"
msgstr "Cursos por linha"

#: classes/Tutor_Setup.php:455
msgid "Allows a Q&A forum on each course."
msgstr "Permite um fórum de perguntas e respostas em cada curso."

#: classes/Options_V2.php:811
msgid "Spotlight Mode"
msgstr "Modo Spotlight"

#: classes/Tutor_Setup.php:343
msgid "Admin / Owner"
msgstr "Administrador / Proprietário"

#: classes/Instructors_List.php:62 classes/Options_V2.php:733
#: classes/Tutor_Setup.php:338 classes/Tutor_Setup.php:471
#: classes/Tutor_Setup.php:540 classes/Utils.php:9333
#: templates/dashboard/question-answer.php:63
msgid "Instructor"
msgstr "Instrutores"

#: templates/loop/add-to-cart-edd.php:26
msgid "Purchase"
msgstr "Comprar"

#: templates/dashboard/settings/reset-password.php:28
#: templates/dashboard/settings/reset-password.php:29
msgid "Current Password"
msgstr "Senha atual"

#: restapi/RestAuth.php:383 templates/dashboard/announcements/details.php:45
#: templates/dashboard/reviews/given-reviews.php:76
#: templates/single/assignment/content.php:523
#: views/fragments/announcement-list.php:174
#: views/fragments/announcement-list.php:322 views/modal/edit_quiz.php:62
#: views/options/field-types/toggle_switch_button_thumb.php:28
#: views/pages/course-list.php:368 views/pages/ecommerce/coupon-list.php:155
#: views/pages/ecommerce/order-list.php:185 views/pages/instructors.php:219
msgid "Edit"
msgstr "Editar"

#: classes/Options_V2.php:870
msgid "Automatically Load Next Course Content."
msgstr "Carregue automaticamente o conteúdo do próximo curso."

#: classes/Options_V2.php:1671
msgid "Hide Course Products on Shop Page"
msgstr "Ocultar produtos do curso na página da loja"

#: classes/Addons.php:327
msgid "Restrict Content Pro"
msgstr "Restrict Content Pro"

#: classes/Addons.php:315
msgid "BuddyPress"
msgstr "BuddyPress"

#: classes/Options_V2.php:1334 templates/single/course/course-entry-box.php:38
#: templates/single/course/course-entry-box.php:39
msgid "Duration"
msgstr "Duração"

#: classes/Tutor_Setup.php:617 classes/Tutor_Setup.php:646
#: templates/single/common/footer.php:34
#: templates/single/next-previous-pagination.php:17
msgid "Previous"
msgstr "Anterior"

#: classes/Options_V2.php:1775
msgid "Maintenance Mode"
msgstr "Modo de manutenção"

#: classes/Options_V2.php:1335 classes/Options_V2.php:1407
msgid "Disable"
msgstr "Desativar"

#: views/options/template/tutor_pages.php:18
#: views/pages/tools/tutor_pages.php:121
msgid "Note: This tool will install all the missing Tutor pages. Pages already defined and set up will not be replaced."
msgstr "Nota: Esta ferramenta instalará todas as páginas ausentes do Tutor. As páginas já definidas e configuradas não serão substituídas."

#: views/options/template/tutor_pages.php:82
#: views/pages/tools/tutor_pages.php:113
msgid "Re-Generate Tutor Pages"
msgstr "Gerar novamente páginas do Tutor"

#: views/pages/tools/tutor_pages.php:60
msgid "Page visibility is not public"
msgstr "A visibilidade da página não está pública"

#: views/pages/tools/tutor_pages.php:53
msgid " Page deleted, please set new one"
msgstr " Página excluída, defina uma nova"

#: views/pages/tools/tutor_pages.php:46 views/pages/tools/tutor_pages.php:101
msgid " Page not set"
msgstr " Página não definida"

#: views/options/template/tutor_pages.php:30
#: views/pages/tools/tutor_pages.php:19
msgid "Page Name"
msgstr "Nome da página"

#: templates/dashboard/announcements/details.php:44
#: templates/dashboard/my-courses.php:270
#: templates/dashboard/reviews/given-reviews.php:81
#: templates/dashboard/settings/profile.php:102
#: views/fragments/announcement-list.php:171
#: views/fragments/announcement-list.php:328 views/modal/edit_quiz.php:68
#: views/options/field-types/media.php:43
#: views/options/template/import_export.php:118 views/qna/qna-single.php:81
#: views/qna/qna-table.php:151
msgid "Delete"
msgstr "Excluir"

#: classes/Admin.php:303 classes/Tools_V2.php:318
#: views/options/template/tutor_pages.php:15
msgid "Tutor Pages"
msgstr "Páginas do Tutor"

#: classes/Admin.php:171 views/options/tools.php:16 views/pages/tools.php:14
msgid "Tools"
msgstr "Ferramentas"

#: classes/Utils.php:3065
msgid "Retrieve Password"
msgstr "Recuperar senha"

#. translators: %s: site name
#: classes/FormHandler.php:221
msgid "Password Reset Request for %s"
msgstr "Solicitação de redefinição de senha para %s"

#: classes/FormHandler.php:175
msgid "Passwords do not match."
msgstr "As senhas não correspondem."

#: classes/FormHandler.php:170
msgid "Please enter your password."
msgstr "Insira sua senha."

#: classes/FormHandler.php:164
msgid "This key is invalid or has already been used. Please reset your password again if needed."
msgstr "Esta chave é inválida ou já foi usada. Redefina sua senha novamente, se necessário."

#: classes/FormHandler.php:130
msgid " If you don't see the email, check other places it might be, like your junk, spam, social, promotion or others folders."
msgstr " Se você não vir o Email, verificar outros lugares que podem ser, como seu lixo, spam, pastas sociais, promocionais ou outras."

#: classes/FormHandler.php:129
msgid "We've sent an email to this account's email address. Click the link in the email to reset your password."
msgstr "Enviamos um email para o seu endereço desta conta. Clique no link enviado no email para redefinir sua senha."

#: classes/FormHandler.php:128
msgid "Check your E-Mail"
msgstr "Verifique seu e-mail"

#: classes/FormHandler.php:101
msgid "Password reset is not allowed for this user"
msgstr "A redefinição de senha não é permitida para este usuário"

#: classes/FormHandler.php:84 classes/FormHandler.php:89
msgid "Invalid username or email."
msgstr "Nome de usuário ou e-mail inválido."

#: classes/FormHandler.php:62
msgid "Enter a username or email address."
msgstr "Insira um nome de usuário ou endereço de e-mail."

#: classes/Ajax.php:446
msgid "Username is required."
msgstr "O nome de usuário é obrigatório."

#: templates/ecommerce/billing-form-fields.php:53
#: templates/ecommerce/billing-form-fields.php:55
#: views/pages/instructors.php:317
msgid "Email Address"
msgstr "Endereço de email"

#: templates/email/send-reset-password.php:44
msgid "Thanks for reading."
msgstr "Obrigado pela leitura."

#: templates/email/send-reset-password.php:41
msgid "Click here to reset your password"
msgstr "Clique aqui para redefinir sua senha"

#: templates/email/send-reset-password.php:37
msgid "If you didn't make this request, just ignore this email. If you'd like to proceed:"
msgstr "Se você não fez essa solicitação, ignore este e-mail. Se você deseja prosseguir:"

#. translators: %s: user login
#: templates/email/send-reset-password.php:33
msgid "Username: %s"
msgstr "Nome de usuário: %s"

#. translators: %s: site name
#: templates/email/send-reset-password.php:26
msgid "Someone has requested a new password for the following account on %s:"
msgstr "Alguém solicitou uma nova senha para a seguinte conta em %s:"

#. translators: %s: user login
#: templates/email/send-reset-password.php:19
msgid "Hi %s,"
msgstr "Oi %s,"

#: templates/template-part/retrieve-password.php:34
msgid "Username or email"
msgstr "Nome de usuário ou e-mail"

#: templates/template-part/retrieve-password.php:29
msgid "Lost your password? Please enter your username or email address. You will receive a link to create a new password via email."
msgstr "Esqueceu sua senha? Digite seu nome de usuário ou endereço de e-mail. Você receberá  por e-mail um link para criar uma nova senha."

#: templates/template-part/form-retrieve-password.php:59
#: templates/template-part/retrieve-password.php:47
#: templates/template-part/retrieve-password.php:48
msgid "Reset password"
msgstr "Redefinir senha"

#: templates/template-part/form-retrieve-password.php:45
msgid "Confirm Password"
msgstr "Confirme a senha"

#: templates/template-part/form-retrieve-password.php:27
msgid "Enter Password and Confirm Password to reset your password"
msgstr "Digite a senha e confirme-a para redefinir sua senha"

#: classes/Lesson.php:366 classes/Options_V2.php:855
msgid "Lesson"
msgstr "Aula"

#: views/modal/topic-form.php:33
msgid "Topic titles are displayed publicly wherever required. Each topic may contain one or more lessons, quiz and assignments."
msgstr "Os títulos dos tópicos são exibidos publicamente sempre que solicitado. Cada tópico pode conter uma ou mais lições, questionários e tarefas."

#: views/metabox/lesson-metabox.php:38
msgid "Choose the course for this lesson"
msgstr "Escolha o curso para essa aula"

#: views/metabox/lesson-metabox.php:23
msgid "Select a course"
msgstr "Selecione um curso"

#: classes/Options_V2.php:1310 views/pages/course-list.php:184
msgid "Author"
msgstr "Autor"

#: includes/translate-text.php:129 models/OrderModel.php:186
#: templates/course-filter/filters.php:16
msgid "Paid"
msgstr "Pago"

#: views/modal/edit-lesson.php:63
msgid "The idea of a summary is a short text to prepare students for the activities within the topic or week. The text is shown on the course page under the topic name."
msgstr "A idéia de um resumo é um texto curto para preparar os alunos para as atividades dentro do módulo ou semana. O texto é mostrado na página do curso, abaixo do nome do tópico."

#: views/modal/topic-form.php:38
msgid "Topic Summary"
msgstr "Resumo do módulo"

#: views/modal/topic-form.php:29
msgid "Topic Name"
msgstr "Nome do módulo"

#: views/metabox/user-profile-fields.php:58
msgid "Upload"
msgstr "Envio"

#: views/metabox/user-profile-fields.php:41
msgid "Write a little bit more about you, it will show publicly."
msgstr "Escreva um breve texto sobre você, que aparecerá para todos."

#: views/metabox/user-profile-fields.php:34
msgid "Profile Bio"
msgstr "Bio"

#: views/metabox/user-profile-fields.php:27
msgid "Job Title"
msgstr "Ocupação"

#: views/metabox/user-profile-fields.php:14
msgid "Tutor Fields"
msgstr "Campos do instrutor"

#: templates/dashboard/announcements.php:73
#: views/fragments/announcement-list.php:380 views/pages/announcements.php:103
msgid "Create Announcement"
msgstr "Criar anúncio."

#: classes/Course_Widget.php:150 views/pages/course-list.php:177
msgid "Title"
msgstr "Título"

#: classes/Utils.php:9515
msgid "Embedded"
msgstr "Incorporado"

#: classes/Utils.php:9511
msgid "Vimeo"
msgstr "Vimeo"

#: classes/Utils.php:9503
msgid "External URL"
msgstr "URL externo"

#: classes/Utils.php:9499
msgid "HTML 5 (mp4)"
msgstr "HTML5 (mp4)"

#: views/fragments/attachments.php:51
msgid "Upload Attachments"
msgstr "Carregar Anexos"

#: classes/Course.php:2278 classes/Options_V2.php:1177
msgid "Difficulty Level"
msgstr "Nível de dificuldade"

#: views/pages/instructors.php:264
msgid "Add New Instructor"
msgstr "Adicionar novo instrutor"

#: views/pages/get-pro.php:13
msgid "Get pro plugin from themeum.com"
msgstr "Comprar versão pro em themeum.com"

#: templates/loop/add-to-cart-edd.php:26 views/pages/addons.php:85
msgid "Buy Now"
msgstr "Comprar agora"

#: views/pages/addons.php:28
msgid "Themes"
msgstr "Temas"

#: views/pages/addons.php:23
msgid "Plugins"
msgstr "Plugins"

#: views/pages/answer.php:74
msgid "on"
msgstr "em"

#: views/pages/answer.php:42
msgid "Place answer"
msgstr "Coloque a resposta"

#: views/pages/answer.php:38
msgid "Write an answer here"
msgstr "Escreva uma resposta aqui"

#: views/pages/add_new_instructor.php:13 views/pages/add_new_instructor.php:150
msgid "Add new instructor"
msgstr "Adicionar novo instrutor"

#: classes/Utils.php:9421 templates/dashboard/question-answer.php:53
#: templates/single/course/enrolled/question_and_answer.php:114
#: views/pages/question_answer.php:50
msgid "Question & Answer"
msgstr "Perguntas & Respostas"

#. translators: %s: mbstring functions
#: classes/Tools_V2.php:504 views/pages/tools/status.php:307
msgid "Your server does not support the %s functions - this is required for better character encoding. Some fallbacks will be used instead for it."
msgstr "Seu servidor não tem suporte às funções %s - que são requisitos para melhor codificação de caracteres. Outras funções de contingenciamento serão utilizadas no lugar."

#: views/pages/tools/status.php:296
msgid "Multibyte String (mbstring) is used to convert character encoding, like for emails or converting characters to lowercase."
msgstr "Multibyte String (mbstring) é usada para converter a codificação de caracteres, como para e-mails ou para converter caracteres para mínusculas."

#: classes/Tools_V2.php:299 views/pages/tools/status.php:295
msgid "Multibyte string"
msgstr "Multibyte string"

#. translators: %s: gzopen function
#: classes/Tools_V2.php:498 views/pages/tools/status.php:288
msgid "Your server does not support the %s function - this is required to use the GeoIP database from MaxMind."
msgstr "Seu servidor não tem suporte à função %s - que é requisito para usar o banco de dados GeoIP da MaxMind."

#: views/pages/tools/status.php:279
msgid "GZip (gzopen) is used to open the GEOIP database from MaxMind."
msgstr "GZip (gzopen) é usado para descompactar o banco de dados GeoIP da MaxMind."

#: classes/Tools_V2.php:292 views/pages/tools/status.php:278
msgid "GZip"
msgstr "GZip"

#. translators: %s: DOMDocument class
#: classes/Tools_V2.php:492 views/pages/tools/status.php:271
msgid "Your server does not have the %s class enabled - HTML/Multipart emails, and also some extensions, will not work without DOMDocument."
msgstr "Seu servidor não tem a classe %s ativada - e-mails HTML/Multipart e também algumas extensões não funcionarão sem o DOMDocument."

#: views/pages/tools/status.php:262
msgid "HTML/Multipart emails use DOMDocument to generate inline CSS in templates."
msgstr "E-mails HTML/Multipart usam o DOMDocument para gerar CSS inline nos modelos."

#: classes/Tools_V2.php:285 views/pages/tools/status.php:261
msgid "DOMDocument"
msgstr "DOMDocument"

#: classes/Tools_V2.php:486 views/pages/tools/status.php:254
msgid "Your server does not have fsockopen or cURL enabled - PayPal IPN and other scripts which communicate with other servers will not work. Contact your hosting provider."
msgstr "Seu servidor não tem fsockopen ou cURL ativado(s) - O PayPal IPN e outros scripts que se comunicam com outros servidores não irão funcionar. Entre em contato com sua hospedagem."

#: views/pages/tools/status.php:246
msgid "Payment gateways can use cURL to communicate with remote servers to authorize payments, other plugins may also use it when communicating with remote services."
msgstr "Gateways de pagamento podem usar cURL para comunicação com servidores remotos para autorizar pagamentos, outros plugins também podem utilizar dela ao se comunicar com serviços remotos."

#: classes/Tools_V2.php:278 views/pages/tools/status.php:245
msgid "fsockopen/cURL"
msgstr "fsockopen/cURL"

#. translators: %s: default timezone
#: classes/Tools_V2.php:481 views/pages/tools/status.php:237
msgid "Default timezone is %s - it should be UTC"
msgstr "O fuso horário padrão é %s - deveria ser UTC"

#: views/pages/tools/status.php:232
msgid "The default timezone for your server."
msgstr "O fuso horário padrão do seu servidor."

#: classes/Tools_V2.php:271 views/pages/tools/status.php:231
msgid "Default timezone is UTC"
msgstr "O fuso horário padrão é UTC"

#: views/pages/tools/status.php:227
msgid "The largest filesize that can be uploaded to your WordPress installation."
msgstr "O maior tamanho de arquivo que pode ser enviado para a sua instalação do WordPress."

#: views/pages/tools/status.php:226
msgid "Max upload size"
msgstr "Tamanho máximo para envio"

#. translators: 1: MySQL version number, 2: WordPress requirements URL
#: classes/Tools_V2.php:475 views/pages/tools/status.php:216
msgid "WordPress requirements"
msgstr "Requisitos do WordPress"

#. translators: 1: MySQL version number, 2: WordPress requirements URL
#: classes/Tools_V2.php:475 views/pages/tools/status.php:216
msgid "%1$s - We recommend a minimum MySQL version of 5.6. See: %2$s"
msgstr "%1$s - Recomendamos no mínimo a versão 5.6 do MySQL. Vide: %2$s"

#: views/pages/tools/status.php:209
msgid "The version of MySQL installed on your hosting server."
msgstr "A versão do MySQL instalada no seu servidor."

#: classes/Tools_V2.php:264 views/pages/tools/status.php:208
msgid "MySQL version"
msgstr "Versão do MySQL"

#: views/pages/tools/status.php:194
msgid "Suhosin is an advanced protection system for PHP installations. It was designed to protect your servers on the one hand against a number of well known problems in PHP applications and on the other hand against potential unknown vulnerabilities within these applications or the PHP core itself. If enabled on your server, Suhosin may need to be configured to increase its data submission limits."
msgstr "Suhosin é um sistema avançado de proteção para instalações PHP. Ele foi desenvolvido para proteger seus servidores, por um lado de diversos problemas conhecidos em aplicações PHP e de outro lado contra potenciais vulnerabilidades desconhecidas dentro destas aplicações ou mesmo do próprio core do PHP. Uma vez ativado no seu servidor, Suhosin precisa ter seus limites de envio de dados configurados."

#: views/pages/tools/status.php:193
msgid "SUHOSIN installed"
msgstr "SUHOSIN instalado"

#: views/pages/tools/status.php:189
msgid "The version of cURL installed on your server."
msgstr "A versão do cURL instalada em seu servidor."

#: classes/Tools_V2.php:246 views/pages/tools/status.php:188
msgid "cURL version"
msgstr "versão do cURL"

#: views/pages/tools/status.php:184
msgid "The maximum number of variables your server can use for a single function to avoid overloads."
msgstr "O número máximo de variáveis que seu servidor suporta para uma única função para evitar sobrecargas."

#: views/pages/tools/status.php:183
msgid "PHP max input vars"
msgstr "Máximo de variáveis de input no PHP"

#: views/pages/tools/status.php:179
msgid "The amount of time (in seconds) that your site will spend on a single operation before timing out (to avoid server lockups)"
msgstr "Tempo (em segundos) que seu servidor irá dedicar em uma única operação antes de dar timeout (para evitar travamentos)"

#: classes/Tools_V2.php:238 views/pages/tools/status.php:178
msgid "PHP time limit"
msgstr "Limite de tempo do PHP"

#: views/pages/tools/status.php:174
msgid "The largest filesize that can be contained in one post."
msgstr "O maior tamanho de arquivo que um post pode conter."

#: classes/Tools_V2.php:230 views/pages/tools/status.php:173
msgid "PHP post max size"
msgstr "Tamanho máximo de post via PHP"

#: classes/Tools_V2.php:458 views/pages/tools/status.php:162
msgid "We recommend using PHP version 7.2 or above for greater performance and security."
msgstr "Nós recomendamos utilizar PHP na versão 7.2 ou superior para melhor desempenho e segurança."

#: classes/Tools_V2.php:457 views/pages/tools/status.php:160
msgid "Tutor will run under this version of PHP, however, it has reached end of life. We recommend using PHP version 7.2 or above for greater performance and security."
msgstr "O Tutor LMS funcionará nesta versão de PHP, porém ela já alcançou o fim de vida útil. Recomendamos usar a versão 7.2 ou superior para melhor desempenho e segurança."

#: views/pages/tools/status.php:151
msgid "The version of PHP installed on your hosting server."
msgstr "A versão do PHP instalada em seu servidor."

#: classes/Tools_V2.php:222 views/pages/tools/status.php:150
msgid "PHP version"
msgstr "Versão do PHP"

#: views/pages/tools/status.php:146
msgid "Information about the web server that is currently hosting your site."
msgstr "Informações sobre o servidor que está hospedando seu site."

#: classes/Tools_V2.php:214 views/pages/tools/status.php:145
msgid "Server info"
msgstr "Informações do servidor"

#: classes/Tools_V2.php:206 views/pages/tools/status.php:139
msgid "Server environment"
msgstr "Ambiente do servidor"

#: views/pages/tools/status.php:122
msgid "Displays whether or not WordPress is using an external object cache."
msgstr "Exibe se o WordPress está ou não usando um cache de objetos externo."

#: classes/Tools_V2.php:197 views/pages/tools/status.php:121
msgid "External object cache"
msgstr "Cache de objetos externo"

#: views/pages/tools/status.php:117
msgid "The current language used by WordPress. Default = English"
msgstr "Idioma atual utilizado no WordPress. Padrão = Inglês"

#: classes/Tools_V2.php:153 classes/Tools_V2.php:254
#: views/pages/tools/status.php:116
msgid "Language"
msgstr "Idioma"

#: views/pages/tools/status.php:106
msgid "Displays whether or not WP Cron Jobs are enabled."
msgstr "Exibe se as WP Cron Jobs estão ativadas ou não."

#: views/pages/tools/status.php:105
msgid "WordPress cron"
msgstr "WordPress cron"

#: views/pages/tools/status.php:95
msgid "Displays whether or not WordPress is in Debug Mode."
msgstr "Exibe se o WordPress está ou não em modo depurar."

#: classes/Tools_V2.php:145 views/pages/tools/status.php:94
msgid "WordPress debug mode"
msgstr "Modo depurar do WordPress"

#. Translators: %1$s: Memory limit, %2$s: Docs link.
#: classes/Tools_V2.php:445 views/pages/tools/status.php:86
msgid "Increasing memory allocated to PHP"
msgstr "Aumentando a memória alocada para o PHP"

#. Translators: %1$s: Memory limit, %2$s: Docs link.
#: classes/Tools_V2.php:445 views/pages/tools/status.php:86
msgid "%1$s - We recommend setting memory to at least 64MB. See: %2$s"
msgstr "%1$s - Recomendamos no mínimo 64MB de memória. Vide: %2$s"

#: views/pages/tools/status.php:81
msgid "The maximum amount of memory (RAM) that your site can use at one time."
msgstr "A quantidade máxima de memória (RAM) liberada para uso em seu site."

#: classes/Tools_V2.php:181 views/pages/tools/status.php:80
msgid "WordPress memory limit"
msgstr "Limite de memória do WordPress"

#: views/pages/tools/status.php:76
msgid "Whether or not you have WordPress Multisite enabled."
msgstr "Exibe se você tem ou não o WordPress Multisite ativado."

#: classes/Tools_V2.php:137 views/pages/tools/status.php:75
msgid "WordPress multisite"
msgstr "WordPress multisite"

#: views/pages/tools/status.php:70
msgid "The version of tutor."
msgstr "Versão do Tutor."

#: views/pages/tools/status.php:69
msgid "Tutor Version"
msgstr "Versão do Tutor"

#. Translators: %1$s: Current version, %2$s: New version
#: views/pages/tools/status.php:60
msgid "%1$s - There is a newer version of WordPress available (%2$s)"
msgstr "%1$s - Uma nova versão do WordPress está disponível (%2$s)"

#: views/pages/tools/status.php:41
msgid "The version of WordPress installed on your site."
msgstr "A versão do WordPress instalada em seu site."

#: classes/Tools_V2.php:129 views/pages/tools/status.php:40
msgid "WordPress version"
msgstr "Versão do WordPress"

#: views/pages/tools/status.php:35
msgid "The root URL of your site."
msgstr "O URL raiz do seu site."

#: classes/Tools_V2.php:163 views/pages/tools/status.php:34
msgid "Site URL"
msgstr "URL do site"

#: views/pages/tools/status.php:29
msgid "The homepage URL of your site."
msgstr "URL da página inicial do seu site."

#: classes/Tools_V2.php:120 views/pages/tools/status.php:27
msgid "Home URL"
msgstr "URL da página inicial"

#: classes/Tools_V2.php:111 views/pages/tools/status.php:22
msgid "WordPress environment"
msgstr "Ambiente do WordPress"

#: views/pages/tools/status.php:13
msgid "Tutor Environment Status"
msgstr "Status de ambiente do Tutor"

#: templates/course-filter/filters.php:38 views/elements/filters.php:206
msgid "Search"
msgstr "Pesquisar"

#: views/pages/addons.php:110
msgid "Version:"
msgstr "Versão:"

#: views/modal/question_answer_list.php:103
msgid "Add An Option"
msgstr "Adicionar uma opção"

#: views/modal/question_form.php:164
msgid "Input options for the question and select the correct answer."
msgstr "Insira opções para a pergunta e selecione a resposta correta."

#: views/modal/question_form.php:137
msgid "Display Points"
msgstr "Exibir pontuação"

#: views/modal/question_form.php:132
msgid "set the mark ex. 10"
msgstr "ajuste a pontuação ex. 10"

#: views/modal/question_form.php:128
msgid "Point(s) for this answer"
msgstr "Ponto(s) para esta resposta"

#: views/modal/question_form.php:117
msgid "Randomize"
msgstr "Aleatório"

#: views/modal/question_form.php:111
msgid "Answer Required"
msgstr "Resposta obrigatória"

#: views/modal/question_form.php:96
msgid "Pro version required"
msgstr "Somente na versão Pro"

#: views/modal/question_form.php:32
msgid "Write your question here"
msgstr "Escreva sua pergunta aqui"

#: views/modal/question_answer_form.php:127
msgid "Separate multiple answers by a vertical bar <strong>|</strong>. 1 answer per <strong>{dash}</strong> variable is defined in the question. Example: Apple | Banana | Orange"
msgstr "Separar multiplas respostas com uma barra vertical <strong>|</strong>. 1 resposta por variável <strong>{dash}</strong> é definida na pergunta. Exemplo: Maçã | Banana | Laranja"

#: views/modal/question_answer_form.php:114
msgid "Please make sure to use the <strong>{dash}</strong> variable in your question title to show the blanks in your question. You can use multiple <strong>{dash}</strong> variables in one question."
msgstr "Certifique-se de usar a variável <strong>{dash}</strong> no título da sua pergunta para mostrar as lacunas em branco na sua pergunta. Você pode usar diversas variáveis <strong>{dash}</strong> em uma pergunta."

#: views/modal/question_answer_form.php:32
msgid "Select the correct option"
msgstr "Selecione a opção correta"

#: views/modal/edit-lesson.php:68
msgid "Feature Image"
msgstr "Imagem destacada"

#: views/modal/edit_quiz.php:261
msgid "Student will place answer in short answer question type within this characters limit."
msgstr "O aluno deverá responder as perguntas do tipo resposta curta dentro deste limite de caracteres."

#: views/modal/edit_quiz.php:253
msgid "Short answer characters limit"
msgstr "Limite de caracteres para resposta curta"

#: views/modal/edit_quiz.php:247
msgid "Show/hide question number during attempt."
msgstr "Exibir/ocultar número de perguntas durante a tentativa."

#: views/modal/edit_quiz.php:244
msgid "Hide question number"
msgstr "Ocultar número de perguntas"

#: classes/Quiz.php:265
msgid "Descending"
msgstr "Descendente"

#: classes/Quiz.php:264
msgid "Ascending"
msgstr "Ascendente"

#: classes/Quiz.php:263
msgid "Sorting"
msgstr "Classificação"

#: classes/Quiz.php:262
msgid "Random"
msgstr "Aleatório"

#: views/modal/edit_quiz.php:230
msgid "Questions Order"
msgstr "Ordem das perguntas"

#: classes/Quiz.php:247
msgid "Question below each other"
msgstr "Uma pergunta abaixo da outra"

#: classes/Quiz.php:246
msgid "Question Pagination"
msgstr "Paginação das perguntas"

#: classes/Quiz.php:245
msgid "Single Question"
msgstr "Pergunta única"

#: classes/Quiz.php:244
msgid "Set question layout view"
msgstr "Selecione o layout de visualização de pergunta"

#: views/modal/edit_quiz.php:217
msgid "Question Layout"
msgstr "Layout de pergunta"

#: views/modal/edit_quiz.php:211
msgid "If you enable this option, the quiz will start automatically after the page is loaded."
msgstr "Se ativada esta opção, o questionário começará automaticamente após carregar a página."

#: views/modal/edit_quiz.php:208
msgid "Quiz Auto Start"
msgstr "Início automático do questionário"

#: views/modal/edit_quiz.php:178
msgid "This amount of question will be available for students to answer, and question will comes randomly from all available questions belongs with a quiz, if this amount is greater than available question, then all questions will be available for a student to answer."
msgstr "Esta quantidade de questões ficarão disponíveis para os alunos responderem, as questões aparecerão de forma aleatória a partir de todas as questões disponíveis no questionário. Se esta quantidade for maior que a disponibilidade de questões todas as questões ficarão disponíveis aos alunos."

#: views/modal/edit_quiz.php:174
msgid "Max Question Allowed to Answer"
msgstr "Pergunta máxima permitida para responder"

#: views/modal/edit_quiz.php:168
msgid "Set the passing percentage for this quiz"
msgstr "Configure a porcentagem para aprovação neste questionário"

#: views/modal/edit_quiz.php:164
msgid "Passing Grade (%)"
msgstr "Nota mínima (%)"

#: views/modal/edit_quiz.php:156
msgid "Restriction on the number of attempts a student is allowed to take for this quiz. 0 for no limit"
msgstr "Restrição do número de tentativas que um aluno pode ter neste questionário. 0 para ilimitadas"

#: ecommerce/OptionKeys.php:55 views/modal/question_form.php:145
msgid "Optional"
msgstr "Opcional"

#: views/modal/edit_quiz.php:118
msgid "Time limit for this quiz. 0 means no time limit."
msgstr "Limite de tempo para este questionário. 0 significa ilimitado."

#: views/modal/edit_quiz.php:113
msgid "Hide quiz time - display"
msgstr "Ocultar o tempo para resposta do questionário"

#: classes/Tutor_Setup.php:625 templates/single/common/footer.php:40
#: templates/single/next-previous-pagination.php:23
msgid "Next"
msgstr "Próximo"

#: templates/dashboard/assignments/review.php:42
#: templates/dashboard/assignments/submitted.php:34
#: templates/single/quiz/parts/question.php:210
#: views/modal/question_form.php:25 views/qna/qna-single.php:56
#: views/quiz/header-context/backend-dashboard-students-attempts.php:20
#: views/quiz/header-context/course-single-previous-attempts.php:17
#: views/quiz/header-context/frontend-dashboard-my-attempts.php:14
#: views/quiz/header-context/frontend-dashboard-students-attempts.php:15
msgid "Back"
msgstr "Voltar"

#: views/modal/edit_quiz.php:84
msgid "Add Question"
msgstr "Adicionar pergunta"

#: views/modal/edit_quiz.php:19
msgid "Type your quiz title here"
msgstr "Digite o título do questionário aqui"

#: views/quiz/contexts.php:17
msgid "Quiz Info"
msgstr "Informações do questionário"

#: views/modal/question_answer_form.php:286
msgid "Update Answer"
msgstr "Atualizar resposta"

#: views/modal/question_answer_form.php:276
msgid "The answers that students enter should match with this text. Write in <strong>small caps</strong>"
msgstr "As respostas inseridas pelos alunos devem corresponder a este texto. Escreva em <strong> letras minúsculas </strong>"

#: views/modal/question_answer_form.php:270
msgid "Answer input value"
msgstr "Valor de entrada da resposta"

#: views/modal/question_answer_form.php:240
msgid "Image matched text"
msgstr "Imagem correspondente ao texto"

#: views/modal/question_answer_form.php:145
#: views/modal/question_answer_form.php:168
msgid "Matched Answer title"
msgstr "Título da resposta correspondente"

#: views/modal/question_answer_form.php:121
msgid "Correct Answer(s)"
msgstr "Resposta(s) correta(s)"

#: views/modal/question_answer_form.php:98
#: views/modal/question_answer_form.php:214
msgid "Text &amp; Image both"
msgstr "Ambos texto &amp; imagem"

#: views/modal/question_answer_form.php:92
#: views/modal/question_answer_form.php:208
msgid "Only Image"
msgstr "Somente imagem"

#: views/modal/question_answer_form.php:86
#: views/modal/question_answer_form.php:202
msgid "Only text"
msgstr "Somente texto"

#: views/modal/question_answer_form.php:80
#: views/modal/question_answer_form.php:196
msgid "Display format for options"
msgstr "Formato de exibição para opções"

#: views/modal/question_answer_form.php:52
#: views/modal/question_answer_form.php:136
#: views/modal/question_answer_form.php:159
msgid "Answer title"
msgstr "Título da resposta"

#: views/metabox/course/field-types/groups/select.php:15
#: views/metabox/course/field-types/select.php:15
#: views/options/field-types/radio.php:13
#: views/options/field-types/select.php:22
msgid "Select Option"
msgstr "Selecione uma opção"

#: views/options/withdraw/withdraw_admin_options_generator.php:43
msgid "Enable "
msgstr "Ativar "

#: views/options/withdraw/withdraw_admin_options_generator.php:38
msgid "Enable/Disable"
msgstr "Ativar/Desativar"

#: views/pages/withdraw_requests.php:87
msgid "Request By"
msgstr "Solicitado por"

#: classes/Options_V2.php:978
msgid "Disable Monetization"
msgstr "Desativar monetização"

#: classes/Options_V2.php:959
msgid "Monetization"
msgstr "Monetização"

#: classes/Options_V2.php:1598
msgid "Text Color"
msgstr "Cor do texto"

#: classes/Options_V2.php:1589
msgid "Primary Hover Color"
msgstr "Cor de foco primário"

#: classes/Options_V2.php:1580
msgid "Primary Color"
msgstr "Cor primária"

#: classes/Options_V2.php:1097
msgid "Instructors should earn equal or above this amount to make a withdraw request."
msgstr "Os instrutores devem ganhar igual ou acima desse valor para fazer uma solicitação de saque."

#: classes/Options_V2.php:1095
msgid "Minimum Withdrawal Amount"
msgstr "Valor mínimo de retirada"

#: classes/Options_V2.php:1076
msgid "Fixed"
msgstr "Fixa"

#: classes/Options_V2.php:1075 views/pages/ecommerce/coupon-list.php:133
msgid "Percent"
msgstr "Porcentagem"

#: classes/Options_V2.php:1051
msgid "Fees are charged from the entire sales amount. The remaining amount will be divided among admin and instructors."
msgstr "Taxas serão deduzidas do valor total da venda. O valor restante será dividido entre o administrador e os instrutores."

#: classes/Options_V2.php:1699
msgid "Choose the page for student registration."
msgstr "Escolha a página para cadastro de alunos"

#: classes/Options_V2.php:1696 classes/Utils.php:7460
msgid "Student Registration Page"
msgstr "Página de cadastro de aluno"

#: classes/Options_V2.php:756
msgid "Become an Instructor Button"
msgstr "Torne -se um botão de instrutor"

#: classes/Options_V2.php:1687 classes/Utils.php:7459
msgid "Instructor Registration Page"
msgstr "Página de cadastro de instrutores"

#: classes/Options_V2.php:720
msgid "Allow multiple instructors to upload their courses."
msgstr "Permitir que múltiplos instrutores enviem seus cursos."

#: classes/Options_V2.php:929
msgid "When multiple attempts are allowed, select which method should be used to calculate a student's final grade for the quiz."
msgstr "Quando múltiplas tentativas são permitidas, escolha qual método deve ser utilizado para calcular a nota final do aluno."

#: classes/Options_V2.php:935
msgid "Last Attempt"
msgstr "Última tentativa"

#: classes/Options_V2.php:934
msgid "First Attempt"
msgstr "Primeira tentativa"

#: classes/Options_V2.php:933
msgid "Average Grade"
msgstr "Média de pontuação"

#: classes/Options_V2.php:932
msgid "Highest Grade"
msgstr "Pontuação mais alta"

#: classes/Options_V2.php:901
msgid "Choose which action to follow when the quiz time expires."
msgstr "Escolha qual ação seguir quando o tempo do questionário expirar."

#: classes/Options_V2.php:899
msgid "Attempts must be submitted before time expires, otherwise they will not be counted"
msgstr "Tentativas deverão ser enviadas antes que o tempo expire ou não serão contadas"

#: classes/Options_V2.php:897
msgid "The current quiz answers are submitted automatically."
msgstr "As respostas atuais do questionário são enviadas automaticamente."

#: classes/Options_V2.php:893
msgid "When time expires"
msgstr "Quando o tempo expirar"

#: classes/Quiz.php:145 includes/translate-text.php:184
#: templates/single/quiz/top.php:73
msgid "Seconds"
msgstr "Segundos"

#: classes/Quiz.php:146 includes/translate-text.php:178
#: templates/single/quiz/top.php:74
msgid "Minutes"
msgstr "Minutos"

#: classes/Quiz.php:147 includes/translate-text.php:172
#: templates/single/assignment/content.php:62 templates/single/quiz/top.php:75
msgid "Hours"
msgstr "Horas"

#: classes/Quiz.php:148 includes/translate-text.php:166
#: templates/single/assignment/content.php:62 templates/single/quiz/top.php:76
msgid "Days"
msgstr "Dias"

#: classes/Quiz.php:149 templates/single/quiz/top.php:77
msgid "Weeks"
msgstr "Semanas"

#: views/modal/edit_quiz.php:94
msgid "Time Limit"
msgstr "Tempo limite"

#: classes/Utils.php:10295
msgid "Classic Editor"
msgstr "Editor clássico"

#: classes/Options_V2.php:1130
msgid "Design Settings"
msgstr "Configurações de Design"

#: classes/Options_V2.php:1164 classes/Tutor_Setup.php:464
msgid "Courses Per Page"
msgstr "Cursos por página"

#: classes/Options_V2.php:1150
msgid "Define how many columns you want to use to display courses."
msgstr "Defina quantas colunas você deseja usar para exibir cursos."

#: classes/Options_V2.php:1142
msgid "Column Per Row"
msgstr "Coluna por linha"

#: classes/Options_V2.php:1681
msgid "This page will be used to list all the published courses."
msgstr "Esta página será utilizada para listar todos os cursos publicados."

#: classes/Options_V2.php:1678
msgid "Course Archive Page"
msgstr "Página de arquivo do curso"

#: views/qna/qna-single.php:74 views/qna/qna-single.php:76
#: views/qna/qna-table.php:132 views/qna/qna-table.php:135
msgid "Archive"
msgstr "Arquivo"

#: classes/Tutor_Setup.php:454
msgid "Question and Answer"
msgstr "Perguntas e respostas"

#: classes/Options_V2.php:1664
msgid "Gutenberg Editor"
msgstr "Editor Gutenberg"

#: classes/Options_V2.php:726
msgid "Pagination"
msgstr "Paginação"

#: classes/Options_V2.php:1625
msgid "Video Player"
msgstr "Player de vídeo"

#: classes/Options_V2.php:814
msgid "This will hide the header and the footer and enable spotlight (full screen) mode when students view lessons."
msgstr "Isto ocultará o cabeçalho e o rodapé e ativará o modo de destaque (tela cheia) quando os alunos visualizarem as aulas."

#: classes/Options_V2.php:1770
msgid "Delete all data during uninstallation"
msgstr "Excluir todos os dados durante a desinstalação"

#: classes/Options_V2.php:1767
msgid "Erase upon uninstallation"
msgstr "Remover ao desinstalar"

#: classes/Options_V2.php:783
msgid "Students must be logged in to view course"
msgstr "Alunos precisam estar conectados para visualizar os cursos"

#: classes/Options_V2.php:780
msgid "Course Visibility"
msgstr "Visibilidade do curso"

#: classes/Options_V2.php:679
msgid "This page will be used for student and instructor dashboard"
msgstr "Esta página será utilizada como painel para alunos e instrutores"

#: classes/Options_V2.php:676 classes/Utils.php:7458
msgid "Dashboard Page"
msgstr "Painel de controle"

#: classes/Tutor_Setup.php:390
msgid "Unlimited"
msgstr "Ilimitadas"

#. translators: 1: strong tag start 2: min withdrawal amount 3: strong tag end
#: classes/Withdraw.php:264
msgid "Minimum withdrawal amount is %1$s %2$s %3$s "
msgstr "Valor mínimo para saque é de %s %s %s"

#: classes/Withdraw.php:258
msgid "Please save withdraw method "
msgstr "Salve o método de saque "

#: classes/Withdraw.php:107
msgid "PayPal E-Mail Address"
msgstr "E-mail do PayPal"

#: classes/Withdraw.php:96
msgid "We will send you an E-Check to this address directly."
msgstr "Nós lhe enviaremos um E-Check diretamente para este endereço."

#: classes/Withdraw.php:95
msgid "Your Physical Address"
msgstr "Seu Endereço para correspondências"

#: classes/Withdraw.php:90
msgid "E-Check"
msgstr "E-Check"

#: classes/Withdraw.php:83
msgid "BIC / SWIFT"
msgstr "BIC / SWIFT"

#: classes/Withdraw.php:79
msgid "IBAN"
msgstr "IBAN"

#: classes/Withdraw.php:75
msgid "Bank Name"
msgstr "Nome do banco"

#: classes/Withdraw.php:71
msgid "Account Number"
msgstr "Número da conta"

#: classes/Withdraw.php:67
msgid "Account Name"
msgstr "Nome do beneficiado"

#: classes/Withdraw.php:62
msgid "Get your payment directly into your bank account"
msgstr "Solicite saque direto para sua conta bancária"

#: classes/Withdraw.php:60
msgid "Bank Transfer"
msgstr "Transferência bancária"

#: classes/Post_types.php:467
msgid "Preview course"
msgstr "Prévia do curso"

#: classes/Post_types.php:461
msgid "View course"
msgstr "Ver curso"

#: classes/Post_types.php:455
msgid "Course draft updated."
msgstr "Rascunho do curso atualizado."

#: classes/Post_types.php:440
msgid "M j, Y @ G:i"
msgstr "M j, Y @ G:i"

#: classes/Post_types.php:453
msgid "Course submitted."
msgstr "Curso enviado."

#: classes/Post_types.php:452
msgid "Course saved."
msgstr "Curso salvo."

#: classes/Post_types.php:451
msgid "Course published."
msgstr "Curso publicado."

#: classes/Post_types.php:433
msgid "Course restored to revision from "
msgstr "Curso restaurado para revisão de "

#: classes/Post_types.php:448
msgid "Custom field deleted."
msgstr "Campo custom excluído."

#: classes/Post_types.php:447
msgid "Custom field updated."
msgstr "Campo custom atualizado."

#: classes/Post_types.php:446 classes/Post_types.php:449
msgid "Course updated."
msgstr "Curso atualizado."

#: classes/Post_types.php:381
msgid "No Assignments found in Trash."
msgstr "Nenhuma tarefa foi encontrada na lixeira."

#: classes/Post_types.php:380
msgid "No Assignments found."
msgstr "Nenhuma tarefa foi encontrada."

#: classes/Post_types.php:379
msgid "Parent Assignments:"
msgstr "Tarefas ascendentes:"

#: classes/Post_types.php:378
msgid "Search Assignments"
msgstr "Pesquisar tarefas"

#: classes/Post_types.php:377
msgid "Assignments"
msgstr "Tarefas"

#: classes/Post_types.php:376
msgid "View Assignment"
msgstr "Ver tarefa"

#: classes/Post_types.php:375
msgid "Edit Assignment"
msgstr "Editar tarefa"

#: classes/Post_types.php:374
msgid "New Assignment"
msgstr "Nova tarefa"

#: classes/Post_types.php:373
msgid "Add New Assignment"
msgstr "Adicionar nova tarefa"

#: classes/Post_types.php:371
msgctxt "add new on admin bar"
msgid "Assignment"
msgstr "Tarefa"

#: classes/Post_types.php:370
msgctxt "admin menu"
msgid "Assignments"
msgstr "Tarefas"

#: classes/Post_types.php:369
msgctxt "post type singular name"
msgid "Assignment"
msgstr "Tarefa"

#: classes/Post_types.php:368
msgctxt "post type general name"
msgid "Assignments"
msgstr "Tarefas"

#: classes/Post_types.php:302
msgid "No quizzes found in Trash."
msgstr "Nenhum questionário encontrado na lixeira."

#: classes/Post_types.php:301
msgid "No quizzes found."
msgstr "Nenhum questionário encontrado."

#: classes/Post_types.php:300
msgid "Parent Quizzes:"
msgstr "Questionários ascendentes:"

#: classes/Post_types.php:299
msgid "Search Quizzes"
msgstr "Pesquisar questionários"

#: classes/Post_types.php:298
msgid "Quizzes"
msgstr "Questionários"

#: classes/Post_types.php:297
msgid "View Quiz"
msgstr "Ver questionário"

#: classes/Post_types.php:296
msgid "Edit Quiz"
msgstr "Editar questionário"

#: classes/Post_types.php:295
msgid "New Quiz"
msgstr "Novo questionário"

#: classes/Post_types.php:294
msgid "Add New Quiz"
msgstr "Adicionar novo questionário"

#: classes/Post_types.php:292
msgctxt "add new on admin bar"
msgid "Quiz"
msgstr "Questionário"

#: classes/Post_types.php:291
msgctxt "admin menu"
msgid "Quizzes"
msgstr "Questionários"

#: classes/Post_types.php:290
msgctxt "post type singular name"
msgid "Quiz"
msgstr "Questionário"

#: classes/Post_types.php:289
msgctxt "post type general name"
msgid "Quizzes"
msgstr "Questionários"

#: classes/Post_types.php:235
msgid "No lessons found in Trash."
msgstr "Nenhuma aula encontrada na lixeira."

#: classes/Post_types.php:234
msgid "No lessons found."
msgstr "Nenhuma aula encontrada."

#: classes/Post_types.php:233
msgid "Parent Lessons:"
msgstr "Aulas ascendentes:"

#: classes/Post_types.php:232
msgid "Search Lessons"
msgstr "Pesquisar aulas"

#: classes/Post_types.php:230
msgid "View Lesson"
msgstr "Ver aula"

#: classes/Post_types.php:229
msgid "Edit Lesson"
msgstr "Editar aula"

#: classes/Post_types.php:228
msgid "New Lesson"
msgstr "Nova aula"

#: classes/Post_types.php:227
msgid "Add New Lesson"
msgstr "Adicionar nova aula"

#: classes/Post_types.php:225
msgctxt "add new on admin bar"
msgid "Lesson"
msgstr "Aula"

#: classes/Post_types.php:224
msgctxt "admin menu"
msgid "Lessons"
msgstr "Aulas"

#: classes/Post_types.php:223
msgctxt "post type singular name"
msgid "Lesson"
msgstr "Aula"

#: classes/Post_types.php:222
msgctxt "post type general name"
msgid "Lessons"
msgstr "Aulas"

#: classes/Post_types.php:192
msgid "No Tags found."
msgstr "Nenhuma tag encontrada."

#: classes/Post_types.php:191
msgid "Choose from the most used Tags"
msgstr "Escolha entre as tags mais usadas"

#: classes/Post_types.php:190
msgid "Add or remove Tags"
msgstr "Adicionar ou remover tags"

#: classes/Post_types.php:189
msgid "Separate Tags with commas"
msgstr "Separe as tags entre virgulas"

#: classes/Post_types.php:188
msgid "New Tag Name"
msgstr "Novo nome da tag"

#: classes/Post_types.php:187
msgid "Add New Tag"
msgstr "Adicionar nova tag"

#: classes/Post_types.php:186
msgid "Update Tag"
msgstr "Atualizar tag"

#: classes/Post_types.php:185
msgid "Edit Tag"
msgstr "Editar tag"

#: classes/Post_types.php:182
msgid "All Tags"
msgstr "Todas as tags"

#: classes/Post_types.php:181
msgid "Popular Tags"
msgstr "Tags populares"

#: classes/Post_types.php:180
msgid "Search Tags"
msgstr "Procurar tags"

#: classes/Post_types.php:179
msgctxt "taxonomy singular name"
msgid "Tag"
msgstr "Tag"

#: classes/Post_types.php:178
msgctxt "taxonomy general name"
msgid "Tags"
msgstr "Tags"

#: classes/Post_types.php:161
msgid "Course Categories"
msgstr "Categorias de cursos"

#: classes/Post_types.php:160
msgid "No categories found."
msgstr "Nenhuma categoria encontrada."

#: classes/Post_types.php:159
msgid "Choose from the most used categories"
msgstr "Escolha entre as categorias mais usadas"

#: classes/Post_types.php:158
msgid "Add or remove categories"
msgstr "Adicionar ou remover categorias"

#: classes/Post_types.php:157
msgid "Separate categories with commas"
msgstr "Separe categorias entre virgulas"

#: classes/Post_types.php:156
msgid "New Category Name"
msgstr "Novo nome da categoria"

#: classes/Post_types.php:155
msgid "Add New Category"
msgstr "Adicionar nova categoria"

#: classes/Post_types.php:154
msgid "Update Category"
msgstr "Atualizar categoria"

#: classes/Post_types.php:153
msgid "Edit Category"
msgstr "Editar categoria"

#: classes/Post_types.php:150
msgid "All Categories"
msgstr "Todas as categorias"

#: classes/Post_types.php:149
msgid "Popular Categories"
msgstr "Categorias populares"

#: classes/Post_types.php:148
msgid "Search Categories"
msgstr "Pesquisar categorias"

#: classes/Post_types.php:147
msgctxt "taxonomy singular name"
msgid "Category"
msgstr "Categoria"

#: classes/Post_types.php:146
msgctxt "taxonomy general name"
msgid "Course Categories"
msgstr "Categorias dos cursos"

#: classes/Post_types.php:108 classes/Post_types.php:240
#: classes/Post_types.php:307 classes/Post_types.php:347
#: classes/Post_types.php:386 classes/Post_types.php:501
msgid "Description."
msgstr "Descrição."

#: classes/Post_types.php:103
msgid "No courses found in Trash."
msgstr "Nenhum curso encontrado na lixeira."

#: classes/Post_types.php:102
msgid "No courses found."
msgstr "Nenhum curso foi encontrado."

#: classes/Post_types.php:101
msgid "Parent Courses:"
msgstr "Cursos ascendentes:"

#: classes/Post_types.php:100
msgid "Search Courses"
msgstr "Pesquisar cursos"

#: classes/Post_types.php:97
msgid "Edit Course"
msgstr "Editar curso"

#: classes/Course.php:765 classes/Post_types.php:96
msgid "New Course"
msgstr "Novo curso"

#: classes/Post_types.php:95
msgid "Add New Course"
msgstr "Adicionar novo curso"

#: views/pages/course-list.php:49 views/pages/ecommerce/coupon-list.php:48
#: views/pages/ecommerce/order-list.php:41 views/pages/instructors.php:83
#: views/pages/tools/manage-tokens.php:37
msgid "Add New"
msgstr "Adicionar novo"

#: classes/Post_types.php:93
msgctxt "add new on admin bar"
msgid "Course"
msgstr "Curso"

#: classes/Post_types.php:92
msgctxt "admin menu"
msgid "Courses"
msgstr "Cursos"

#: classes/Post_types.php:91
msgctxt "post type singular name"
msgid "Course"
msgstr "Curso"

#: classes/Post_types.php:90
msgctxt "post type general name"
msgid "Courses"
msgstr "Cursos"

#: classes/Course.php:2279 classes/Options_V2.php:1311
#: classes/Options_V2.php:1319 classes/Options_V2.php:1327
#: classes/Options_V2.php:1343 classes/Options_V2.php:1351
#: classes/Options_V2.php:1359 classes/Options_V2.php:1367
#: classes/Options_V2.php:1375 classes/Options_V2.php:1383
#: classes/Options_V2.php:1391 classes/Options_V2.php:1399
#: classes/Options_V2.php:1415 classes/Options_V2.php:1423 classes/User.php:431
msgid "Enable"
msgstr "Ativar"

#: classes/Options_V2.php:664
msgid "General Settings"
msgstr "Configurações gerais"

#: classes/Options_V2.php:768 views/metabox/course/settings-tabs.php:25
msgid "Course Settings"
msgstr "Configurações do curso"

#: classes/TutorEDD.php:94
msgid "Easy Digital Downloads"
msgstr "Easy Digital Downloads"

#: classes/TutorEDD.php:73
msgid "This will enable sell your product via EDD"
msgstr "Isso irá ativar a venda de produtos via EDD"

#: classes/TutorEDD.php:72
msgid "Enable EDD"
msgstr "Ativar EDD"

#: classes/TutorEDD.php:68
msgid "Tutor Course Attachments Settings"
msgstr "Tutor - Configurações de anexos dos cursos"

#: classes/TutorEDD.php:63
msgid "EDD"
msgstr "EDD"

#: classes/Instructor.php:308
msgid "Instructor has been added successfully"
msgstr "O instrutor foi adicionado com sucesso"

#: classes/Instructor.php:214
msgid "Already applied for instructor"
msgstr "Já se inscreveu para instrutor"

#. translators: %s: plugin name
#: classes/Admin.php:587
msgid "If you like %1$s please leave us a %2$s rating. A huge thanks in advance!"
msgstr "Se você gostou de %1$s deixe sua classificação com %2$s. Muito obrigado!"

#: classes/Admin.php:563
msgid "<strong style=\"color: #03bd24\">Get Support</strong>"
msgstr "<strong style=\"color: #03bd24\">Suporte</strong>"

#: classes/Admin.php:558
msgid "<strong style=\"color: #03bd24\">Documentation</strong>"
msgstr "<strong style=\"color: #03bd24\">Documentação</strong>"

#: classes/Admin.php:178 classes/Admin.php:532
#: views/pages/feature-promotion.php:22
msgid "Upgrade to Pro"
msgstr "Atualizar para Pro"

#: classes/Admin.php:164
msgid "Withdraw Requests"
msgstr "Solicitações de saque"

#: classes/Admin.php:107
msgid "Pro"
msgstr "Pro"

#: classes/Utils.php:7419
msgid "Not Taken"
msgstr "Não tomado"

#: classes/Utils.php:7417
msgid "In Progress"
msgstr "Em progresso"

#: classes/Utils.php:7413 includes/translate-text.php:69
#: models/OrderModel.php:171 templates/dashboard/purchase_history.php:205
msgid "Completed"
msgstr "Concluído"

#: classes/Utils.php:5511
msgid "Expert"
msgstr "Avançado"

#: classes/Utils.php:5510
msgid "Intermediate"
msgstr "Intermediário"

#: classes/Utils.php:5509
msgid "Beginner"
msgstr "Iniciante"

#: classes/Utils.php:5508
msgid "All Levels"
msgstr "Todos os níveis"

#: classes/Utils.php:5169
msgid "Ordering"
msgstr "Ordenação"

#: classes/Utils.php:5164
msgid "Image Answering"
msgstr "Imagem de resposta"

#: classes/Utils.php:5159
msgid "Image Matching"
msgstr "Imagem correspondente"

#: classes/Utils.php:5154
msgid "Matching"
msgstr "Correspondente"

#: classes/Utils.php:5149
msgid "Short Answer"
msgstr "Resposta curta"

#: classes/Utils.php:5144
msgid "Fill In The Blanks"
msgstr "Preencha as lacunas"

#: classes/Utils.php:5134
msgid "Multiple Choice"
msgstr "Múltipla escolha"

#: classes/Utils.php:5129
msgid "Single Choice"
msgstr "Escolha única"

#: classes/Utils.php:5124
msgid "True/False"
msgstr "Verdadeiro/Falso"

#: classes/Utils.php:3183 includes/translate-text.php:93
#: views/pages/instructors.php:56
msgid "Blocked"
msgstr "Bloqueado"

#: classes/Utils.php:3182 classes/Withdraw_Requests_List.php:79
#: includes/translate-text.php:61 views/pages/instructors.php:55
msgid "Approved"
msgstr "Aprovado"

#: classes/Backend_Page_Trait.php:106 classes/Course_List.php:163
#: classes/Instructors_List.php:104 classes/Quiz_Attempts_List.php:232
#: classes/Utils.php:3181 classes/Withdraw_Requests_List.php:85
#: includes/translate-text.php:41 templates/dashboard/dashboard.php:153
#: templates/dashboard/my-courses.php:64
#: templates/dashboard/purchase_history.php:225
#: templates/single/assignment/content.php:459
#: templates/single/course/reviews-loop.php:41 views/pages/course-list.php:133
#: views/pages/ecommerce/order-list.php:57 views/pages/instructors.php:54
#: views/quiz/attempt-details.php:294 views/quiz/attempt-details.php:775
#: views/quiz/attempt-table.php:156
msgid "Pending"
msgstr "Pendente"

#: classes/Utils.php:3041
msgid "Logout"
msgstr "Sair"

#: classes/Admin.php:161 classes/Quiz_Attempts_List.php:59
#: classes/Utils.php:9368 templates/dashboard.php:54
#: templates/dashboard/quiz-attempts.php:31
msgid "Quiz Attempts"
msgstr "Tentativas de questionários"

#: classes/Utils.php:9063 classes/Utils.php:9404
#: templates/dashboard/reviews.php:28
#: templates/dashboard/reviews/given-reviews.php:27
msgid "Reviews"
msgstr "Avaliações"

#: classes/Admin.php:142 classes/Utils.php:9338
msgid "Create Course"
msgstr "Criar curso"

#: classes/Utils.php:2599 templates/public-profile.php:149
msgid "Course Enrolled"
msgstr "Curso matriculado"

#: classes/Admin.php:159 classes/Options_V2.php:1303 classes/Utils.php:9067
#: templates/dashboard.php:54
msgid "Q&A"
msgstr "P&R"

#: templates/single/lesson/content.php:127
msgid "Overview"
msgstr "Visão geral"

#: classes/Quiz.php:1399 classes/Quiz.php:1795
#: views/modal/question_answer_form.php:41
msgid "False"
msgstr "Falso"

#: classes/Quiz.php:1392 classes/Quiz.php:1788
#: views/modal/question_answer_form.php:37
msgid "True"
msgstr "Verdadeiro"

#: classes/Quiz.php:944
msgid "Quiz has been timeout already"
msgstr "Tempo de resposta do questionário se esgotou"

#: views/pages/instructors.php:177
msgid "Total Course"
msgstr "Total de curso"

#: views/pages/ecommerce/coupon-list.php:88
#: views/pages/ecommerce/order-list.php:92 views/pages/instructors.php:119
msgid "Name"
msgstr "Nome"

#: classes/Tutor.php:1186
msgid "Instructor Registration"
msgstr "Cadastro de instrutores"

#: classes/Tutor.php:1177
msgid "Student Registration"
msgstr "Cadastro de alunos"

#: classes/Addons.php:271
msgid "Tutor Report"
msgstr "Tutor relatórios"

#: classes/Addons.php:311
msgid "Tutor Prerequisites"
msgstr "Pré-requisitos pro Tutor"

#: classes/Addons.php:251
msgid "Tutor Multi Instructors"
msgstr "Tutor múltiplos instrutores"

#: classes/Addons.php:259
msgid "Tutor Course Preview"
msgstr "Tutor prévia do curso"

#: classes/Addons.php:263
msgid "Tutor Course Attachments"
msgstr "Tutor anexos de cursos"

#: classes/Addons.php:303
msgid "Tutor Certificate"
msgstr "Tutor certificados"

#: classes/Addons.php:255
msgid "Tutor Assignments"
msgstr "Tarefas do Tutor"

#: classes/Addons.php:323
msgid "Paid Memberships Pro"
msgstr "Paid Memberships Pro"

#: classes/Addons.php:319
msgid "WooCommerce Subscriptions"
msgstr "Assinaturas WooCommerce"

#: classes/Addons.php:299
msgid "Enrollment"
msgstr "Inscrição"

#: classes/Addons.php:247
msgid "Content Drip"
msgstr "Conteúdo progressivo"

#: classes/Addons.php:307
msgid "Gradebook"
msgstr "Boletim"

#: classes/Course.php:1922
msgid "Please Sign In first"
msgstr "Faça o acesso primeiro"

#: classes/Post_types.php:231
msgid "Lessons"
msgstr "Aulas"

#: classes/Admin.php:157 classes/Announcements.php:52
#: classes/Options_V2.php:1414 classes/Utils.php:9072 classes/Utils.php:9358
#: views/fragments/announcement-list.php:253
msgid "Announcements"
msgstr "Recados"

#: classes/Admin.php:154
msgid "Instructors"
msgstr "Instrutores"

#: classes/Options_V2.php:941
msgid "Video"
msgstr "Vídeo"

#: classes/Taxonomies.php:236
msgid "Image"
msgstr "Imagem"

#: classes/Taxonomies.php:79 classes/Taxonomies.php:166
msgid "Use image"
msgstr "Usar imagem"

#: classes/Taxonomies.php:77 classes/Taxonomies.php:164
msgid "Choose an image"
msgstr "Escolha uma imagem"

#: classes/Taxonomies.php:53 classes/Taxonomies.php:140
msgid "Remove image"
msgstr "Remover a imagem"

#: classes/Taxonomies.php:52 classes/Taxonomies.php:139
msgid "Upload/Add image"
msgstr "Enviar/adicionar imagem"

#: classes/Taxonomies.php:48 classes/Taxonomies.php:132
#: classes/Taxonomies.php:265
msgid "Thumbnail"
msgstr "Miniatura"

#: classes/Student.php:299
msgid "New password and confirm password does not matched"
msgstr "Nova senha e confirmação não batem"

#: classes/Student.php:296
msgid "Confirm Password Required"
msgstr "Confirmação de senha é obrigatória"

#: classes/Student.php:293
msgid "New Password Required"
msgstr "Nova senha obrigatória"

#: classes/Student.php:290
msgid "Incorrect Previous Password"
msgstr "Senha anterior está incorreta"

#: classes/Instructor.php:119 classes/Instructor.php:264 classes/Student.php:90
msgid "Valid E-Mail is required"
msgstr "É obrigatório um e-mail válido"

#: classes/Instructor.php:97 classes/Student.php:70
msgid "Password Confirmation field is required"
msgstr "Obrigatório preencher o campo 'Confirmação de Senha'"

#: classes/Instructor.php:96 classes/Instructor.php:251 classes/Student.php:69
msgid "Password field is required"
msgstr "Obrigatório preencher o campo 'Senha'"

#: classes/Instructor.php:95 classes/Instructor.php:250 classes/Student.php:68
msgid "User Name field is required"
msgstr "Obrigatório preencher o campo 'Usuário'"

#: classes/Instructor.php:94 classes/Instructor.php:249 classes/Student.php:67
msgid "E-Mail field is required"
msgstr "Obrigatório preencher o campo 'E-Mail'"

#: classes/Instructor.php:93 classes/Instructor.php:248 classes/Student.php:66
msgid "Last name field is required"
msgstr "Obrigatório preencher o campo 'Sobrenome'"

#: classes/Instructor.php:92 classes/Instructor.php:247 classes/Student.php:65
msgid "First name field is required"
msgstr "Obrigatório preencher o campo 'Nome'"

#: templates/dashboard/question-answer/answers.php:20
#: views/modal/question_answer_list.php:36 views/pages/answer.php:18
msgid "Answer"
msgstr "Resposta"

#: classes/Options_V2.php:662 classes/TutorEDD.php:67
msgid "General"
msgstr "Geral"

#: classes/WooCommerce.php:479
msgid "WooCommerce"
msgstr "WooCommerce"

#: classes/TutorEDD.php:107 classes/WooCommerce.php:361
msgid "Add Product"
msgstr "Adicionar produto"

#: classes/WooCommerce.php:332
msgid "This checkmark ensure that you will sell a specific course via this product."
msgstr "Esta marca de seleção garante que você venderá um curso específico por meio deste produto."

#: classes/WooCommerce.php:331
msgid "For Tutor"
msgstr "Integração Tutor LMS"

#: classes/Admin.php:452 includes/tutor-template-functions.php:1601
#: templates/permission-denied.php:25
#: templates/single/lesson/required-enroll.php:15
msgid "Permission Denied"
msgstr "Permissão negada"

#: classes/Instructor.php:222 classes/Template.php:258
msgid "Permission denied"
msgstr "Permissão negada"

#: classes/Ajax.php:338
msgid "Course added to wish list"
msgstr "Curso adicionado à lista de desejos"

#: classes/Ajax.php:345
msgid "Course removed from wish list"
msgstr "Curso removido da lista de desejos"

#: classes/Course_Widget.php:213
msgid "Total results you like to show"
msgstr "Total de resultados a serem exibidos"

#: classes/Course_Widget.php:210
msgid "Count:"
msgstr "Contagem:"

#: classes/Course_Widget.php:200
msgid "Order"
msgstr "Ordem"

#: classes/Course_Widget.php:185
msgid "OrderBy"
msgstr "Ordenar Por"

#: classes/Course_Widget.php:179
msgid "Place comma (,) separated category ids"
msgstr "Preencha com os IDs de categoria separados por vírgula (,)"

#: classes/Course_Widget.php:169
msgid "Place comma (,) separated courses ids which you like to exclude from the query"
msgstr "Preencha com os IDs separados por vírgula (,) dos cursos que você gostaria de excluir da consulta"

#: classes/Course_Widget.php:166
msgid "Exclude IDS:"
msgstr "Excluir IDs:"

#: classes/Course_Widget.php:161
msgid "Place single course id or comma (,) separated course ids"
msgstr "Preencha com o ID do curso ou vários IDs separados por vírgula (,)"

#: classes/Course_Widget.php:140
msgid "New title"
msgstr "Novo Título"

#: classes/Course_Widget.php:34
msgid "Display courses wherever widget support is available."
msgstr "Exibir cursos sempre que houver suporte para widgets."

#: classes/Course_Widget.php:33
msgid "Tutor Course"
msgstr "Curso Tutor LMS"

#: templates/ecommerce/checkout-details.php:228 views/elements/filters.php:41
#: views/elements/search-filter.php:28 views/elements/search-filter.php:48
#: views/options/template/import_export.php:103
msgid "Apply"
msgstr "Aplicar"

#: classes/Course.php:1972 classes/Lesson.php:432
msgid "Please Sign-In"
msgstr "Faça o acesso"

#: templates/dashboard/announcements/create.php:31
#: templates/dashboard/announcements/update.php:32
#: views/fragments/announcement-list.php:54 views/metabox/lesson-metabox.php:15
msgid "Select Course"
msgstr "Selecione o curso"

#: includes/tutor-general-functions.php:124
msgid "Select a category"
msgstr "Selecione uma categoria"

#: includes/tutor-general-functions.php:123
msgid "Search Course Category. ex. Design, Development, Business"
msgstr "Pesquisar categorias de cursos. ex. design, desenvolvimento, negócios"

#: templates/single/lesson/sidebar_question_and_answer.php:45
msgid "No questions yet"
msgstr "Nenhuma pergunta ainda"

#: classes/Post_types.php:98 includes/tutor-template-functions.php:1606
#: templates/single/lesson/required-enroll.php:21
msgid "View Course"
msgstr "Visualizar curso"

#. translators: %s: course name
#: templates/single/lesson/required-enroll.php:18
msgid "Course name : %s"
msgstr "Nome do curso: %s"

#: templates/single/course/add-to-cart-woocommerce.php:79
msgid "Please make sure that your product exists and valid for this course"
msgstr "Certifique-se que o seu produto existe e é válido para este curso"

#: templates/single/course/reviews.php:186
msgid "Submit Review"
msgstr "Enviar revisão"

#: templates/single/course/reviews.php:138
msgid "Write a review"
msgstr "Escrever uma revisão"

#: templates/single/course/reviews.php:138
msgid "Edit review"
msgstr "Editar revisão"

#: templates/single/course/continue-lesson.php:40
msgid "Continue to lesson"
msgstr "Continuar aula"

#: templates/single/course/material-includes.php:24
msgid "Material Includes"
msgstr "Materiais inclusos"

#: templates/single/course/q_and_a_turned_off.php:17
msgid "This feature has been disabled by the administrator"
msgstr "Este recurso foi desativado pelo administrador"

#: templates/single/course/add-to-cart-edd.php:20
msgid "Please make sure that your EDD product exists and valid for this course"
msgstr "Certifique-se de que o seu produto EDD existe e é válido para este curso"

#: classes/Options_V2.php:1398
#: templates/single/course/course-requirements.php:25
msgid "Requirements"
msgstr "Requisitos"

#: templates/single/quiz/parts/image-answer.php:36
msgid "Write your answer here"
msgstr "Escreva sua resposta aqui"

#: views/modal/question_answer_form.php:108
msgid "Question Title"
msgstr "Título da pergunta"

#: views/qna/qna-new.php:43
msgid "Ask a New Question"
msgstr "Faça uma Pergunta"

#: templates/single/course/course-content.php:30
msgid "About Course"
msgstr "Sobre o curso"

#: views/options/template/import_export.php:36
msgid "Last Update"
msgstr "Última atualização"

#: classes/Options_V2.php:1342 templates/single/course/course-entry-box.php:33
#: templates/single/course/course-entry-box.php:34
msgid "Total Enrolled"
msgstr "Total matriculado"

#: classes/Admin.php:147 views/pages/course-list.php:181
msgid "Categories"
msgstr "Categorias"

#: views/course-share.php:24
msgid "Share"
msgstr "Compartilhar"

#: classes/Backend_Page_Trait.php:54 templates/dashboard/dashboard.php:312
#: templates/loop/enrolled-course-progress.php:23
#: templates/single-content-loader.php:96
#: templates/single/course/course-entry-box.php:98
msgid "Complete"
msgstr "Completo"

#: templates/single/course/course-entry-box.php:265
msgid "Enroll now"
msgstr "Inscreva -se agora"

#: classes/Options_V2.php:1406
msgid "Target Audience"
msgstr "Público-alvo"

#: templates/single/course/course-entry-box.php:189
msgid "Complete Course"
msgstr "Completar Curso"

#: classes/Admin.php:149 classes/Post_types.php:193
#: templates/single/course/tags.php:18
msgid "Tags"
msgstr "Tags"

#: templates/single/quiz/no_course_belongs.php:16
msgid "It seems there is no course belongs with this quiz, you can not attempt on this quiz without a course belongs, please notify to your instructor to fix this issue."
msgstr "Parece que não há curso associado com este questionário e você não poderá respondê-los sem estar vinculado à um curso relacionado à ele. Verifique com o seu instrutor para corrigir este problema."

#: templates/single/quiz/no_course_belongs.php:15
msgid "No course found for this quiz"
msgstr "Nenhum curso encontrado para este questionário"

#: templates/single/quiz/previous-attempts.php:58
#: templates/single/quiz/top.php:129
msgid "Start Quiz"
msgstr "Iniciar questionário"

#: classes/Tutor_Setup.php:546 templates/single/quiz/body.php:73
msgid "Finish"
msgstr "Finalizar"

#: templates/single/quiz/parts/question.php:216
#: templates/single/quiz/parts/question.php:234
msgid "Submit Quiz"
msgstr "Enviar questionário"

#: templates/single/quiz/parts/question.php:118
msgid "Marks : "
msgstr "Marcas : "

#: templates/single/quiz/parts/meta.php:71
msgid "Time remaining: "
msgstr "Tempo restante: "

#: templates/single/quiz/top.php:110
msgid "Passing Grade"
msgstr "Nota para aprovação"

#: templates/single/quiz/parts/meta.php:45 views/modal/edit_quiz.php:144
msgid "Attempts Allowed"
msgstr "Tentativas permitidas"

#: templates/single/assignment/content.php:611
msgid "Start Assignment Submit"
msgstr "Iniciar a atribuição de envio"

#: templates/single/assignment/content.php:475
#: views/quiz/attempt-details.php:321
msgid "Instructor Note"
msgstr "Anotações do instrutor"

#: includes/translate-text.php:133 models/OrderModel.php:188
#: templates/single/assignment/content.php:452
msgid "Failed"
msgstr "Reprovado"

#: templates/single/assignment/content.php:448
msgid "Passed"
msgstr "Aprovado"

#: templates/single/assignment/content.php:354
msgid "Submit Assignment"
msgstr "Enviar tarefa"

#: templates/single/assignment/content.php:254
msgid "Assignment answer form"
msgstr "Formulário de respostas da tarefa"

#: templates/single/assignment/content.php:134
#: templates/single/quiz/parts/meta.php:41
msgid "No limit"
msgstr "Sem limite"

#: templates/dashboard/instructor/registration.php:165
msgid "Register as instructor"
msgstr "Cadastrar como instrutor"

#: classes/Options_V2.php:1048
msgid "Deduct Fees"
msgstr "Taxas de dedução"

#: ecommerce/EmailController.php:658
#: templates/dashboard/purchase_history.php:103
msgid "Order ID"
msgstr "ID do pedido"

#: templates/course-filter/filters.php:124
#: templates/dashboard/purchase_history.php:112 views/pages/course-list.php:187
msgid "Price"
msgstr "Preço"

#: classes/Utils.php:8943
msgid "All Time"
msgstr "Histórico total"

#: classes/Options_V2.php:1382 templates/single/assignment/content.php:190
#: templates/single/assignment/content.php:367
#: templates/single/assignment/content.php:579
#: views/modal/question_form.php:145 views/pages/tools/manage-tokens.php:114
#: views/pages/tools/manage-tokens.php:180
msgid "Description"
msgstr "Descrição"

#: classes/Utils.php:9059
msgid "Course Info"
msgstr "Informações do curso"

#: templates/dashboard/reviews/edit-review-form.php:25
#: templates/dashboard/reviews/given-reviews.php:122 views/modal/review.php:42
msgid "Update Review"
msgstr "Atualizar avaliação"

#: templates/dashboard/reviews/edit-review-form.php:22
#: templates/dashboard/reviews/given-reviews.php:115
#: templates/single/course/reviews.php:182
msgid "write a review"
msgstr "escrever uma revisão"

#: templates/dashboard/assignments/review.php:25
msgid "Assignments submission not found or not completed"
msgstr "Envio das tarefas não encontrado ou incompleto"

#: templates/dashboard/assignments/review.php:160
msgid "Evaluate this submission"
msgstr "Avaliar este envio"

#: templates/dashboard/assignments/review.php:131
msgid "Evaluation"
msgstr "Avaliação"

#: templates/dashboard/assignments/review.php:93
msgid "Attach assignment file(s)"
msgstr "Anexar arquivo(s) da tarefa"

#: templates/dashboard/assignments/review.php:81
msgid "Assignment Description:"
msgstr "Descrição da tarefa:"

#: templates/dashboard/assignments.php:83
msgid "Total Submit"
msgstr "Total Enviar"

#: templates/dashboard/assignments/review.php:60
#: templates/dashboard/assignments/submitted.php:80
#: templates/dashboard/question-answer.php:61
#: templates/dashboard/reviews.php:54 templates/public-profile.php:137
#: views/elements/search-filter.php:43 views/qna/contexts.php:16
#: views/quiz/header-context/frontend-dashboard-students-attempts.php:31
msgid "Student"
msgstr "Aluno"

#: views/pages/course-list.php:269
msgid "Assignment:"
msgstr "Atribuição:"

#: templates/dashboard/logged-in.php:14
msgid "You are already logged in"
msgstr "Você já está logado"

#: templates/dashboard/registration.php:166
msgid "Register"
msgstr "Cadastrar"

#: templates/dashboard/instructor/registration.php:134
#: templates/dashboard/registration.php:135
#: views/pages/add_new_instructor.php:126
msgid "Password Confirmation"
msgstr "Confirmação de senha"

#: templates/dashboard/instructor/registration.php:129
#: templates/dashboard/registration.php:130
#: views/pages/add_new_instructor.php:120
msgid "Password confirmation"
msgstr "Confirmação de senha"

#: templates/dashboard/instructor/registration.php:93
#: templates/dashboard/instructor/registration.php:96
#: templates/dashboard/registration.php:93
#: templates/dashboard/registration.php:96
#: views/pages/add_new_instructor.php:81 views/pages/add_new_instructor.php:87
msgid "E-Mail"
msgstr "E-mail"

#: templates/dashboard/instructor/registration.php:83
#: templates/dashboard/instructor/registration.php:86
#: templates/dashboard/registration.php:83
#: templates/dashboard/registration.php:86
#: templates/dashboard/settings/profile.php:142
#: views/pages/add_new_instructor.php:68 views/pages/add_new_instructor.php:74
msgid "User Name"
msgstr "Usuário"

#: templates/dashboard/dashboard.php:353
msgid "Enrolled"
msgstr "Matriculado"

#: ecommerce/EmailController.php:661 templates/dashboard/dashboard.php:350
#: templates/dashboard/purchase_history.php:106
msgid "Course Name"
msgstr "Nome do curso"

#: templates/dashboard/dashboard.php:234
msgid "Total Earnings"
msgstr "Total de ganhos"

#: templates/dashboard/dashboard.php:221 views/pages/instructors.php:127
msgid "Total Courses"
msgstr "Total de cursos"

#: templates/dashboard/dashboard.php:208
msgid "Total Students"
msgstr "Total de alunos"

#: templates/loop/course-price-edd.php:29
#: templates/loop/course-price-tutor.php:21
#: templates/loop/course-price-woocommerce.php:23
#: templates/loop/course-price.php:31
msgid "Enroll Course"
msgstr "Matricular-se no Curso"

#: classes/Tutor.php:1168 classes/Utils.php:9388 templates/dashboard.php:48
#: templates/dashboard/dashboard.php:136
msgid "Dashboard"
msgstr "Painel"

#: classes/Admin.php:133 classes/Course_List.php:54 classes/Post_types.php:99
#: templates/archive-course-init.php:132
#: templates/dashboard/announcements.php:91
#: templates/dashboard/assignments.php:40
#: templates/dashboard/elements/filters.php:26
#: templates/instructor/cover.php:39 templates/instructor/default.php:33
#: templates/instructor/minimal-horizontal.php:31
#: templates/instructor/minimal.php:35
#: templates/instructor/portrait-horizontal.php:36
#: templates/public-profile.php:130 templates/public-profile.php:185
msgid "Courses"
msgstr "Cursos"

#: classes/Course_Widget.php:157 views/options/template/tutor_pages.php:27
#: views/pages/ecommerce/order-list.php:88 views/pages/tools/tutor_pages.php:18
msgid "ID"
msgstr "ID"

#: views/quiz/attempt-details.php:796
msgid "Mark as In correct"
msgstr "Marcar como incorreta"

#: views/modal/question_answer_list.php:59
#: views/modal/question_answer_list.php:65 views/quiz/attempt-details.php:792
msgid "Mark as correct"
msgstr "Marcar como correta"

#: views/quiz/attempt-details.php:779
msgid "Incorrect"
msgstr "Incorreto"

#: includes/translate-text.php:49 views/quiz/attempt-details.php:771
msgid "Correct"
msgstr "Correto"

#: views/quiz/contexts.php:98
msgid "Given Answer"
msgstr "Resposta dada"

#: classes/Quiz.php:1604 views/qna/contexts.php:17 views/quiz/contexts.php:19
#: views/quiz/contexts.php:67
msgid "Question"
msgstr "Pergunta"

#: views/pages/ecommerce/coupon-list.php:94 views/quiz/contexts.php:96
msgid "Type"
msgstr "Tipo"

#: views/quiz/attempt-details.php:333
msgid "Quiz Overview"
msgstr "Visão geral do questionário"

#: views/quiz/contexts.php:69
#: views/quiz/header-context/frontend-dashboard-my-attempts.php:32
#: views/quiz/header-context/frontend-dashboard-students-attempts.php:37
msgid "Attempt Time"
msgstr "Tempo da tentativa"

#: templates/single/quiz/top.php:81 views/quiz/contexts.php:68
#: views/quiz/header-context/course-single-previous-attempts.php:38
#: views/quiz/header-context/frontend-dashboard-my-attempts.php:29
#: views/quiz/header-context/frontend-dashboard-students-attempts.php:34
msgid "Quiz Time"
msgstr "Tempo do questionário"

#: templates/dashboard/assignments/submitted.php:86
#: templates/single/assignment/content.php:421 views/quiz/contexts.php:24
#: views/quiz/contexts.php:75 views/quiz/contexts.php:100
msgid "Result"
msgstr "Resultado"

#: classes/Options_V2.php:766 classes/Options_V2.php:1135
#: classes/Options_V2.php:1657 classes/Tutor_Setup.php:436
#: classes/Tutor_Setup.php:537 ecommerce/EmailController.php:417
#: templates/dashboard/announcements/details.php:31
#: templates/dashboard/assignments.php:101
#: templates/dashboard/assignments/review.php:49
#: templates/dashboard/assignments/submitted.php:40
#: templates/instructor/cover.php:39 templates/instructor/default.php:33
#: templates/instructor/minimal-horizontal.php:31
#: templates/instructor/minimal.php:35
#: templates/instructor/portrait-horizontal.php:36
#: templates/public-profile.php:130 views/elements/filters.php:98
#: views/elements/search-filter.php:40
#: views/fragments/announcement-list.php:144
#: views/fragments/announcement-list.php:301 views/qna/qna-table.php:102
#: views/quiz/contexts.php:18
#: views/quiz/header-context/backend-dashboard-students-attempts.php:25
#: views/quiz/header-context/frontend-dashboard-my-attempts.php:19
#: views/quiz/header-context/frontend-dashboard-students-attempts.php:21
msgid "Course"
msgid_plural "Courses"
msgstr[0] "Curso"
msgstr[1] ""

#: views/quiz/contexts.php:65
msgid "Attempt By"
msgstr "Tentativa por"

#: views/pages/view_attempt.php:25 views/pages/view_attempt.php:29
msgid "Attemp not found"
msgstr "Tentativa não encontrada"

#: ecommerce/EmailController.php:542 templates/dashboard/reviews.php:93
msgid "Course:"
msgstr "Curso:"

#: classes/Lesson.php:578 classes/Utils.php:1814
#: templates/single/lesson/comments-loop.php:30
#: templates/single/lesson/comments-loop.php:63
msgid " ago"
msgstr " atrás"

#: classes/Options_V2.php:886 templates/single/quiz/top.php:43
#: views/quiz/header-context/backend-dashboard-students-attempts.php:28
#: views/quiz/header-context/course-single-previous-attempts.php:23
msgid "Quiz"
msgstr "Questionário"

#: classes/Options_V2.php:1422 views/quiz/attempt-table.php:172
#: views/quiz/contexts.php:101
msgid "Review"
msgstr "Revisar"

#: classes/Admin.php:304 classes/Tools_V2.php:103
#: templates/dashboard/purchase_history.php:115
#: templates/dashboard/withdraw.php:233 views/elements/filters.php:162
#: views/options/template/status.php:13
#: views/options/template/tutor_pages.php:33
#: views/pages/ecommerce/coupon-list.php:100
#: views/pages/ecommerce/order-list.php:105 views/pages/instructors.php:137
#: views/pages/instructors.php:193 views/pages/instructors.php:212
#: views/pages/tools/tutor_pages.php:20 views/pages/withdraw_requests.php:100
#: views/pages/withdraw_requests.php:250 views/qna/contexts.php:20
msgid "Status"
msgstr "Status"

#: includes/tinymce_translate.php:27
msgid "Count"
msgstr "Contagem"

#: classes/Admin.php:151 classes/Students_List.php:62
#: templates/public-profile.php:137 views/pages/students.php:84
msgid "Students"
msgstr "Alunos"

#: classes/Utils.php:9400 templates/dashboard/wishlist.php:23
#: templates/single/course/lead-info.php:101
msgid "Wishlist"
msgstr "Lista de desejos"

#: views/pages/withdraw_requests.php:411
msgid "Reject Withdrawal?"
msgstr "Rejeitar a retirada?"

#: templates/dashboard/withdraw.php:227
msgid "Requested On"
msgstr "Solicitado em"

#: templates/dashboard/announcements.php:116
#: templates/dashboard/assignments/submitted.php:77
#: templates/dashboard/purchase_history.php:109
#: templates/dashboard/reviews.php:57
#: templates/single/assignment/content.php:409 views/elements/filters.php:199
#: views/fragments/announcement-list.php:245
#: views/fragments/announcement-list.php:249
#: views/options/template/import_export.php:84 views/pages/course-list.php:190
#: views/pages/ecommerce/order-list.php:95 views/quiz/contexts.php:16
#: views/quiz/contexts.php:66
msgid "Date"
msgstr "Data"

#: views/pages/withdraw_requests.php:91
msgid "Withdraw Method"
msgstr "Método de saque"

#: templates/dashboard/withdraw.php:172 templates/dashboard/withdraw.php:230
#: views/pages/ecommerce/coupon-list.php:133
#: views/pages/withdraw_requests.php:97 views/pages/withdraw_requests.php:245
msgid "Amount"
msgstr "Valor"

#. translators: %1$s: a tag start, %2$s: a tag end
#: templates/dashboard/withdraw.php:121
msgid "You can change your %1$s Withdraw Preference %2$s"
msgstr "Você pode alterar sua %s de preferência para saque %s"

#. translators: %s: available balance
#: templates/dashboard/withdraw.php:82
msgid "You have %s and this is insufficient balance to withdraw"
msgstr "Você tem %1$s %2$s %3$s e este é um saldo insuficiente para retirar"

#: templates/dashboard/dashboard.php:290
msgid "Completed Lessons:"
msgstr "Aulas completas:"

#: templates/dashboard/dashboard.php:192
#: templates/dashboard/enrolled-courses.php:22
msgid "Completed Courses"
msgstr "Cursos completos"

#: templates/dashboard/dashboard.php:179
#: templates/dashboard/enrolled-courses.php:21
msgid "Active Courses"
msgstr "Cursos ativos"

#: views/elements/filters.php:103
msgid "All Courses"
msgstr "Todos os cursos"

#: classes/Utils.php:9396 templates/dashboard/dashboard.php:166
#: templates/dashboard/enrolled-courses.php:20
msgid "Enrolled Courses"
msgstr "Cursos matriculados"

#: templates/dashboard/settings/reset-password.php:38
msgid "New Password"
msgstr "Nova senha"

#: templates/dashboard/settings/profile.php:209
#: templates/dashboard/settings/social-profile.php:46
msgid "Update Profile"
msgstr "Atualizar perfil"

#: views/fragments/thumbnail-uploader.php:61
#: views/modal/question_answer_form.php:61
#: views/modal/question_answer_form.php:177
#: views/modal/question_answer_form.php:223
#: views/modal/question_answer_form.php:252
#: views/options/field-types/upload_half.php:42
msgid "Upload Image"
msgstr "Enviar imagem"

#: views/metabox/user-profile-fields.php:46
msgid "Profile Photo"
msgstr "Foto do perfil"

#: templates/dashboard/settings/withdraw-settings.php:146
msgid "Save Withdrawal Account"
msgstr "Salvar conta de retirada"

#: templates/dashboard/settings/withdraw-settings.php:53
msgid "Min withdraw"
msgstr "Saque mínimo"

#: templates/dashboard/settings/withdraw-settings.php:28
msgid "Select a withdraw method"
msgstr "Escolha um método para saques"

#: classes/Backend_Page_Trait.php:145
#: templates/dashboard/announcements/create.php:65
#: templates/dashboard/announcements/details.php:41
#: templates/dashboard/announcements/update.php:64
#: templates/dashboard/my-courses.php:301
#: templates/dashboard/reviews/given-reviews.php:119
#: templates/dashboard/withdraw.php:191 templates/modal/confirm.php:55
#: views/elements/bulk-confirm-popup.php:33
#: views/elements/common-confirm-popup.php:65
#: views/fragments/announcement-list.php:88
#: views/fragments/announcement-list.php:164
#: views/fragments/announcement-list.php:217 views/modal/review.php:39
#: views/modal/topic-form.php:51
#: views/options/template/common/modal-confirm.php:38
#: views/pages/instructors.php:366 views/pages/tools/manage-tokens.php:125
#: views/pages/tools/manage-tokens.php:190
#: views/pages/withdraw_requests.php:372 views/pages/withdraw_requests.php:430
#: views/qna/qna-new.php:30 views/qna/qna-table.php:176
msgid "Cancel"
msgstr "Cancelar"

#: templates/dashboard/my-courses.php:296
msgid "Delete This Course?"
msgstr "Excluir este curso?"

#: views/quiz/attempt-table.php:136
msgid "Student:"
msgstr "Estudante:"

#: templates/single/assignment/content.php:132
msgid "Duration:"
msgstr "Duração:"

#: classes/Utils.php:9348 templates/dashboard.php:48
#: templates/dashboard/dashboard.php:338 templates/dashboard/my-courses.php:51
msgid "My Courses"
msgstr "Meus cursos"

#: templates/dashboard/reviews/given-reviews.php:59
msgid "Course: "
msgstr "Curso: "

#: templates/dashboard/reviews.php:34
#: templates/dashboard/reviews/given-reviews.php:32
msgid "Received"
msgstr "Recebido"

#: templates/dashboard/reviews.php:40
#: templates/dashboard/reviews/given-reviews.php:35
msgid "Given"
msgstr "Dado"

#: templates/single/assignment/content.php:415 views/quiz/contexts.php:71
msgid "Pass Marks"
msgstr "Notas mínimas"

#: classes/Quiz_Attempts_List.php:226 includes/translate-text.php:53
#: views/quiz/attempt-details.php:298 views/quiz/attempt-table.php:160
msgid "Fail"
msgstr "Falhou"

#: classes/Quiz_Attempts_List.php:220 includes/translate-text.php:45
#: views/quiz/attempt-details.php:296 views/quiz/attempt-table.php:159
msgid "Pass"
msgstr "Passou"

#: templates/single/assignment/content.php:418 views/quiz/contexts.php:23
#: views/quiz/contexts.php:74
msgid "Earned Marks"
msgstr "Marcas conquistadas"

#: templates/dashboard/assignments.php:80
#: templates/single/assignment/content.php:412 views/quiz/contexts.php:20
#: views/quiz/contexts.php:70
#: views/quiz/header-context/course-single-previous-attempts.php:44
msgid "Total Marks"
msgstr "Marcas totais"

#: templates/single/quiz/top.php:60 views/quiz/contexts.php:97
#: views/quiz/header-context/course-single-previous-attempts.php:32
msgid "Questions"
msgstr "Perguntas"

#: classes/Utils.php:9408 templates/dashboard/my-quiz-attempts.php:35
msgid "My Quiz Attempts"
msgstr "Minhas tentativas de questionários"

#: classes/Options_V2.php:1088 templates/dashboard/settings/nav-bar.php:30
msgid "Withdraw"
msgstr "Saques"

#: templates/dashboard/settings/reset-password.php:89
msgid "Reset Password"
msgstr "Redefinir senha"

#: templates/dashboard/settings/nav-bar.php:20
msgid "Profile"
msgstr "Perfil"

#: classes/Admin.php:173 classes/Admin.php:537 classes/Utils.php:3037
#: templates/dashboard/notifications/profile-completion.php:22
#: templates/dashboard/settings.php:13
#: templates/dashboard/settings/reset-password.php:14
#: templates/dashboard/settings/social-profile.php:15
#: templates/dashboard/settings/withdraw-settings.php:21
#: views/options/settings.php:21
msgid "Settings"
msgstr "Configurações"

#: templates/dashboard/settings/profile.php:175
#: views/pages/add_new_instructor.php:133 views/pages/instructors.php:351
msgid "Bio"
msgstr "Bio"

#: templates/dashboard/my-profile.php:33
#: templates/dashboard/settings/profile.php:149
#: templates/dashboard/settings/profile.php:151
#: views/pages/add_new_instructor.php:94 views/pages/add_new_instructor.php:100
#: views/pages/instructors.php:303
msgid "Phone Number"
msgstr "Número de telefone"

#: classes/Addons.php:275 templates/dashboard/my-profile.php:32
#: views/pages/instructors.php:123 views/pages/instructors.php:170
#: views/pages/students.php:88
msgid "Email"
msgstr "E-mail"

#: templates/dashboard/my-profile.php:31 views/pages/instructors.php:295
msgid "Username"
msgstr "Usuário"

#: templates/dashboard/instructor/registration.php:68
#: templates/dashboard/instructor/registration.php:71
#: templates/dashboard/my-profile.php:30
#: templates/dashboard/registration.php:70
#: templates/dashboard/registration.php:73
#: templates/dashboard/settings/profile.php:133
#: templates/dashboard/settings/profile.php:135
#: templates/ecommerce/billing-form-fields.php:44
#: templates/ecommerce/billing-form-fields.php:46
#: views/pages/add_new_instructor.php:55 views/pages/add_new_instructor.php:61
#: views/pages/instructors.php:285
msgid "Last Name"
msgstr "Sobrenome"

#: templates/dashboard/instructor/registration.php:58
#: templates/dashboard/instructor/registration.php:61
#: templates/dashboard/my-profile.php:29
#: templates/dashboard/registration.php:60
#: templates/dashboard/registration.php:63
#: templates/dashboard/settings/profile.php:126
#: templates/dashboard/settings/profile.php:128
#: templates/ecommerce/billing-form-fields.php:35
#: templates/ecommerce/billing-form-fields.php:37
#: views/pages/add_new_instructor.php:41 views/pages/add_new_instructor.php:47
#: views/pages/instructors.php:277
msgid "First Name"
msgstr "Nome"

#: templates/dashboard/my-profile.php:28 views/pages/students.php:92
msgid "Registration Date"
msgstr "Data de cadastro"

#: classes/Utils.php:9392 templates/dashboard/my-profile.php:39
msgid "My Profile"
msgstr "Meu perfil"

#: templates/course-embed.php:99 templates/loop/course-author.php:36
#: templates/loop/meta.php:57
msgid "In"
msgstr "Dentro de"

#: templates/course-filter/filters.php:15
#: templates/dashboard/my-courses.php:165 templates/loop/course-in-cart.php:19
#: templates/single/course/course-entry-box.php:255
#: templates/single/course/wc-price-html.php:20 views/pages/course-list.php:313
msgid "Free"
msgstr "Gratuito"

#: templates/single/assignment/content.php:205
msgid "Attachments"
msgstr "Anexo(s)"

#: templates/dashboard/instructor/registration.php:108
#: templates/dashboard/instructor/registration.php:111
#: templates/dashboard/registration.php:108
#: templates/dashboard/registration.php:111
#: templates/dashboard/settings/nav-bar.php:25 templates/login-form.php:63
#: templates/template-part/form-retrieve-password.php:36
#: views/pages/add_new_instructor.php:107
#: views/pages/add_new_instructor.php:113 views/pages/instructors.php:328
msgid "Password"
msgstr "Senha"

#: templates/login-form.php:59
msgid "Username or Email Address"
msgstr "Usuário ou endereço de e-mail"

#: templates/course-filter/course-archive-filter-bar.php:35
msgid "Course Title (z-a)"
msgstr "Título do curso (Z-A)"

#: templates/course-filter/course-archive-filter-bar.php:32
msgid "Course Title (a-z)"
msgstr "Título do curso (A-Z)"

#: templates/course-filter/course-archive-filter-bar.php:29
msgid "Release Date (oldest first)"
msgstr "Data de lançamento (mais antiga primeiro)"

#: templates/course-filter/course-archive-filter-bar.php:26
msgid "Release Date (newest first)"
msgstr "Data de lançamento (mais nova primeiro)"

#: templates/dashboard.php:123
msgid "Become an instructor"
msgstr "Torne-se um instrutor"

#: templates/profile/bio.php:24
msgid "Bio data is empty"
msgstr "Informações da bio está vazia"

#. translators: %s: time difference
#. translators: %s: time
#: helpers/DateTimeHelper.php:199
#: templates/dashboard/question-answer/answers.php:41
#: templates/dashboard/question-answer/answers.php:76
#: templates/single/course/enrolled/announcements.php:39
#: templates/single/course/reviews-loop.php:33 views/pages/answer.php:62
#: views/pages/answer.php:107
msgid "%s ago"
msgstr "%s atrás"

#: templates/dashboard/assignments/review.php:19
msgid "Sorry, but you are looking for something that isn't here."
msgstr "Você está procurando por algo que não está aqui."

#: classes/Tutor.php:1068 tutor.php:79
msgid "Tutor Instructor"
msgstr "Instrutor da plataforma"

#. Author URI of the plugin
#: tutor.php
msgid "https://themeum.com"
msgstr "https://themeum.com"

#. Author of the plugin
#: tutor.php
msgid "Themeum"
msgstr "Themeum"

#. Description of the plugin
#: tutor.php
msgid "Tutor is a complete solution for creating a Learning Management System in WordPress way. It can help you to create small to large scale online education site very conveniently. Power features like report, certificate, course preview, private file sharing make Tutor a robust plugin for any educational institutes."
msgstr "Tutor é uma solução completa para criar um LMS no WordPress. Tutor pode ajudar você a criar desde sites pequenos de cursos até portais educativos de uma forma bem conveniente. Recursos poderosos como relatórios, certificados, prévias de cursos, compartilhamento de arquivos privado tornam o Tutor um plugin robusto pronto para uso por qualquer instituição educacional."

#. Plugin URI of the plugin
#: tutor.php
msgid "https://www.themeum.com/product/tutor-lms/"
msgstr "https://www.themeum.com/product/tutor-lms/"

#. Plugin Name of the plugin
#: tutor.php classes/Admin.php:123 classes/Admin.php:124 classes/Admin.php:588
#: classes/Gutenberg.php:144
msgid "Tutor LMS"
msgstr "Tutor LMS"