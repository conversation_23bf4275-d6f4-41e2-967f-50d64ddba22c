(()=>{"use strict";const e=window.wp.blocks,t=window.wp.i18n,n=window.wp.blockEditor,a=window.wp.components,l=function(e){var a=e.attributes,l=e.setAttributes,r=a.label,o=a.hide_label,c=a.required,i=a.instructions;return React.createElement("div",{className:"acf-field"},React.createElement("div",{className:"acf-label"},React.createElement("label",null,!o&&React.createElement(n.RichText,{tagName:"label",onChange:function(e){return l({label:e})},withoutInteractiveFormatting:!0,placeholder:(0,t.__)("Text Field","acf-frontend-form-element"),value:r}),c&&React.createElement("span",{className:"acf-required"},"*"))),React.createElement("div",{className:"acf-input"},i&&React.createElement(n.RichText,{tagName:"p",className:"description",onChange:function(e){return l({instructions:e})},withoutInteractiveFormatting:!0,value:i}),React.createElement("div",{className:"acf-input-wrap",style:{display:"flex",width:"100%"}},e.children)))},r=window.React;var o="acf-frontend-form-element";const c=function(e){var l=e.attributes,c=e.setAttributes,i=l.label,u=l.hide_label,s=l.required,d=l.instructions,p=function(e){return e.toLowerCase().replace(/[^a-z0-9 _]/g,"").replace(/\s+/g,"_")};return(0,r.useEffect)((function(){"field_key"in l&&!l.field_key&&c({field_key:Math.random().toString(36).substring(2,10)})}),[]),React.createElement(n.InspectorControls,{field_key:"fea-inspector-controls"},React.createElement(a.PanelBody,{title:(0,t.__)("General",o),initialOpen:!0},React.createElement(a.TextControl,{label:(0,t.__)("Label",o),value:i,onChange:function(e){return c({label:e})}}),React.createElement(a.ToggleControl,{label:(0,t.__)("Hide Label",o),checked:u,onChange:function(e){return c({hide_label:e})}}),"name"in l&&React.createElement(a.TextControl,{label:(0,t.__)("Name",o),value:l.name||p(i),onChange:function(e){return c({name:p(e)})}}),"field_key"in l&&React.createElement(a.TextControl,{label:(0,t.__)("Field Key",o),value:l.field_key,readOnly:!0,onChange:function(e){}}),React.createElement(a.TextareaControl,{label:(0,t.__)("Instructions",o),rows:"3",value:d,onChange:function(e){return c({instructions:e})}}),React.createElement(a.ToggleControl,{label:(0,t.__)("Required",o),checked:s,onChange:function(e){return c({required:e})}}),e.children))};var i="acf-frontend-form-element";const u=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":2,"name":"frontend-admin/post-title-field","title":"Post Title Field","description":"Displays a post title field.","category":"frontend-admin","textdomain":"frontend-admin","icon":"list-view","supports":{"align":["wide"]},"usesContext":["frontend-admin/form/form_settings","frontend-admin/form/form_key"],"attributes":{"label":{"type":"string","default":"Post Title"},"hide_label":{"type":"boolean","default":""},"required":{"type":"boolean","default":""},"instructions":{"type":"string","default":""},"default_value":{"type":"string","default":""},"placeholder":{"type":"string","default":""},"prepend":{"type":"string","default":""},"append":{"type":"string","default":""},"maxlength":{"type":"number","default":""},"post_slug":{"type":"boolean","default":""}},"editorScript":"file:../../post-title/index.js"}');(0,e.registerBlockType)(u,{edit:function(e){var r=e.attributes,o=e.setAttributes,u=r.default_value,s=r.placeholder,d=r.prepend,p=r.append,f=r.maxlength,m=(0,n.useBlockProps)();return React.createElement("div",m,React.createElement(c,e,React.createElement(a.TextControl,{type:"text",maxLength:f,label:(0,t.__)("Default Value",i),value:u,onChange:function(e){return o({default_value:e})}}),React.createElement(a.TextControl,{label:(0,t.__)("Placeholder",i),value:s,onChange:function(e){return o({placeholder:e})}}),React.createElement(a.TextControl,{label:(0,t.__)("Prepend",i),value:d,onChange:function(e){return o({prepend:e})}}),React.createElement(a.TextControl,{label:(0,t.__)("Append",i),value:p,onChange:function(e){return o({append:e})}}),React.createElement(a.TextControl,{type:"number",label:(0,t.__)("Character Limit",i),value:f,onChange:function(e){return o({maxlength:e})}}),"post_slug"in r&&React.createElement(a.ToggleControl,{label:(0,t.__)("Use as Post Slug",i),checked:r.post_slug,onChange:function(e){return o({post_slug:e})}})),React.createElement(l,e,d&&React.createElement("span",{className:"acf-input-prepend"},d),React.createElement("input",{type:"text",maxLength:f,placeholder:s,value:u,onChange:function(e){o({default_value:e.target.value})},style:{width:"auto",flexGrow:1}}),p&&React.createElement("span",{className:"acf-input-append"},p)))},save:function(){return null}})})();