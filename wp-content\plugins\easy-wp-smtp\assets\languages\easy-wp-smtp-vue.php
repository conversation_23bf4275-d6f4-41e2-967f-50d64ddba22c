<?php
/* THIS IS A GENERATED FILE. DO NOT EDIT DIRECTLY. */
$generated_i18n_strings = array(
	// Reference: src/plugins/setup-wizard-helper-plugin.js:106
	__( 'Error Message:', 'easy-wp-smtp' ),

	// Reference: src/plugins/setup-wizard-helper-plugin.js:123
	__( 'OK', 'easy-wp-smtp' ),

	// Reference: src/plugins/setup-wizard-helper-plugin.js:128
	__( 'Heads up!', 'easy-wp-smtp' ),

	// Reference: src/plugins/setup-wizard-helper-plugin.js:129
	__( 'Please fill out all the required fields to continue.', 'easy-wp-smtp' ),

	// Reference: src/plugins/setup-wizard-helper-plugin.js:31
	__( 'Settings Updated', 'easy-wp-smtp' ),

	// Reference: src/plugins/setup-wizard-helper-plugin.js:61
	__( 'Could Not Save Changes', 'easy-wp-smtp' ),

	// Reference: src/plugins/setup-wizard-helper-plugin.js:85
	__( 'Return to Mailer Settings', 'easy-wp-smtp' ),

	// Reference: src/plugins/setup-wizard-helper-plugin.js:90
	__( 'Whoops, we found an issue!', 'easy-wp-smtp' ),

	// Reference: src/plugins/setup-wizard-helper-plugin.js:91
	__( 'It looks like something went wrong...', 'easy-wp-smtp' ),

	// Reference: src/plugins/compatibility-plugin.js:14
	__( 'Yikes! WordPress Update Required', 'easy-wp-smtp' ),

	// Reference: src/plugins/compatibility-plugin.js:16
	/* Translators: Current WordPress version. */
	__( 'Easy WP SMTP has detected that your site is running an outdated version of WordPress (%s). Easy WP SMTP requires at least WordPress version 5.2.', 'easy-wp-smtp' ),

	// Reference: src/plugins/compatibility-plugin.js:26
	__( 'Return to Plugin Settings', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/store/actions.js:102
	__( 'It looks like we can\'t load oAuth redirect.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/store/actions.js:11
	__( 'It looks like we can\'t load existing settings.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/store/actions.js:123
	__( 'It looks like we can\'t remove oAuth connection.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/store/actions.js:31
	__( 'It looks like we can\'t retrieve the Amazon SES Identities.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/api/index.js:84
	// Reference: src/modules/settings/store/actions.js:40
	__( 'It looks like we can\'t register the Amazon SES Identity.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/api/index.js:16
	__( 'It looks like we can\'t perform the mailer configuration check.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/api/index.js:37
	__( 'It looks like we can\'t send the feedback.', 'easy-wp-smtp' ),

	// Reference: src/modules/plugins/api/index.js:69
	// Reference: src/modules/settings/api/index.js:272
	// Reference: src/modules/setup-wizard/api/index.js:39
	/* Translators: Error status and error text. */
	__( '%1$s, %2$s', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/api/index.js:276
	// Reference: src/modules/setup-wizard/api/index.js:43
	__( 'You appear to be offline.', 'easy-wp-smtp' ),

	// Reference: src/modules/plugins/api/index.js:25
	__( 'It looks like the plugin installation failed!', 'easy-wp-smtp' ),

	// Reference: src/modules/plugins/api/index.js:35
	__( 'It looks like we can\'t install the plugin.', 'easy-wp-smtp' ),

	// Reference: src/modules/plugins/api/index.js:41
	__( 'You appear to be offline. Plugin not installed.', 'easy-wp-smtp' ),

	// Reference: src/modules/plugins/api/index.js:58
	__( 'Can\'t fetch plugins information.', 'easy-wp-smtp' ),

	// Reference: src/modules/plugins/api/index.js:67
	__( 'It looks like we can\'t fetch plugins information.', 'easy-wp-smtp' ),

	// Reference: src/modules/plugins/api/index.js:73
	__( 'You appear to be offline. Plugin information not retrieved.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/api/index.js:113
	__( 'It looks like we can\'t save the settings.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/api/index.js:119
	__( 'Network error encountered. Settings not saved.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/api/index.js:139
	__( 'It looks like we can\'t import the plugin settings.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/api/index.js:145
	__( 'Network error encountered. SMTP plugin import failed!', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/api/index.js:172
	__( 'It looks like we can\'t load authentication details.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/api/index.js:270
	__( 'It looks like we can\'t remove OAuth connection.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/api/index.js:21
	__( 'It looks like we can\'t load the settings.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/api/index.js:51
	__( 'It looks like we can\'t retrieve Amazon SES Identities.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/api/index.js:57
	__( 'Can\'t retrieve Amazon SES Identities.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/api/index.js:90
	__( 'Can\'t register the Amazon SES Identity', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/Welcome.vue:12
	__( 'Welcome to the Easy WP SMTP Setup Wizard!', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/Welcome.vue:13
	__( 'We’ll guide you through setting up Easy WP SMTP on your site step by step.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/Welcome.vue:14
	__( 'Let\'s Get Started', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/Welcome.vue:15
	__( 'Go back to the Dashboard', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepChooseMailer.vue:12
	__( 'Choose Your SMTP Mailer', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepChooseMailer.vue:175
	__( 'Mailer', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepChooseMailer.vue:185
	__( 'I Understand, Continue', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepChooseMailer.vue:186
	__( 'Choose a Different Mailer', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepChooseMailer.vue:14
	/* Translators: Link to the SMTP Mailer docs page. */
	__( 'Select the mailer you would like to use to send emails. Need more information on our mailers? See our %1$scomplete mailer guide%2$s for additional details.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigureMailer.vue:15
	__( 'Save and Continue', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigureMailer.vue:16
	__( 'Previous Step', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepChooseMailer.vue:17
	__( 'Your mailer is already configured in a Easy WP SMTP constant, so the options below have been disabled. To change your mailer, please edit or remove the <code>EasyWPSMTP_MAILER</code> constant in your <code>wp-config.php</code> file.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepChooseMailer.vue:230
	__( 'is a PRO Feature', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepChooseMailer.vue:27
	__( ' - Send your first 200 emails for free.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepChooseMailer.vue:90
	__( 'Microsoft 365 / Outlook', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Postmark.vue:13
	__( 'Server API Token', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Postmark.vue:14
	__( 'Message Stream ID', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Postmark.vue:16
	/* Translators: Link to the Postmark API settings. */
	__( '%1$sFollow this link%2$s to get a Server API Token for Postmark.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Postmark.vue:18
	/* Translators: Link to the Postmark Message Stream ID settings. */
	__( 'Message Stream ID is <strong>optional</strong>. By default <strong>outbound</strong> (Default Transactional Stream) will be used. More information can be found in our %1$sPostmark documentation%2$s.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/AmazonSES.vue:21
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:16
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:25
	__( 'From Name', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/AmazonSES.vue:22
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:17
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:26
	__( 'Force From Name', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/AmazonSES.vue:23
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:18
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:27
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:27
	__( 'From Email', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/AmazonSES.vue:24
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:22
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:29
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:28
	__( 'Force From Email', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:19
	__( 'If enabled, the From Name setting above will be used for all emails, ignoring values set by other plugins.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:30
	__( 'If enabled, the From Email setting above will be used for all emails, ignoring values set by other plugins.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/AmazonSES.vue:27
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:20
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:29
	__( 'The name that emails are sent from.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/AmazonSES.vue:28
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:21
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:28
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:30
	__( 'The email address that emails are sent from.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Postmark.vue:27
	__( 'Read how to set up Postmark', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/Steps.vue:12
	__( 'Close and exit the Setup Wizard', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/ElasticEmail.vue:13
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendlayer.vue:13
	__( 'API Key', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendinblue.vue:14
	__( 'Sending Domain', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendgrid.vue:16
	/* Translators: Link to the SendGrid API settings. */
	__( '%1$sFollow this link%2$s to get an API Key for SendGrid.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendgrid.vue:19
	/* Translators: italic styling. */
	__( 'To send emails you will need only a %1$sMail Send%2$s access level for this API key.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendgrid.vue:21
	/* Translators: Link to the SendGrid doc page on easywpsmtp.com. */
	__( 'Please input the sending domain/subdomain you configured in your SendGrid dashboard. More information can be found in our %1$sSendGrid documentation%2$s', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendgrid.vue:30
	__( 'Read how to set up SendGrid', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigureMailer.vue:13
	__( 'Configure Mailer Settings', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigureMailer.vue:14
	__( 'Fill out the required settings below to set up this mailer.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepCheckConfiguration.vue:10
	__( 'Checking Mailer Configuration', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepCheckConfiguration.vue:11
	__( 'We\'re running some tests in the background to make sure everything is set up properly.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepCheckConfiguration.vue:12
	__( 'Checking mailer configuration image', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepHelpImprove.vue:14
	__( 'Help Us Improve Easy WP SMTP', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepHelpImprove.vue:15
	__( 'Enter your email address to receive helpful suggestions from Easy WP SMTP. We’ll help you optimize your email deliverability and grow your business.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepHelpImprove.vue:17
	__( 'Skip this Step', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepHelpImprove.vue:19
	__( 'Your Email Address', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepHelpImprove.vue:20
	__( 'Your email address is needed if you want to receive recommendations.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepHelpImprove.vue:21
	__( 'Help make Easy WP SMTP better for everyone', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepHelpImprove.vue:22
	__( 'Yes, count me in', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepHelpImprove.vue:23
	__( 'Allowing us to track usage data enables us to better help you because we know with which WordPress configurations, themes, and plugins to test.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:14
	__( 'Which email features do you want to enable?', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:173
	__( 'The following plugin will be installed for free:', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:15
	__( 'Get more value out of Easy WP SMTP! Select which of the following features you’d like to use, and we’ll enable them for you.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:18
	__( 'Improved Email Deliverability', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:19
	__( 'Send emails from your website successfully and reliably.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:20
	__( 'Email Error Tracking', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:21
	__( 'Monitor email delivery issues so you can easily resolve them.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:22
	__( 'Smart Contact Form', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:23
	__( 'Install the WPForms plugin and create beautiful contact forms with just a few clicks.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:24
	__( 'Detailed Email Logs', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:25
	__( 'Store information from all emails sent from your site.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:28
	__( 'Complete Email Reports', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:29
	__( 'View your emails’ delivery status, open & click tracking, and deliverability charts.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:30
	__( 'Weekly Email Summary', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:31
	__( 'Receive a weekly email delivery report in your inbox.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationFailure.vue:11
	__( 'Whoops, looks like something isn’t configured quite right.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationFailure.vue:12
	__( 'We tried sending a test email, but we’re not able to do so. For more details about the issue we’ve found, as well as steps for resolving it, please begin troubleshooting.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationFailure.vue:13
	__( 'Start Troubleshooting', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationFailure.vue:14
	__( 'Send us Feedback', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationFailure.vue:15
	__( 'Finish Setup', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigureEmailLogs.vue:13
	__( 'Configure Email Logs', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigureEmailLogs.vue:14
	__( 'You’ve chosen to enable email logging. Please select which additional email logging features you would like to use.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigureEmailLogs.vue:17
	__( 'Store the content for all sent emails', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigureEmailLogs.vue:18
	__( 'Email content may include sensitive information, such as plain text passwords. For security purposes, consider carefully whether to enable this option. All email content will be stored in your site\'s database. To resend emails from our Email Log, this option must be enabled.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigureEmailLogs.vue:19
	__( 'Save file attachments sent from WordPress', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigureEmailLogs.vue:20
	__( 'When enabled, all sent attachments will be saved to your WordPress Uploads folder. For sites that send a high volume of unique large attachments, this option could result in a disk space issue.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigureEmailLogs.vue:21
	__( 'Track when an email is opened', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigureEmailLogs.vue:22
	__( 'When enabled, the email log will note whether or not an email has been opened.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigureEmailLogs.vue:23
	__( 'Track when a link in an email is clicked', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigureEmailLogs.vue:24
	__( 'When enabled, the email log will note whether or not a link has been clicked in the specified email.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/AmazonSES.vue:16
	__( 'Access Key ID', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/AmazonSES.vue:17
	__( 'Secret Access Key', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/AmazonSES.vue:18
	__( 'Region', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/AmazonSES.vue:19
	__( 'SES Identities', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/AmazonSES.vue:20
	__( 'Please select the Amazon SES API region which is the closest to where your website is hosted. This can help to decrease network latency between your site and Amazon SES, which will speed up email sending.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/AmazonSES.vue:25
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:32
	__( 'If enabled, your specified From Name will be used for all outgoing emails, regardless of values set by other plugins.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/AmazonSES.vue:26
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:23
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:33
	__( 'If enabled, your specified From Email Address will be used for all outgoing emails, regardless of values set by other plugins.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/AmazonSES.vue:29
	__( 'Read how to set up Amazon SES', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/AmazonSES.vue:30
	__( 'Amazon SES requires an SSL certificate, and so is not currently compatible with your site. Please contact your host to request a SSL certificate, or check out ', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/AmazonSES.vue:31
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:25
	__( 'WPBeginner\'s tutorial on how to set up SSL', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/AmazonSES.vue:32
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:26
	__( 'If you\'d prefer not to set up SSL, or need an SMTP solution in the meantime, please go back and select a different mailer option.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:17
	__( 'Application ID', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:18
	__( 'Application Password', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:19
	__( 'Redirect URI', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:15
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:20
	__( 'Authorization', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:21
	__( 'Read how to set up Microsoft Outlook / 365', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:24
	__( 'Outlook / 365 requires an SSL certificate, and so is not currently compatible with your site. Please contact your host to request a SSL certificate, or check out ', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendlayer.vue:15
	/* Translators: Link to the SendLayer API settings. */
	__( '%1$sFollow this link%2$s to get an API Key for SendLayer.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendlayer.vue:24
	__( 'Get Started with SendLayer', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendlayer.vue:25
	__( 'Read how to set up SendLayer', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:103
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:138
	__( 'Verification Error!', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:24
	__( 'Read how to set up the Gmail mailer', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:33
	__( 'Gmail mailer requires a valid Easy WP SMTP Pro license. Please activate your license key.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:34
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:41
	__( 'Paste your license key here', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:35
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:42
	__( 'License key input', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:36
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:43
	__( 'Verify License Key', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:37
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:44
	__( 'The License Key format is incorrect. Please enter a valid key and try again.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:93
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:128
	__( 'Successful Verification!', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Gmail.vue:94
	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:129
	__( 'Now you can continue mailer configuration.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SmtpCom.vue:14
	__( 'Sender Name', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SmtpCom.vue:16
	/* Translators: Link to the SMTP.com API settings. */
	__( '%1$sFollow this link%2$s to get an API Key for SMTP.com.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SmtpCom.vue:18
	/* Translators: Link to the SMTP.com Senders/Channel settings. */
	__( '%1$sFollow this link%2$s to get a Sender Name for SMTP.com.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SmtpCom.vue:27
	__( 'Get Started with SMTP.com', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SmtpCom.vue:28
	__( 'Read how to set up SMTP.com', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendinblue.vue:29
	__( 'Transparency and Disclosure', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SmtpCom.vue:31
	__( 'We believe in full transparency. The SMTP.com links above are tracking links as part of our partnership with SMTP (j2 Global). We can recommend just about any SMTP service, but we only recommend products that we believe will add value to our users.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:12
	__( 'Congrats, you’ve successfully set up Easy WP SMTP.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:13
	__( 'Send a Test Email', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:17
	/* Translators: Different bold styles and discount value (%5$s). */
	__( 'Star icon', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:32
	__( 'Thanks for the feedback!', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:35
	/* Translators: %1$s and %2$s are HTML bold tags; %3$s is a new line HTML tag; %4$s are 5 golden star icons in HTML. */
	__( 'Help us spread the word %1$sby giving Easy WP SMTP a 5-star rating %3$s(%4$s) on WordPress.org%2$s. Thanks for your support and we look forward to bringing you more awesome features.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:40
	__( 'Rate on WordPress.org', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:53
	__( 'What could we do to improve?', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:54
	__( 'We\'re sorry things didn\'t go smoothly for you, and want to keep improving. Please let us know any specific parts of this process that you think could be better. We really appreciate any details you\'re willing to share!', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:58
	__( 'Yes, I give Easy WP SMTP permission to contact me for any follow up questions.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:66
	__( 'Submit Feedback', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:90
	__( 'How was your Easy WP SMTP setup experience?', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepConfigurationSuccess.vue:91
	__( 'Our goal is to make your SMTP setup as simple and straightforward as possible. We\'d love to know how this process went for you!', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:13
	__( 'Enter your Easy WP SMTP License Key', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:15
	/* Translators: %1$s and %2$s are bold tags. */
	__( 'You\'re using Easy WP SMTP Lite - no license key required. Enjoy!', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:16
	__( 'Continue', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:167
	__( 'Would you like to purchase the following features now?', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:168
	__( 'These features are available as part of Easy WP SMTP Pro plan.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:183
	__( 'Purchase Now', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:184
	__( 'I\'ll do it later', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:20
	/* Translators: Link to the EasyWPSMTP.com pricing page. */
	__( 'To unlock the following features, %1$sUpgrade to Pro%2$s and enter your license key below.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:22
	__( 'Enhanced Weekly Email Summary', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:24
	/* Translators: bold HTML tags. */
	__( 'Already purchased %1$sEasy WP SMTP Pro%2$s? Enter your license key below!', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:25
	__( 'Add your license key here to access plugin updates and support.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:26
	__( 'Connect', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:28
	__( 'Your license was successfully verified! You are ready for the next step.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:34
	__( 'Pro badge', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:101
	__( 'Successful Upgrade!', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepLicense.vue:101
	__( 'Upgrade Failed!', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:15
	__( 'SMTP Host', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:16
	__( 'Encryption', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:17
	__( 'SMTP Port', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:18
	__( 'Auto TLS', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:19
	__( 'Enable Auto TLS', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:20
	__( 'By default, TLS encryption is automatically used if the server supports it (recommended). In some cases, due to server misconfigurations, this can cause issues and may need to be disabled.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:21
	__( 'Authentication', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:22
	__( 'Enable Authentication', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:23
	__( 'SMTP Username', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:24
	__( 'SMTP Password', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:31
	__( 'For most servers TLS is the recommended option. If your SMTP provider offers both SSL and TLS options, we recommend using TLS.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:37
	__( 'None', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:42
	__( 'SSL', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Smtp.vue:47
	__( 'TLS', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendinblue.vue:16
	/* Translators: Link to the Brevo API settings. */
	__( '%1$sFollow this link%2$s to get an API Key for Brevo.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendinblue.vue:18
	/* Translators: Link to the Brevo doc page on easywpsmtp.com. */
	__( 'Please input the sending domain/subdomain you configured in your Brevo dashboard. More information can be found in our %1$sBrevo documentation%2$s', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendinblue.vue:27
	__( 'Get Started with Brevo', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendinblue.vue:28
	__( 'Read how to set up Brevo', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Sendinblue.vue:31
	__( 'We believe in full transparency. The Brevo links above are tracking links as part of our partnership with Brevo. We can recommend just about any SMTP service, but we only recommend products that we believe will add value to our users.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Mailgun.vue:14
	__( 'Mailgun API Key', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Mailgun.vue:15
	__( 'Domain Name', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Mailgun.vue:18
	/* Translators: Link to the Mailgun API settings. */
	__( '%1$sFollow this link%2$s to get a Mailgun API Key. Generate a key in the "Mailgun API Keys" section.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Mailgun.vue:20
	/* Translators: Link to the Mailgun Domain settings. */
	__( '%1$sFollow this link%2$s to get a Domain Name from Mailgun.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Mailgun.vue:22
	/* Translators: Link to the Mailgun documentation. */
	__( 'Define which endpoint you want to use for sending messages. If you are operating under EU laws, you may be required to use EU region. %1$sMore information%2$s on Mailgun.com.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Mailgun.vue:31
	__( 'Read how to set up Mailgun', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SparkPost.vue:31
	__( 'US', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SparkPost.vue:35
	__( 'EU', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/TheWizardHeader.vue:8
	__( 'Easy WP SMTP logo', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/TheWizardStepCounter.vue:11
	/* Translators: %1$s - the number of current step, %2$s - number of all steps. */
	__( 'Step %1$s of %2$s', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsInputRadiosWithIcons.vue:14
	__( 'Recommended', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsInputLongCheckbox.vue:20
	// Reference: src/modules/settings/components/input/SettingsInputRadio.vue:33
	// Reference: src/modules/settings/components/input/SettingsInputSelect.vue:38
	__( 'This setting is already configured with the Easy WP SMTP constant. To change it, please edit or remove the <code></code> constant in your <code>wp-config.php</code> file.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsInputText.vue:36
	__( 'Copy input value', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsInputText.vue:37
	__( 'Copied!', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsInputText.vue:63
	__( 'The value entered does not match the required format', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:23
	__( 'Please enter a domain', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:24
	__( 'Please enter a valid email address', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:28
	__( 'Enter the domain name to verify it on Amazon SES and generate the required DNS CNAME records.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:29
	__( 'Enter a valid email address. A verification email will be sent to the email address you entered.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:33
	/* Translators: Email address. */
	__( 'Please check the inbox of <b>%s</b> for a confirmation email.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:36
	__( 'Verify Email', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:41
	__( 'No registered domains or emails.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:42
	__( 'You will not be able to send emails until you verify at least one domain or email address for the selected Amazon SES Region.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:43
	__( 'View DNS', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:44
	__( 'Resend', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:45
	__( 'Here are the domains and email addresses that have been verified and can be used as the From Email.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:46
	__( 'Verify SES Identity', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:47
	__( 'Add New SES Identity', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:48
	__( 'Name', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:49
	__( 'Value', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:51
	/* Translators: Link to Amazon SES documentation. */
	__( 'Please add these CNAME records to your domain\'s DNS settings. For information on how to add CNAME DNS records, please refer to the %1$sAmazon SES documentation%2$s.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:56
	__( 'Verify Domain', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsAmazonSESIdentities.vue:60
	__( 'Verify Email Address', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:111
	__( 'There was an error while processing the authentication request. The state key is invalid. Please try again.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:117
	__( 'There was an error while processing the authentication request.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:121
	__( 'There was an error while processing the authentication request. Please recheck your Client ID and Client Secret and try again.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:128
	__( 'There was an error while processing the authentication request. The nonce is invalid. Please try again.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:132
	__( 'There was an error while processing the authentication request. The authorization code is missing. Please try again.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:135
	__( 'You have successfully connected your site with your Gmail account. This site will now send emails via your Gmail account.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:138
	__( 'You have successfully linked the current site with your Microsoft API project. Now you can start sending emails through Outlook.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:150
	__( 'Successful Authorization', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:150
	__( 'Authorization Error!', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:22
	/* Translators: name of the oAuth provider (Google, Microsoft, ...). */
	__( 'Connect to %s', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:23
	__( 'Sign in with Google', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:25
	__( 'Before continuing, you\'ll need to allow this plugin to send emails using your %s account.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:26
	__( 'Remove OAuth Connection', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:28
	/* Translators: link to the Google documentation page. */
	__( 'If you want to use a different From Email address you can setup a Google email alias. %1$sFollow these instructions%2$s, then input the alias address in the From Email section below.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:30
	/* Translators: name of the oAuth provider (Google, Microsoft, ...). */
	__( 'Removing this OAuth connection will give you the ability to redo the OAuth connection or connect to different %s account.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:31
	__( 'Connected as', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsInputNumber.vue:38
	/* Translators: Minimum and maximum number that can be used. */
	__( 'Please enter a value between %1$s and %2$s', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsInputNumber.vue:39
	__( 'Value has to be a round number', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/misc/SpinLoader.vue:19
	__( 'Loading', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SparkPost.vue:17
	/* Translators: Link to the SparkPost documentation. */
	__( 'Select your SparkPost account region. %1$sMore information%2$s on SparkPost.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SparkPost.vue:26
	__( 'Read how to set up SparkPost', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SparkPost.vue:55
	/* Translators: Link to the SparkPost Account API section. */
	__( '%1$sFollow this link%2$s to get an API Key for SparkPost.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SMTP2GO.vue:14
	/* Translators: Link to the SMTP2GO API settings. */
	__( 'Generate an API key on the Sending → API Keys page in your %1$scontrol panel%2$s.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/SMTP2GO.vue:23
	__( 'Read how to set up SMTP2GO', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:26
	__( 'Instant Email Alerts', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepPluginFeatures.vue:27
	__( 'Get notifications via email, SMS, Slack, or webhook when emails fail to send.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Zoho.vue:17
	__( 'The data center location used by your Zoho account.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Zoho.vue:18
	__( 'Client ID', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Zoho.vue:19
	__( 'Client Secret', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Zoho.vue:26
	__( 'Read how to set up Zoho Mail', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:114
	__( 'There was an error while processing the authentication request. Please try again.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:124
	__( 'There was an error while processing the authentication request. Please recheck your Region, Client ID and Client Secret and try again.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:144
	__( 'You have successfully linked the current site with your Zoho Mail API project. Now you can start sending emails through Zoho Mail.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Mailjet.vue:15
	/* Translators: Link to the SMTP2GO API settings. */
	__( 'Follow this link to get the API key from Mailjet: %1$sAPI Key Management%2$s.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Mailjet.vue:16
	__( 'Secret Key', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Mailjet.vue:17
	__( 'Follow this link to get the Secret key from Mailjet: %1$sAPI Key Management%2$s.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Mailjet.vue:26
	__( 'Read how to set up Mailjet', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/WizardStepChooseMailer.vue:17
	__( 'Your mailer is already configured in a Easy WP SMTP constant, so the options below have been disabled. To change your mailer, please edit or remove the <code>EASY_WP_SMTP_MAILER</code> constant in your <code>wp-config.php</code> file.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/ElasticEmail.vue:15
	/* Translators: Link to the Elastic Email API settings. */
	__( '%1$sFollow this link%2$s to get an API Key for Elastic Email.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/ElasticEmail.vue:25
	__( 'Read how to set up Elastic Email', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:15
	__( 'One-Click Setup', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:16
	__( 'Provides a quick and easy way to connect to Outlook that doesn\'t require creating your own app.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:22
	__( 'Enabled', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:23
	__( 'Disabled', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/Outlook.vue:40
	__( 'One-Click Setup for Microsoft Outlook requires an active license. Verify your license to proceed with this One-Click Setup, please.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:141
	__( 'You have successfully connected your site with your Outlook account. Now you can start sending emails through Outlook.', 'easy-wp-smtp' ),

	// Reference: src/modules/settings/components/input/SettingsOAuthConnection.vue:24
	__( 'Sign in with Outlook', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/MailerSend.vue:14
	__( 'Pro Plan Features', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/MailerSend.vue:16
	/* Translators: Link to the MailerSend API settings. */
	__( '%1$sFollow this link%2$s to get an API Key for MailerSend.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/MailerSend.vue:17
	__( 'Enable if you have a Pro plan or higher to use advanced features like custom headers.', 'easy-wp-smtp' ),

	// Reference: src/modules/setup-wizard/components/steps/configure-mailer/MailerSend.vue:26
	__( 'Read how to set up MailerSend', 'easy-wp-smtp' )
);
/* THIS IS THE END OF THE GENERATED FILE */
