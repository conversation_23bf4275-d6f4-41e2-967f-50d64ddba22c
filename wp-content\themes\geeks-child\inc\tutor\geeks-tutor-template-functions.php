<?php
/**
 * Dashboard Navigation
 */
function geeks_tutor_dashboard_navigation( $dashboard_page_slug, $dashboard_page_name ) {
	?>

	<nav class="navbar navbar-expand-md navbar-light shadow-sm mb-4 mb-lg-0 sidenav">
		
		<a href="#" class="d-xl-none d-lg-none d-md-none text-inherit fw-bold"><?php esc_html_e( 'Menu', 'geeks' ); ?></a>

		<button class="navbar-toggler d-md-none icon-shape icon-sm rounded bg-primary text-light" type="button" data-bs-toggle="collapse" data-bs-target="#sidenav" aria-controls="sidenav" aria-expanded="false" aria-label="<?php esc_attr_e( 'Toggle navigation', 'geeks' ); ?>">
			<span class="fe fe-menu"></span>
		</button>
		
		<div class="collapse navbar-collapse" id="sidenav">
			<div class="navbar-nav flex-column">
				<span class="navbar-header"><?php esc_html_e( 'Dashboard', 'geeks' ); ?></span>
				<ul class="tutor-dashboard-permalinks list-unstyled ms-n2 mb-0">
				<?php
					$dashboard_pages = tutils()->tutor_dashboard_nav_ui_items();
					// get reviews settings value.
					$disable = !get_tutor_option('enable_course_review');
					foreach ( $dashboard_pages as $dashboard_key => $dashboard_page ) {

						if ($disable && 'reviews' === $dashboard_key) {
							continue;
						}
						$menu_title = $dashboard_page;
						$menu_link  = tutils()->get_tutor_dashboard_page_permalink( $dashboard_key );
						$separator  = false;
						$icon       = isset( $dashboard_page['icon'] ) ? $dashboard_page['icon'] : geeks_tutor_get_nav_item_icon( $dashboard_key );
						
						if ( is_array( $dashboard_page ) ) {
							$menu_title = tutils()->array_get( 'title', $dashboard_page );
							
							if($menu_title == 'Order History')
							    continue;
							
							// Add new menu item property "url" for custom link
							if ( isset( $dashboard_page['url'] ) ) {
								$menu_link = $dashboard_page['url'];
							}
							if ( isset( $dashboard_page['type'] ) && $dashboard_page['type'] == 'separator' ) {
								$separator = true;
							}
						}
						if ( $separator ) {
							if ( $menu_title ) {
								?><li class="mt-4 tutor-dashboard-menu-divider-header">
									<span class="navbar-header"><?php echo esc_html( $menu_title ); ?></span>
								</li><?php
							}
						} else {
							$li_class = "tutor-dashboard-menu-{$dashboard_key} nav-item";
							
							if ( 'index' === $dashboard_key ) {
								$dashboard_key = '';
							}

							if ( $dashboard_key === $dashboard_page_name ) {
								$li_class .= ' active';
							}

							?><li class="<?php echo esc_attr( $li_class ); ?>">
								<a href="<?php echo esc_url( $menu_link ); ?>" class="nav-link">
									<?php if ( ! empty( $icon ) ) : ?>
										<i class="<?php echo esc_attr( $icon ); ?> nav-icon"></i>
									<?php endif; ?>
									<?php echo esc_html( $menu_title ); ?>
								</a>
							</li><?php
						}
					}
				?>
				<!-- </ul> -->
			</div>
		</div>
	</nav>
	<?php
}