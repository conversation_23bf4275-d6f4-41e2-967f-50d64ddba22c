=== All-In-One Security (AIOS) – Security and Firewall ===
Contributors: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ips and Tricks HQ, w<PERSON>ol<PERSON>s, <PERSON>, <PERSON><PERSON><PERSON>, mbrsolution
Donate link: https://david.dw-perspective.org.uk/donate
Tags: security, malware scanning, two factor authentication, firewall, login security
Requires PHP: 5.6
Requires at least: 5.0
Tested up to: 6.8
Stable tag: 5.4.1
License: GPLv3 or later

Protect your website investment with All-In-One Security (AIOS) – a comprehensive and easy to use security plugin designed especially for WordPress. Featuring login security tools, a cutting-edge firewall and much more.

== Privacy Policy ==
This plugin may collect IP addresses for security reasons such as mitigating brute force login threats and malicious activity.
The collected information is stored on your server. No information is transmitted to third parties or remote server locations.


== Description ==

### THE TOP RATED WORDPRESS SECURITY AND FIREWALL PLUGIN

All-in-One Security (AIOS) is a security plugin designed especially for WordPress, now brought to you from the team at UpdraftPlus.
Customers love All-In-One Security because it’s easy to use, and it does a whole lot for free.

All-In-One Security gives you **Login Security Tools**, to keep bots at bay and protect your website from brute force attacks.

Our **Web Application Firewall** gives you automatic protection from security threats.

**Content Protection Features**  protect what you’ve worked so hard to build; All-In-One Security eliminates comment spam and prevents other websites from stealing your content with features like iFrame prevention and copywriting protection.

https://www.youtube.com/watch?v=CJvCTlVtazA

#### Still on the fence?
* We’re currently the **Only WordPress Security Plugin with a 5 Star user rating** across more than 1 million installs.
* Our security team maintains a list of known exploits, actively building protections against them and releasing these as new firewall rules to free and paying customers, at the same time.
* We’re already the world’s number one for backups, so you know you can trust us with the security of your website too.

#### LOGIN SECURITY FEATURE SUITE
Protect against brute-force attacks and keep bots at bay. All-In-One Security takes WordPress’ default login security features to a whole new level.

* **Supports best practice:** All-In-One Security detects if an account has the default ‘admin’ username or if a user has identical login and display names, prompting the user to change this in support of better security practices.
* **Hide login page from bots:** Configure a custom URL for the WordPress ‘Admin’ login page, making it harder for bots to find.
* **Change default `wp_` prefix:** Hackers use automated code to attack websites like yours. Make life harder for them and protect your site with this simple but effective AIOS security feature.
* **Login lockout:** External users making multiple login attempts can be locked out for a configured period of time. You can also lockout users with invalid usernames. See a list of all locked out users and unlock with one click.
* **Reporting:**  All-In-One Security provides a wealth of information about website users. View activity by username, IP address, login and logout dates and times. See a list of users currently logged in, and a list of all failed login attempts.
* **Force logouts:** Ensure users don’t stay logged in indefinitely. With All-In-One Security you can force logouts for all users after a configurable amount of time.
* **Robot verification:** For additional security and to prevent spam registrations, implement Cloudflare Turnstile, Google reCAPTCHA, plain maths CAPTCHA or a honeypot to registration pages, or enable manual approval of user accounts instead.
* **Stops user enumeration:** Prevent external users and bots from fetching user information via author permalink.
* **Two-factor authentication:** All-In-One Security TFA supports Google Authenticator, Microsoft Authenticator, Authy and many more.
* **Password strength tool:** Calculates how long it would take for your password to be cracked through a brute force attack.
* **General visitor lockout** Put your site into “maintenance mode” and lock down the front-end to all visitors. This can be useful while doing back end tasks, like performing site upgrades or investigating security threats.
* **WordPress Salts Security Feature Extended:** All-In-One Security adds 64 new characters to WordPress Salts and changes them weekly, making it even more challenging for hackers to crack your users’ WordPress passwords. 

#### FIREWALL & FILE PROTECTION SECURITY SUITE
A Web Application Firewall (WAF) is your website’s first line of defence, protecting your site by monitoring traffic and blocking malicious requests.

* **Progressively activate firewall settings:** These range from basic, intermediate and advanced.
* **Automatic protection from the latest threats:** Our team maintains a list of known exploits, actively building protections against them which are then released as new firewall rules to free and paying customers.
* **6G blacklist:** All-In-One Security incorporates ‘6G Blacklist’ firewall rules, protecting your site against a known list of malicious URL requests, bots, spam referrers and other attacks (courtesy of Perishable Press).
* **Protect against fake Google bots:** Bots presenting as Google crawlers can steal your content and litter your webpage with comment spam. Protect against it with the All-In-One Security Web Application Firewall.
* **Blacklist functionality:** Ban users by IP address, IP address range or by specifying user agents.
* **Prevent DDOS attacks:** Prevent malicious users from performing DDOS attacks through a known vulnerability in WordPress XML-RPC pingback functionality.
* **Prevent image hotlinking:** Protect server bandwidth and your website’s content by preventing other sites from using your imagery via hotlinking.
* **Cross site scripting (XSS) protection:** All-In-One Security prevents attackers from injecting malicious script into your website via a special cookie.
* **File change detection:** Security scanners alert you to file changes in your WordPress system, so you can see if a change is legitimate or suspicious, and investigate as appropriate.
* **Disable PHP file editing:** Protect your PHP code by disabling the ability to edit files in the WordPress administration area.
* **Permission setting alerts:** Identify files or folders where the permission settings are not secure and correct with one-click.
* **Ability to create custom rules:** Advanced users can add custom rules to block access to various resources on your site.
* **Access prevention:** Prevent external users from accessing the readme.html, license.txt and wp-config-sample.php files of your WordPress site.

#### CONTENT PROTECTION SECURITY SUITE
Eliminate spam, protect your WordPress content, and your search engine rankings with these important security features from All-In-One-Security.

* **Comment SPAM prevention :** Webpages littered with spam comments damage your brand, effect the user experience and impact SEO.
All-In-One Security stops SPAM at the source by preventing comments that originate from other domains. AIOS automatically and permanently blocks spammers’ IP addresses. Site owners can use Cloudflare Turnstile or Google reCAPTCHA to reduce comment spam and block malicious users with just one click.
* **iFrame protection:** Preventing other websites from reproducing your content via an ‘iFrame’ is a useful security feature that protects your intellectual property and your website visitors.
* **Copywriting protection:** Stop users from stealing your content by disabling the right-click, select and copy text function.
* **Disable RSS and Atom Feeds:** RSS and Atom Feeds can be used by bots to ‘scrape’ your website content and present it as their own. This feature prevents that by disabling RSS and Atom Feeds on your website.

#### LATEST AND GENERAL SECURITY FEATURES
* **Audit Log:** The All-In-One Security audit log gives Admins a view of events taking place on their WordPress website. They can see if anything strange is happening and detect security risks. For example, you can see if a plugin or theme has been added, removed, updated, activated or deactivated without your knowledge or consent.

#### INTERESTED IN AIOS PREMIUM?
For even greater protections, consider All-In-One Security (AIOS) Premium. It’s one of the most cost-effective and comprehensive WordPress Security plugins on the market and extends the powers of ‘Free’ with:

#### MALWARE SCANNING (Premium only)
Finding out by accident that your website’s security has been compromised due to malware is too late.  

Malware can have a dramatic effect on search rankings. It can slow your site down, access customer data, send unsolicited emails, change your content or prevent users from accessing it.

* **Alerts you to blacklisting:** Search engines can very quickly blacklist a site hacked with malicious code. All-In-One Security Premium monitors your site's status daily and alerts you if you've been blacklisted.
* **Notification if something is amiss:** We’ll notify you of any malware issues within 24 hours so you can take action, before it’s too late.
* **Response time monitoring:** You’ll know immediately if website response time is negatively affected.
* **Up-time monitoring:** All-In-One Security checks website uptime every 5 minutes. We’ll notify you if your site/server goes down.
* **Flexible assignment:** Register and remove WordPress sites from security scanning at any time.
* **Security Reports:** Security Reports are available via the ‘My Account’ page and directly via email.

#### FLEXIBLE TWO-FACTOR AUTHENTICATION (PREMIUM ONLY)
TFA is available in our free packages. All-In-One Security Premium affords whole new levels of control over how TFA is implemented.

* **Role specific configuration:** Make TFA compulsory for certain roles, e.g. for admin and editor roles.
* **Require TFA after set time period:** For example, you could require all admins to have TFA once their accounts are a week old.
* **Trusted Devices:** Ask for TFA after a chosen number of days for trusted devices instead of on every login.
* **Anti-bot Protection:** Option to hide the existence of forms on WooCommerce login pages unless JavaScript is active.
* **Customise design layout:** Customise the design of TFA so that it aligns with your existing web design.
* **Emergency Codes:** Generate a one-time use emergency code to allow access if your device is lost.
* **Multisite Compatible:** Compatible with WordPress multisite networks and sub-sites.
* **Support for login forms:** Support for WooCommerce and Affiliates-WP, Elementor Pro, bbPress and all third-party login forms without any further coding needed. Also compatible with ‘Theme my Login’

#### SMART 404 BLOCKING (PREMIUM ONLY)
404 errors occur when someone legitimately mistypes a URL, but they’re also generated by hackers searching for security weaknesses in your site.

* **Block bots producing 404s:** All-In-One Security Premium automatically and permanently blocks IP addresses of bots and hackers based on how many 404 errors they generate.
* **Reporting:** Handy charts keep you informed of how many 404s have occurred and which IP address or country is producing them

#### COUNTRY BLOCKING (PREMIUM ONLY)
Most security attacks come from a handful of countries and so it’s possible to prevent most attacks with our country blocking tool.
* **Block traffic based on country of origin:** All-In-One Security Premium utilises an IP database that promises 99.5% accuracy.
* **Block traffic to specific pages:** Block access to your whole WordPress site or on a page-by-page basis.
* **Whitelist some users from blocked countries:** Whitelist IP addresses or IP ranges even if they are part of a blocked country.

#### PREMIUM SUPPORT
* **Unlimited support:** Personalised, email support as and when you need it.
* **Fastest response times:** We offer a response time of three days. 99% of All-In-One Security Premium customers receive a response to
their enquiry within 24 hours.

#### Plugin Support
* If you have a question or problem with the All-In-One Security plugin, post it on the support forum and we will help you. Premium customers can log queries directly with the team via https://teamupdraft.com/all-in-one-security/

= Developers =
* If you are a developer and you need some extra hooks or filters for this plugin then let us know.

= Translations =
* All-In-One Security plugin can be translated to any language.

Currently available translations:

- English
- German
- Spanish
- French
- Hungarian
- Italian
- Swedish
- Russian
- Chinese
- Portuguese (Brazil)
- Persian

== Frequently Asked Questions ==

= How is All-In-One Security (AIOS) supported? =
Customers of ‘Free’ AIOS can get support from this very webpage. Select ‘Support’ from the tabs above and post a topic. We aim to respond to all support requests within 24 hours during the working week.

= Is All-In-One Security compatible with other plugins? =
Yes. AIOS works smoothly with most popular WordPress plugins.

= Is All-in-One-Security regularly updated? =
Yes. WordPress Security is something that evolves over time. We update AIOS with new security features (and fixes if required) on a regular basis so you can be assured that your site will keep benefitting from new security protection techniques for as long as you need them.

= Will All-In-One Security slow down my website? =
No.

= Should I install All-In-One Security for free or should I purchase AIOS Premium? =
The decision is yours to make. ‘Free’ AIOS incorporates a web application firewall, comprehensive login security tools including two-factor authentication and all the latest recommended WordPress security practices and techniques.
But if your WordPress site is a business website, if it showcases what you do, or who you are, we generally recommend AIOS Premium. Prices start from as little as $70 for the year.

= What are the additional features of All-In-One Security Premium? =
AIOS Premium scans your WordPress website for malware whilst also monitoring your site's response time and uptime, notifying you of any issues within 24 hours, AIOS Premium customers also benefit from hands-on ticketed support via email (rather than via WP Support forums).
Additional security tools include Country Blocking, Smart 404 Error Blocking and Advanced Two Factor Authentication.
More information is available from our [All-In-One Security website](https://teamupdraft.com/all-in-one-security/)

= How do I get started with All-In-One Security Premium? =
In the web shop, purchase your preferred subscription. After completing the purchase, you will be emailed a link to download the plugin. You can also access the link through your "My Account" page.
After downloading the zip file, install and activate the plugin through WP Admin->Plugins->Add New->Upload Plugin.
The premium extends the free version. Therefore you should keep the free version installed and active. You will also be prompted to enter your AIOS username and password to connect your site to licenses. This will allow the plugin to receive updates.

= Do I need to have the free version before downloading Premium? =
Yes, you need to have the free version of the plugin installed and activated before installing Premium. Premium plugin is an add-on that requires the free version to be present.

= Does All-In-One Security work with multi-site network installations? =
Yes, AIOS Premium is compatible with WordPress multisites. For multisite networks, the protection will apply to the network as a whole, and the dashboard and options will be available on the main site of the WordPress multisite.

= Can a WordPress security plugin stop all attacks on my site? =
There is no 100% guarantee that a security plugin will be able to protect against all attacks, as there is always the possibility of unknown WordPress vulnerabilities or other unexpected factors, and attackers are always seeking to develop new ways around protections. However, All-In-One Security gives good protection against known attack methods, and is under continuous development to monitor and improve protections.

= Does All-In-One Security work on all servers and hosts? =
AIOS should be compatible with most hosts, unless the host has specifically restricted the use of security plugins. Similarly, certain features may not work on some servers, especially Windows/IIS platforms. Features that use the ‘.htaccess’ file will not apply on a Windows IIS server or NGINX server (but development is ongoing to port those protections to all servers).

= Can I cover my subdomains and test sites with a licence for AIOS Premium? =
Development and test sites require their own licence if updates to the plugin are needed.
However, these sites can be disconnected from the licence when they have served their purpose. You can disconnect the licence via the site's WP Admin->Plugins page, and it will be available to be reassigned to a different site.

= Is the All In One Security & Firewall Plugin GDPR and other privacy law compliant? =
Please read more about GDPR compliance here: https://teamupdraft.com/privacy/ .

== Installation ==

To begin making your WordPress site more secure:

1. Upload the 'all-in-one-wp-security.zip' file from the Plugins->Add New page in the WordPress administration panel.
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Go to Settings menu under 'WP Security' and start activating the security features of the plugin.

== Usage ==

Go to the settings menu after you activate the plugin and follow the instructions.

== Screenshots ==

1. Features list.

== Changelog ==

= 5.4.1 - 21/May/2025 =

* FIX: Call to undefined function AIOWPS\Firewall\sanitize_text_field() fatal error solved. 
* FIX: Resolved an issue where some information in the debugging report email was inconsistent with the information shown at Dashboard > Debugging
* FIX: Fixed a “call to undefined function wp_strip_tags” error in wp-security-user-login.php
* FIX: Resolved an issue where raw HTML was displaying in the info box under User Security > User Accounts > User Display Name
* FIX: Renamed the login page when it was exposed via auth_redirect by other plugins (e.g., Gravity Forms preview)
* FIX: Fixed an issue where the password reset functionality did not work with the renamed login page feature
* FIX: Resolved missing translations on the login page after enabling the “Rename login page” feature
* FIX: Updated the custom login page layout to match the new default WordPress login page design
* FIX: Fixed the redirection issue occurring after plugin reactivation when the cookie brute force options are saved in the database
* FIX: Fixed the undefined variable $error in wp-security-user-security-commands.php 
* FIX: Fixed the login lockout request issue
* FIX: Bulk "Delete selected" action in the Audit Log list was not working
* FIX: Corrected AIOWSPEC prefixes to AIOWPSEC
* FIX: The 5G Firewall switch is behaving inversely, enabling it removes .htaccess rules, while disabling adds them.
* FIX: Fixed the HTML code shown incorrectly on the .htaccess tab
* TWEAK: Updated links to point to our new website

= 5.4.0 - 27/Mar/2025 =

* FIX: Replaced firewall URI parsers with non-WordPress methods
* FIX: Resolved PHP 5.6 compatibility issue caused by the ?? operator in 5.3.10

= 5.3.10 - 26/Mar/2025 =

* FEATURE: Added commenting capability to IP whitelists
* FEATURE: Added diagnostics reporting
* FEATURE: Added a whitelist and user role-based access limit to the REST API firewall
* FIX: "Undefined index: path" error when front-end HTTP Authentication is enabled.
* FIX: Resolved dashboard translation issue where text lacked whitespace and was not properly translated
* TWEAK: Remove uses of unserialize without restriction of allowed_classes
* TWEAK: Refactored IP commands class to use response helper
* TWEAK: Removed WP REST API tab
* TWEAK: Switched "Critical Feature Status" toggle buttons on the dashboard to a status light system
* TWEAK: Updated the security strength meter on the dashboard
* TWEAK: Improved the dashboard widget to display a chart showing the number of logins over the last 7 days
* TWEAK: Enhanced the maintenance mode switch on the dashboard for consistency with the rest of the plugin
* TWEAK: Converted Brute Force menu actions to use AJAX
* TWEAK: Updated seasonal notices 

= 5.3.8 - 16/Dec/2024 =

* FIX: Updated the plugin notices to fix translation related fatal errors.

= 5.3.7 - 5/Dec/2024 =

* TWEAK: Change response code for blocked unauthorized REST requests to 403.
* TWEAK: Temporarily removed firewall logging

= 5.3.6 - 3/Dec/2024 =

* FIX: Resolved an issue with the AIOS_Firewall_Resource class

= 5.3.5 - 24/Nov/2024 =

* FIX: Custom .htaccess rules are now properly escaped, with backslashes removed.
* FIX: Import settings failed when visitor lockout messages had text alignment or other formatting applied
* FIX: The audit log filter for event type now works correctly, even when the event type is translated into languages other than English
* FIX: Resolved text overflow in the blue box on the Settings > WP Version Info page
* FIX: Some user meta keys were not being removed after uninstalling the plugin
* FIX: Subsites no longer incorrectly detect the Database Prefix feature as active
* FIX: Prevented fatal errors from missing firewall resources, replacing them with debug log entries
* FIX: WordPress database error: BLOB, TEXT, GEOMETRY, or JSON columns cannot have a default value set
* FIX: The load_plugin_textdomain function is called during the init action, and translations are applied afterward
* FIX: Renamed login page is now using the WordPress translations
* TWEAK: Added a filter for PHP firewall rules templates
* TWEAK: Updated the country code field for audit logs to be based on the IP address (Premium)
* TWEAK: Improved the text in the 404 detection tab
* TWEAK: Moved the allowlist into the blacklist tab, and renamed it to "Block & Allow Lists"
* TWEAK: Moved the WP REST API feature to the PHP rules tab
* TWEAK: Refactored multiple command classes to use the new AJAX response helper method: Tools, File scan, Files, Settings, and Log commands classes
* TWEAK: Updated the UI for the .htaccess rules, Captcha settings and file protection tabs
* TWEAK: Added a note in Settings > Delete plugin settings tab
* TWEAK: Early calls to get_plugin_data() no longer require translations
* TWEAK: Refactored the firewall command class to use the response helper method
* TWEAK: Added a constant AIOS_DISABLE_HTTP_AUTHENTICATION. Define this in your wp-config.php to disable HTTP authentication

= 5.3.4 - 21/Oct/2024 =

* FEATURE: Added a HTTP authentication feature that allows protecting the site with a username/password login.
* FIX: Added a new method to reset the firewall rules under general settings
* FIX: Resolved the issue with post cache which caused an issue with comment spam prevention
* TWEAK: Added a helper class for API requests
* TWEAK: Removed whitespaces at end of sentences

= 5.3.3 - 16/Sep/2024 =

* FEATURE: Added captcha option for WooCommerce classic guest checkout page.
* FIX: Fixed responsive layout issues with dashboard notice logo on mobile devices.
* FIX: Turnstile captcha widget showing multiple times
* FIX: Solved memory issue for reading larger host system log file 
* FIX: Removed .htaccess options from the Settings menu on Nginx, IIS and unsupported web servers
* FIX: Resolved UX popup issue and firewall allowlist sanitization
* FIX: Resolved an issue where bulk table actions were still executed even if the confirmation dialog was canceled.
* FIX: Added a null check to prevent PHP warnings in firewall rules
* TWEAK: Ajaxified the actions in the settings, filesystem security, spam prevention and user security menu
* TWEAK: Added Ajax support to list tables and the audit log
* TWEAK: Added CAPTCHA field to MemberPress forgot password and registration forms
* TWEAK: Excluded .htaccess tabs from settings if the server is not supported
* TWEAK: Updated the firewall rules UI and malware scanner description
* TWEAK: Tweaked the htaccess backup method to generate the random filename
* TWEAK: Removed 'prevent access to default WP files' from .htaccess and added 'license.txt' to deletion list.

= 5.3.2 - 06/Aug/2024 =

* FIX: Bug that allowed subsite admins to delete audit logs of other subsites
* FIX: Disabled blacklisting on subsites because the PHP-based firewall currently applies to the entire multisite
* FIX: An issue with getting the google bot ip ranges
* TWEAK: Added extra protections in place before modifying the .htaccess file
* TWEAK: Actions in the tools, firewall and scanner menu are now processed via AJAX
* TWEAK: Trimmed leading and trailing whitespace from inputs in the WHOIS lookup tab
* TWEAK: Added a confirmation pop-up when users clear records in the Debug Logs table
* TWEAK: Added captcha support for the MemberPress plugin
* TWEAK: Improved the UX of the WP REST API options
* TWEAK: Internal code improvements to improve maintainability
* TWEAK: Updated the feature manager to improve performance
* TWEAK: Fixed the issue of blank tables on mobile view
 
= 5.3.1 - 26/Jun/2024 =

* FEATURE: Added CAPTCHA to password protected pages/posts
* FIX: Captcha not showing on the BuddyPress registration page
* FIX: WooCommerce logout issue when the renamed login page and login whitelist features are both enabled
* FIX: Missing CAPTCHAs when multiple WooCommerce login and register forms are on the same page
* FIX: Fixed an issue with the 404 detection actions
* FIX: A UI issue with the 2FA QR code image
* TWEAK: Added the attribute data-cfasync="false" to the default captcha url to allow loading on Cloudflare Rocket Loader
* TWEAK: Purge login lockdown table records after 90 days to restrict size. The AIOS_PURGE_LOGIN_LOCKOUT_RECORDS_AFTER_DAYS constant has been added to change the default.
* TWEAK: Updated the malware scanner frequency text from daily to weekly
* TWEAK: Updated the password strength meter UI for the password tool
* TWEAK: Add a 'Lock IP' and 'Blacklist IP' link to the IP column of the audit log.
* TWEAK: Enhance fake Googlebot detection. In the case where gethostbyaddr fails, the firewall will fallback to checking against known Googlebot IP ranges
* TWEAK: Updated the column header for the  "Permanent Blocked IP Addresses" table to be consistent with other tables
* TWEAK: Prevent warning when DISALLOW_FILE_EDIT has already been defined
* TWEAK: Fix instances of one translation function being used for multiple sentences
* TWEAK: Improved the UX during AJAX calls
* TWEAK: Removed Trash spam comments duplicated description

= 5.3.0 - 01/May/2024 =

* FEATURE: Added bulk force logout features for logged in users
* FIX: An issue with the WooCommerce my account page logout function when the cookie based brute force feature is turned on
* FIX: Warning undefined array key SCRIPT_FILENAME
* FIX: Custom redirection after login not working if url contains the redirect_to parameter
* FIX: List of administrator accounts not showing on the user security page
* FIX: Issue with cookie based bruteforce prevention solved if salt postfix feature is on.
* FIX: Fixed country field not showing in the 404 event logs (Premium)
* FIX: Fixed country field not showing in the smart 404 blocked IP log (Premium)
* TWEAK: Fixed translation issue not showing as per admin user set language instead of site settings
* TWEAK: Firewall upgrade changes are applied without access to the admin interface
* TWEAK: Change the labels for the switches to a more appropriate wording
* TWEAK: In the file scanner results show the file sizes in a human readable format
* TWEAK: Updated the default message for attempts to access wp-admin
* TWEAK: Internal refactor of the update code to improve code clarity.
* TWEAK: Port the 'Block fake Googlebots' feature to the PHP-based firewall
* TWEAK: Remove requirement for at least one IP for 'Blacklist', 'Login whitelist' and 'Login lockout IP whitelist' to be enabled.
* TWEAK: Added error message when a user tries to block their own IP on registration approval
* TWEAK: Added method to update badge on AJAX call
* TWEAK: internal refactor of the AIOWPSecurity_Utility_File class to improve code clarity
* TWEAK: Seasonal notice content update for 2024

= 5.2.9 - 06/Mar/2024 =

* FIX: Remove call to update_event_table_column_to_timestamp in update routine
* FIX: Remove call to wp_timezone() which is only available in WP 5.3+

= 5.2.8 - 05/Mar/2024 =

* FIX: The user check that affects the Duo authentication plugin
* FIX: Database update routine is now run without needing to visit the admin interface or each individual site in a multisite
* FIX: Some settings in the firewall menu not resetting after deactivating and reactivating the plugin.
* TWEAK: Audit log and 404 events CSV export file date time column is now in a human readable format not unix timestamp
* TWEAK: Debug log table existing datetime field converted to timestamp to be timezone independent
* TWEAK: Global meta table existing datetime field converted to timestamp to be timezone independent
* TWEAK: Permanent block table existing datetime field converted to timestamp to be timezone independent
* TWEAK: Refactor list item actions to further improve code clarity
* TWEAK: Removed blacklist admin menu as previously announced
* TWEAK: Removed miscellaneous admin menu as previously announced
* TWEAK: Removed various admin menu tabs as previously announced
* TWEAK: Store IP lookup result for other types of entries in the login lockdown table
* TWEAK: Update the footer review prompt
* TWEAK: Max file upload size limit to 250 MB by aiowps_max_allowed_upload_config filter removed
* TWEAK: Improve comment spam detection to not interfere with other forms

= 5.2.7 - 06/Feb/2024 =

* SECURITY: Added nonce checks to various list table actions to prevent a CSRF vulnerability. Thanks to dhakal_ananda for disclosing this defect. This would allow an attacker who persuaded a logged-in administrator to visit a specially crafted link to perform actions on the 404 event records.

= 5.2.6 - 06/Feb/2024 =

* SECURITY: Removed unnecessary use of the "tab" query parameter on various admin menu pages to prevent a non-persistent XSS vulnerability. Thanks to Matthew Rollings for disclosing this defect. (This would allow an attacker who deliberately targets you whilst logged in as an administrator and persuades you to visit a link he controls to inject unwanted scripts on a single visit to your AIOS admin page).
* FEATURE: Added logout event to the audit logs
* FEATURE: Add ability to delete the default readme.html file and wp-config-sample.php file
* FIX: Correct some translation calls that were using the wrong text domain
* FIX: PHP notice caused by the file scanner being unable to read its data file
* FIX: Unlock request button was not showing and redirects to 127.0.0.1
* FIX: Database errors for the aiowps_login_lockdown table during plugin installation
* TWEAK: Refactor the 6G UI
* TWEAK: Added an option to set the Cloudflare Turnstile CAPTCHA theme
* TWEAK: Added CSS styling for audit log details column
* TWEAK: Dashboard critical feature status links fixed and only show features that can be enabled in a multisite subsite
* TWEAK: Deactivating the plugin now removes stored login info so on the next activation users are not force logged out
* TWEAK: Display json string instead of null if json_decode does not work for audit log details
* TWEAK: Event table existing datetime field converted to timestamp to be timezone independent
* TWEAK: Various tweaks to get codebase up to coding standards
* TWEAK: Various tweaks to ensure multiple sentences are not passed to a single translation function
* TWEAK: Fix the broken UI for RSS and Atom firewall settings and added a more info box
* TWEAK: Fix the issue of unique ID in DOM
* TWEAK: Merge Username and Display Name tabs in User Security Settings 
* TWEAK: Moved the '404 detection' tab to the 'Brute force' admin menu
* TWEAK: Moved the 'PHP file editing' tab into 'File Protection' tab
* TWEAK: Moved the 'User enumeration' tab into the 'User accounts' tab in the User Security Menu
* TWEAK: Moved the 'WP Rest API' tab into the Firewall Menu
* TWEAK: Moved the 'Copy protection' and 'Frames' tab into the Filesystem security menu
* TWEAK: Moved the 'Salt' tab into the User security menu
* TWEAK: Moved 'Blacklist Manager' tab into the Firewall menu.
* TWEAK: Password resets, removed and deleted users are now recorded in the audit log
* TWEAK: Stop 404 IP from being locked if there's a current lock on that IP
* TWEAK: Unify date and time conversion with users timezone support
* TWEAK: Changed how empty data in ip lookup result is stored in the database
* TWEAK: Rework Firewall Menu page to have two tabs for PHP and .htaccess rules
* TWEAK: Add captcha support for Contact Form 7
* TWEAK: Added a AJAX save settings and get features details badge function as part of ongoing work to add AJAX support to the plugin settings
* TWEAK: Enhance reset password email by adding IP info
* TWEAK: Remove defunct imagetoolbar meta tag
* TWEAK: Login lockout tables existing datetime field converted to timestamp to be timezone independent
* TWEAK: Code improvements - utilising WP_Error objects instead of arrays

= 5.2.5 - 25/Oct/2023 =

* SECURITY: On a multisite install, if using the AIOS feature for renaming and hiding the login page, a route existed for an attacker to discover the hidden login page, thus negating the usefulness of the feature. Thanks to Naveen Muthusamy for disclosing this defect.
* FEATURE: Block POST requests that have a blank user-agent and referer
* FEATURE: Added reverse IP Lookup data to the login lockdown notification email
* FIX: Prevent a fatal error when setting up the firewall if the host has disabled the function parse_ini_file
* FIX: Prevent the firewall message store from filling up with unused entries
* FIX: Prevent legitimate Googlebot traffic being blocked on sites where the gethostbyaddr function fails or is disabled
* FIX: An issue that prevented MainWP updates from being performed correctly
* FIX: Prevent user enumeration via the REST API and oEmbed protocol
* FIX: User agent blacklist not matching all strings correctly
* FIX: Logged in user table not showing the correct information
* TWEAK: Improve comment spam detection by using hidden fields and cookies
* TWEAK: Login whitelist suggests both IPv4 and IPv6 addresses to whitelist
* TWEAK: The menu actions in the dashboard admin menu are now processed via AJAX
* TWEAK: Converted checkboxes in the admin menu pages to switches
* TWEAK: Add network_id and site_id column to debug logs table for differentiating logs between sites on multisite
* TWEAK: Combined various user admin menus into a new 'User Security' admin menu
* TWEAK: Export configuration filename now reflects the local timezone.
* TWEAK: Improve the UI/UX of the file scanner making way for future improvements
* TWEAK: Redesign the feature manager badges
* TWEAK: Removed various admin menu tabs as previously announced
* TWEAK: Add features that depend on other plugins to the feature manager conditionally
* TWEAK: Added a null check to function that removes wp meta info from scripts and styles src to prevent a PHP deprecation warning
* TWEAK: Audit log date and time are now displayed in the sites timezone
* TWEAK: PHP warning undefined array key REQUEST_METHOD in rule-proxy-comment-posting.php
* TWEAK: When TranslatePress is active, logging out via WooCommerce should not show a 404 page if the "rename login page" setting is on.

= 5.2.4 - 16/Aug/2023 =

* FIX: Ported firewall settings from disabling on upgrade

= 5.2.3 - 09/Aug/2023 =

* FIX: Fatal error "set_value() on null" when the firewall config is missing
* FIX: PHP notices when running under cron
* FIX: Revert change that caused the Brute force login whitelist to show the server IPs and not the users
* TWEAK: Add communication mechanism so that firewall can send data to WordPress
* TWEAK: Remove incorrect mentions of the .htaccess file on PHP Firewall rules

= 5.2.2 - 04/Aug/2023 =

* FEATURE: An allow list of IP addresses which bypass the firewall rules
* FIX: Fix get_class() on null fatal error when updating via ManageWP
* FIX: No such file or directory notice generated by the firewall's config file
* FIX: Only send the upgrade email if one or more of the ported rules had been enabled
* FIX: Fake Google bots are now blocked if bot server IP address does not resolve to a hostname
* FIX: Google reCaptcha now appears correctly on the WooCommerce checkout page
* FIX: Prevent Woocommerce auto login if manual registration approval is turned on
* FIX: Premium upgrade tab UI overlapping issue.
* FIX: Allow maintenance mode to be controlled via WP-CLI (Premium)
* FIX: Use the correct site id for login success events added to audit log table on Multisite
* FIX: Added missing features to the feature manager list
* FIX: A warning when using the update all command via WP-CLI
* TWEAK: AIOS settings based IP address is now used instead of the REMOTE_ADDR server variable for multiple wrong 2FA code notification
* TWEAK: Added 'aios_audit_log_record_event' filter to allow events to not be recorded
* TWEAK: Improve the feature item manager code structure making way for future improvements
* TWEAK: Login whitelist suggests both IPv4 and IPv6 addresses to whitelist.
* TWEAK: Move the 'Custom rules' tab from the 'Firewall' section to its own tab in the 'Tools' section
* TWEAK: Move the 'Prevent hotlinking' tab to the 'File protection' tab in the 'Filesystem Security' menu
* TWEAK: Moved all CAPTCHA settings to the 'CAPTCHA settings' tab in the 'Brute Force' menu
* TWEAK: Moved the 'Password tool' tab to the 'Tools' admin menu
* TWEAK: Moved the 'Visitor lockout' tab to the 'Tools' admin menu
* TWEAK: Moved the 'User registration honeypot' tab to the 'Brute force' admin menu
* TWEAK: Remove 'Account activity table' as these entries are also recorded in the audit log
* TWEAK: Removed the 'Failed login records' tab as previously announced, these are now recorded in the audit log
* TWEAK: Improve list table code performance
* TWEAK: Removed use of $_GET, $_POST, $_REQUEST from all template files making way for future improvements

= 5.2.1 - 12/Jul/2023 =

* FIX: Include helper class file from loader
* TWEAK: Conditionally load TFA block JavaScript

= 5.2.0 - 10/Jul/2023 =

* SECURITY: Remove authentication data from the stacktrace before saving to the database. This defect meant that a site administrator had the potential, between releases 5.1.9 to 5.2.0 (which purges the existing data), to know what site users' passwords are. This information has limited value (an admin can already reset anyone's password) except insofar as the passwords may be re-used by users on other sites. In that "hostile admin" scenario, your site has other problems (since the hostile admin has a whole raft of equivalent ways of causing mischief to users, especially if not on multisite where a site admin is potentially not a super admin and may not be able to install or configure plugins). This changelog has been expanded in response to incorrect reports which suggested a wider problem (for example, they did not mention that the attacker needs to already be logged in as an admin to read the log, or that upgrading to 5.2.0 deletes the affected data).
* SECURITY: Set tighter restrictions on what subsite admins can do in a multisite.
* FIX: After editing a file reset permissions back to the original permissions
* FIX: Corrected some broken links in the plugin
* FIX: Fatal error: cannot declare class
* FIX: Normalise all arguments in the stacktrace
* FIX: Wrong login entries added to login activity table on multisite when user logs into subsite they don't belong to.
* FIX: Too many redirects error for forced logout users solved
* TWEAK: For Cronjob, WP CLI and AIOS_DISABLE_EXTERNAL_IP_ADDR defined constant do not use external services for user IP addresses. Silenced api.ipify.org request failed warning.
* TWEAK: Reset password page missing translation and generate password button added for renamed login page
* TWEAK: Added 'aios_audit_log_event_user_ip' filter to allow filtering of IP addresses in the audit log 
* TWEAK: Added action hook "aios_reset_all_settings" for reset all settings.
* TWEAK: Renamed login page to have language change dropdown and other tweaks as per the WordPress 6.2

= 5.1.9 - 09/May/2023 =

* FEATURE: IP addresses - Blacklist manager functionality based on PHP instead of .htaccess rules. Added AIOS_DISABLE_BLACKLIST_IP_MANAGER constant, Define it in your wp-config.php to disable IP Blacklist manager.
* FEATURE: Detect spambots posting comments and discard it completely or mark as spam.
* FEATURE: Encrypt TFA secret keys that are stored in the database (extra protection in case of your database being hacked)
* FEATURE: Added a "Delete all" and "Delete filtered" bulk action to the audit log table
* FIX: Prevent Cloudflare Turnstile being added to login forms when no credentials where set
* FIX: Change where the audit log event handler is loaded to prevent an error on plugin deletion
* FIX: Fix context class checks to support cli
* TWEAK: Multisite super admin can access the subsite dashboard without login again if salt postfix enabled
* TWEAK: Captcha JavaScript file is unnecessarily loaded on some site pages if comment captcha or custom login captcha enabled
* TWEAK: Change some nonce checks to use our internal function to check user capability and nonces
* TWEAK: User registrations and successful logins are now recorded in the audit log
* TWEAK: Added a commands class and refactored AJAX handlers
* TWEAK: Captcha verification to prevent conflicts with some plugins that recall the WordPress authentication code
* TWEAK: Improve database table prefix feature UI.
* TWEAK: WordPress core updates are now recorded in the audit log
* TWEAK: Translation updates are now recorded in the audit log
* TWEAK: Add an entity changed event to the audit log when upgrader information is not available
* TWEAK: Automated emails sent by AIOS that failed to send due to from address

= 5.1.8 - 11/April/2023 =

* FIX: 404 detection - Individual record blacklisting, delete, temp block actions stopped working in 5.1.7
* FIX: Uncaught fatal error on null 'set_value'
* FIX: Remove audit log event handler actions on plugin deletion to prevent an error
* FIX: Remove some audit log event handler on plugin deletion to prevent an error
* FIX: Get correct wp-config path when installed in a subdirectory
* TWEAK: AIOS_Helper::request_remote timed out exception ignored. 
* TWEAK: Requests_IPv6 class name deprecated in WordPress 6.2.
* TWEAK: Failed login attempts are now recorded in the audit log

= 5.1.7 - 24/March/2023 =

* FIX: Prevent fatal error when calling get_server_detected_user_ip_address() when the firewall is not setup
* TWEAK: Clarify dashboard notice title and change image.

= 5.1.6 - 21/March/2023 =

* FEATURE: Added an audit log
* FEATURE: Add salt postfix option to improve your site's security
* FEATURE: Shared library that can be used from the firewall.
* FIX: Rename login slug used like wp-login-RANDOM_SUFFIX showing 404 page issue solved and code clean up for multisite activation.
* FIX: Divi child theme conflict - Call to undefined function et_builder_get_fonts() in functions.php on line 208 solved.
* FIX: Captcha settings tab in multisite installation for subsites not showing
* FIX: Cron reschedule event error for hook aios_15_minutes_cron_event if plugin deactivated or uninstalled
* TWEAK: Stop user enumeration now shows 403 forbidden error code instead of 500 server error
* TWEAK: PHP 8.1 warning rawurldecode passing null instead type string is deprecated for block request string 6g rule
* TWEAK: Code clean up for disable cookie based brute force constant as rule moved to firewall
* TWEAK: Comment spam IP monitoring page UI
* TWEAK: Updated seasonal notices
* TWEAK: Improve internal code structure making way for future improvements
* TWEAK: Remove mention of the 6g firewall rules being .htaccess based as they are now php based
* TWEAK: Added new internal function to check user capability and nonces
* TWEAK: Improve config code with inline saving.
* TWEAK: Allow audit log to be filtered and exported to CSV

= 5.1.5 - 13/February/2023 =

* FEATURE: Added Cloudflare Turnstile CAPTCHA support
* FIX: Notices about undefined array key HTTP_USER_AGENT solved.
* FIX: New v5 features not saved in export file and not properly reset after uninstallation.
* FIX: File permission change being applied to the last record not selected one. Also, no longer change permissions when they are already tighter than the suggested.
* FIX: Fatal error 'Call to a member function contains_contents() on null'
* TWEAK: Removed wrong information about login whitelist being implemented via htaccess.
* TWEAK: Refactoring settings tasks for WP CLI AIOS premium commands.
* TWEAK: Page load performance issue due to incompatible tfa premium plugin active check improved.
* TWEAK: Make sure translation domain is registered before attempting to use it
* TWEAK: Replaced click with press in text because users could be on mobile etc and not using a mouse.
* TWEAK: Registration, comment, Buddypress and bbPress admin pages to show notice enable the captcha settings.
* TWEAK: Improve the UI/UX for the 404 detection tab
* TWEAK: Improve internal code structure making way for future improvements
* TWEAK: PHP 8.2 deprecation warning for dynamic properties
* TWEAK: Remove the unintended ability for directory traversal and lack of escaping when outputting files with the "view system log" feature. This facility is only available to an administrator (who can of course already do anything on the site, so this has no security implications) and allow them to view (the last 50 lines) from any file or list any directory on the system where the web server has read access.
* FIX: Fatal error 'Call to a member function contains_contents() on null'
* TWEAK: Firewall gets constants from a single source.

= 5.1.4 - 14/December/2022 =

* FEATURE: Add option to disable RSS and ATOM feeds.
* FIX: The IP address blacklist manager wasn't working.

= 5.1.3 - 09/December/2022 =

* SECURITY: No longer save settings import files in a publicly accessible folder where they can be potentially indexed by search engines if the administrator does not actually import the settings (which deletes the import file)
* FEATURE: Implement firewall events system
* FIX: Protect subsites when firewall is loaded via plugins_hook
* TWEAK: Improve the UX for uploading import files
* TWEAK: Add a default CAPTCHA option making way for new CAPTCHAs in the future

= 5.1.2 - 07/December/2022 =

* FEATURE: User Agent - Blacklist manager functionality should be based on PHP instead .htaccess rules.
* FIX: Sorting by 'status' on the comment spam table
* FIX: Copy protection feature not working on iPhone
* FIX: Cookie based brute force prevention locks out if plugin deactivated and activated again.
* FIX: The notice to reapply .htaccess rules after reactivating the plugin is displayed on subsites.
* FIX: Various WordPress command line notices about undefined $_SERVER indexes
* FIX: Deactivate and reactivate plugin firewall settings file sync issue solved.
* TWEAK: 2FA setting page to show premium options for AIOS premium.
* TWEAK: Remove characters that should not have been on the scanner page
* TWEAK: Organise firewall rules into subdirectories
* TWEAK: Added GDPR question answer to the AIOS WP org plugin's FAQ section.
* TWEAK: Allow AIOS management permission to be filtered via `aios_management_permission` filter
* TWEAK: Make use of is_main_site() function.
* TWEAK: Copy IP to clipboard when clicking on it at WP Security -> Brute Force -> Login whitelist.
* TWEAK: Better context detection for the firewall

= 5.1.1 - 16/November/2022 =

* SECURITY: Fixed a failure to check bulk action nonces, leading to a CSRF vulnerability. Exploitation would require an attacker to craft a link specifically for your site, and persuade you to click it whilst logged in; if you did so, this could result in bulk actions being carried out on AIOS list tables (e.g. delete entries from blocked IP address lists), with the attacker being restricted to deleting entries by database ID numbers that he cannot know directly (e.g. 15, 16, 17) and not IP address (e.g. ***************).
* FEATURE: Cookie-based brute force prevention implemented with the new PHP based firewall system.
* FIX: AIOWPSecurity_WP_Loaded_Tasks::site_lockout_tasks() method visibility
* FIX: Prevent the dismiss notice button removing all notices from page including notices that contained important information
* FIX: Brute Force > Login Whitelist issue access password protected pages by user solved.
* FIX: Force logout link not working in the currently logged-in users list.
* FIX: Google reCAPTCHA site key and secret key are not verified immediately.
* TWEAK: Code style changes for scanner related pages and future item manager class.
* TWEAK: Capitalisation style reapply for firewall menu tabs.
* TWEAK: Instead login lockdown used login lockout word in UI and mail content. Changed constant AIOWPS_DISABLE_LOGIN_LOCKDOWN to AIOWPS_DISABLE_LOGIN_LOCKOUT.
* TWEAK: Update tabs, links to match capitalisation style of other UpdraftPlus plugins.
* TWEAK: Added the filter `aios_server_type` to override the `AIOWPSecurity_Utility::get_server_type()` method's return value.
* TWEAK: Notice - Account activity logs, 404 event logs older than 90 days cleared automatically to show.
* TWEAK: Premium upgrade page FAQs linked to correct URL.
* TWEAK: IP address lookup called only once in same page request. Visitor blocking called when user is not logged in. User online information updated on login only.
* TWEAK: User login lockout - minimum lockout time length should be less than maximum lockout time length validated.
* TWEAK: Take a backup of wp-config before inserting firewall contents.
* TWEAK: Ability to downgrade the firewall's protection which allows users to reverse the changes from setting up the firewall.
* TWEAK: Set a global context for $wp_file_descriptions context so that it gets assigned to correctly, preventing a subtle visual change in the theme editor
* TWEAK: Black Friday notice
* TWEAK: Update readme.txt file

= 5.1.0 - 12/October/2022 =

* FIX: The login loader is visible infinitely on the login screen and administrators can't log in if the user has enabled maintenance mode and 2FA authentication simultaneously.
* FIX: Pressing the "Disable Firewall" button didn't clear new 6G firewall rules.
* FIX: The application password was disabled by default on the activation of the AIOS plugin.
* FIX: The error occurred with the error message: Uncaught TypeError: fclose(): Argument #1 ($stream) must be of type resource, bool given in all-in-one-wp-security-and-firewall/classes/wp-security-utility-htaccess.php:164 in the server where the root folder is not writable.
* TWEAK: IP address lookup service whatismyipaddress removed, API for bot.whatismyipaddress.com is no longer available.
* TWEAK: The simple math captcha box was shown when the user was filling in the 2FA code at login time.
* TWEAK: Firewall max upload limit default value increased instead 10MB to 100MB.
* TWEAK: Google reCaptcha multilingual implemented to show in local language messages instead of English only.
* TWEAK: Update headings, labels and buttons to match capitalisation style of other plugins.
* TWEAK: Add premium upgrade tab.

= 5.0.9 - 06/October/2022 =

* FIX: PHP Notice:  Only variables should be passed by reference in /wp-content/plugins/all-in-one-wp-security-and-firewall/classes/wp-security-notices.php on line 202.
* TWEAK: Auto disable the login whitelisting on upgrade for all server types and shown related notice.
* TWEAK : 2FA - Warning: Deprecated: Call get_controller('totp'), not get_totp_controller() in /includes/simba-tfa/simba-tfa.php on line 713.

= 5.0.8 - 29/September/2022 =

* SECURITY/FEATURE: Fix IP address detection, and give IP address detection settings in the Admin Dashboard > WP Security > Settings > Advanced Settings, provide user guidance on how to use them, and notify the user if there any problem is apparent. Versions from 5.0.0 to 5.0.7 had a defect allowing an attacker to spoof their IP address, aiding them to avoid detection or locking out legitimate users. Thanks to Calvin Alkan for the responsible disclosure.
* FIX: The 403 forbidden error was shown on the wp login screen if the login url contains the redirect_to parameter and the deny bad query strings firewall feature is enabled on localhost.
* FIX: The PUT request method was blocked when the user enabled the 6G firewall.
* FIX: The login whitelisting didn't work on servers not supporting .htaccess files, without this information being displayed in the user interface. The feature is now ported to PHP so that it works on all servers. Thanks to Calvin Alkan for identifying this issue.
* TWEAK: Add index keys to the login lockdown, failed_logins and the permanent block tables to prevent poor database reading performance in the event of vast numbers of rows being stored in these tables (see the "SECURITY" item above, since the defect described there can allow this). Thanks to Calvin Alkan for identifying this issue.
* TWEAK: Resolve a PHP-firewall 'Unable to locate workspace' log message.
* TWEAK: Added a constant AIOS_DISABLE_GET_EXTERNAL_IP. Define this in your wp-config.php to disable getting the IP address via an external API when the IP retrieval method fail to get a valid IP address.
* TWEAK: Replace deprecated jQuery(document).ready() calls.
* TWEAK: Disable cookie access via JS and HTTP for cookie-based brute force prevention.
* TWEAK: Enhanced cookie storage mechanism for cookie-based brute force prevention. Thanks to Calvin Alkan for identifying this improvement.
* TWEAK: Display notice alerting the user that the block spam comment doesn't work on non-apache servers in the block spam comment section. Thanks to Calvin Alkan for identifying this omission.
* TWEAK: Added a constant AIOS_DISABLE_LOGIN_WHITELIST. Define this in your wp-config.php to disable login IP whitelist.

= 5.0.7 - 08/September/2022 =

* FIX: The Login URL was prefixed with the site URL instead of the home URL when the home URL is different than the site URL.
* FIX: Rename login and cookie-based brute force protection couldn't work simultaneously when the permalink was set to plain.
* FIX: Disabling the 5G Firewall Protection didn't remove the 5G rules from the .htaccess file.
* TWEAK: Add a 'Dismiss' button to the firewall setup notice.

= 5.0.6 - 07/September/2022 =

* FIX: Stopped host cron job working in a specific situation.
* FIX: A few setting options like enabling the honeypot feature for registration page, disabling the application password, enabling move spam comments to trash after specified days, moving spam comments to trash after days, enabling remove database tables upon uninstalling, and enabling remove all plugin settings upon uninstalling the plugin were overridden on upgrading the plugin.
* TWEAK: Add a 'safe message' comment to the firewall's settings file.

= 5.0.5 - 05/September/2022 =

* FIX: Cookie based brute force etc rules to be removed from .htaccess if set in older version 4.4.12.
* FIX: The IP lock notification mail was sent out for the 404 lockdown event.
* TWEAK:  Resolve a PHP-firewall 'Unable to locate workspace' log message.

= 5.0.4 - 03/September/2022 =

* FIX: PHP coding warning in latest PHP version when handling email address parameter.
* TWEAK: Added a constant, AIOS_DISABLE_COOKIE_BRUTE_FORCE_PREVENTION. Define this in your wp-config.php to disable cookie based brute force login prevention.

= 5.0.3 - 02/September/2022 =

* FIX: An empty IP lock notification mail could be sent out after upgrading to the 5.0.0 version.
* FIX: The PHP file couldn't be loaded via commandline if the rename login page is enabled.
* FIX: When running WordPress from the command line, the warning Undefined index: REQUEST_METHOD was logged.
* TWEAK: Import latest TFA module, loading JS less aggressively to avoid potential for conflicts.

= 5.0.2 - 02/September/2022 =

* FIX: The user can't login if the user set forced logout and the site's timezone is different than UTC.
* FIX: Avoid an incompatibility with Wordfence Login Security by not loading our TFA module if that plugin is active

= 5.0.0 - 01/September/2022 =

* FEATURE: Two-Factor Authentication (2FA) functionality & related settings.
* FEATURE: Set up a mechanism to load the firewall PHP file early.
* FEATURE: PHP firewall rule engine.
* FEATURE: Add WHOIS lookup functionality.
* FEATURE: Implement 6G firewall rules in the new PHP-based firewall.
* FEATURE: Disable WordPress application passwords.
* FEATURE: Remove the plugin's tables and options when uninstalling the plugin according to configuration settings.
* FEATURE: Trash spam comments after n number of days as per configuration set in Admin Dashboard > WP Security > SPAM Prevention > the "Comment SPAM" tab > the  "Comment Processing" section > the "Trash Comments After" settings.
* FEATURE: Brute force Cookie-based Firewall Protection based on the PHP code instead of htaccess rules so that it also works with Nginx, IIS etc servers.
* FEATURE: Allow multiple email addresses for the User Login > Notify By Email setting.
* FEATURE: IPv6 range support in CIDR Format enabled.
* FIX: The WooCommerce customer was redirected to the wp-login page after payment with an external payment gateway if forced logout configured after a specific number of minutes.
* FIX: If the WordPress language was set to something other than English, then auto-update core, plugin, and theme emails sent in English instead of the configured language.
* FIX: Database error for multisite when creating a new site solved.
* FIX: Captcha options should not be autoloaded.
* FIX: Database error for multisite cronjob column name.
* FIX: The plugin clogs up the database with lots of rows. Delete old data after 90 days.
* FIX: Rename Login issue with wp plugin list command solved.
* FIX: Rename Login breaks logout functionality if WP_HOME is set to a different URL than the WordPress core files URL.
* FIX: PHP Fatal error:  Uncaught Error: Class 'AIOWPSecurity_Admin_Init' not found in html/wp-content/plugins/all-in-one-wp-security-and-firewall/wp-security-core.php:366.
* FIX: The Spam comment blocked IP address remains blocked even after spammed comments are approved.
* FIX: Admin Dashboard > WP Security > Security Points Breakdown Section piechart tooltips flickering.
* FIX: The "Time Length of 404 Lockout" option doesn't do anything.
* FIX: Search did not work for the 404 Event Logs list table.
* FIX: Search did not work for Failed Logins list table.
* FIX: Search did not work for the Account Activity list table.
* FIX: Bulk deletions did not work for the Account Activity list table.
* FIX: Warning when bots make malformed requests.
* FIX: When the user had pressed the bottom bulk action button of the list table, the bulk action was confirmed by two confirm alerts.
* FIX: Unblock link in 404 Event Logs list table redirected to wrong tab.
* FIX: Temp Block, Blacklist IP and Delete links in 404 Event Logs list table didn't work.
* FIX: Rename login page and Cookie based brute force login prevention configurations didn't work simultaneously.
* FIX: Fatal error when activating using older PHP versions
* FIX: If auto_prepend_file is already pointed to the firewall bootstrap file from php.ini manually, the bootstrap file try to include itself.
* FIX: The custom logo wasn't displayed on the login lockdown unlock request form.
* TWEAK: Allow taking database backups via the UpdraftPlus backup plugin.
* TWEAK: Make lockout reasons more specific.
* TWEAK: Update notice class.
* TWEAK: If the user has not performed the cookie test, the brute force attack prevention configuration fields remain disabled in the Admin Dashboard > WP Security > Brute Force > Cookie Based Brute Force Prevention.
* TWEAK: Display locked IP addresses lockout date and release date in WordPress settings format.
* TWEAK: Improve success or messages when performing bulk actions on the table list.
* TWEAK: 404 events date is displayed in WordPress settings format.
* TWEAK: Account activity login date and logout date are displayed in WordPress settings format.
* TWEAK: Add a label for each setting field.
* TWEAK: JQMIGRATE: jQuery.fn.click() event shorthand is deprecated.
* TWEAK: Fix typos at Admin Dashboard > WP Security > Firewall > Basic Firewall Rules > Block Access to Debug Log File.

= 4.4.12 - 22/April/2022 =

* FEATURE: Disable login lockdown feature when the AIOWPS_DISABLE_LOGIN_LOCKDOWN constant with true value.
* FEATURE: Implement lockout time multiplied on each lockout up to the maximum lockout time configured.
* FIX: For multisite giving fatal error on settings and dashboard page Call to a member function on null.
* FIX: Scores not updating correctly if a feature activated and "Remove wp generator meta info" activated shows 5/5 for all.
* FIX: Change hard-coded references of wp-content to WP_CONTENT_DIR constant.
* FIX: The AIOS plugin should not be site-wide activated in a multisite WordPress setup.
* TWEAK: Get user IP Address using an external service in local server setup.
* TWEAK: Filter name changed to "aiowps_pre_add_to_permanent_block" from "pre_add_to_permanent_block".
* TWEAK: Filter name changed to "aiowps_filter_event_logger_data" from "filter_event_logger_data".
* TWEAK: Disables the "Secret Word" and "Re-direct URL" input fields when the "Enable Brute Force Attack Prevention" option is unchecked.
* TWEAK: Show nice error on activation if site php version is lower than 5.6.

= 4.4.11 - 29/March/2022 =

* FEATURE: Reset all settings by clicking on the "Reset Settings" button on the Settings Page.
* FEATURE: Verify the Google reCaptcha Site key before rendering and disable it if the Google reCaptcha site key is invalid.
* FIX: PHP Fatal error: Cannot redeclare wp_install_maybe_enable_pretty_permalinks() in specific server.
* FIX: throwing database error for creating debug log table in specific MySQL server.
* FIX: Compatibility issue with WPML plugin for login and logout functionality.
* FIX: Update email sent in English instead of setting language.
* FIX: The Simple Math Captcha can't be validated when a third-party plugin clears transients more frequently.
* FIX: The login lockdown unlock request was not working in a few specific server environments.
* FIX: The warning headers already sent was displayed in a few specific server environments.
* FIX: Handle invalid tabs appropriately in setting pages.
* FIX: A Fatal error occurred when WooCommerce was activated, but it was disabled on the frontend by the Asset Cleanup Pro plugin.
* FIX: Fix login lockout issue with different timezone.
* TWEAK: Add review notice.
* TWEAK: Improve functionality of fake google bot prevents to access the site.
* TWEAK: Remove IP address retrieval setting and detect IP address automatically.
* TWEAK: Verify Google reCaptcha site key before rendering the reCaptcha.
* TWEAK: Remove force logout checking from REST API Call.
* TWEAK: Made Admin Dashboard > WP Security > Settings tabs extensible.
* TWEAK: Add G2 review message in the admin footer.
* TWEAK: Format failed login date time according to WordPress general settings.
* TWEAK: Remove unused codes from AIOWPSecurity_Config.
* TWEAK: Add more specific instructions to change the Display name compared to the username in Admin Dashboard > WP Security > User Accounts > "Display Name" tab > "Modify Accounts With Identical Login Name & Display Name" section.
* TWEAK: Remove Admin Dashboard > WP Security > Site Info tab (now redundant because of WP's "Site Health" tool)
* TWEAK: The "Allow Login Lockout Request" checkbox is ticked by default.

= 4.4.10 - 21/Jan/2022 =

* FEATURE: Send site login lockout emails by batch processing instead of sending them instantly.
* FEATURE: Auto-purge failed login records after 90 days.
* FEATURE: Change the debug log so it's stored in the database and not a file
* FIX: Missing Plugin header fields are added.
* FIX: PHP Warning Notice for finding IP Address appears when a dual proxy used.
* FIX: Logout date-time shows 1000-10-10 10:00:00 for non-logged out user.
* FIX: The notification for re-inserting the security rules in your .htaccess file appears after deactivating and activating the plugin to non-admin users.
* TWEAK: Replace obsolete variable reference style
* TWEAK: Sanitize $_REQUEST parameters in redirect function
* TWEAK: View debug logs from within the UI
* FIX: Compatibility issues with PHP 8.1.
* TWEAK: Advertise All in One WP Security Premium Plugin instead of Addons.

= 4.4.9 =
- Added Polish language translation file to the plugin. Thanks to Dariusz for submitting the language files.
- Fixed a typo in the help text.
- Allow the "redirect_to" parameter to be used on renamed login page when logged in. Thanks to @tvartom.
- Fixed a Typo in the help text located in the "Custom Rules tab".
- Added a new filter hook (aiowps_execute_backup_set_memory_limit) to allow overriding of the PHP memory limit setting when executing a backup.
- WordPress 5.8

= 4.4.8 =
- Fixed an issue with the rename login page feature on WordPress v5.7.

= 4.4.7 =
- Updated the renamed login page code to reflect the latest WordPress wp-login.php code.
- Cleaned up/improved repeated code.
- Translation string fix in the rename login feature.
- Added action hook "aiowps_site_lockout_settings_saved" that is triggered after the site lockout configuration is saved.
- Updated some queries to use $wpdb->prepare() or esc_sql() wherever possible.

= 4.4.6 =
- Fixed potential vulnerability with the Banned User Agents feature (in the blacklist menu). Thanks to WonTae Jang.

= 4.4.5 =
- Fixed default DateTime to prevent DB error.
- Added Korean language translation files. Thanks to Jonghyun Cho.
- Reworked the code for the "Generate New DB Table Prefix" feature to make it more robust. Thanks to @baddiedev.
- Added translation ability to some strings.

= 4.4.4 =
- Fixed bugs and improved functionality related to "logged in users" functionality.
- Google recaptcha checks for WooCommerce product reviews
- Replaced use of deprecated hook "wpmu_new_blog" with "wp_insert_site"
- Fixed a potential XSS issue in the settings menu of the plugin for IE11 or older browsers.

= 4.4.3 =
- Improved file change detection feature to address DB backups failing silently in some cases due to very large serialized data stored in a single row.
- Added new action hook (aiowps_rename_login_load) just before renamed login page is loaded.
- Added a check to ensure that woocommerce captcha settings are displayed only if woocommerce plugin is installed/active.
- Fixed recaptcha bugs.
- Added configurable item for max file upload size in basic firewall rules.

= 4.4.2 =
- Fixed vulnerability related to open redirect and exposure of hidden login page for specific case. (Thanks to Erwan (wpscanteam) for letting us know)

= 4.4.1 =
- Fixed bug where Apache directives were not being re-added into the .htaccess file after plugin re-activation.
- Fixed bug related to account activity logout date not being set.

= 4.4.0 =
- Added robustness to login lockdown feature by replacing the strtotime function with DateTime/DateInterval.
This should prevent 32-bit systems from being constrained to the max date of 19 Jan 2038.
- Fixed bugs related to captcha features.
- Fixed and improved "Logged In Users" functionality for multisite.
- Always set valid dates, to avoid errors when strict mode is enabled on mysql. Thanks to Davide.

= 4.3.9.4 =
- Removed whois feature because it adds relatively little value and the third-party library used is not being maintained regularly.
- Fixed "headers already sent" error when bulk action performed using aiowps list table.

= ******* =
- Fixed another captcha bug related to comment form.

= ******* =
- Fixed various captcha bugs: woocommerce lost password page, custom login form page, etc

= ******* =
- Fixed rename login page feature bug introduced after WP core change in version 5.2.

= 4.3.9 =
- Fixed captcha bug.
- Fixed PHP_EOL issue where some IPv6 and v4 addresses saved in settings were incorrectly deemed invalid.
- Tightened file permission for wp-config.php to "640"
- Fixed DB prefix change bug for cases where DB had tables of type "view".
- Fixed some translation string issues.
- Minor style fix for wp list table pagination nav buttons.

= ******* =
- Trying again - Fixed login captcha authentication bug.

= ******* =
- Fixed login captcha authentication bug.

= ******* =
- Minor bug fix - added missing check to enqueue recaptcha script only if that feature is enabled.

= 4.3.8 =
- Added ability to hide secret rename login page link when sending emails to people asking for personal data export.
- Fixed Google reCaptcha not showing on comment page.
- Fixed activation handler and creation of DB tables to handle multi-site activations more robustly.
- Improved reCaptcha code to prevent the occasional occurrence of "Uncaught Error: reCAPTCHA placeholder element must be an element or id" error.
- Added extra check for PHP_OS value to prevent Apple "DARWIN" being interpreted as windows server.
- Corrected some minor translation issues on rename login page.
- Increased priority of authenticate hook for captcha check.
- Updated the Dutch Language file.

= ******* =
- More "get_home_path" fatal error preventions.

= ******* =
- Fixed fatal error regarding "get_home_path" function.


= 4.3.7 =
- Added Google reCaptcha feature for login forms.
- Improved code which checks if site is main for multi-site installations.
- Removed the text domain string from the translation functions in the wp-security-rename-login-feature.php file.
- Changed .htaccess path location to use get_home_path().
- Fixed minor woocommerce captcha bug

= 4.3.6 =
- Added new tab called "WP REST API" in the Miscellaneous menu and created separate feature which disables unauthorized REST access for non-logged in users independent of the users enumeration feature.
- Improved dashboard page widget area display.
- Small translation string fix in the rename login page feature.

= 4.3.5 =
- Fix - Error: Call to undefined function the_privacy_policy_link() in older versions of WordPress.
- Added a check to disable file change detection feature and prevent fatal errors when FilesystemIterator is not available due to old versions of PHP.
- Improved get_login_fail_count method in the AIOWPSecurity_User_Login class which will fix cases where login lockdown
was not working on some servers due to timestamp difference between PHP current_time( 'mysql' ) and mysql now().

= 4.3.4 =
- Modified rename login page to handle GDPR Export/Erase Personal Data request.
- Fixed woocommerce registration page captcha bug.
- Improved users enumeration so that authenticated requests to the REST API are allowed but others are blocked.
- Improved logic in Renamed Login Page settings such that unnecessary call of AIOWPSecurity_Utility_Htaccess::write_to_htaccess() function is avoided.

= ******* =
- Fixed a typo with the newly added action hook - aiowps_before_wp_die_renamed_login

= 4.3.3 =
- Fixed bug - aiowps will now allow access to admin-post.php from front-end when rename login feature is active.
- Modified login lockdown feature so that the exact IP address is locked down and not the IP range.
- Added new filter (aiowps_ip_blocked_output_page) which allows user to filter the complete output when someone's IP has been locked out.
- Added new action hook (aiopws_before_wp_die_renamed_login) for the renamed login feature which fires just before the wp_die event which produces the "Not available" behaviour.
- Removed unused code.
- Modified get_user_ip_address to get the first IP address in cases where there are multiple comma separated addresses provided - example X-Forwarded-For.

= 4.3.2 =
- Added new IP address settings page which user the ability to configure which $_SERVER global the IP address will be retrieved from. (New setting found in WP Security >> Settings >> Advanced Settings)
- Fixed bug in .htaccess rules caused when 6G and IP blacklist firewall rules were simultaneously enabled.
- Fixed bug where captcha answer was being ignored on woocommerce login page.
- Added support for unlock requests made from woocomerce account login page when rename login feature is active.
- Added useful debug code for troubleshooting in the fake googlebot function.
- Some general code cleanup and improvement.
- Added code to prevent direct access data leaks.
- Added captcha settings for BBPress new topic form.
- Fixed minor bug in dashboard page when checking if htaccess rules applied.
- Added a check for Windows server installation in File Permissions feature - this feature is not applicable for Windows servers.
- Added check to display comment captcha only when user not logged in.

= 4.3.1 =
- Improved white list directives to cater for Apache 2.4 and earlier versions.
- Added 3 filters for the manual account registration approval email: aiowps_register_approval_email_subject, aiowps_register_approval_email_msg, aiowps_register_approval_email_from_name
- Added configuration option to allow custom firewall rules to be applied at beginning of all rules applied by aiowps.
- Changed record insertions to DB table aiowps_failed_logins to store the full IP address instead of IP range.

= 4.3.0 =
- Updated wp-security-rename-login-feature.php to include latest WordPress core changes.
- Added captcha for woocommerce login and registration forms.
- Fixed "mixed line endings" warnings for whois library.
- Moved DB cleanup task cron job from daily to hourly.
- Updated the reapply htaccess function so it doesn't create the header already sent error.

= 4.2.9 =
- Changed the parameter in current_user_can function to use an administrator capability instead of the "administrator" role name.
- Added some new hooks to the AIOWPSecurity_WP_Loaded_Tasks called aiowps_wp_loaded_tasks_start and aiowps_wp_loaded_tasks_end.
- Improved get_locked_ips() function and added $wpdb->prepare statement.
- Added more missing translation domain parameters for translatable strings in the rename login page.
- Deleted local copy of the Persian and Italian language files. These translations are available on translate.wordpress.org.
- Domain path and text domain added to plugin header.
- Changed the get_user_ip_address functions so that $_SERVER['REMOTE_ADDR'] is the primary method used to obtain IP address.
- Added enumeration block via REST API (wp >= 4.7)

= 4.2.8 =
- Improved "User Registration" feature to bypass the pending approval status for new users created in admin side.
- Fixed bug in whois library.
- Added translation domain parameter for translatable strings in the rename login page.
- Updated the chinese language file.

= 4.2.7 =
- The PHPWhois library updated to their latest version to include a security patch.

= 4.2.6 =
- Added new Login Lockdown whitelist feature which allows immunity for IP address or ranges from being locked by the lockdown feature.
- Fixed bug - Replaced date_i18n with current_time to prevent cases where some localizations produce foreign characters in date stamp output.
- Added a new feature to add Honeypot to the WordPress's user registration form (this can help reduce registration attempts by robots).
- Added "Export to CSV" buttons for 404 Event Logs, Account Activity Logs and Failed Login Records.
- Minor update to 6G rules.
- Minor spelling and wording fixes and changes.

= 4.2.5 =
- Fixed bug - added code which caters for mysql view definitions when DB prefix is changed.
- Fixed a typo in the user login security menu.
- Fixed storage of time stamp in lockdown table to match the local Wordpress server time and be consistent with the timestamp stored in the failed logins table.
- Prevent direct access to wp-security-core.php
- Updated the POT file.

= 4.2.4 =
- Fix error on block_ip_if_locked(), doesn't exit with a wp_user. This is needed for other plugins that create the $user (aka ldap auth plugins).
- Fix login error message for users with pending account approval.
- Wordpress 4.7 compatibility.

= 4.2.3 =
- Fixed bug when math captcha was displayed on Woocommerce registration page.
- Fixed login page bug for cases where email address and captcha are used to submit login form (thanks to @chesio for fix).
- Logs directory now contains a .htaccess file with proper deny directives.
- Small UX improvement: add for attribute to captcha label.
- Added check for IIS server in get_server_type function.

= 4.2.2 =
- Debug logger class improvements.
- Added a message in the debug settings area to state that the log files are reset on every plugin update.
- Always return an array from scan_dir_sort_date() to prevent PHP notices.
- Improvements for Automated DB backups filling up space - old backup file will be deleted first.
- Thanks to RIPS Analyzer for sending us the vulnerability report.

= 4.2.1 =
- Improve output of .htaccess to include <IfModule mod_rewrite.c> checks and RewriteEngine On directives.
- Fall back to default DB backup interval in case of invalid value.
- The aiowps_delete_backup_files() function will produce a debug log message on every call (to help with troubleshooting when needed).

= 4.2.0 =
- WPML plugin compatibility fix for the renamed admin login page feature.
- Fixed a few potential XSS vulnerabilities.

= 4.1.9 =
- Small improvement to the new "immediate blocking of specific usernames" feature.

= 4.1.8 =
- New feature to allow immediate blocking of specific usernames.
- Only activate copy (right-click) protection for non-admin users.
- Fixed bug where logout link in admin bar does not get updated on after the $_POST submit to reflect the new rename login setting.
- Fixed small bug in return_regularized_url function.
- Improvement/bug fix: When currently logged in user attempts to access renamed login page, redirect them to dashboard.
- Removed Spanish language files so they can be automatically pulled from WordPress.org.
- Drop unnecessary WHERE clause in some backend listings.
- Improvement: do not schedule a cronjob, if it is already scheduled.

= 4.1.7 =
- Added sanitisation for log file data in textarea.
- Disabled autocomplete for Captcha field.

= 4.1.6 =
- Added cleanup code for captcha string info transients.
- Minor change to the username label in the renamed login page to keep it inline with the standard WordPress login page.
- Fixed a potential vulnerability when viewing AIOWPS log files in the Dashboard menu. Thanks to Manuel LLOP for pointing this out.

= 4.1.5 =
- Fixed bug where username is an email and captcha was being ignored.
- Reduce memory footprint of database backup.
- Improvements: Make hard-coded strings localizable.
- Partial Apache 2.3 compatibility.
- Improved: Hide WP version number by replacing it with a hash. This way, WordPress version number is not exposed, but browser caching is not obscured by missing version numbers.

= 4.1.4 =
- Improved and tweaked the login captcha feature to avoid some issues people had with the last modification.
- Deleted reference to ini_get('safe_mode') to avoid fatal errors for newer versions of PHP where that setting has been totally removed.

= 4.1.3 =
- Added new checkbox for XMLRPC to disable only pingback methods but leave other XMLRPC functionality accessible. This will be useful for people who use Jetpack or Wordpress iOS or other apps.
- Updated the French language file.
- Fix: decbin doesn't add leading zero. Comparing empty strings return bad results.
- Fix: bugfix in the login captcha. Thanks to Sipke Mellema for pointing it out.

= 4.1.2 =
- Fixed bug introduced by last file change scanner code changes.
- Fixed bug in SPAM comment blocking functionality.
- Fixed fatal error case when Divi theme and front end lockout is enabled.

= 4.1.1 =
- Fixed Fatal error conflict between Rename Login feature and Yoast SEO and some themes when attempting to access wp-admin page directly.
- Added "Pending Approval" message when manual registration approval feature is enabled and a user registers.
- Fix (minor): No need to use strcmp to compare integer values.
- Updated and simplified wp-security-stop-users-enumeration.php for bug (thanks to @davidegiunchidiennea)
- Minor code cleanup (Thanks to @chesio for the following changes).
- File scanner codebase cleanup.
- Fix: properly report invalid email addresses in file scanner configuration.
- Code clean-up in AIOWPSecurity_Scan::do_file_change_scan() method.
- Tweak: Compare file scan data faster.

= 4.1.0 =
- Fixed bug in Maintenance menu page when trying to attach a media file to the message text box.
- Added a new filter (called "aiowps_ip_blocked_error_msg") which allows the modification of the error message displayed on the login page when an IP address has been blocked by the login lockdown feature.
- Updated French language translation. Thanks to Claude Ribaux for providing the translation files.
- Thanks to @chesio for making the following two changes.
- Replaced deprecated call to get_currentuserinfo() function.
- Minor code fixes in the backup class file.
- Fix: display correct (error) message when write_to_htaccess() fails.
- Tweak: database backup filename is more human-readable.
  Before: 24x7eg8l6i-database-backup-1463042767.zip
  After: database-backup-20160512-104607-24x7eg8l6i.zip

= 4.0.9 =
- Made file change scanner code more robust for cases when open_basedir restriction is in effect. (Thanks to Manuel Jeanne for pointing this out).
- Added code which will remove WordPress version info during CSS and JS script loading if you have the "Remove WP Generator Meta Info" option checked. (Thanks to aldemarcalazans for pointing this out).
- Fixed some potential SQL injection vulnerabilities. (Thanks to Julio Potier for pointing these out).
- Changed the feature category of blacklist manger from "Intermediate" to "Advanced".
- Tweak: Remove "@" from list of characters blocked by advanced character string filter. (Because it is often used in retina-ready images).
- Fix: Use home URL instead of site URL in lock notification email subject. Thanks to @chesio for fixing this.

= 4.0.8 =
- Added ability to identify IP addresses during user registration and option to block selected IPs.
- Added login form captcha functionality for sub-sites in a multi-site installation. (see the Brute Force menu)
- Fixed multi-site bug related to manual user-chosen DB prefix change.
- Added extra XSS protection inside admin menu pages for the "tab" query parameter.
- Added a note to the features that has the potential to lock you out if it doesn't work correctly on your site.
- Updated Brazil-Portuguese language file.
- Fixed issue with firewall custom rules being corrupted by magic quotes. Thanks to @chesio for fixing this.

= 4.0.7 =
- Added a new action hook "aiopws_before_set_404" which triggers just before the AIOWPS sets a 404. (handy for cases when rename login page is used which affects some themes when accessing "wp-admin" directly)
- Fixed some potential SQL injection vulnerabilities.
- Thanks to @chesio for submitting the following changes and applying the fixes.
- Sub-directory install fixes.
- Improve behavior of WP File Access tab.
- Fix invalid nesting of HTML elements.
- Do not block HTTP requests that contain "tag=" in query string.
- Option to enable the 6G firewall.

= 4.0.6 =
- Removed the viewing of contents of wp-config.php and .htaccess files in order to protect sensitive info.
- Fixed more potential XSS vulnerabilities in some other settings pages. (Once again many thanks to Erin Germ for pointing these out)

= 4.0.5 =
- Fixed some potential XSS vulnerability in the blacklist, file system and file change detection settings pages. (Many thanks to Erin Germ for pointing these out)

= 4.0.4 =
- Added new feature: Auto Block Spammer IPs. This feature will automatically and permanently block IP addresses which are linked to comment SPAM. (see SPAM Prevention -> Comment SPAM IP Monitoring tab)
- Added compatibility fix for the qTranslate-X plugin in the rename login page feature.
- Added ability to send to more than one email address for file change detection feature notification.
- Fixed bug in whois library when searching ARIN registry.
- Fixed the handling of display of longer IPV6 strings in dashboard summary table.
- Added hook for WooCommerce login form to display unlock button.
- Added Dutch language translation. Thanks to Jeroen van der Linde for providing the translation files.
- Typo fix in the "stop users enumeration" feature.

= 4.0.3 =
- Added urlencode to query strings in URLs to prevent unexpected behaviour. Thanks to @chesio for spotting the issue.
- Added new feature to stop users enumeration. Thanks to Davide Giunchi @davidegiunchidiennea for adding this.
- Added a more robust code for check_user_exists function. Thanks to Christian Carey.
- Added cron cleanup of the global meta table.
- Added a title in each of the admin interface menu.

= 4.0.2 =
- Added ability to enable/disable debug from the settings menu.
- Fixed bug related to using IP ranges in the whitelist settings.
- Added IPv6 support for the whitelist feature.
- Added check in file permissions feature for cases where wp-config.php may be located outside of root.
- Added wp cron DB cleanup events for various tables which may grow large over time.
- Changed firewall rule for proxy comment prevention to reflect suggestion made by Thomas O. in forum (https://wordpress.org/support/topic/high-server-cpu-with-proxy-login)
- Fixed CSS styling issue in admin pages for WordPrss 4.4

= 4.0.1 =
- Renamed the language files to match the new textdomain slug to fix the language translation bug.
- Fixed bug related to the rename login feature and force logout or logout expiry events.
- Applied fix for log being generated by events table DB insert.
- Corrected a function call to static version of display error msg.

= 4.0.0 =
- Updated text domain to match expected value for translate.wordpress.org translation system.
- Fixed bug related to multi-site user_roles not being updated for child sites.
- Fixed minor bug in rename login feature.
- Updated the Italian language file.

= 3.9.9 =
- Fixed an issue with the rename login page feature for WordPress 4.3
- Added esc_attr() sanitization to some of the relevant parameters
- Added the necessary changes to allow activation via wp-cli

= 3.9.8 =
- Added guard against possible XSS in the unlock request feature.

= 3.9.7 =
- Added new feature which allows custom .htaccess rules. (See "Custom Rules" tab in Firewall menu). You can now use this to add custom rules to block access to various resources on your site.
- Added a new feature to block access to the wp-content/debug.log file (WordPress creates this file if you enabled debug loggin option in the config file).
- Removed the "v" from version number of the plugin.
- Completed testing with WordPress 4.3.

= 3.9.6 =
- Added Rename Login page feature from the "Brute Force" menu to multisite sub-sites.
- Removed invalid "length" attribute from input element in captcha code.
- Fixed reset password feature whereby the URL which is sent out in the email for cases when rename login feature is enabled was not decoded properly.
- Corrected the check for boolean false if returned from wpdb query result.
- Added media button for wp editor in maintenance settings page.

= 3.9.5 =
- Fixed minor bug - IP addresses blocked due to '404' were not being listed in the display table.
- Updated the Russian language translation file.
- The automatic database table prefix generation value will use a-z characters only.
- Added esc_url sanitization to the add_query_arg/remove_query_arg function instances to prevent possible XSS.

= 3.9.4 =
- The sort order and orderby parameters now use a whitelisting approach for sanitization.

= 3.9.3 =
- Fixed the sort order not working in the 404 error logging and account activity page.

= 3.9.2 =
- Added a check for registration captcha feature to prevent errors when using another captcha plugin.
- Improved a few SQL statements.

= 3.9.1 =
- Added new "Force Logout" feature which will instantly force a certain user to be logged out of their session. (See the "Logged In Users" tab in User Login menu)
- Added more security protection for aiowps log files by creating .htaccess file and rules. AIOWPS log files can now only be viewed via dashboard menu, in new tab called "AIOWPS Logs". (NOTE:This security currently applies only for apache or similar servers)
- Added backticks to SQL statement for DB prefix change to help prevent errors.
- Added protection against possible SQL injection attacks.

= 3.9.0 =
- Added some robustness to the file-scan code.
- Added extra security to all relevant list table instances to prevent unlikely malicious deletion commands.
- Fixed the user agent part of the blacklist settings code to allow user-agents to be cleared upon saving.

= 3.8.9 =
- Fixed bug in the new feature which allows permanent blocking of IP addresses that create 404 events.
- Fixed minor bug for all instances where wpdb "prepare" was being used with order/orderby parameters.
- Fixed a possible open redirect vulnerability. Thanks to Sucuri for pointing it out.

= 3.8.8 =
- Added extra robustness and security for wp list table db commands by using wpdb "prepare" command.
- Fixed minor bug with undeclared variable in rename login feature page.

= 3.8.7 =
- Added an improvement for login lockdown feature - locked IP addresses will no longer be allowed to register.
- Added a "view" link for each account in the pending registration approval table list.
- Fixed 404 logging/lockout bug.
- Added ability to permanently block IP addresses from the 404 event list for both bulk and single cases.
- Added ability to do bulk temp blocking for IP addresses in 404 list.
- Fixed a minor bug with validate_ip_list function.

= 3.8.6 =
- DB cleanup cron event bug fixed.
- Added Swedish language translation. The translation was submitted by Tor-Björn Fjellner.
- Updated the Russian language translation file. Update submitted by Tor-Björn Fjellner.
- The events table will automatically be cleaned up so it only keeps the last 5000 entries. You can override it using a filter (if you wanted to).

= 3.8.5 =
- Added functionality to prevent the aiowps_events table from getting too large.
- Added file change scan summary inside the alert email.
- Fixed the unlock feature so that it works correctly when the Rename Login Page feature is active.
- Added a check in the list logged in users file to prevent error when get_transient returns false.

= 3.8.4 =
- Updated POT language file.
- Tweaked the function which retrieves the IP address to handle cases where traffic is coming from cloudflare
- The MySQL database will not be forced anymore at the time of creating the table. It also reads the characters set value from the system first.
- Applied fixes to prevent remotely exploitable vulnerabilities.

= 3.8.3 =
- Modified "Pingback Protection" .htaccess rules to prevent xmlrpc login attacks and to be compatible with more servers.
- Made improvements to ensure that the rename login and white list features can be used together.
- Added a check to force user to enter alphanumeric string for renamed login slug.
- Improved the turn_off_all_firewall_rules() and turn_off_all_security_features() functions so that they also handle the updating of the htaccess file.
- Added an alternative way to import settings via a text box (Thanks to Dave McHale). This is for people who might have issues using the config settings file uploader.
- Added fix to properly update options tables when changing DB prefix in multisite system.
- Greatly improved the Renamed Login Page feature by removing various potential vulnerabilities.
- Added an if statement check to fix bug with rename login page feature - special case where user had non permalink structure was not working correctly in some rare scenarios.
- Updated the Italian language file.
- Fixed bug regarding wp_mail malformed header when "From" string was empty due to "site title" not being set.
- Fixed bug in IP list validation function for blacklist feature.
- Removed strict filtering of IP addresses so as to allow internal IP address ranges.
- Added stripping of orderby and order query parameters in the plugin.
- Added search capability by IP address, URL or referer for the 404 events list table.

= 3.8.2 =
- Fixed a CSS issue with the honeypot feature.
- Fixed a call to the login action handler static function.

= 3.8.1 =
- Minor bug fix for the honeypot feature - loading of css style sheet was not occurring when main login page rendered.

= 3.8.0 =
- Improved deactivation and re-activation tasks - AIOWPS will now gracefully clean up the .htaccess rules when the plugin is deactivated.
- Tweaked code so that all login pages including custom ones will correctly load the CSS style sheet file needed for honeypot feature.
- Updated the Portuguese language translation.
- Fixed the copy protection feature so it doesn't interfere with iframes and shortcodes.
- The plugin will now work fine even if your wp-config.php file is outside the wordpress root folder.

= ******* =
- copy protection feature JS code improvement

= ******* =
- Added captcha functionality for custom login form which is produced by the WP function: wp_login_form()
- Fixed a minor bug with the copy protection feature's JavaScript code.
- Tweaked file change scan algorithm to help prevent getMTime fatal runtime errors.
- Added a link to the github repository in the readme.txt file for developers.

= 3.7.9 =
- Fixed a small bug related to the cookie test in the Cookie Based Brute Force feature.

= 3.7.8 =
- Added new feature called Login Honeypot which will help reduce brute force login attempts by robots. (This can be found in the Brute Force menu)
- Added new feature to prevent other sites from displaying your content via a frame or iframe. (This can be found in the Miscellaneous menu)
- Added captcha feature for BuddyPress registration form.
- Added a new filter for the site lockout message so it can be customized.
- Added a new filter for template include of the site lockout feature.
- Temporarily deactivated the "DB Scan" feature.

= 3.7.7 =
- Improved DB prefix change code to make it more robust.
- Fixed a minor bug for the Rename Login page feature.
- Added check when processing rename login page to see if maintenance (lockout) mode enabled. Plugin will now display lockout message instead of 404 page if site lockout enabled.
- Made the Cookie Based Brute Force Prevention feature more secure by introducing a 10 digit random suffix to the test cookie name.

= 3.7.6 =
- Added ability to insert captcha in WordPress Multi Site registration form.
- Added a condition around the management permission constant. This will allow users to define a custom capability for this plugin's admin side via the wp-config file. This was submitted by Samuel Aguilera.
- Fixed a bug with the hidden login page feature.
- Fixed a small settings bug with the "block fake google bot" feature.

= 3.7.5 =
- Added a new DB scan feature. Go to the "Scanner" menu to use this new feature.
- Added new settings import/export feature.
- Modified user accounts feature to alert administrator if one or both "admin" or "Admin" usernames are being used.
- Added Persian language translation. The translation was submitted by Amir Mousavi Pour (<EMAIL>).
- Small change to get_mysql_tables function to prevent fatal error when mysqli query is unsuccessful.
- Added Italian language translation. The translation was submitted by Marco Guglielmetti.

= 3.7.4 =
- Added a new feature to add copy protection for your front-end. You can find this feature under the "Miscellaneous" menu.
- Fixed comment captcha bug for multi-site. Now this feature can be activated/deactivated for subsites of a multisite installation.
- Added Hungarian language translation. The translation was submitted by Daniel Kocsis.
- Moved the custom login page feature's handling code to wp-loaded hook so other plugins that modify the login page can do their task before our one is triggered. This change was suggested by Mark Hudnall.
- Added German language translation. The translation was submitted by Manuel Fritsch.
- Updated the Brazilian language translation file.


= 3.7.3 =
- Added Brazilian language translation. The translation was submitted by Sergio Siqueira.
- Added two new action hooks for plugin activation and deactivation time.
- Improved the get_user_ip_address() function so it handles cases when multiple addresses are returned due to proxy.
- Fixed the mis-alignment of login page which was broken by WP3.9 when rename login feature is used.
- WordPress 3.9 compatibility

= 3.7.2 =
- Added a PHP Info section in the system info interface to show some important PHP details of the server.
- Added a filter to allow the user to have a custom translation in a place (which will be loaded instead of the default one from the plugin). This change was submitted by Samuel Aguilera.
- Replaced myslqi fetch_all method with fetch_assoc to cover cases where some servers do not have the correct mysql drivers.
- Added a new filter to allow manipulation of the htaccess rules from your custom code. The name of the filter is 'aiowps_htaccess_rules_before_writing'.
- Added a "Delete All 404 Event Logs" button to purge all 404 logs from DB
- Added code to automatically send an email to the registrant when an account has been manually "Approved" from the User Registration menu.

= 3.7.1 =
- Fixed a minor bug: dashboard link was pointing to the wrong tab for the "Logged In Users" tab.
- Fix a bug with the login page captcha. The captcha wansn't shown if the rename login page feature was enabled at the same time.

= 3.7 =
- Added new feature - 404 detection. This allows you to log 404 events and block selected IPs. This feature can be found in the Firewall menu.
- Added new dashboard info box to display number of blocked IP addresses in the lockout table.
- Fixed bug where user could not access login page when maintenance mode and rename login page features were both active.
- Tweaked the hotlinking .htaccess directives to cover both http and https.
- Fixed code to prevent mysql errors due to some variables not having default value in failed login and lockdown tables
- Replaced deprecated PHP function mysql_query with mysqli.
- Added language file for Spanish language. The Spanish translation was done by Samuel Montoya.
- Added code to hide the "DB Prefix" menu for the non-main sites in multi-site installation

= 3.6 =
- Added a new feature to prevent image hot-linking. (See the "Prevent Hotlinks" tab in the firewall menu)
- Added a check in the Rename Login Page feature to prevent people from setting the slug to "wp-admin"
- Fixed a small bug with Login Lockdown feature.

= 3.5.1 =
- Fixed a bug where the cookie-based brute force directives were not being deleted from the .htaccess file when the Rename Login Page feature was being activated.

= 3.5 =
- Added new feature which will Block Fake Googlebots from crawling your site. Check the Firewall menu for this new feature.
- Added code to prevent users from having both the Rename Login Page and Cookie-Based Brute Force features active at the same time.
- Added some useful info boxes in the dashboard: 1) to inform the user if the cookie based brute force or rename login page features are active, 2) last 5 logins to your site.
- Fixed minor bug with .htaccess backup feature.
- Updated the from email address value used for sending backups and file change notification. Thanks to @TheAssurer for the tip.
- Updated the warning message for the disable index view feature.


= 3.4 =
- Consolidated "Brute Force" features by moving all such features to the "Brute Force" menu.
- Improved the file change detection scan feature: Introduced a button allowing admin to view the file change results from the last scan and fixed small bug whereby the change detected flag was not being cleared for applicable cases.
- Fixed a small bug with "rename login page" (hide admin login) feature.
- Made wp-config.php and .htaccess file backups more secure. Thanks to @TheAssurer for the tip.
- Made the login code more robust by catering for cases where the "wp_login" action was not passing 2 parameters.

= 3.3 =
- Added a brand new brute force prevention feature - Rename Login Page. This feature can be found in the new menu item called "Brute Force".
- Modified the new unlock request feature so that the locked out user will only have to enter email address when they submit an unlock request.
- Replaced the deprecated PHP function "mysql_list_tables" with alternative code.
- Added warning message regarding WordPress iOS app when pingback protection feature in the firewall settings is active.
- Added Malware scan tab and information.
- Some minor html form and CSS corrections.

= 3.2 =
- Added new feature which allows users to generate an automated unlock request link via email when they get locked out because of the login lockdown feature.
- Added a check to ensure that user cannot enter 0 minutes in the Force Logout feature.
- Fixed translations so that various previously omitted strings can now be translated.
- Added a new filter before locking down a user's IP address - aiowps_before_lockdown.
- Generated a new translation (POT) file.

= 3.1 =
- Added a new feature that will allow you to add a captcha to the lost password form (useful if you are allowing user registration on your site).
- Added ability to specify a system log file in the "Host System Logs" tab of the "File System Security" menu
- Fixed a tab link bug. One link was going to the wrong menu tab.
- Updated the POT file of the plugin.

= 3.0 =
- Added a new feature which allows you to add captcha to the Wordpress user registration page.
- Added some more helpful comments and link to video tutorial in the brute force and white list features settings pages.

= 2.9 =
- Added new feature which automatically sets the status of newly registered wordpress user accounts to "pending" and allows manual approval by an administrator.
- Improved robustness of file change detection iteration code.
- WordPress 3.7 compatibility

= 2.8.1 =
- Improved the login captcha implementation
- Changed the management permission to manage_options

= 2.8 =
- Added a feature to insert a simple math captcha to the WordPress comment form (to reduce comment spam). Check the spam prevention menu for this new feature.
- Fixed a minor bug with bulk unlock/delete in user login menu
- Fixed a minor bug with math captcha logic.

= 2.7 =
- Added a simple math captcha functionality for the WP login page. This is another easy yet effective way to combat Brute Force Login Attacks. You can enable this new feature from the user login security menu.

= 2.6 =
- Added a new Login Whitelist feature. This feature enables you to specify one or more IP addresses in a special whitelist which will have access to your WP login page.
All other IP addresses trying to access your WP login page which are not in the whitelist will be automatically blocked.
- The IP address will also be included in the email that gets sent to the admin for the ip address lockout notification.
- Language file loading fix for Chinese language.
- Tweaked the code which creates a .htaccess file in the backup directory to ensure it gets run even if the directory already existed.
- Made DB backups more secure.
- Added more useful debug logs for .htaccess file manipulation failure scenarios.

= 2.5 =
- Added a new feature which will list the currently logged in users who have been active within the last 15 minutes.
- Added a new feature in settings menu which will disable all firewall rules and clear all applicable directives in the .htaccess file.
- Improved the way the wp-config.php file is handled when it contains an ending PHP tag "?>" (older sites that were using PHP4 earlier).

= 2.4 =
- Added new feature/checkbox which will instantly lockout IP address ranges which attempt to login with an invalid username.
- Fixed a bug in the Comment SPAM IP Monitoring page where trying to block one or more IPs was failing.
- Removed the word "config" from the list of bad query strings check (to add compatibility with a few more plugins)
- Added a notice in the dashboard menu to show you if there are any recent file changes that the plugin detected.
- Fixed bug with php File Editing feature. Code now also handles older style wp-config.php files which have the php end tag "?>"
- Fixed bug with "Disable All Security Features" button functionality. When clicked, this will now also make the appropriate changes to the .htacces and wp-config.php files if necessary.
- Changed the storage of backup files from the plugin's directory to the uploads directory. Also added a .htaccess file for security.
- Fixed the way user-agent strings were written to the .htacess file from the Blacklist feature. The code now will correctly identify and represent spaces and escaped chars.
- Fixed a bug related to sending backup to correct email address.

= 2.3 =
- Added new menu called Scanner with a new feature called File Change Detection. This feature will alert you if any files have changed, added or removed from your system.
- Fixed "Deny Bad Query Strings" rules to not break the ability to drag components in the WordPress "Appearance->Menus" page
- Fixed an activation time warning (on sites with WP_DEBUG option enabled)
- Re-implemented the wp-config.php file content backup feature. It now directly downloads the contents of the file to your computer.
- Multi-site enhancements: Suppressed access to configuration settings for features which are not allowed to be configured from subsites of multi-site installations.
- Fixed a bug with login lockdown feature.

= 2.2 =
- Added a new feature which will block some spambots from submitting comments.
- Moved Comment SPAM IP monitoring interface to the new "SPAM Prevention" menu.
- Fixed a bug with login lockdown feature for both multi and single site.
- Improved firewall feature for multi-site by making the "Firewall" menu available only for the main site and not the sub-sites.
- Added random prefix to backup file names.
- Fixed a bug for WP multi-site install where DB tables do not get created when new blog are created in the network.

= 2.1.1 =
- Fixed a version tagging issue.

= 2.1 =
- Fixed an issue with install time error on some sites for WordPress 3.6
- Fixed some WP Debug related errors for WordPress 3.6
- Replaced the deprecated $wpdb->escape() function calls with esc_sql() calls

= 2.0 =
- Fixed a bug for general DB backup functionality.
- Fixed multi-site DB backup - the plugin will now backup only the tables relevant for the sub-site in question.
- Added blank index.html files in various folders inside the plugin.
- Disabled the wp-config.php file backup feature until we find a more secure method of doing the backup.

= 1.9 =
- Added new WordPress PingBack Vulnerability Protection feature. This allows the user to prohibit access to the xmlrpc.php file in order to protect against certain vulnerabilities in the pingback functionality.
- Added a configuration item in the brute force login prevention feature to allow ajax functionality to work properly when this feature is enabled.
- Added a POT file for language translations.
- Made the DB Prefix feature more robust by adding a check to ensure that plugin can write to the wp-config.php file. This will prevent user from losing access to their site in cases where the system changed the prefix but not the entry in the wp-config.php file.
- Tightened the data validation for the cookie based brute force login feature to ensure that the user must enter a secret word which consists of alphanumeric characters.
- Added edit links to the user account list in the "User Accounts" menu.

= 1.8 =
- Moved the front end site lockout feature to a new menu called "Maintenance".
- Added a feature in the front-end lockout feature to allow people to specify their own message which will be displayed on the front-end to visitors who try to access the site when it is in lock out state.
- Fixed a bug in the front-end lockout feature by adding some checks which ensure that the admin will not get locked if the feature is still active and their login session expires or they log out.
- Added a widget in the dashboard menu to show the status of the "maintenance mode" feature.

= 1.7 =
- Added a new feature which is a password strength tool which calculates how easy it is for your chosen password to be cracked using a desktop PC and the appropriate SW. This tool should help you create strong passwords.
- Added a front-end general visitor lockout feature. This feature allows you to temporarily lock down the front end of your site while you do security investigation, site upgrades, tweaks etc.

= 1.6 =
- Added a new option in the cookie-based Brute Force Login Attack prevention feature to allow users to use this feature together with the WordPress's post/page password protection feature.
- Fixed a bug in the 5G firewall rules to so that the printed rules include the correct number of '\' characters.
- Fixed a minor bug in the "restore from backed up htaccess file" feature.
- Enhanced the "Save current wp-config.php file" feature so it will continue to work with all of the firewall rules active on the site.
- Added extra checks to account for some error scenarios caused on some servers when recursive file search is done.

= 1.5 =
- Added new feature - Cookie-based Brute Force Login Attack Prevention. Check under the "Firewall" menu for this new feature.
  This feature will stop hackers in their tracks when they try to access your wp-admin or login pages. This feature will secure your WordPress backend by enforcing the requirement that anybody trying to access these pages will require a special cookie.

- Fixed bug related to setting of default configuration for first-time plugin activation.

= 1.4 =
- Tweaked the "Deny Bad Query Strings" firewall rules so that plugin deletion and update operations from the WordPress plugins menu are not affected.
- Fixed a minor bug related to scheduled database backups.
- Added some extra default settings to be applied to the plugin's configuration pages upon activation for the first time.
- Plugin will now display a recommendation message if user sets scheduled backup frequency to less than 24 hours.

= 1.3 =
- Added a new feature to remove the WordPress Generator Meta information from the HTML source of your site.
- Tweaked the "Advanced Character String Filter" to fix issue which was affecting plugins such as "Admin Management Xtended" and also pages with keywords such as "password" in the URL.
- Updated one rule in the "Advanced Character String Filter" feature to make it compatible with W3 Total Cache Plugin's minify feature.
- Added a "Delete All Failed Login Records" option in the "Failed Login Records" tab. This will delete all entries in the failed logins table and will make it less tedious for users who get a lot of brute force attacks on their site.

= 1.2 =
- Moved the rules which disable index views from the "basic firewall" rules to the "additional rules" section. This will prevent any site breakage for
those who want to enable the basic firewall but do not have "AllowOverride" option enabled in their httpd.conf

= 1.1 =
- Added the following new feature:
- Prevent people from accessing the readme.html, license.txt and wp-config-sample.php files.

= 1.0 =
- First commit to the WP repository.

== Upgrade Notice ==
* 5.4.1: Multiple bug fixes. See changelog for full details. A recommended update for all.
