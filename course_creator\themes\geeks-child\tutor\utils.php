<?php
//Tutor LMS API para elementor
function get_course_topics($course_id) {
    $response = wp_remote_get('https://cursos.institutopanapana.org.br/wp-json/tutor/v1/course-topic/' . $course_id, array(
        'headers' => array(
            'Authorization' => 'Basic ' . base64_encode( 'key_7c01d7a59e38762337eb8cd05ed457cb' . ':' . 'secret_6a743d568b29fd6f34edc066b9fee75f15b54ea4a2e2984a9ae848002a52a050' )
        )
    ));
  
    if ( is_wp_error( $response ) ) {
        return false;
    }
  
    $body = wp_remote_retrieve_body( $response );
    $data = json_decode( $body );
  
    if ( empty( $data ) ) {
        return false;
    }
  
    return $data;
}

function display_course_topics($atts) {
    $atts = shortcode_atts( array(
        'id' => null,
    ), $atts );

    if ( null === $atts['id'] ) {
        return 'No course ID provided.';
    }

    $data = get_course_topics($atts['id']);
  
    if ( ! $data ) {
        return 'Could not retrieve data.';
    }
  
    $output = '<div class="course-topics">';

    foreach ( $data as $topic ) {
        $output .= '<div class="course-topic">';
        $output .= '<h2>' . esc_html( $topic ) . '</h2>';
        $output .= '</div>';
    }
  
    $output .= '</div>';
  
    return $output;
}
add_shortcode('course_topics', 'display_course_topics');

// function export_lessons_videos_csv() {
//     if (isset($_GET['export_lessons_videos_csv'])) {
//         global $wpdb;

//         $filename = 'lessons_videos.csv';
//         header('Content-Type: text/csv');
//         header('Content-Disposition: attachment; filename="' . $filename . '"');

//         $output = fopen('php://output', 'w');
//         fputcsv($output, ['Curso', 'Título da Aula', 'URL do Vídeo']);

//         // Obtém todos os posts do tipo 'courses'
//         $courses_args = [
//             'post_type' => 'courses',
//             'posts_per_page' => -1,
//         ];
//         $courses = get_posts($courses_args);

//         foreach ($courses as $course) {
//             $course_title = get_the_title($course->ID);
//             // Obtém todos os tópicos relacionados ao curso
//             $topics_args = [
//                 'post_type' => 'topics',
//                 'post_parent' => $course->ID,
//                 'posts_per_page' => -1,
//             ];
//             $topics = get_posts($topics_args);

//             foreach ($topics as $topic) {
//                 // Obtém todas as lessons relacionadas ao tópico
//                 $lessons_args = [
//                     'post_type' => 'lesson',
//                     'post_parent' => $topic->ID,
//                     'posts_per_page' => -1,
//                 ];
//                 $lessons = get_posts($lessons_args);

//                 foreach ($lessons as $lesson) {
//                     $lesson_title = get_the_title($lesson->ID);

//                     // Obtém o valor do meta _video
//                     $video_meta = get_post_meta($lesson->ID, '_video', true);
//                     $video_url = '';

//                     // Verifica se o vídeo é do tipo 'shortcode' ou 'youtube'
//                     if ($video_meta['source'] == 'youtube' && !empty($video_meta['source_youtube'])) {
//                         $video_url = $video_meta['source_youtube'];
//                     } elseif ($video_meta['source'] == 'shortcode' && !empty($video_meta['source_shortcode'])) {
//                         // Extrai o URL do vídeo do shortcode
//                         preg_match('/src="([^"]+)"/', $video_meta['source_shortcode'], $matches);
//                         if (!empty($matches[1])) {
//                             $video_url = $matches[1];
//                         }
//                     }

//                     if (!empty($video_url)) {
//                         fputcsv($output, [$course_title, $lesson_title, $video_url]);
//                     }
//                 }
//             }
//         }

//         fclose($output);
//         exit();
//     }
// }

// add_action('init', 'export_lessons_videos_csv');

// function download_lessons_videos_button_shortcode() {
//     $button = '<a href="' . esc_url(add_query_arg('export_lessons_videos_csv', 'true')) . '" class="button">Baixar URLs de Vídeos das Aulas</a>';
//     return $button;
// }

// add_shortcode('download_lessons_videos_button', 'download_lessons_videos_button_shortcode');
