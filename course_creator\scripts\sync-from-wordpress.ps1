# Sync From WordPress Script - Fixed Version
param(
    [string]$WordPressPath = ".."
)

Write-Host "Syncing changes from WordPress..." -ForegroundColor Green

# Check if WordPress theme exists
$SourcePath = "$WordPressPath\wp-content\themes\geeks-child"
if (-not (Test-Path $SourcePath)) {
    Write-Host "WordPress theme not found: $SourcePath" -ForegroundColor Red
    exit 1
}

# Check if repository theme directory exists
if (-not (Test-Path "themes\geeks-child")) {
    New-Item -ItemType Directory -Path "themes\geeks-child" -Force | Out-Null
    Write-Host "Created repository theme directory" -ForegroundColor Yellow
}

# Copy files from WordPress to repository
try {
    Copy-Item "$SourcePath\*" -Destination "themes\geeks-child\" -Recurse -Force
    Write-Host "Files synced successfully!" -ForegroundColor Green
} catch {
    Write-Host "Failed to sync files: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Sync Summary:" -ForegroundColor Cyan
Write-Host "All files and folders synced from WordPress to repository" -ForegroundColor Green

Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Cyan
Write-Host "1. Review changes: git diff" -ForegroundColor White
Write-Host "2. Add changes: git add ." -ForegroundColor White
Write-Host "3. Commit: git commit -m Description" -ForegroundColor White
Write-Host "4. Push: git push origin main" -ForegroundColor White
