<?php
/**
 * Plugin Name: Coordenador Painel Access Controller
 * Description: Painel personalizado para coordenadores com acesso restrito e exportação de progresso.
 * Version: 1.1
 * Author: Your Name
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Create custom dashboard page
function create_coordinator_dashboard() {
    add_menu_page(
        'Coordenador Painel',
        'Coordenador Painel',
        'read', // Base capability, specific checks done later
        'coordenador-dashboard',
        'render_coordinator_dashboard',
        'dashicons-dashboard',
        2
    );
}
add_action('admin_menu', 'create_coordinator_dashboard');

// Render the dashboard page with logout and export buttons
function render_coordinator_dashboard() {
    // Ensure user has the 'ifal' role OR is an Administrator to view this page
    $user = wp_get_current_user();
    if (!in_array('ifal', (array) $user->roles) && !current_user_can('administrator')) {
        wp_die('Acesso não permitido.');
    }

    $logout_url = wp_logout_url(home_url());
    ?>
    <div class="wrap">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h1>Coordenador Painel</h1>
            <a href="<?php echo esc_url($logout_url); ?>" class="button button-secondary">
                <span class="dashicons dashicons-exit" style="margin-top: 3px;"></span> Sair
            </a>
        </div>
        
        <div class="card" style="max-width: 800px; padding: 20px; margin-top: 20px;">
            <h2>Acesso Rápido</h2>
            <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin-top: 20px;">
                <a href="<?php echo admin_url('admin.php?page=tutor-students'); ?>" class="button button-primary" style="text-align: center; padding: 15px;">
                    <span class="dashicons dashicons-groups" style="display: block; margin: 0 auto 10px; font-size: 30px;"></span>
                    Alunos
                </a>
                <a href="<?php echo admin_url('admin.php?page=enrollments'); ?>" class="button button-primary" style="text-align: center; padding: 15px;">
                    <span class="dashicons dashicons-clipboard" style="display: block; margin: 0 auto 10px; font-size: 30px;"></span>
                    Inscrição
                </a>
                <a href="<?php echo admin_url('admin.php?page=tutor_report'); ?>" class="button button-primary" style="text-align: center; padding: 15px;">
                    <span class="dashicons dashicons-chart-area" style="display: block; margin: 0 auto 10px; font-size: 30px;"></span>
                    Relatórios
                </a>
                <a href="<?php echo site_url(); ?>" class="button button-secondary" style="text-align: center; padding: 15px;">
                    <span class="dashicons dashicons-admin-home" style="display: block; margin: 0 auto 10px; font-size: 30px;"></span>
                    Voltar ao Site
                </a>
            </div>
        </div>

    </div>
    <?php
}

// Hide admin menu items for coordinator
function hide_admin_menus_for_ifal() {
    // Only apply to 'ifal' role
    $user = wp_get_current_user();
    if (!in_array('ifal', (array) $user->roles)) return;
    
    // Menu items to keep (don't remove these)
    $keep_menus = array(
        'coordenador-dashboard', // Our custom dashboard
        'tutor-students', // Tutor students list
        'enrollments', // Tutor enrollments
        'tutor_report' // Tutor reports
        // Note: We might need the main 'tutor' slug if submenus depend on it
        // 'tutor' 
    );
    
    global $menu, $submenu;
    $new_menu = array();
    $new_submenu = array();

    // Filter top-level menus
    foreach ($menu as $key => $item) {
        foreach ($keep_menus as $keep) {
            // Check if the menu slug ($item[2]) contains any of the slugs we want to keep
            if (isset($item[2]) && strpos($item[2], $keep) !== false) {
                $new_menu[$key] = $item;
                // If we keep a parent menu, also keep its submenu items
                if (isset($submenu[$item[2]])) {
                    $new_submenu[$item[2]] = $submenu[$item[2]];
                }
                break; // Keep this item and move to the next menu item
            }
        }
    }
    
    // Explicitly keep the profile and separator menus if they exist
    foreach ($menu as $key => $item) {
         if (isset($item[2]) && ($item[2] == 'profile.php' || $item[1] == '' && strpos($item[0], 'wp-menu-separator') !== false)) {
             $new_menu[$key] = $item;
         }
    }

    // Reassign the filtered menus
    $menu = $new_menu;
    $submenu = $new_submenu;
}
add_action('admin_menu', 'hide_admin_menus_for_ifal', 9999);

// Hide admin bar completely for IFAL users
function hide_admin_bar_for_ifal($show) {
    if (!is_user_logged_in()) return $show;
    
    $user = wp_get_current_user();
    if (in_array('ifal', (array) $user->roles)) {
        return false; // Hide admin bar completely
    }
    
    return $show;
}
add_filter('show_admin_bar', 'hide_admin_bar_for_ifal');

// Hide WordPress notices, admin messages, etc. for IFAL users
function hide_wp_elements_for_ifal() {
    $user = wp_get_current_user();
    if (!in_array('ifal', (array) $user->roles)) return;
    ?>
    <style type="text/css">
        /* Hide WordPress footer */
        #wpfooter { display: none !important; }
        /* Hide admin bar */
        html.wp-toolbar { padding-top: 0 !important; }
        #wpadminbar { display: none !important; }
        /* More space at bottom */
        #wpbody-content { padding-bottom: 40px; }
        /* Hide notifications/ads */
        .notice, .updated, .update-nag, .update-message, .error, .warning,
        .info, .e-notice, .elementor-message, .wp-header-end, #message,
        .wp-core-ui .notice, div.error, div.updated,
        [class*="promo"], [class*="promotion"], [class*="ad-"], [class*="banner"],
        [id*="promo"], [id*="promotion"], [id*="ad-"], [id*="banner"],
        [data-notice_id*="promotion"], [data-notice_id*="experiment"],
        [data-notice_id*="welcome"], [data-notice_id*="promo"],
        .e-notice, .elementor-message, .elementor-panel-alert,
        div[class*="elementor"], div[id*="elementor"],
        .tutor-notice, .tutor-announcement, .tutor-setup-wizard-notice,
        .tutor-quiz-builder-modal-wrapper .tutor-pro-badge,
        .tutor-option-field p.desc span.tutor-pro-badge,
        .tutor-option-field p span.tutor-pro-badge,
        #screen-meta, #screen-meta-links, #contextual-help-wrap,
        #screen-options-link-wrap, #contextual-help-link-wrap,
        .welcome-panel {
            display: none !important;
        }
        /* Hide generic containers after H1/H2 that often contain notices */
        #wpbody-content > .wrap > h1 + div:not([class]),
        #wpbody-content > div.wrap > h1 + div:not([class]),
        #wpbody-content > div.wrap > h2 + div:not([class]) {
             display: none !important;
        }
        /* Customize the look and feel */
        .card {
            border-radius: 8px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }
        .button-primary {
            background-color: #2271b1 !important;
            border-color: #2271b1 !important;
        }
        /* Responsive fixes */
        @media (max-width: 782px) {
            #wpbody { padding-top: 0; }
        }
    </style>
    
    <script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function() {
        const removeSelectors = [
            '.notice', '.updated', '.update-nag', '.error', '.e-notice', 
            '.elementor-message', '[data-notice_id]', '#screen-meta', 
            '#screen-meta-links'
        ];
        const removeElements = () => {
            removeSelectors.forEach(selector => {
                document.querySelectorAll(selector).forEach(el => {
                    el.style.display = 'none';
                    // Optionally remove completely: el.remove(); 
                });
            });
        };
        removeElements();
        setTimeout(removeElements, 500); // Catch dynamically added ones
        const observer = new MutationObserver(removeElements);
        observer.observe(document.body, { childList: true, subtree: true });
    });
    </script>
    <?php
}
add_action('admin_head', 'hide_wp_elements_for_ifal');

// Disable admin notices action hooks for IFAL role users
function disable_admin_notices_for_ifal() {
    $user = wp_get_current_user();
    if (in_array('ifal', (array) $user->roles)) {
        // Be cautious removing *all* actions, might break things.
        // Instead of removing all, rely on CSS/JS hiding which is safer.
        // remove_all_actions('admin_notices');
        // remove_all_actions('all_admin_notices');
    }
}
add_action('admin_init', 'disable_admin_notices_for_ifal', 1);

// Redirect IFAL role to custom dashboard on login
function redirect_coordinator_on_login($redirect_to, $request, $user) {
    if (isset($user->roles) && is_array($user->roles) && in_array('ifal', $user->roles)) {
        return admin_url('admin.php?page=coordenador-dashboard');
    }
    return $redirect_to;
}
add_filter('login_redirect', 'redirect_coordinator_on_login', 10, 3);


// Add necessary capabilities to IFAL role on plugin activation
function add_tutor_capabilities_for_ifal() {
    $role = get_role('ifal');
    if ($role) {
        // Basic reading capability
        $role->add_cap('read'); 
        
        // Capabilities needed for the specific Tutor pages we allow
        // Check Tutor LMS documentation for the exact capabilities required for:
        // admin.php?page=tutor-students
        // admin.php?page=enrollments
        // admin.php?page=tutor_report
        // These might include things like 'manage_tutor_instructor', 'manage_tutor_student', 'view_tutor_report' etc.
        // Add them explicitly here. Example:
        // $role->add_cap('view_tutor_reports'); 
        // $role->add_cap('manage_tutor_students');
        // $role->add_cap('manage_tutor_enrollments');
        
        // Capability to trigger the export (custom)
        $role->add_cap('export_tutor_progress'); 
    }
}
register_activation_hook(__FILE__, 'add_tutor_capabilities_for_ifal');

// Remove capabilities on deactivation
function remove_tutor_capabilities_for_ifal() {
    $role = get_role('ifal');
    if ($role) {
        // Remove the capabilities we added
        // $role->remove_cap('view_tutor_reports');
        // $role->remove_cap('manage_tutor_students');
        // $role->remove_cap('manage_tutor_enrollments');
        $role->remove_cap('export_tutor_progress');
    }
}
register_deactivation_hook(__FILE__, 'remove_tutor_capabilities_for_ifal');

// Add submenu page for Student Progress
function add_student_progress_submenu_page() {
    add_submenu_page(
        'coordenador-dashboard',           // Parent slug
        'Progresso dos Alunos',            // Page title
        'Progresso dos Alunos',            // Menu title
        'manage_options',                  // Use a standard capability like 'manage_options' for menu visibility
        'coordenador-progresso-alunos',    // Menu slug
        'render_student_progress_page'     // Function to display the page
    );
}
add_action('admin_menu', 'add_student_progress_submenu_page');

// Enqueue Chart.js and our custom script for the progress page
function enqueue_progress_page_scripts($hook_suffix) {
    // Only load on our specific admin page (coordenador-painel_page_coordenador-progresso-alunos)
    // Find the correct hook suffix by inspecting the $hook_suffix variable on the page if needed
    if ('coordenador-painel_page_coordenador-progresso-alunos' !== $hook_suffix) {
        return;
    }

    // Enqueue Chart.js from CDN
    wp_enqueue_script('chartjs', 'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js', array(), '3.9.1', true);

    // Enqueue our custom script (we'll add inline script later, but good practice to enqueue a handle)
    wp_enqueue_script('coordenador-progress-charts', plugin_dir_url(__FILE__) . 'coordenador-charts.js', array('chartjs', 'jquery'), '1.0', true); // Assuming a JS file exists
    // Since we dont have a file, we will use wp_add_inline_script attached to chartjs handle below
}
add_action('admin_enqueue_scripts', 'enqueue_progress_page_scripts');

// Render the Student Progress page
function render_student_progress_page() {
    // Check capabilities again for direct access
    if (!current_user_can('export_tutor_progress') && !current_user_can('administrator')) {
         wp_die('You do not have sufficient permissions to view this page.', 'Permission Denied', array('response' => 403));
    }

    // Get the progress data
    $progress_data_result = get_all_student_progress_data();

    // --- Data Aggregation for Charts ---
    $chart_data = null;
    $total_unique_students = 0;
    $students_started_any = 0;
    $students_completed_any = 0;
    $error_message = null;

    if (isset($progress_data_result['error'])) {
        $error_message = $progress_data_result['error'];
    } elseif (empty($progress_data_result['data'])) {
        // No data, but not an error
    } else {
        $student_stats = []; // Store stats per unique student ID
        $all_data = $progress_data_result['data'];

        foreach ($all_data as $row) {
            $user_id = $row['user_id'];
            $progress = (int)$row['progress'];

            if (!isset($student_stats[$user_id])) {
                $student_stats[$user_id] = ['started' => false, 'completed' => false];
            }

            if ($progress > 0) {
                $student_stats[$user_id]['started'] = true;
            }
            if ($progress >= 100) { // Use >= 100 for completion
                $student_stats[$user_id]['completed'] = true;
            }
        }

        $total_unique_students = count($student_stats);

        foreach ($student_stats as $stats) {
            if ($stats['started']) {
                $students_started_any++;
            }
            if ($stats['completed']) {
                $students_completed_any++;
            }
        }

        // Prepare data specifically for the JS chart
        $students_in_progress_only = $students_started_any - $students_completed_any;
        $students_not_started = $total_unique_students - $students_started_any;

        $chart_data = [
            'completed' => $students_completed_any,
            'in_progress' => $students_in_progress_only > 0 ? $students_in_progress_only : 0, // Ensure non-negative
            'not_started' => $students_not_started > 0 ? $students_not_started : 0, // Ensure non-negative
        ];
    }
    // --- End Data Aggregation ---

    ?>
    <div class="wrap">
        <h1>Progresso dos Alunos</h1>

        <div style="margin-bottom: 20px;">
             <a href="<?php echo esc_url(wp_nonce_url(admin_url('admin-post.php?action=export_tutor_progress'), 'export_tutor_progress_nonce', 'export_nonce')); ?>" class="button button-primary">
                 <span class="dashicons dashicons-download" style="margin-top: 4px;"></span>
                 Exportar Progresso (CSV)
             </a>
        </div>

        <?php if ($error_message): ?>
            <div class="notice notice-error is-dismissible">
                <p><?php echo esc_html($error_message); ?></p>
            </div>
        <?php elseif ($total_unique_students === 0 && !isset($progress_data_result['error'])): ?>
            <div class="notice notice-info is-dismissible">
                <p>Nenhum dado de progresso de aluno encontrado para os roles especificados.</p>
            </div>
        <?php else: ?>

            <!-- Chart Area -->
            <h2>Visão Geral</h2>
            <div style="display: flex; gap: 20px; margin-bottom: 30px; flex-wrap: wrap;">
                <div style="flex: 1; min-width: 250px; max-width: 350px; padding: 20px; background: #fff; border: 1px solid #ccd0d4; box-shadow: 0 1px 1px rgba(0,0,0,.04);">
                    <h4>Status Geral (<?php echo $total_unique_students; ?> Alunos)</h4>
                    <?php if ($chart_data): ?>
                        <canvas id="studentCompletionPieChart"></canvas>
                    <?php else: ?>
                        <p>Não há dados suficientes para exibir o gráfico.</p>
                    <?php endif; ?>
                </div>
                 <div style="flex: 2; min-width: 300px; background: #fff; padding: 20px; border: 1px solid #ccd0d4; box-shadow: 0 1px 1px rgba(0,0,0,.04);">
                    <h4>Estatísticas Rápidas</h4>
                    <ul>
                        <li><strong>Total de Alunos Únicos:</strong> <?php echo $total_unique_students; ?></li>
                        <li><strong>Alunos que Iniciaram Algum Curso:</strong> <?php echo $students_started_any; ?></li>
                        <li><strong>Alunos que Completaram Algum Curso:</strong> <?php echo $students_completed_any; ?></li>
                    </ul>
                </div>
            </div>

            <!-- Table Area -->
            <h2>Dados Detalhados</h2>
            <style>
                /* Basic styling for the progress table */
                .wp-list-table { margin-top: 20px; }
                .wp-list-table th { font-weight: bold; }
            </style>
            <table class="wp-list-table widefat fixed striped table-view-list">
                <thead>
                    <tr>
                        <th scope="col">User ID</th>
                        <th scope="col">User Email</th>
                        <th scope="col">User Name</th>
                        <th scope="col">Course ID</th>
                        <th scope="col">Course Title</th>
                        <th scope="col">Progress (%)</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($progress_data_result['data'] as $row): ?>
                        <tr>
                            <td><?php echo esc_html($row['user_id']); ?></td>
                            <td><?php echo esc_html($row['user_email']); ?></td>
                            <td><?php echo esc_html($row['user_name']); ?></td>
                            <td><?php echo esc_html($row['course_id']); ?></td>
                            <td><?php echo esc_html($row['course_title']); ?></td>
                            <td><?php echo esc_html($row['progress']); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>

    </div>
    <?php

    // Add Inline JS for Chart if data is available
    if ($chart_data) {
        $chart_js = sprintf(
            "document.addEventListener('DOMContentLoaded', function() {
                const ctx = document.getElementById('studentCompletionPieChart').getContext('2d');
                const studentCompletionPieChart = new Chart(ctx, {
                    type: 'pie',
                    data: {
                        labels: ['Completaram', 'Em Progresso', 'Não Iniciaram'],
                        datasets: [{
                            label: 'Status do Aluno',
                            data: [%d, %d, %d],
                            backgroundColor: [
                                'rgba(75, 192, 192, 0.6)', // Completed (Greenish)
                                'rgba(255, 206, 86, 0.6)', // In Progress (Yellowish)
                                'rgba(255, 99, 132, 0.6)'  // Not Started (Reddish)
                            ],
                            borderColor: [
                                'rgba(75, 192, 192, 1)',
                                'rgba(255, 206, 86, 1)',
                                'rgba(255, 99, 132, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'top',
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        let label = context.label || '';
                                        if (label) {
                                            label += ': ';
                                        }
                                        if (context.parsed !== null) {
                                             label += context.parsed + ' aluno(s)';
                                        }
                                        return label;
                                    }
                                }
                            }
                        }
                    }
                });
            });",
            $chart_data['completed'],
            $chart_data['in_progress'],
            $chart_data['not_started']
        );
        // Attach the inline script to the Chart.js handle we enqueued earlier
        wp_add_inline_script('chartjs', $chart_js);
    }
}

// --- Refactored Data Fetching Function ---
/**
 * Fetches all student progress data based on specific roles.
 *
 * @return array An array containing 'data' with progress records or 'error' message.
 */
function get_all_student_progress_data() {
    $output_data = array();

    // Ensure Tutor Utils is available
    if (!function_exists('tutor_utils')) {
         return ['error' => 'Tutor LMS core functions are not available.'];
    }

    // Fetch Students (using specific 'aluno_...' roles found on the site)
    $students = get_users(array(
        'role__in' => [
            'aluno_design',
            'aluno_edio_de_vdeo',
            'aluno_marketing',
            'aluno_programacao',
            'aluno_wordpress',
            'alunos_ia',
            'test_role'
        ],
        'fields' => array('ID', 'display_name', 'user_email')
    ));

    if (empty($students)) {
        // Return empty data array instead of error if no students found matching criteria
        return ['data' => []];
    }

    // Loop through students and courses
    foreach ($students as $student) {
        $user_id = $student->ID;
        $enrolled_course_ids = tutor_utils()->get_enrolled_courses_ids_by_user($user_id);

        if (is_array($enrolled_course_ids) && !empty($enrolled_course_ids)) {
            foreach ($enrolled_course_ids as $course_id) {
                 if (empty($course_id) || !is_numeric($course_id) || get_post_type($course_id) !== tutor()->course_post_type) {
                     // Basic validation: Skip if ID is empty, not numeric, or not a tutor course
                     continue;
                 }

                 $course_title = get_the_title($course_id);
                 // Ensure progress is numeric, default to 0 if not
                 $progress_raw = tutor_utils()->get_course_completed_percent($course_id, $user_id);
                 $progress = is_numeric($progress_raw) ? round($progress_raw) : 0;

                 // Add row to output array
                 $output_data[] = array(
                     'user_id'      => $user_id,
                     'user_email'   => $student->user_email,
                     'user_name'    => $student->display_name,
                     'course_id'    => $course_id,
                     'course_title' => $course_title ? $course_title : 'N/A',
                     'progress'     => $progress
                 );
            }
        }
        // No enrollments found for this student, continue to next
    }

    return ['data' => $output_data]; // Return the structured data
}

// Export handler function
function handle_tutor_progress_export() {
    // 1. Verify Nonce
    if (!isset($_GET['export_nonce']) || !wp_verify_nonce(sanitize_key($_GET['export_nonce']), 'export_tutor_progress_nonce')) {
        wp_die('Invalid security token.', 'Security Check Failed', array('response' => 403));
    }

    // 2. Check User Capabilities
    if (!current_user_can('export_tutor_progress') && !current_user_can('administrator')) {
         wp_die('You do not have sufficient permissions to perform this action.', 'Permission Denied', array('response' => 403));
    }

    // 3. Get Progress Data using the refactored function
    $progress_data_result = get_all_student_progress_data();

    // Handle potential errors from data fetching
    if (isset($progress_data_result['error'])) {
        wp_die('Error fetching student progress data: ' . esc_html($progress_data_result['error']));
    }

    $progress_data = $progress_data_result['data'];

    // Check if there's data to export
    if (empty($progress_data)) {
        wp_die('No student progress data found to export.');
    }

    // 4. Prepare CSV Output
    $filename = 'student_course_progress_' . date('Y-m-d_H-i-s') . '.csv';
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    $output = fopen('php://output', 'w');

    // Add UTF-8 BOM for Excel compatibility
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

    // Header Row (Using keys from the first data row)
    fputcsv($output, array_keys($progress_data[0]));

    // 5. Loop through data and write to CSV
    foreach ($progress_data as $row) {
        fputcsv($output, $row);
    }

    // 6. Close stream and exit
    fclose($output);
    exit();
}
add_action('admin_post_export_tutor_progress', 'handle_tutor_progress_export');

// Note: The custom_tutor_page_loader function was removed as it's generally better
// to rely on correctly assigned capabilities via register_activation_hook rather than
// temporarily elevating privileges on page load, which can be a security risk.
// Ensure the 'ifal' role has the necessary Tutor LMS capabilities assigned via
// add_tutor_capabilities_for_ifal or a role editor plugin.