<?php
function aluno_ia_signup_form() {
	
    // CPF Validation function (can be potentially moved to a shared utils file later)
    function validaCPF($cpf) {
        $cpf = preg_replace('/\D/', '', $cpf);

        if (strlen($cpf) != 11) {
            return false;
        }

        if (preg_match('/(\d)\1{10}/', $cpf)) {
            return false;
        }

        for ($t = 9; $t < 11; $t++) {
            for ($d = 0, $c = 0; $c < $t; $c++) {
                $d += $cpf[$c] * (($t + 1) - $c);
            }
            $d = ((10 * $d) % 11) % 10;
            if ($cpf[$c] != $d) {
                return false;
            }
        }

        return true;
    }

    if (isset($_POST['submit'])) {
        $errors = [];

        // Basic Field Validations
        if (empty($_POST['nome'])) {
            $errors[] = 'Nome é obrigatório.';
        }

        if (empty($_POST['sobrenome'])) {
            $errors[] = 'Sobrenome é obrigatório.';
        }

        if (!filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Email inválido.';
        }

        if (empty($_POST['senha'])) {
            $errors[] = 'Senha é obrigatória.';
        }

        if ($_POST['senha'] !== $_POST['confirmar_senha']) {
            $errors[] = 'As senhas não coincidem.';
        }

        // CPF Validation
        if (!validaCPF($_POST['cpf'])) {
            $errors[] = 'CPF inválido.';
        }

        // CPF Uniqueness Check
        $cpf_meta_query = new WP_User_Query([
            'meta_key' => 'cpf',
            'meta_value' => $_POST['cpf'],
        ]);
        if (!empty($cpf_meta_query->get_results())) {
            $errors[] = 'CPF já cadastrado.';
        }

        // Date of Birth Validation
        if (empty($_POST['data_nascimento'])) {
            $errors[] = 'Data de Nascimento é obrigatória.';
        }

        // Phone Validation
        if (empty($_POST['telefone']) || !preg_match('/^\(\d{2}\) \d{5}-\d{4}$/', $_POST['telefone'])) {
            $errors[] = 'Telefone inválido.';
        }
        
        // School Name Validation
        if (empty($_POST['escola'])) {
            $errors[] = 'Nome da Escola é obrigatório.'; // Updated label
        }

        // Display Errors if any
        echo '<ul class="errors" id="aluno-ia-signup-errors" style="margin-bottom: 15px;">'; // Added margin-bottom for spacing
        if (!empty($errors)) {
            foreach ($errors as $error) {
                echo '<li class="erro">' . esc_html($error) . '</li>'; // Use esc_html for security
            }
            echo '<script>window.location.hash = "#signup-form-ia";</script>'; // Use unique hash
        }
        echo '</ul>';

        // Process if no errors
        if (empty($errors)) {
            $user_id = wp_create_user($_POST['email'], $_POST['senha'], $_POST['email']);

            if (!is_wp_error($user_id)) {
                // Update User Meta
                update_user_meta($user_id, 'first_name', sanitize_text_field($_POST['nome'])); // Use standard WP first_name
                update_user_meta($user_id, 'last_name', sanitize_text_field($_POST['sobrenome'])); // Use standard WP last_name
                wp_update_user( array( 'ID' => $user_id, 'display_name' => sanitize_text_field($_POST['nome']) . ' ' . sanitize_text_field($_POST['sobrenome']) ) ); // Set display name
                update_user_meta($user_id, 'cpf', sanitize_text_field($_POST['cpf']));
                update_user_meta($user_id, 'data_nascimento', sanitize_text_field($_POST['data_nascimento']));
                update_user_meta($user_id, 'telefone', sanitize_text_field($_POST['telefone']));
                update_user_meta($user_id, 'escola', sanitize_text_field($_POST['escola'])); // Keep 'escola' key for consistency

                // Assign Role
                $user = new WP_User($user_id);
                $user->set_role('alunos_ia'); // Assign the new role
				
                // Send Welcome Email (Consider updating content later)
                $to = $_POST['email'];
                $subject = 'Sua Inscrição no Curso de IA - Panapaná'; // Updated Subject
                $headers = [
                    'Content-Type: text/html; charset=UTF-8',
                    'From: Instituto Panapaná <<EMAIL>>'
                ];
                // Reusing existing email structure, content might need adjustment
                $message = '<html><body>';
                $message .= '<table style="width: 100%; max-width: 600px; margin: 0 auto; border-collapse: collapse;">';
                $message .= '<tr><td style="background-color: #f3f3f3; padding: 20px; text-align: center;">';
                $message .= '<img style="max-width: 150px; height: auto;" src="https://cursos.institutopanapana.org.br/wp-content/uploads/2023/06/LOGO-insituto-panapana-org-hr.png" alt="Instituto Panapaná Logo">';
                $message .= '</td></tr>';
                $message .= '<tr><td style="background-color: #ffffff; padding: 20px; font-family: Arial, sans-serif;">';
                $message .= '<h1 style="font-size: 24px; color: #333333;">Inscrição Confirmada!</h1>';
                $message .= '<p style="font-size: 16px; color: #333333;">Olá, ' . sanitize_text_field($_POST['nome']) . ',</p>';
                $message .= '<p style="font-size: 16px; color: #333333;">Sua inscrição foi realizada com sucesso!</p>';
                $message .= '<p style="font-size: 16px; color: #333333;">Para acessar a plataforma, utilize os dados cadastrados:</p>';
                $message .= '<p style="font-size: 16px; color: #333333;"><strong>Usuário:</strong> ' . sanitize_text_field($_POST['email']) . '</p>';
                $message .= '<p style="font-size: 16px; color: #333333;">Se precisar redefinir sua senha, clique aqui: <a href="https://cursos.institutopanapana.org.br/password-reset/" style="color: #ff7300; text-decoration: none;">redefinir senha</a></p>';
                $message .= '<p style="font-size: 16px; color: #333333;">Acesse a plataforma e comece a aprender:<br><br><a href="https://cursos.institutopanapana.org.br/login" style="background-color: #ff7300; padding:8px 48px; text-decoration: none;color: white;border-radius: 6px;font-weight: bold; margin: 12px 0px;">Acessar Plataforma</a></p>';
                 $message .= '<p style="font-size: 16px; color: #333333;">Estamos muito felizes em ter você conosco!</p>';
                $message .= '<p style="font-size: 16px; color: #333333;">Até breve,</p>';
                $message .= '<p style="font-size: 16px; color: #333333;">Equipe Instituto Panapaná</p>';
                $message .= '</td></tr>';
                $message .= '</table>';
                $message .= '</body></html>';

                wp_mail($to, $subject, $message, $headers);
				
                // Attempt Login
                $credentials = array(
                    'user_login' => $_POST['email'],
                    'user_password' => $_POST['senha'],
                    'remember' => true,
                );
				
                $user_signon = wp_signon($credentials, false);

                // Redirect on successful login - Handled by wp_login hook in auth/utils.php now
                if (!is_wp_error($user_signon)) {
                    // $redirect_url = '/minha-formacao/';
                    // error_log('[Aluno IA Signup Debug] Login successful. Preparing to redirect to: ' . $redirect_url);
                    // wp_redirect($redirect_url);
                    // error_log('[Aluno IA Signup Debug] wp_redirect() called. Calling exit.');
                    // exit;
                    // Intentionally no redirect here anymore, let the wp_login hook handle it.
                    // Need to exit script execution though, to prevent rendering the form again below.
                    exit; 
                } else {
                    // Handle login error (rare after successful creation/password set)
                    $errors[] = $user_signon->get_error_message();
                    echo '<ul class="errors" id="aluno-ia-signup-errors">';
                    foreach ($errors as $error) {
                        echo '<li class="erro">' . $error . '</li>';
                    }
                    echo '</ul>';
                    echo '<script>window.location.hash = "#signup-form-ia";</script>';
                }
            } else {
                // Handle user creation error
                $errors[] = $user_id->get_error_message();
                echo '<ul class="errors" id="aluno-ia-signup-errors">';
                foreach ($errors as $error) {
                    echo '<li class="erro">' . $error . '</li>';
                }
                echo '</ul>';
                echo '<script>window.location.hash = "#signup-form-ia";</script>';
            }
        }
    }

    // Start Form HTML Output
    ob_start();

    // Always render the error container so JS can target it
    echo '<ul class="errors" id="aluno-ia-signup-errors" style="margin-bottom: 15px;">'; // Added margin-bottom for spacing
    if (isset($_POST['submit']) && !empty($errors)) {
        foreach ($errors as $error) {
            echo '<li class="erro">' . esc_html($error) . '</li>'; // Use esc_html for security
        }
        // Scroll logic remains conditional
        echo '<script>window.location.hash = "#signup-form-ia";</script>';
    }
    echo '</ul>';

    ?>

    <style>
        /* Reusing styles from application_form, potentially move to shared CSS */
        .flex-col {
            display: flex;
            flex-direction: column;
        }

        .flex-row {
            display: flex;
            flex-direction: row;
            gap: 10px;
        }

        label {
            display: block;
            font-weight: bold;
            font-size: 1em;
            margin-bottom: 5px;
            color: white;
            margin-top: 14px;
        }

        input, select {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
            background-color: white;
            border: solid 1px white;
            border-radius: 4px;
            color: black;
        }

        option {
            color: black;
        }

        .half-width {
            width: 50%;
        }
         .full-width {
            width: 100%;
        }

        .errors {
            /* Styles always applied */
            list-style: none;
            border-radius: 4px;
            /* Removed background, color, padding from here */
            margin-bottom: 15px; /* Keep margin */
        }

        .errors:not(:empty) {
            /* Styles applied only when NOT empty */
            background-color: red;
            color: white;
            padding: 10px;
        }

         .errors li {
             margin-bottom: 5px;
         }

        /* Ensure form background is transparent if needed */
        .elementor-widget-container {
            background-color: transparent!important;
        }
        
        /* Style submit button */
        #signup-form-ia input[type="submit"] { /* Scope to this form's ID */
            background-color: #ff7300;
            height: 48px;
            color: #fff;
            width: 100%;
            margin-top: 30px; /* From Elementor CSS */
            font-size: 20px;
            line-height: normal;
            font-family: 'montserrat', sans-serif; /* From Elementor CSS */
            text-transform: uppercase; /* From Elementor CSS */
            font-weight: 800; /* From Elementor CSS */
            border: none;
            padding: 12px 20px; /* Kept padding */
            border-radius: 4px;
            cursor: pointer;
            transition: transform 0.3s ease-in-out; /* From Elementor CSS */
        }
        #signup-form-ia input[type="submit"]:hover {
            background-color: #e66900; /* Keep slight darken on hover */
            transform: scale(1.05); /* From Elementor CSS */
        }

    </style>

    <form method="POST" class="flex-col" id="signup-form-ia">  <?php // Unique ID ?>
        <div class="flex-row">
            <div class="half-width">
                <label for="nome">Nome</label>
                <input type="text" id="nome" name="nome" value="<?php echo isset($_POST['nome']) ? esc_attr($_POST['nome']) : ''; ?>" required>
            </div>
            <div class="half-width">
                <label for="sobrenome">Sobrenome</label>
                <input type="text" id="sobrenome" name="sobrenome" value="<?php echo isset($_POST['sobrenome']) ? esc_attr($_POST['sobrenome']) : ''; ?>" required>
            </div>
        </div>

        <div class="flex-row">
            <div class="half-width">
                <label for="telefone">Telefone Whatsapp</label>
                <input type="text" id="telefone" name="telefone" value="<?php echo isset($_POST['telefone']) ? esc_attr($_POST['telefone']) : ''; ?>" required>
            </div>
            <div class="half-width">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" value="<?php echo isset($_POST['email']) ? esc_attr($_POST['email']) : ''; ?>" required>
            </div>
        </div>

        <div class="flex-row">
            <div class="half-width">
                <label for="senha">Senha</label>
                <input type="password" id="senha" name="senha" required>
            </div>
            <div class="half-width">
                <label for="confirmar_senha">Confirmação de Senha</label>
                <input type="password" id="confirmar_senha" name="confirmar_senha" required>
            </div>
        </div>

        <div class="flex-row">
            <div class="half-width">
                <label for="cpf">CPF</label>
                <input type="text" id="cpf" name="cpf" value="<?php echo isset($_POST['cpf']) ? esc_attr($_POST['cpf']) : ''; ?>" required>
            </div>
            <div class="half-width">
                <label for="data_nascimento">Data de Nascimento</label>
                <?php
                $display_date = '';
                if (isset($_POST['data_nascimento'])) {
                    $date_value = $_POST['data_nascimento'];
                    // Check if it's in YYYY-MM-DD format (from previous JS submission attempt)
                    if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $date_value)) {
                        $date_parts = explode('-', $date_value);
                        // Reconstruct as DD/MM/YYYY for display
                        if (count($date_parts) === 3) {
                             $display_date = $date_parts[2] . '/' . $date_parts[1] . '/' . $date_parts[0];
                        } else {
                            $display_date = $date_value; // Fallback to original value if explode failed unexpectedly
                        }
                    } else {
                        // Assume it's already DD/MM/YYYY or some other format user entered
                        $display_date = $date_value;
                    }
                }
                ?>
                <input type="text" id="data_nascimento" name="data_nascimento" placeholder="dd/mm/yyyy" value="<?php echo esc_attr($display_date); ?>" required>
            </div>
        </div>

        <?php // Address and Bairro fields removed ?>

        <div class="flex-row">
            <div class="full-width"> <?php // Make school full width ?>
                <label for="escola">Nome da Escola</label> <?php // Updated label ?>
                <input type="text" id="escola" name="escola" value="<?php echo isset($_POST['escola']) ? esc_attr($_POST['escola']) : ''; ?>" required>
            </div>
        </div>

        <input type="submit" name="submit" value="Cadastrar">
    </form>

    <script>
        // Reusing scripts from application_form, potentially move to shared JS file
        function formatCPF(cpf) {
            cpf = cpf.replace(/\D/g, '');
            cpf = cpf.substring(0, 11);
            cpf = cpf.replace(/(\d{3})(\d)/, '$1.$2');
            cpf = cpf.replace(/(\d{3})(\d)/, '$1.$2');
            cpf = cpf.replace(/(\d{3})(\d{1,2})$/, '$1-$2');
            return cpf;
        }

        function formatPhone(phone) {
            phone = phone.replace(/\D/g, '');
            phone = phone.substring(0, 11);
            phone = phone.replace(/(\d{2})(\d)/, '($1) $2');
            phone = phone.replace(/(\d{5})(\d{4})$/, '$1-$2');
            return phone;
        }

        function validateCPF(cpf) {
            cpf = cpf.replace(/\D/g, '');
            if (cpf.length !== 11 || /^(\d)\1+$/.test(cpf)) return false;
            let soma = 0, resto;
            for (let i = 1; i <= 9; i++) soma += parseInt(cpf.substring(i-1, i)) * (11 - i);
            resto = (soma * 10) % 11;
            if ((resto === 10) || (resto === 11)) resto = 0;
            if (resto !== parseInt(cpf.substring(9, 10))) return false;
            soma = 0;
            for (let i = 1; i <= 10; i++) soma += parseInt(cpf.substring(i-1, i)) * (12 - i);
            resto = (soma * 10) % 11;
            if ((resto === 10) || (resto === 11)) resto = 0;
            return resto === parseInt(cpf.substring(10, 11));
        }

        function displayError(targetId, message) {
            // Target the UL by its specific ID
            const errorList = document.querySelector('#aluno-ia-signup-errors'); 
             if (!errorList) { 
                 console.error("Error container #aluno-ia-signup-errors not found!"); // Add log if container missing
                 return; 
             }
            // Clear previous errors for this specific message if needed, or just append
            const errorItem = document.createElement('li');
            errorItem.classList.add('erro');
            errorItem.textContent = message;
            errorList.appendChild(errorItem);
        }
         function clearErrors() {
             // Target the specific error list by ID
             const errorList = document.querySelector('#aluno-ia-signup-errors'); 
             if (errorList) {
                 errorList.innerHTML = '';
             }
        }

        // Add event listeners for formatting
        const cpfInput = document.getElementById('cpf');
        if (cpfInput) {
            cpfInput.addEventListener('input', function (e) {
                e.target.value = formatCPF(e.target.value);
            });
        }

        const phoneInput = document.getElementById('telefone');
        if (phoneInput) {
            phoneInput.addEventListener('input', function (e) {
                e.target.value = formatPhone(e.target.value);
            });
        }

        // --- New function for date formatting ---
        function formatDateInput(input) {
            let value = input.value.replace(/\D/g, ''); // Remove non-digits
            let formattedValue = '';

            if (value.length > 0) {
                formattedValue += value.substring(0, 2);
            }
            if (value.length > 2) {
                formattedValue += '/' + value.substring(2, 4);
            }
            if (value.length > 4) {
                formattedValue += '/' + value.substring(4, 8);
            }
            input.value = formattedValue;
        }
        // --- End date formatting function ---

        // --- Add event listener for date formatting ---
        const dateInput = document.getElementById('data_nascimento');
        if (dateInput) {
            dateInput.addEventListener('input', function(e) {
                formatDateInput(e.target);
            });
        }
        // --- End date formatting listener ---

        // Add event listener for form submission validation
        const signupForm = document.getElementById('signup-form-ia');
        if (signupForm) {
            signupForm.addEventListener('submit', function (e) {
                console.log('Form submit handler started.'); // DEBUG
                clearErrors(); // Clear previous errors
                let hasErrors = false;

                // Get field values
                const cpf = cpfInput ? cpfInput.value : '';
                const telefone = phoneInput ? phoneInput.value : '';
                const senha = document.getElementById('senha') ? document.getElementById('senha').value : '';
                const confirmarSenha = document.getElementById('confirmar_senha') ? document.getElementById('confirmar_senha').value : '';
                const dataNascimento = dateInput ? dateInput.value : ''; // Get date value
                console.log('Initial values:', { cpf, telefone, dataNascimento }); // DEBUG

                // --- Start Validations --- 
                console.log('Starting validations...'); // DEBUG
                if (!validateCPF(cpf)) {
                    displayError('cpf', 'CPF inválido.');
                    hasErrors = true;
                    console.log('CPF validation failed.'); // DEBUG
                }

                if (!/^\(\d{2}\) \d{5}-\d{4}$/.test(telefone)) {
                    displayError('telefone', 'Telefone inválido. Formato esperado: (XX) XXXXX-XXXX');
                     hasErrors = true;
                     console.log('Telefone validation failed.'); // DEBUG
                }

                if (senha !== confirmarSenha){
                    displayError('confirmar_senha', 'A confirmação de senha deve ser igual à senha.');
                    hasErrors = true;
                    console.log('Password match validation failed.'); // DEBUG
                }
                
                // Date Format Validation and Conversion
                const datePattern = /^(\d{2})\/(\d{2})\/(\d{4})$/;
                if (!datePattern.test(dataNascimento)) {
                    displayError('data_nascimento', 'Data de Nascimento inválida. Formato esperado: dd/mm/yyyy');
                    hasErrors = true;
                    console.log('Date format validation failed.'); // DEBUG
                } else {
                    // Optional: Basic reality check
                    console.log('Date format OK, performing basic check...'); // DEBUG
                    const parts = dataNascimento.match(datePattern);
                    const day = parseInt(parts[1], 10);
                    const month = parseInt(parts[2], 10);
                    const year = parseInt(parts[3], 10);
                    if (month < 1 || month > 12 || day < 1 || day > 31) { 
                         displayError('data_nascimento', 'Data de Nascimento inválida (mês ou dia incorreto).');
                         hasErrors = true;
                         console.log('Date basic check failed (invalid day/month).'); // DEBUG
                    }
                    // Add more checks if needed
                }
                // --- End Validations --- 

                console.log(`Validation complete. hasErrors = ${hasErrors}`); // DEBUG

                // Prevent submission if errors found
                if (hasErrors) {
                    console.log('Errors found. Preventing form submission.'); // DEBUG
                    e.preventDefault();
                    window.location.hash = '#signup-form-ia'; // Scroll to form
                } else {
                    // Convert date to yyyy-mm-dd before submit
                    console.log('No errors found. Attempting date conversion...'); // DEBUG
                    const parts = dataNascimento.match(datePattern);
                    if (parts) {
                        const isoDate = `${parts[3]}-${parts[2].padStart(2, '0')}-${parts[1].padStart(2, '0')}`; // Convert dd/mm/yyyy to yyyy-mm-dd (added padding)
                        console.log(`Original date: ${dataNascimento}, Converted date (isoDate): ${isoDate}`); // DEBUG
                        dateInput.value = isoDate; // Update the input value right before submission
                        console.log('Date converted and input value updated. Allowing submission...'); // DEBUG
                    } else {
                        // Should not happen if validation passed, but good safety check
                        console.error('Error: Could not parse date parts for conversion even though validation passed.'); // DEBUG
                        e.preventDefault(); 
                        displayError('data_nascimento', 'Erro ao processar data de nascimento.');
                        window.location.hash = '#signup-form-ia';
                    }
                    // End Date Conversion
                }
                console.log('Form submit handler finished.'); // DEBUG
            });
        }
    </script>

    <?php
    return ob_get_clean();
}
add_shortcode('aluno_ia_signup', 'aluno_ia_signup_form'); // Register the new shortcode 