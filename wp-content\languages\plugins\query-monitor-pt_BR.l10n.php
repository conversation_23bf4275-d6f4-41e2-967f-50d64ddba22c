<?php
return ['x-generator'=>'GlotPress/4.0.1','translation-revision-date'=>'2025-05-26 07:55:15+0000','plural-forms'=>'nplurals=2; plural=n > 1;','project-id-version'=>'Plugins - Query Monitor &#8211; The developer tools panel for WordPress - Stable (latest release)','language'=>'pt_BR','messages'=>['Basic Auth'=>'Autenticação básica','Module'=>'Módulo','Assertion failed'=>'Falha na verificação','Assertion failed: %s'=>'Falha na verificação: %s','Assertion passed'=>'Verificação aprovada','Assertion passed: %s'=>'Verificação aprovada: %s','empty string'=>'string vazia','Drop-in: %s'=>'Avançado: %s','Hooks, Actions, & Filters'=>'Ganchos, ações e filtros','Development Mode'=>'Modo de desenvolvimento','Total Doing it Wrong occurrencesTotal: %s'=>'Total: %s','No occurrences.'=>'Nenhuma ocorrência.','Doing it WrongDoing it Wrong (%s)'=>'Fazendo errado (%s)','Doing it Wrong occurrences'=>'Ocorrências de “Fazendo errado”','Doing it WrongTotal: %s'=>'Total: %s','Doing it Wrong'=>'Fazendo errado','The file link format for your editor is set by the %s filter.'=>'O formato do link do arquivo para o seu editor é definido pelo filtro %s.','Hook %1$s is deprecated since version %2$s with no alternative available. %3$s'=>'O gancho %1$s está obsoleto desde a versão %2$s e não há alternativa disponível. %3$s','Hook %1$s is deprecated since version %2$s! Use %3$s instead. %4$s'=>'O gancho %1$s está obsoleto desde a versão %2$s! Use %3$s no seu lugar. %4$s','Function %1$s was called with an argument that is deprecated since version %2$s with no alternative available.'=>'A função %1$s foi chamada com um argumento que está obsoleto desde a versão %2$s e não há alternativa disponível.','Function %1$s was called with an argument that is deprecated since version %2$s! %3$s'=>'A função %1$s foi chamada com um argumento que está obsoleto desde a versão %2$s! %3$s','File %1$s is deprecated since version %2$s with no alternative available. %3$s'=>'O arquivo %1$s está obsoleto desde a versão %2$s e não há alternativa disponível. %3$s','File %1$s is deprecated since version %2$s! Use %3$s instead. %4$s'=>'O arquivo %1$s está obsoleto desde a versão %2$s! Use %3$s no seu lugar. %4$s','The called constructor method for %1$s class in %2$s is deprecated since version %3$s! Use %4$s instead.'=>'O método construtor chamado para a classe %1$s em %2$s está obsoleto desde a versão %3$s! Use %4$s no seu lugar.','The called constructor method for %1$s class is deprecated since version %2$s! Use %3$s instead.'=>'O método construtor chamado para a classe %1$s está obsoleto desde a versão %2$s! Use %3$s no seu lugar.','Function %1$s is deprecated since version %2$s! Use %3$s instead.'=>'A função %1$s está obsoleta desde a versão %2$s! Use %3$s no seu lugar.','Function %1$s is deprecated since version %2$s with no alternative available.'=>'A função %1$s está obsoleta desde a versão %2$s e não há alternativa disponível.','Function %1$s was called incorrectly. %2$s %3$s'=>'A função %1$s foi chamada incorretamente. %2$s %3$s','(This message was added in version %s.)'=>'(Esta mensagem foi adicionada na versão %s.)','PHP error countTotal: %s'=>'Total: %s' . "\0" . 'Total: %s','Block Template'=>'Modelo de bloco','Timings'=>'Tempos','No data logged. <a href="%s">Read about timing and profiling in Query Monitor</a>.'=>'Nenhum dado registrado. <a href="%s">Leia sobre tempo e criação de perfil no Query Monitor</a>.','Site Switch'=>'Alternância de site','Function'=>'Função','No data logged.'=>'Nenhum dado registrado.','Unknown closure'=>'Encerramento desconhecido','%s Language'=>'Idioma do %s','colour schemeDark'=>'Escuro','colour schemeLight'=>'Claro','colour schemeAuto'=>'Automático','Your browser color scheme is respected by default. You can override it here.'=>'O esquema de cores do navegador é respeitado por padrão. Você pode substituí-lo aqui.','Appearance'=>'Aparência','%s MB'=>'%s MB','Speak to your web host about enabling an opcode cache such as OPcache.'=>'Fale com seu provedor de hospedagem sobre como ativar um cache de opcode como o OPcache.','Opcode cache not in use'=>'Cache de opcode não está em uso','Opcode Cache'=>'Cache de opcode','Speak to your web host about enabling an object cache extension such as Redis or Memcached.'=>'Fale com seu provedor de hospedagem sobre como ativar uma extensão de cache de objetos como o Redis ou Memcached.','The %1$s object cache extension for PHP is installed but is not in use by WordPress. You should <a href="%2$s" target="_blank" class="qm-external-link">install a %1$s plugin</a>.'=>'A extensão de cache de objetos %1$s para PHP está instalada, mas não está sendo usada pelo WordPress. Você deveria <a href="%2$s" target="_blank" class="qm-external-link">instalar um plugin %1$s</a>.','Persistent object cache plugin not in use'=>'Plugin de cache de objeto persistente não está em uso','Persistent object cache plugin in use'=>'Plugin de cache de objeto persistente em uso','%1$s bytes (%2$s MB)'=>'%1$s bytes (%2$s MB)','Time in seconds%ss'=>'%sseg','All (%d)'=>'Todos (%d)','Architecture'=>'Arquitetura','%s Q'=>'%s cons' . "\0" . '%s cons','No CSS files were enqueued.'=>'Nenhum arquivo CSS foi enfileirado.','No JavaScript files were enqueued.'=>'Nenhum arquivo JavaScript foi enfileirado.','Non-WordPress Core'=>'Arquivo básico que não é do WordPress','Allow the wp-content/db.php file symlink to be put into place during activation. Set to false to prevent the symlink creation.'=>'Permitir que o link simbólico do arquivo wp-content/db.php seja colocado no lugar durante a ativação. Defina como false para impedir a criação do link simbólico.','Hooks in Use (%s)'=>'Ganchos em uso (%s)','Data collection ceased'=>'Coleta de dados interrompida','WordPress Core'=>'Arquivos básicos do WordPress','HTTP API callsTotal'=>'Total','Context'=>'Contexto','%1$s%% of %2$s MB server limit'=>'%1$s%% do limite do servidor de %2$s MB','verbSponsor'=>'Patrocinar','Extended query information such as the component and affected rows is not available. Query Monitor was unable to symlink its %s file into place.'=>'As informações estendidas da consulta, como o componente e as linhas afetadas, não estão disponíveis. O Query Monitor não conseguiu criar um link simbólico do arquivo %s no local.','Extended query information such as the component and affected rows is not available. Query Monitor was prevented from symlinking its %1$s file into place by the %2$s constant.'=>'As informações estendidas da consulta, como o componente e as linhas afetadas, não estão disponíveis. O Query Monitor foi impedido de criar um link simbólico do arquivo %1$s no local pela constante %2$s.','Extended query information such as the component and affected rows is not available. A conflicting %s file is present.'=>'As informações estendidas da consulta, como o componente e as linhas afetadas, não estão disponíveis. O arquivo %s está em conflito.','Dependency: %s'=>'Dependência: %s','No data logged. <a href="%s">Read about logging variables in Query Monitor</a>.'=>'Nenhum dado registrado. <a href="%s">Leia sobre o registro de variáveis no Query Monitor</a>.','Environment Type'=>'Tipo de ambiente','Hide Query Monitor itself from various panels. Set to false if you want to see how Query Monitor hooks into WordPress.'=>'Oculta o próprio Query Monitor de vários painéis. Defina como false se você quiser ver como o Query Monitor se integra ao WordPress.','PHP Fatal Error'=>'Erro fatal de PHP','Asset files for Query Monitor need to be built. Run %1$s from the %2$s directory.'=>'Os arquivos de ativos do Query Monitor precisam ser criados. Execute %1$s no diretório %2$s.','This message was triggered by %s.'=>'Esta mensagem foi acionada pelo %s.','Saved! Reload to apply changes.'=>'Salvo! Recarregar para aplicar alterações.','Set editor cookie'=>'Definir cookie do editor','You can set your editor here, so that when you click on stack trace links the file opens in your editor.'=>'Você pode definir seu editor aqui para que, ao clicar nos links do rastreamento de pilha, o arquivo seja aberto no seu editor.','Editor'=>'Editor','Logs (%s)'=>'Registros (%s)','Requested URL'=>'URL solicitado','HTTP method'=>'Método HTTP','Remote IP'=>'IP remoto','Global Variable'=>'Variável global','Globals'=>'Globais','Response Headers'=>'Cabeçalhos da resposta','Request Headers'=>'Cabeçalhos da solicitação','Note that header names are not case-sensitive.'=>'Observe que os nomes dos cabeçalhos não diferenciam maiúsculas de minúsculas.','Response Header Name'=>'Nome do cabeçalho da resposta','Request Header Name'=>'Nome do cabeçalho da solicitação','Stopped'=>'Interrompido','Started'=>'Iniciado','Query countTotal: %s'=>'Total: %s' . "\0" . 'Total: %s','Unknown HTTP Response Code'=>'Código de resposta HTTP desconhecido','Request Data'=>'Dados da solicitação','Default value: %s'=>'Valor padrão: %s','Not Loaded'=>'Não carregado','The %1$s plugin requires PHP version %2$s or higher. This site is running PHP version %3$s. <a href="%4$s">Learn about updating PHP</a>.'=>'O plugin %1$s requer a versão %2$s do PHP ou superior. Este site está executando a versão %3$s do PHP. <a href="%4$s">Saiba mais sobre a atualização do PHP</a>.','Enqueued scriptsScripts (%s)'=>'Scripts (%s)','Enqueued stylesStyles (%s)'=>'Estilos (%s)','Object cache statistics are not available'=>'As estatísticas do cache de objetos não estão disponíveis','Toggle more information'=>'Alternar mais informações','Sort data by this column'=>'Ordenar dados nesta coluna','Callback'=>'Callback','%s: Related Hooks with Filters or Actions Attached'=>'%s: Ganchos relacionados com filtros ou ações anexados','Filter'=>'Filtro','Related Hooks with Filters Attached'=>'Ganchos relacionados com filtros anexados','Related Hooks with Actions Attached'=>'Ganchos relacionados com ações anexadas','Translation File'=>'Arquivo de tradução','Opcode cache in use: %s'=>'Cache de opcode em uso: %s','database queriesTotal'=>'Total','None (Classic block)'=>'Nenhum (bloco clássico)','Render Time'=>'Tempo de renderização','Twig Template Files'=>'Arquivos de modelo Twig','Toggle panel position'=>'Alternar posição do painel','Referenced media is of type %1$s instead of %2$s.'=>'A mídia referenciada é do tipo %1$s em vez de %2$s.','Referenced media does not exist.'=>'A mídia referenciada não existe.','Content blocks usedTotal: %s'=>'Total: %s' . "\0" . 'Total: %s','Add-ons'=>'Complementos','No memory limit. The %1$s PHP configuration directive is set to %2$s.'=>'Sem limite de memória. A diretiva de configuração PHP %1$s está definida como %2$s.','No execution time limit. The %1$s PHP configuration directive is set to %2$s.'=>'Sem limite de tempo de execução. A diretiva de configuração PHP %1$s está definida como %2$s.','Insecure content'=>'Conteúdo inseguro','Referenced post is of type %1$s instead of %2$s.'=>'O post referenciado é do tipo %1$s em vez de %2$s.','Referenced block does not exist.'=>'O bloco referenciado não existe.','Inner HTML'=>'HTML interno','Render Callback'=>'Callback de renderização','Attributes'=>'Atributos','Block Name'=>'Nome do bloco','This post contains no blocks.'=>'Este post não contém blocos.','template partsIncluded %s time'=>'Incluído %s vez' . "\0" . 'Incluído %s vezes','Blocks'=>'Blocos','Human readable label for the user capability required to view Query Monitor.View Query Monitor'=>'Ver Query Monitor','No transients set.'=>'Nenhum transiente definido.','PHP errors were triggered during an Ajax request. See your browser developer console for details.'=>'Foram acionados erros de PHP, durante uma solicitação Ajax. Consulte o console do desenvolvedor do seu navegador para mais detalhes.','Logs'=>'Registros','No HTTP API calls.'=>'Nenhuma chamada da API HTTP.','HTTP API callsTotal: %s'=>'Total: %s' . "\0" . 'Total: %s','Client Version'=>'Versão do cliente','Extension'=>'Extensão','Server Version'=>'Versão do servidor','User capability checksTotal: %s'=>'Total: %s' . "\0" . 'Total: %s','Source'=>'Origem','Enqueued stylesTotal: %s'=>'Total: %s','Enqueued scriptsTotal: %s'=>'Total: %s','Sequence'=>'Sequência','Current value: %s'=>'Valor atual: %s','The following PHP constants can be defined in your %s file in order to control the behavior of Query Monitor:'=>'As seguintes constantes PHP podem ser definidas no seu arquivo %s para controlar o comportamento do Query Monitor:','Configuration'=>'Configuração','In the Hooks & Actions panel, show every hook that has an action or filter attached (instead of every action hook that fired during the request).'=>'No painel Ganchos e Ações, mostrar todos os ganchos que tenham uma ação ou filtro anexado (em vez de todos os ganchos de ação que acionaram durante a solicitação).','Don\'t specify jQuery as a dependency of Query Monitor. If jQuery isn\'t enqueued then Query Monitor will still operate, but with some reduced functionality.'=>'Não especifique o jQuery como uma dependência do Query Monitor. Se o jQuery não estiver enfileirado, o Query Monitor ainda funcionará, mas com algumas funcionalidades reduzidas.','Hide WordPress core on the Hooks & Actions panel.'=>'Ocultar os arquivos básicos do WordPress no painel de Ganchos e Ações.','Enable the Capability Checks panel.'=>'Ativar o painel de verificações de permissão.','Disable the handling of PHP errors.'=>'Desativar o tratamento de erros do PHP.','Disable Query Monitor entirely.'=>'Desativar completamente o Query Monitor.','If an individual database query takes longer than this time to execute, it\'s considered "slow" and triggers a warning.'=>'Se uma consulta individual ao banco de dados leva mais tempo do que esse tempo para ser executada, é considerada "lenta" e aciona um alerta.','Authentication cookie is set'=>'O cookie de autenticação está definido','You can set an authentication cookie which allows you to view Query Monitor output when you&rsquo;re not logged in, or when you&rsquo;re logged in as a different user.'=>'Você pode definir um cookie de autenticação que permite visualizar a saída do Query Monitor quando estiver desconectado ou quando estiver conectado como um usuário diferente.','Query Monitor Menu'=>'Menu do Query Monitor','Logger'=>'Registrador','The developer tools panel for WordPress.'=>'O painel de ferramentas para desenvolvedores do WordPress.','Timings (%s)'=>'Tempos (%s)','~%s kB'=>'~%s kB','Memory'=>'Memória','Tracked Function'=>'Função rastreada','Template'=>'Modelo','Current User'=>'Usuário atual',', '=>', ','PHP Errors (%s)'=>'Erros de PHP (%s)','PHP error level%s Warning'=>'%s alerta' . "\0" . '%s alertas','PHP error level%s Notice'=>'%s notificação' . "\0" . '%s notificações','PHP error level%s Strict'=>'%s estrito' . "\0" . '%s estritos','PHP error level%s Deprecated'=>'%s obsoleto' . "\0" . '%s obsoletos','Message'=>'Mensagem','Level'=>'Nível','A JavaScript problem on the page is preventing Query Monitor from working correctly. jQuery may have been blocked from loading.'=>'Há um problema de JavaScript na página que impede o funcionamento correto do Query Monitor. O jQuery pode ter sido impedido de carregar.','Size'=>'Tamanho','IP Address'=>'Endereço IP','Response Content Type'=>'Tipo de conteúdo da resposta','Transfer Start Time (TTFB)'=>'Tempo de início da transferência (TTFB)','Connection Time'=>'Tempo de conexão','DNS Resolution Time'=>'Tempo de resolução de DNS','Redirected to:'=>'Redirecionado para:','URL'=>'URL','Method'=>'Método','Priority'=>'Prioridade','OS'=>'SO','No database queries were logged.'=>'Nenhuma consulta ao banco de dados foi registrada.','False Conditionals'=>'Condicionais falsas','True Conditionals'=>'Condicionais verdadeiras','For performance reasons, this panel is not enabled by default. To enable it, add the following code to your %s file:'=>'Por motivos de desempenho, este painel não é ativado por padrão. Para ativá-lo, adicione o seguinte código ao seu arquivo %s:','Handle'=>'Identificador','Host'=>'Servidor','Other'=>'Outro','Missing Dependencies'=>'Dependências ausentes','Class:'=>'Classe:','List Table'=>'Tabela de lista','Close Panel'=>'Fechar painel','Settings'=>'Configurações','Timer not stopped'=>'O temporizador não parou','Timer not started'=>'O temporizador não foi iniciado','Timing'=>'Tempo','Current Network: #%d'=>'Rede atual: Nº%d','Silenced PHP error levelDeprecated (Silenced)'=>'Obsoleto (silenciado)','Silenced PHP error levelStrict (Silenced)'=>'Estrito (silenciado)','Silenced PHP error levelNotice (Silenced)'=>'Notificação (silenciada)','Silenced PHP error levelWarning (Silenced)'=>'Alerta (silenciado)','Non-blocking'=>'Sem bloqueio','VIP Client MU Plugin: %s'=>'Plugin MU do cliente VIP: %s','Lap %s'=>'Volta %s','userNone'=>'Nenhum','Current User: #%d'=>'Usuário atual: Nº%d','Non-SELECT'=>'Não SELECIONADO','Error Message'=>'Mensagem de erro','Error Code'=>'Código do erro','No capability checks were recorded.'=>'Nenhuma verificação de permissão foi registrada.','Result'=>'Resultado','User'=>'Usuário','Capability Check'=>'Verificação de permissão','Capability Checks'=>'Verificações de permissão','Transient Updates (%s)'=>'Atualizações de transientes (%s)','size of transient valueSize'=>'Tamanho','transient typeType'=>'Tipo','Updated Transient'=>'Transiente atualizado','Transient Updates'=>'Atualizações de transientes','View Main Query'=>'Ver consulta principal','HTTP API Calls (%s)'=>'Chamadas da API HTTP (%s)','Status'=>'Status','HTTP API Calls'=>'Chamadas da API HTTP','Extensions'=>'Extensões','Error Reporting'=>'Relatório de erros','Hooks & Actions'=>'Ganchos e ações','Main Query'=>'Consulta principal','%1$s%% hit rate (%2$s hits, %3$s misses)'=>'Taxa de acerto de %1$s%% (%2$s acertos, %3$s erros)','Template Hierarchy'=>'Hierarquia do modelo','Suppressed PHP error levelDeprecated (Suppressed)'=>'Obsoleto (suprimido)','Suppressed PHP error levelStrict (Suppressed)'=>'Estrito (suprimido)','Suppressed PHP error levelNotice (Suppressed)'=>'Notificação (suprimida)','Suppressed PHP error levelWarning (Suppressed)'=>'Alerta (suprimido)','Position'=>'Posição','Footer'=>'Rodapé','Header'=>'Cabeçalho','Missing'=>'Ausente','Value'=>'Valor','Property'=>'Propriedade','Single %1$s: #%2$d'=>'%1$s único: Nº%2$d','Current Site: #%d'=>'Site atual: Nº%d','PHP error levelDeprecated'=>'Obsoleto','PHP error levelStrict'=>'Estrito','PHP error levelNotice'=>'Notificação','PHP error levelWarning'=>'Alerta','Request not executed due to a filter on %s'=>'Solicitação não executada devido a um filtro em %s','undefined'=>'indefinido','Template Parts'=>'Partes do modelo','Object Cache'=>'Cache de objetos','Certificate verification disabled (%s)'=>'Verificação de certificado desativada (%s)','Duplicate Queries (%s)'=>'Consultas duplicadas (%s)','%s call'=>'%s chamada' . "\0" . '%s chamadas','Potential Troublemakers'=>'Potenciais causadores de problemas','Components'=>'Componentes','Callers'=>'Chamadas','Duplicate Queries'=>'Consultas duplicadas','MU Plugin: %s'=>'Plugin MU: %s','Unknown location'=>'Localização desconhecida','None'=>'Nenhum','All Matching Rewrite Rules'=>'Todas as regras de reescrita correspondentes','Not Found'=>'Não encontrado','Text Domain'=>'Domínio do texto','Software'=>'Programa','Database'=>'Banco de dados','No database queries were logged because the %1$s constant is set to %2$s.'=>'Nenhuma consulta ao banco de dados foi registrada, porque a constante %1$s está definida como %2$s.','Languages'=>'Idiomas','Unable to determine source of lambda function'=>'Não foi possível determinar a origem da função lambda','John Blackbourn'=>'John Blackbourn','https://querymonitor.com/'=>'https://querymonitor.com/','The symlink at %s is no longer pointing to the correct location. Please remove the symlink, then deactivate and reactivate Query Monitor.'=>'O link simbólico em %s não está mais apontando para a localização correta. Remova o link simbólico e depois desative e reative o Query Monitor.','Expiration'=>'Expiração','Type'=>'Tipo','Template: %s'=>'Modelo: %s','Body Classes'=>'Classes do corpo','Template File'=>'Arquivo de modelo','Request (+%s)'=>'Solicitação (+%s)','Queried Object'=>'Objeto consultado','Multisite'=>'Multisite','Query Vars'=>'Variáveis de consulta','Query String'=>'String de consulta','Matched Query'=>'Consulta correspondente','Matched Rule'=>'Regra correspondente','Location'=>'Localização','Count'=>'Contagem','%s kB'=>'%s KB','Peak Memory Usage'=>'Pico de uso de memória','Page Generation Time'=>'Tempo de geração da página','%1$s%% of %2$ss limit'=>'%1$s%% do limite de %2$ss','Timeout'=>'Tempo limite','Response'=>'Resposta','Error: %s'=>'Erro: %s','Warning: The %s action is extremely resource intensive. Try to avoid using it.'=>'Atenção: A ação %s consome muitos recursos. Tente evitá-la.','Action'=>'Ação','Hook'=>'Gancho','Server'=>'Servidor','Help'=>'Ajuda','Slow Queries (%s)'=>'Consultas lentas (%s)','Database Errors (%s)'=>'Erros no banco de dados (%s)','Rows'=>'Linhas','Caller'=>'Chamada','Slow Database Queries (above %ss)'=>'Consultas lentas ao banco de dados (acima de %ss)','Component'=>'Componente','Call stack:'=>'Pilha de chamadas:','Query'=>'Consulta','Database Errors'=>'Erros no banco de dados','Time'=>'Tempo','%s (missing)'=>'%s (ausente)','none'=>'nenhum','Version'=>'Versão','Dependents'=>'Dependentes','Dependencies'=>'Dependências','Styles'=>'Estilos','Scripts'=>'Scripts','Column Action:'=>'Ação da coluna:','Column Filters:'=>'Filtros de coluna:','"All" option for filtersAll'=>'Tudo','Clear authentication cookie'=>'Limpar cookie de autenticação','Set authentication cookie'=>'Definir cookie de autenticação','Authentication'=>'Autenticação','PHP Errors in Ajax Response'=>'Erros de PHP na resposta do Ajax','Query Monitor'=>'Query Monitor','Transients'=>'Transientes','Unknown queried object'=>'Objeto consultado desconhecido','Post type archive: %s'=>'Arquivo do tipo de post: %s','Term archive: %s'=>'Arquivo do termo: %s','Author archive: %s'=>'Arquivo do autor: %s','Request'=>'Solicitação','PHP Errors'=>'Erros de PHP','Overview'=>'Visão geral','Error'=>'Erro','Request timed out'=>'Tempo limite da solicitação','Environment'=>'Ambiente','Database Queries'=>'Consultas ao banco de dados','Queries by Component'=>'Consultas por componente','Queries by Caller'=>'Consultas por chamada','Conditionals'=>'Condicionais','Admin Screen'=>'Tela de administração','Anonymous function on line %1$d of %2$s'=>'Função anônima na linha %1$d de %2$s','Closure on line %1$d of %2$s'=>'Encerramento na linha %1$d de %2$s','Unknown'=>'Desconhecido','Parent Theme'=>'Tema ascendente (pai)','Theme'=>'Tema','Child Theme'=>'Tema descendente (filho)','VIP Plugin: %s'=>'Plugin VIP: %s','Plugin: %s'=>'Plugin: %s']];