.flex-col {
    display: flex;
    flex-direction: column;
}

.flex-row {
    display: flex;
    flex-direction: row;
    gap: 10px;
}

label {
    display: block;
    font-weight: bold;
    font-size: 1em;
    margin-bottom: 5px;
    margin-top: 14px;
}

input[type=submit]{
    border-color: #ff7300;
    color: #ff7300;
    transition: all;
    duration: 0.3s;
}

input[type=submit]:hover{
    background-color: #ff7300;
    color: white;
}

input, select, select#bairro, textarea {
    width: 100%;
    padding: 8px;
    box-sizing: border-box;
    background-color: white;
    border: solid 1px #a0a0a0f0 !important;
    border-radius: 4px;
    color: black;
}

option {
    color: black;
}

.half-width {
    width: 50%;
}

.errors {
    background-color: red;
    color: white;
}

.success {
    background-color: green;
    color: white;
    padding: 10px;
}