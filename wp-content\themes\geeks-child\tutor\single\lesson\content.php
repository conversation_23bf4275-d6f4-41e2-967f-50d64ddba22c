<?php
/**
 * Display the content
 *
 * @package Tu<PERSON>\Templates
 * @subpackage Single\Lesson
 * <AUTHOR> <<EMAIL>>
 * @link https://themeum.com
 * @since 1.0.0
 */

use TUTOR\User;

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

global $post;
global $previous_id;
global $next_id;

// Get the ID of this content and the corresponding course.
$course_content_id = get_the_ID();
$course_id         = tutor_utils()->get_course_id_by_subcontent( $course_content_id );

$_is_preview = get_post_meta( $course_content_id, '_is_preview', true );
$content_id  = tutor_utils()->get_post_id( $course_content_id );
$contents    = tutor_utils()->get_course_prev_next_contents_by_id( $content_id );
$previous_id = $contents->previous_id;
$next_id     = $contents->next_id;

$prev_is_preview = get_post_meta( $previous_id, '_is_preview', true );
$next_is_preview = get_post_meta( $next_id, '_is_preview', true );
$is_enrolled = tutor_utils()->is_enrolled( $course_id );
$is_public = get_post_meta( $course_id, '_tutor_is_public_course', true );

$prev_is_locked = !($is_enrolled || $prev_is_preview || $is_public);
$next_is_locked = !($is_enrolled || $next_is_preview || $is_public);

// Get total content count
$course_stats = tutor_utils()->get_course_completed_percent( $course_id, 0, true );

$json_data                                 = array();
$json_data['post_id']                      = get_the_ID();
$json_data['best_watch_time']              = 0;
$json_data['autoload_next_course_content'] = (bool) get_tutor_option( 'autoload_next_course_content' );

$best_watch_time = tutor_utils()->get_lesson_reading_info( get_the_ID(), 0, 'video_best_watched_time' );
if ( $best_watch_time > 0 ) {
    $json_data['best_watch_time'] = $best_watch_time;
}

$is_comment_enabled = tutor_utils()->get_option( 'enable_comment_for_lesson' ) && comments_open();
?>

<?php do_action( 'tutor_lesson/single/before/content' ); ?>


<?php if ( $is_enrolled && is_user_logged_in() ) : ?>
    <div class="tutor-single-page-top-bar card mb-4 mt-5 d-flex justify-content-between">
        <div class="card-body p-3">
            <div class="d-md-flex align-items-center justify-content-between flex-wrap">
                <div class="d-flex align-items-center tutor-topbar-item tutor-hide-sidebar-bar">
                    <a href="#" class="text-inherit tutor-lesson-sidebar-hide-bar me-2 tutor-course-topics-sidebar-toggler" tutor-course-topics-sidebar-toggler><i class="fe fe-chevron-left"></i> </a>
                    <h2 class="course-title mb-0 h4">
                        <a class="text-inherit" href="<?php echo get_the_permalink( $course_id ); ?>"><?php echo get_the_title( $course_id ); ?></a>
                    </h2>
                </div>

                <div class="tutor-topbar-right-item d-flex align-items-center">
                    <div class="tutor-topbar-assignment-details d-flex align-items-center">
                        <?php
                            do_action( 'tutor_course/single/enrolled/before/lead_info/progress_bar' );
                        ?>
                        <div class="text-regular-caption me-2">
                            <span class="tutor-progress-content text-dark d-none d-xl-inline-block">
                                <?php esc_html_e( 'Seu Progresso:', 'geeks' ); ?>
                            </span>
                            <span class="text-bold-caption">
                                <?php echo esc_html( $course_stats['completed_count'] ); ?>
                            </span> 
                            <?php esc_html_e( 'de ', 'geeks' ); ?>
                            <span class="text-bold-caption">
                                <?php echo esc_html( $course_stats['total_count'] ); ?>
                            </span>
                            (<?php echo esc_html( $course_stats['completed_percent'] . '%' ); ?>)
                        </div>
                        <?php
                            do_action( 'tutor_course/single/enrolled/after/lead_info/progress_bar' );
                        ?>

                        <div class="tutor-topbar-complete-btn tutor-ml-30 tutor-mr-15">
                            <?php tutor_lesson_mark_complete_html(); ?>
                        </div>
                    </div>
                    <div class="tutor-topbar-cross-icon flex-center ms-1 ms-xl-2">
                        <?php $course_id = tutor_utils()->get_course_id_by( 'lesson', get_the_ID() ); ?>
                        <a href="<?php echo get_the_permalink( $course_id ); ?>">
                            <i class="fe fe-x"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="tutor-mobile-top-navigation d-none tutor-bs-d-block tutor-bs-d-sm-none tutor-my-20 tutor-mx-10">
        <div class="tutor-mobile-top-nav d-grid">
            <a href="<?php echo get_the_permalink( $previous_id ); ?>">
                <span class="tutor-top-nav-icon ttr-previous-line design-lightgrey"></span>
            </a>
            <div class="tutor-top-nav-title tutor-text-regular-body text-dark">
                <?php
                    the_title();
                ?>
            </div>
        </div>
    </div>
<?php else : ?>
    <div class="tutor-single-page-top-bar card mb-4 mt-5">
        <div class="card-body p-3">
            <div class="d-md-flex align-items-center justify-content-between flex-wrap">
                <div class="d-flex align-items-center tutor-topbar-item tutor-hide-sidebar-bar">
                    <a href="#" class="tutor-course-topics-sidebar-toggler text-inherit tutor-lesson-sidebar-hide-bar me-2" tutor-course-topics-sidebar-toggler><i class="fe fe-chevron-left"></i> </a>
                    <?php $course_id = get_post_meta( get_the_ID(), '_tutor_course_id_for_lesson', true ); ?>
                    <a href="<?php echo get_the_permalink( $course_id ); ?>" class="text-inherit tutor-topbar-home-btn">
                        <i class="tutor-icon-home"></i> <?php echo esc_html__( 'Go to Course Home', 'geeks' ); ?>
                    </a>
                </div>
                <div class="tutor-topbar-item tutor-topbar-content-title-wrap py-2">
                    <h2 class="course-title mb-0 h4">
                        <a class="text-inherit" href="<?php echo get_the_permalink( $course_id ); ?>"><?php echo get_the_title( $course_id ); ?></a>
                    </h2>

                </div>

                <div class="tutor-topbar-cross-icon flex-center ms-1 ms-xl-2">
                    <?php $course_id = tutor_utils()->get_course_id_by( 'lesson', get_the_ID() ); ?>
                    <a href="<?php echo get_the_permalink( $course_id ); ?>">
                        <i class="fe fe-x"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<div class="d-flex align-items-center justify-content-between mb-4 pt-1">

	<?php $excerpt = get_the_excerpt(); ?>

	<div>
		<h3 class="h3 mb-0 me-2 me-md-0 text-truncate-line-2"><?php echo get_the_title(); ?></h3>
	</div> 

	<?php if ( ! empty( $excerpt ) ) : ?>
		<span class="dropdown">
			<a href="#" class="mr-2 text-muted" id="dropdownInfo" data-bs-toggle="dropdown" aria-expanded="false">
			  <i class="fe fe-help-circle"></i>
			</a>

			<span class="dropdown-menu dropdown-menu-lg p-3 dropdown-menu-right" aria-labelledby="dropdownInfo">
				<?php echo esc_html( wp_strip_all_tags( $excerpt ) ); ?>        
			</span>
		</span>
	<?php endif; ?>
</div>


<div class="tutor-lesson-content-area">

    <!-- Load Lesson Video -->
    <?php
        $video_info = tutor_utils()->get_video_info();
        $source_key = is_object( $video_info) && 'html5' !== $video_info->source ? 'source_' . $video_info->source : null;
        $has_source = (is_object( $video_info) && $video_info->source_video_id ) || (isset($source_key) ? $video_info->$source_key : null);
    ?>
    <?php 
    if ( $has_source ) : 
        $completion_mode                              = tutor_utils()->get_option( 'course_completion_process' );
        $json_data['strict_mode']                     = ( 'strict' === $completion_mode );
        $json_data['control_video_lesson_completion'] = (bool) tutor_utils()->get_option( 'control_video_lesson_completion', false );
        $json_data['required_percentage']             = (int) tutor_utils()->get_option( 'required_percentage_to_complete_video_lesson', 80 );
        $json_data['video_duration']                  = $video_info->duration_sec ?? 0;
        $json_data['lesson_completed']                = tutor_utils()->is_completed_lesson( $content_id, get_current_user_id() ) !== false;
        $json_data['is_enrolled']                     = tutor_utils()->is_enrolled( $course_id, get_current_user_id() ) !== false;
        ?>
        <input type="hidden" id="tutor_video_tracking_information" value="<?php echo esc_attr( json_encode( $json_data ) ); ?>">
    <?php endif; ?>
    <div class="tutor-video-player-wrapper" style="position: relative;">
        <div class="overlay"></div>
        <?php tutor_lesson_video(); ?>
    </div>
    <?php

    $referer_url        = wp_get_referer();
    $referer_comment_id = explode( '#', filter_input( INPUT_SERVER, 'REQUEST_URI' ) );
    $url_components     = parse_url( $referer_url );
    isset( $url_components['query'] ) ? parse_str( $url_components['query'], $output ) : null;
    $page_tab = isset( $_GET['page_tab'] ) ? esc_attr( $_GET['page_tab'] ) : ( isset($output['page_tab']) ?$output['page_tab']: null );
    /**
     * If lesson has no content, lesson tab will be hidden.
     * To enable elementor and SCORM, only admin can see lesson tab.
     *
     * @since 2.2.2
     */
    $the_content = apply_filters (
        'tutor_has_lesson_content',
        User::is_admin() || ! in_array( trim( get_the_content() ), array( null, '', '&nbsp;' ), true ),
        $course_content_id
    );


	if ( $the_content ) :
		?>
		<div class="card-content mb-0-last-child">
		   <?php the_content(); ?>
		</div>
	<?php endif; 

    $attachments    = tutor_utils()->get_attachments();
    if ( is_array( $attachments ) && count( $attachments ) ) {
	   get_tutor_posts_attachments(); 
    } ?>
    
	<?php if ( $is_comment_enabled ) : ?>
		<div class="tab-body-item" id="tutor-course-spotlight-comments">
            <div class="tutor-container">
                <div class="tutor-course-spotlight-comments">
			         <?php tutor_load_template( 'single.lesson.comment' ); ?>
                 </div>
             </div>
		</div>
	<?php endif; ?>
</div>
<?php tutor_next_previous_pagination(); ?>


<?php do_action( 'tutor_lesson/single/after/content' ); ?>
