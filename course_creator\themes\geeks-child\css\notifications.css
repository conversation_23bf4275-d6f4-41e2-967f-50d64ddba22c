*,
*::before,
*::after {
  box-sizing: border-box;
}
.tutor-color-black{
    color: black!important;
}
.info{
    display: grid;
    width: 100%;
    grid-template-columns: 1fr 1fr;
    justify-content: space-between;
}

.announcement-type{
    margin: 0px!important;
    padding: 0px!important;
}

.annoucement-date{
    text-align: right;
}

.annoucement-content{
    margin: 0!important;
}

.empty{
    text-align: center;
    margin-top: 32px;
    font-weight: bold;
    font-size: 16px;
}

#sec-center {
  position: relative;
  max-height: 40px;
}

[type="checkbox"]:checked,
[type="checkbox"]:not(:checked) {
  position: absolute;
  left: -9999px;
  opacity: 0;
  pointer-events: none;
}

.has-notification{
    transform: translateY(-16px) !important;
}
.dropdown:checked + label,
.dropdown:not(:checked) + label {
    height: 38px;
    display: grid;
    transition: all 200ms linear;
    width: 40px;
    border: none;
    background-color: #ff7300;
    cursor: pointer;
    color: #fff;
    box-shadow: 0 12px 35px 0 rgba(255, 235, 167, .15);
    max-width: 150px;
    transform: translateY(-30px);
    place-items: center;
    border-radius: 50%;
}

.dropdown:checked + label:before,
.dropdown:not(:checked) + label:before {
  position: fixed;
  top: 0;
  left: 0;
  content: '';
  width: 100%;
  height: 100%;
  z-index: -1;
  cursor: auto;
  pointer-events: none;
}

label.for-dropdown.empty{
    background: transparent;
    border: 3px solid #FF7300;
    transition: all 0.2s;
}

label.for-dropdown.empty:hover{
    background: #FF7300;
}

label.for-dropdown.empty:hover svg{
    fill: #fff;
}

label.for-dropdown.empty svg{
    transition: all 0.2s;
}

.dropdown:checked + label:before {
  pointer-events: auto;
}

.dropdown:not(:checked) + label .uil {
  font-size: 24px;
  margin-left: 10px;
  transition: transform 200ms linear;
}

.dropdown:checked + label .uil {
  transform: rotate(180deg);
  font-size: 24px;
  margin-left: 10px;
  transition: transform 200ms linear;
}

.section-dropdown {
  position: absolute;
  padding: 2em;
  background-color: #fff;
  top: 70px;
  width: 400px;
  max-height: 700px;
  overflow-y: scroll;
  
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 14px 35px 0 rgba(9,9,12,0.4);
  z-index: 2;
  opacity: 0;
  pointer-events: none;
  transform: translateY(20px);
  transition: all 200ms linear;
}

.dropdown:checked ~ .section-dropdown {
  opacity: 1;
  pointer-events: auto;
  transform: translateY(0);
}

.section-dropdown:before {
  position: absolute;
  top: -20px;
  left: 0;
  width: 100%;
  height: 20px;
  content: '';
  display: block;
  z-index: 1;
}

.section-dropdown:after {
  position: absolute;
  top: -7px;
  left: 30px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid #fff;
  content: '';
  display: block;
  z-index: 2;
  transition: all 200ms linear;
}

.notification-item{
  position: relative;
  color: #111;
  transition: all 200ms linear;
  font-family: 'Roboto', sans-serif;
  font-weight: 500;
  font-size: 15px;
  border-radius: 2px;
  padding: 8px;
  margin: 12px 0px 2px 0;
  text-align: left;
  text-decoration: none;
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
  -webkit-align-items: center;
  -moz-align-items: center;
  -ms-align-items: center;
  align-items: center;
  justify-content: space-between;
  -ms-flex-pack: distribute;
}

.notification-item:hover {
  background-color: #f4f4f4;
}


.dropdown-sub:checked + label,
.dropdown-sub:not(:checked) + label {
  color: #fff;
  transition: all 200ms linear;
  font-family: 'Roboto', sans-serif;
  font-weight: 500;
  font-size: 15px;
  border-radius: 2px;
  padding: 5px 0;
  padding-left: 20px;
  padding-right: 15px;
  text-align: left;
  text-decoration: none;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -moz-align-items: center;
  -ms-align-items: center;
  align-items: center;
  justify-content: space-between;
  -ms-flex-pack: distribute;
  cursor: pointer;
}

.dropdown-sub:checked + label .uil,
.dropdown-sub:not(:checked) + label .uil {
  font-size: 22px;
}

.dropdown-sub:not(:checked) + label .uil {
  transition: transform 200ms linear;
}

.dropdown-sub:checked + label .uil {
  transform: rotate(135deg);
  transition: transform 200ms linear;
}

.dropdown-sub:checked + label:hover,
.dropdown-sub:not(:checked) + label:hover {
  color: #102770;
  background-color: #ffeba7;
}

.section-dropdown-sub {
  position: relative;
  display: block;
  width: 100%;
  pointer-events: none;
  opacity: 0;
  max-height: 0;
  padding-left: 10px;
  padding-right: 3px;
  overflow: hidden;
  transition: all 200ms linear;
}

.dropdown-sub:checked ~ .section-dropdown-sub {
  pointer-events: auto;
  opacity: 1;
  max-height: 999px;
}

.section-dropdown-sub a {
  font-size: 14px;
}

.section-dropdown-sub a .uil {
  font-size: 20px;
}

.logo {
  position: fixed;
  top: 50px;
  left: 40px;
  display: block;
  z-index: 11000000;
  background-color: transparent;
  border-radius: 0;
  padding: 0;
  transition: all 250ms linear;
}

.logo:hover {
  background-color: transparent;
}

.logo img {
  height: 26px;
  width: auto;
  display: block;
  transition: all 200ms linear;
}

@media screen and (max-width: 991px) {
  .logo {
    top: 30px;
    left: 20px;
  }
}

#mark-all-read{
    background-color: transparent;
    color: #ff7300;
    padding: 7px 8px;
    border-radius: 4px;
    border: none;
    transition: all 0.3s;
}

#mark-all-read:hover{
    background-color: #ff7300;
    color: white;
}


.notification-counter{
    position: absolute;
    top: -8px;
    right: -8px;
    background: #e53f3c;
    width: 20px;
    height: 20px;
    display: grid;
    place-items: center;
    border-radius: 50%;
    font-weight: bold;
}

@media screen and (min-width: 769px){
    .section-dropdown{
        left: 0;
    }
}

@media screen and (max-width: 768px){
    .dropdown:checked + label, .dropdown:not(:checked) + label{
        padding: 0px;
        width: 34px;
        height: 34px;
        display: grid;
    	place-items: center;
    	box-sizing: border-box;
    }
    
    .section-dropdown{
        max-width: 90vw;
        right: -200%;
        width: 90vw;
        top: 40px;
    }
}
