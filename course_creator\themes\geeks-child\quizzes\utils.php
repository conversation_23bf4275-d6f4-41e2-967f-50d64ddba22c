<?php
function all_quizzes_done(){
	$user_id = get_current_user_id();
	
	update_user_meta($user_id, 'status_candidatura', 'done');

}

add_shortcode('mark_as_done', 'all_quizzes_done');

function is_quiz_completed($quiz_id, $user_id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'mlw_results';
    $result = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table_name WHERE quiz_id = %d AND user = %d",
        $quiz_id, $user_id
    ));
    return $result > 0;
}

function check_quiz_completion($atts) {
    global $wpdb;
    $atts = shortcode_atts(array(
        'quiz_id' => '',
        'quiz_conditions' => '',
    ), $atts, 'check_quiz');
    
    $quiz_id = sanitize_text_field($atts['quiz_id']);
    $quiz_conditions = array_map('intval', explode(',', $atts['quiz_conditions']));
    
    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_safe_redirect(wp_login_url());
        exit;
    }
    
    if ($quiz_id === 'prova') {
        $quiz_conditions = [3, 2];
        $all_completed = true;
        foreach ($quiz_conditions as $condition_quiz_id) {
            if (!is_quiz_completed($condition_quiz_id, $user_id)) {
                $all_completed = false;
                break;
            }
        }
        if ($all_completed) {
            return '[prova]';
        } else {
            wp_safe_redirect(home_url('/painel-inscricao'));
            exit;
        }
    }
    
    // Verificar se os quizzes nas condições foram completados
    $all_completed = true;
    foreach ($quiz_conditions as $condition_quiz_id) {
        if (!is_quiz_completed($condition_quiz_id, $user_id)) {
            $all_completed = false;
            break;
        }
    }
    
    if ($all_completed) {
        return '[qsm quiz=' . intval($quiz_id) . ']';
    } else {
        wp_safe_redirect(home_url('/painel-inscricao'));
        exit();
    }
}
add_shortcode('check_quiz', 'check_quiz_completion');

function get_max_score_shortcode() {
    $quiz_ids = array( 4, 6, 7, 9 );
    $user_id = get_current_user_id();
    $max_score = 0;

    global $wpdb;

    foreach ($quiz_ids as $quiz_id) {
        $query = $wpdb->prepare("SELECT MAX(point_score) FROM wp_mlw_results WHERE quiz_id = %d AND user = %d", $quiz_id, $user_id);
        $score = $wpdb->get_var($query);

        if ($score > $max_score) {
            $max_score = $score;
        }
    }

    $label = $max_score . '/40';
    return "<label>{$label}</label>";
}

function registrar_scripts() {
    // wp_register_script('custom-script', get_stylesheet_directory_uri() . '/js/custom_script.js', array('jquery'), null, true);
}
add_action('wp_enqueue_scripts', 'registrar_scripts');

function enqueue_custom_scripts() {
    wp_enqueue_script('custom-script');
}
add_action('wp_enqueue_scripts', 'enqueue_custom_scripts');

function get_quiz_details($quiz_id) {
    global $wpdb;
    $user_id = 
    $quiz = $wpdb->get_row($wpdb->prepare("SELECT quiz_id, quiz_name, message_before FROM wp_mlw_quizzes WHERE quiz_id = %d", $quiz_id));
    return $quiz;
}

function completed_prova($quiz){
    global $wpdb;
    $user_id = get_current_user_id();

    $result = $wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM wp_mlw_results WHERE quiz_id = %d AND user = %d", $quiz, $user_id));
    
    return $result > 0;
}

function render_quiz_shortcode_based_on_user_id( $atts ) {
    $user_id = get_current_user_id();

    $next_quiz = get_user_meta($user_id, 'next_quiz', true);

    if (empty($next_quiz) || completed_prova($next_quiz)) {
        $quiz_ids = array(4, 6, 7, 9);

        do {
            $index = array_rand($quiz_ids);
            $next_quiz = $quiz_ids[$index];
        } while (completed_prova($next_quiz));

        update_user_meta($user_id, 'next_quiz', $next_quiz);
    }

    return do_shortcode("[qsm quiz={$next_quiz}]");
}


add_shortcode( 'prova', 'render_quiz_shortcode_based_on_user_id' );


function set_documentation_status(){

    $user_id = get_current_user_id();
    
    if(has_user_completed_quiz(10 ,$user_id))
        update_user_meta($user_id, 'documentacao', 'sent');
    
    wp_redirect( "/painel-inscricao", 301 );
    exit();
    
}

add_shortcode('verify_documentation', 'set_documentation_status');


function render_quiz_card($atts) {
    $atts = shortcode_atts(array(
        'id' => '',
        'url' => '',
        'etapa' => ''
    ), $atts);

    if (!is_user_logged_in()) {
        return '';
    }

    $user_id = get_current_user_id();
    $quiz_id = intval($atts['id']);
    $etapa = intval($atts['etapa']);
    
    $quiz = get_quiz_details($quiz_id);
    
    if (!$quiz) {
        return 'Quiz não encontrado.';
    }

    $completed = has_user_completed_quiz($quiz_id, $user_id);
    $completed_special_quizzes = has_user_completed_any_special_quiz($user_id);
    $disabled = false;
    $badge_class = 'badge ';
    $btn_class = 'btn-card ';
    $badge_text = 'Continue de onde Parou';

    if ($etapa == 2 && !has_user_completed_quiz(3, $user_id)) {
        $disabled = true;
    } elseif ($etapa == 3 && (!has_user_completed_quiz(2, $user_id) || !has_user_completed_quiz(3, $user_id))) {
        $disabled = true;
    }
	$done = false;
    if (in_array($quiz_id, array(4, 6, 7, 9)) && $completed) {
        $disabled = true;
    } elseif ($completed_special_quizzes && in_array($quiz_id, array(2, 3, 4,6,7,9))) {
        $completed = true;
        $disabled = true;
		$done = true;
    }
	
    if ($disabled && !$done) {
        $card_class = 'quiz-card card-desabilitado';
        $badge_class .= 'badge-desabilitado';
        $badge_text = 'Acesso Bloqueado';
        $btn_class .= 'btn-desabilitado';
        $btn_url = 'javascript:void(0);';
    } else {
        $card_class = 'quiz-card';
        if ($completed) {
			if($done){
				$btn_url = 'javascript:void(0);';
				$btn_class .= 'btn-desabilitado';
			}
			else{
				$btn_url = esc_html($atts['url']);
			}
            $card_class .= ' completed';
            $badge_class .= 'done';
            $badge_text = 'Etapa Concluída';
        } else {
            $badge_class .= 'doing';
			$btn_url = esc_html($atts['url']);
        }
        
    }

    $html = '<div class="' . $card_class . '">';
    $html .= '<span class="' . $badge_class . '">' . $badge_text . '</span>';
    $html .= '<h3>' . esc_html($quiz->quiz_name) . '</h3>';
    $html .= '<p>' . esc_html($quiz->message_before) . '</p>';
    $html .= '<div class="card-actions">';
    $html .= '<a href="' . $btn_url . '" class="' . $btn_class . '">Acesse aqui</a>';
    $html .= '<span class="etapa">Etapa ' . esc_html($atts['etapa']) . '</span>';
    $html .= '</div>';
    $html .= '</div>';

    return $html;
}

function has_user_completed_quiz($quiz_id, $user_id) {
    global $wpdb;
    $results = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM wp_mlw_results WHERE quiz_id = %d AND user = %d", 
        $quiz_id, $user_id
    ));
    return $results > 0;
}

function has_user_completed_any_special_quiz($user_id) {
    global $wpdb;
    $special_quiz_ids = array(4, 6, 7, 9);
    $results = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM wp_mlw_results WHERE quiz_id IN (" . implode(',', $special_quiz_ids) . ") AND user = %d", 
        $user_id
    ));
    return $results > 0;
}

add_shortcode('quiz_card', 'render_quiz_card');



function exportar_resultados_quiz($atts) {
    global $wpdb;

    // ID do quiz (obtido do atributo do shortcode)
    $quiz_id = isset($atts['id']) ? intval($atts['id']) : 0;

    if ($quiz_id <= 0) {
        return 'ID de quiz inválido.';
    }

    // Consulta SQL para obter os resultados do quiz especificado
    $sql = $wpdb->prepare("SELECT quiz_results FROM {$wpdb->prefix}mlw_results WHERE quiz_id = %d", $quiz_id);
    $resultados = $wpdb->get_results($sql);

    // Array para armazenar todos os resultados
    $todos_resultados = array();

    // Iterar sobre os resultados para obter todas as questões e respostas
    foreach ($resultados as $resultado) {
        $quiz_results = unserialize($resultado->quiz_results);
        $todos_resultados[] = $quiz_results;
    }

    // Converter para JSON
    $json_resultados = json_encode($todos_resultados);

    // Definir cabeçalhos para download do arquivo JSON
    header('Content-Type: application/json');
    header('Content-Disposition: attachment; filename="resultados_quiz_' . $quiz_id . '.json"');

    // Output do JSON
    echo $json_resultados;

    // Parar a execução do WordPress
    exit;
}

function adicionar_inscricao_completa() {
    // Verifica se o usuário está logado
    if (is_user_logged_in()) {
        $user_id = get_current_user_id();

        // Adiciona ou atualiza o meta do usuário
        update_user_meta($user_id, 'inscricao', 'completa');

        return '<p>Inscrição completa!</p>';
    } else {
        return '<p>Você precisa estar logado para completar sua inscrição.</p>';
    }
}

add_shortcode('adicionar_inscricao', 'adicionar_inscricao_completa');
