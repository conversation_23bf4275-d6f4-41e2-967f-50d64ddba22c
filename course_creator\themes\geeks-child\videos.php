<?php
function enqueue_videojs() {
    // Verifica se o post atual NÃO é o curso com ID 19682
    if (is_single() && get_the_ID() == 19682) {
        // Não aplica o código personalizado para este curso específico
        return;
    }
    
    wp_enqueue_style('videojs-css', 'https://vjs.zencdn.net/7.20.3/video-js.min.css');
    wp_enqueue_script('videojs', 'https://vjs.zencdn.net/7.20.3/video.min.js', array(), null, true);
    wp_enqueue_script('videojs-youtube', 'https://cdnjs.cloudflare.com/ajax/libs/videojs-youtube/2.6.1/Youtube.min.js', array('videojs'), null, true);

    wp_add_inline_script('videojs', '
    document.addEventListener("DOMContentLoaded", function() {
        var players = document.querySelectorAll(".video-js");
        players.forEach(function(player) {
            var videoPlayer = videojs(player.id, {
                techOrder: ["youtube"],
                controls: true,
                youtube: {
                    iv_load_policy: 3,
                    modestbranding: 1,
                    showinfo: 0,
                    rel: 0,
                    fs: 1,
                    cc_load_policy: 1
                }
            });

            videoPlayer.ready(function() {
                var overlay = document.getElementsByClassName("overlay")[0];
                overlay.style.position = "absolute";
                overlay.style.top = "0";
                overlay.style.left = "0";
                overlay.style.width = "100%";
                overlay.style.height = "calc(100% - 30px)";
                overlay.style.backgroundColor = "transparent";
                overlay.style.zIndex = "1";

                function adjustOverlayHeight() {
                    var playerRect = player.getBoundingClientRect();
                    var controlsHeight = player.querySelector(".vjs-control-bar").offsetHeight;
                    overlay.style.height = (playerRect.height - controlsHeight + 16) + "px";
                }

                window.addEventListener("resize", adjustOverlayHeight);

                this.on("pause", function() {
                    if (!this.seeking()) {
                        overlay.classList.add("visible");
                    }
                });

                this.on("play", function() {
                    setTimeout(function() {
                        overlay.classList.remove("visible");
                    }, 300);
                });

                this.on("seeked", function() {
                    if (this.paused()) {
                        overlay.classList.add("visible");
                    }
                });

                this.on("playing", function() {
                    overlay.classList.remove("visible");
                });

                overlay.addEventListener("click", function() {
                    if (videoPlayer.paused()) {
                        videoPlayer.play();
                    } else {
                        videoPlayer.pause();
                    }
                });

                // Adicionando o meta ao usuário quando o vídeo termina
                this.on("ended", function() {
                    var postID = ' . get_the_ID() . ';
                    var ajaxurl = "' . admin_url('admin-ajax.php') . '";
                    var data = {
                        action: "mark_lesson_complete",
                        post_id: postID
                    };

                    fetch(ajaxurl, {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/x-www-form-urlencoded"
                        },
                        body: new URLSearchParams(data)
                    });
                });
            });
        });
    });
    ');
}
add_action('wp_enqueue_scripts', 'enqueue_videojs');

function custom_video_with_vtt_shortcode($atts) {
    // Verifica se o post atual é o curso com ID 19682
    if (get_the_ID() == 19682) {
        // Retorna null para que o shortcode padrão do tema original seja usado
        return null;
    }
    
    $atts = shortcode_atts(array(
        'src' => '',
        'width' => '640',
        'height' => '360',
        'vtt' => '',
    ), $atts);

    if (!$atts['src']) {
        return 'URL do vídeo é necessária';
    }

    ob_start();
    $unique_id = uniqid('my-video-');
    ?>
    <video id="<?php echo $unique_id; ?>" class="video-js vjs-default-skin vjs-big-play-centered" controls preload="auto" width="<?php echo esc_attr($atts['width']); ?>" height="<?php echo esc_attr($atts['height']); ?>" data-setup='{ "techOrder": ["youtube"], "sources": [{"type": "video/youtube", "src": "<?php echo esc_url($atts['src']); ?>"}] }'>
        <?php if ($atts['vtt']): ?>
        <track kind="captions" src="<?php echo esc_url($atts['vtt']); ?>" srclang="pt" label="Português" default>
        <?php endif; ?>
    </video>
    <?php
    return ob_get_clean();
}
add_shortcode('custom_video_with_vtt', 'custom_video_with_vtt_shortcode');

function mark_lesson_complete() {
    // A função de marcação de lição como completa pode permanecer a mesma, 
    // pois ela funciona com IDs específicos enviados via AJAX
    if (isset($_POST['post_id']) && is_user_logged_in()) {
        $post_id = intval($_POST['post_id']);
        $user_id = get_current_user_id();

        update_user_meta($user_id, '_tutor_completed_lesson_id_' . $post_id, true);
    }
    wp_die();
}
add_action('wp_ajax_mark_lesson_complete', 'mark_lesson_complete');
add_action('wp_ajax_nopriv_mark_lesson_complete', 'mark_lesson_complete');