/*
=======================================================
Loco Addon Dashboard - Admin Panel Styles
=======================================================
*/

:root {
    --primary-color: #30b230;
    --secondary-color: #239d23;
    --background-color: #f8f9fa;
    --border-color: #ddd;
    --text-color: #222;
    --text-color-light: #666;
    --shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    --font-size-base: 16px;
    --line-height-base: 1.5;
}

/* General Wrapper */
.atlt-dashboard-wrapper {
    margin: 0 auto;
    padding: 0 20px 0 0;
    max-width: 1240px;
    box-sizing: border-box;
}

.atlt-dashboard-wrapper * {
    box-sizing: border-box;
    font-family: Inter, Roboto, Helvetica, Arial, sans-serif;
}

.atlt-dashboard-wrapper h3 {
    margin: 0 0 20px;
    padding: 0;
}

:is(.toplevel_page_loco-addon,.loco-translate_page_loco-atlt-dashboard) #wpbody #wpbody-content {
    margin-top: 80px !important;
    margin-block-start: 80px !important;
}

/* ================================================
   Buttons & Links
================================================ */
.atlt-dashboard-btns-row {
    display: flex;
    gap: 10px;
}

.atlt-dashboard-btn {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease-in-out;
    border: 1px solid var(--border-color);
    background: var(--background-color);
    color: var(--text-color);
}

.atlt-dashboard-btn:focus {
   box-shadow: none;
}

.atlt-dashboard-btn.primary {
    background: var(--primary-color);
    color: #fff;
    border-color: var(--secondary-color);
}

.atlt-dashboard-btn:hover {
    background: #efffef;
    color: var(--primary-color);
    border-color: var(--border-color);
}

.atlt-dashboard-btn.svg-hover:hover svg path {
    stroke: var(--primary-color);
}

/*


/* ================================================
   Header Section
================================================ */
.atlt-dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--background-color);
    padding: 5px 20px;
    border-bottom: 1px solid var(--border-color);
    border-radius: 6px 6px 0 0;
    position: absolute;
    top: 0;
    left: 0;
    width: calc(100% + 20px);
    margin: 0 0 0 -20px;
    box-shadow: var(--shadow);
    height: 70px;
}
.atlt-dashboard-social-icons a:focus{
    box-shadow:none;
}
.atlt-dashboard-header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}
  	.atlt-dashboard-header-left img{
	max-width: 170px;
}
.atlt-dashboard-header-left svg {
    height: 50px;
}

.atlt-dashboard-tab-title {
    display: flex;
    gap: 5px;
    font-size: 14px;
}

.atlt-dashboard-header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}


/* ================================================
   Dashboard Layout
================================================ */
.atlt-dashboard-dashboard {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-top: 25px;
    flex: 2;
}

@media (min-width: 768px) {
    .atlt-dashboard-dashboard {
        flex-direction: row;
        gap: 40px;
    }
}

.atlt-dashboard-left-section {
    width: auto;
    flex: 2;
    margin-top: 25px;
}


/* ================================================
   Welcome Section
================================================ */
.atlt-dashboard-welcome {
    display: flex;
    justify-content: space-between;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow);
	gap: 5px;
}

.atlt-dashboard-translation-providers{
    margin-top: 24px;
}

.atlt-dashboard-welcome-text {
    width: calc(100% - 45px);
    flex-direction: column;
}

.atlt-dashboard-welcome-text h2 {
    font-size: 1.5rem;
    font-weight: bold;
    margin: 10px 0;
	line-height: 25px;
}

.atlt-dashboard-welcome p {
    font-size: 0.875rem;
    color: var(--text-color-light);
    margin: 10px 0 20px;
}

.atlt-dashboard-welcome-video {
    width: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.atlt-dashboard-welcome-video .play-icon {
    width: 54px;
    height: 54px;
    position: absolute;
    left: calc(50% - 27px);
    top: calc(50% - 27px);
    animation: play-icon 1s ease-in-out infinite;
}

.atlt-dashboard-welcome-video img.loco-video {
    width: 100%;
    height: auto;
}

a.atlt-dashboard-docs {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-top: 30px;
    font-size: 14px;
    color: var(--text-color);
    font-weight: 500;
    text-decoration: underline;
}

a.atlt-dashboard-docs:hover {
    color: var(--primary-color);
}


/* ================================================
   Translation Providers
================================================ */
.atlt-dashboard-providers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 20px;
}

.atlt-dashboard-provider-card {
    background: #fff;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow);
}

.atlt-dashboard-provider-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 15px;
}

.atlt-dashboard-provider-header img {
    height: 25px;
}

.atlt-dashboard-badge {
    background: #131413;
    color: #fff;
    padding: 2px 8px;
    font-size: 12px;
    border-radius: 12px;
}

.atlt-dashboard-badge.free {
    background: var(--primary-color);
}

.atlt-dashboard-provider-buttons {
    display: flex;
    gap: 10px;
    font-size: 11px;
}

/* ================================================
   Sidebar
================================================ */
.atlt-dashboard-sidebar {
    width: 548px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-top: 25px;
    flex: 1;
}

/* Translation Status */
.atlt-dashboard-sidebar .atlt-dashboard-status {
    display: flex;
    justify-content: space-between;
    padding: 20px 20px 10px;
    background: #fff;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow);
    flex-direction: column;
}

.atlt-dashboard-sts-top {
    display: flex;
    flex-direction: column;
    margin: 15px 0;
    padding-bottom: 15px;
    border-bottom: 1px solid #c3c4c7;
    gap: 12px;
    font-size: 15px;
}

.atlt-dashboard-sts-top span:first-child {
    font-weight: 800;
    font-size: 24px;
}

ul.atlt-dashboard-sts-btm {
    padding: 0;
    margin: 9px 0;
}

ul.atlt-dashboard-sts-btm li {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    margin-bottom: 10px;
    padding: 0;
}

ul.atlt-dashboard-sts-btm li span:first-child {
    font-weight: 600;
}


/* Full Website Translate */
.atlt-dashboard-translate-full .atlt-dashboard-addon {
    border-bottom: 1px solid #c3c4c7;
    margin-bottom: 15px;
    padding-bottom: 15px;
    display: flex;
    gap: 15px;
    justify-content: space-between;
    align-items: center;
}

.atlt-dashboard-addon.first {
    border-top: 1px solid #c3c4c7;
    padding-top: 15px;
}

.atlt-dashboard-addon-l {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 3px;
}

.atlt-dashboard-addon-r img {
    width: 72px;
}

.atlt-dashboard-translate-full .installed {
    background: #cfd5d1;
    color: #7f7f7f;
    padding: 4px 10px;
    border-radius: 4px;
    font-weight: 500;
}

.atlt-dashboard-translate-full .addon-desc {
    font-size: 12px;
    color: #838389;
    margin-bottom: 5px;
}

/* ================================================
   Rate Us Section
================================================ */
.atlt-dashboard-rate-us {
    background: #fff;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow);
}
.atlt-dashboard-rate-us h3{
    line-height: 25px;
}

.atlt-dashboard-rate-us .review-link {
    display: inline-block;
    margin-top: 10px;
    color: #007cba;
    font-weight: bold;
}

/* ================================================
   Animations
================================================ */
@keyframes play-icon {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/*===============================================
    Tab Content
================================================ */
.atlt-dashboard-wrapper .tab-content{
    display: flex;
    gap: 25px;
}

.atlt-dashboard-free-vs-pro .header,
.atlt-dashboard-ai-translations .header,
.atlt-dashboard-license .header ,
.atlt-dashboard-settings .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

:where(.atlt-dashboard-free-vs-pro,.atlt-dashboard-ai-translations ,.atlt-dashboard-license,.atlt-dashboard-settings) .atlt-dashboard-status {
    display: flex;
    align-items: center;
    gap: 10px;
}

:where(.atlt-dashboard-free-vs-pro,.atlt-dashboard-ai-translations ,.atlt-dashboard-license,.atlt-dashboard-settings) .atlt-dashboard-status span{
    font-size: 15px;
    font-weight: 600;
}

/* ================================================
   Free vs Pro
================================================ */
.atlt-dashboard-free-vs-pro {
    width: 100%;
    margin-top: 25px;
    flex: 2;
}

.atlt-dashboard-free-vs-pro-container{
    background: white;
    padding: 24px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.atlt-dashboard-free-vs-pro p {
    font-size: 14px;
    color: #666;
    margin-bottom: 20px;
}

.atlt-dashboard-free-vs-pro table {
    width: 100%;
    border-collapse: collapse;
    border-radius: 10px;
    overflow: hidden;
    border: 1px solid #eee;
}

.atlt-dashboard-free-vs-pro th {
    background-color: #e7e7e7;
    text-align: left;
    font-size: 14px;
    font-weight: 700;
    padding: 12px;
}

.atlt-dashboard-free-vs-pro td {
    padding: 12px;
    font-size: 14px;
    border-bottom: 1px solid #eee;
}

.atlt-dashboard-free-vs-pro .check {
    color: green;
    font-size: 16px;
}

.atlt-dashboard-free-vs-pro .cross {
    color: red;
    font-size: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .atlt-dashboard-free-vs-pro {
        width: 100%;
        padding: 16px;
    }
}

/* ================================================
   AI Translations
================================================ */
.atlt-dashboard-ai-translations {
    width: auto;
    flex: 2;
    margin-top: 25px;
}

.atlt-dashboard-ai-translations-container{
    background: white;
    padding: 24px;
    border-radius: 10px;
    border: 1px solid var(--border-color);
}

.atlt-dashboard-ai-translations .description {
    font-size: 14px;
    color: #666;
    margin: 10px 0;
}

.atlt-dashboard-ai-translations .atlt-dashboard-translations {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.atlt-dashboard-ai-translations .atlt-dashboard-translation-card {
    margin-top: 35px;
    position: relative;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 14px;
    padding: 13px 15px 15px 15px;
    text-align: start;
    flex: 1;
}

.atlt-dashboard-ai-translations .atlt-dashboard-translation-card h3{
    margin-top: 12px;
	font-size: 1.0rem;
    margin-bottom: 10px;
}
.atlt-dashboard-ai-translations .atlt-dashboard-translation-card p {
    padding-right: 4px;
}

.atlt-dashboard-ai-translations .atlt-dashboard-translation-card .logo{
	background-color: white;
    left: 20px;
    position: absolute;
    top: -12px;
    border: 1px solid var(--border-color);
    border-radius: 30px;
    width: 95px;
    padding: 4px 10px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

.atlt-dashboard-ai-translations .atlt-dashboard-translation-card .logo img{
    width: 85%;
    height: 100%;
}



.atlt-dashboard-ai-translations .atlt-dashboard-translation-card .play-btn-container img {
 width: 100%;
 height: 100%;
}
.atlt-dashboard-ai-translations .atlt-dashboard-translation-card .play-btn-container a:focus {
    box-shadow: none;
   }


.atlt-dashboard-ai-translations .atlt-dashboard-play-btn {
    background: #00cc66;
    color: white;
    border: none;
    padding: 10px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
}

/* ================================================
   License
================================================ */
.atlt-dashboard-license {
    width: 100%;
    margin-top: 25px;
    flex: 2;
}

.atlt-dashboard-license-container{
    background: white;
    font-family: Arial, sans-serif;
    color: #333;
    border: 1px solid #ddd;
    padding: 24px;
    border-radius: 5px;
}

.atlt-dashboard-license h1 {
    font-weight: 600;
    color: #000;
    margin: 14px 0px 22px 0px;
    font-size: 22px;
}

.atlt-dashboard-license p {
    margin: 5px 0;
    font-size: 14px;
}

.atlt-dashboard-upgrade-box {
    background-color: #f3fcf3;
    padding: 10px;
    border-radius: 5px;
    margin-top: 10px;
    border-left: 4px solid #30b230;
}

.atlt-dashboard-upgrade-box a {
    color: #0073aa;
    text-decoration: none;
    font-weight: bold;
}

.atlt-dashboard-upgrade-box a:hover {
    text-decoration: underline;
}

.atlt-dashboard-upgrade-box em {
    display: block;
    margin-top: 5px;
    font-size: 13px;
    color: #555;
}
.atlt-dashboard-ai-translations .atlt-dashboard-ai-translations-container .header h1 {
    line-height: 25px;
}

/* ================================================
   Settings Section
================================================ */
.atlt-dashboard-settings {
    width: 100%;
    margin-top: 25px;
    flex: 2;
}

.atlt-dashboard-settings-container{
    padding: 24px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
}

.atlt-dashboard-settings h1{
    margin: 14px 0px 22px 0px;
    font-size: 22px;
	line-height: 25px;
}

.atlt-dashboard-api-settings-container {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.atlt-dashboard-api-settings {
    width: 55%;
}

.atlt-dashboard-api-settings label {
    display: block;
    font-weight: bold;
    margin: 10px 0px;
}

.atlt-dashboard-api-settings input {
    width: 100%;
    padding: 8px;
    margin-bottom: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
}

.atlt-dashboard-api-settings a {
    display: inline-block;
    font-size: 14px;
    color: #0073aa;
    text-decoration: none;
}
.atlt-dashboard-api-settings .atlt-dashboard-save-btn-container{
    margin-top: 15px;
}

/* ================================================
   Info
================================================ */
.atlt-dashboard-info {
    margin-top: 70px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
}

.atlt-dashboard-info-links {
    font-size: 14px;
}

.atlt-dashboard-info-links p{
    display: flex;
    align-items: center;
    gap: 10px;
}

.atlt-dashboard-info .logo {
    width: 150px;
    height: 16px;
    margin-bottom: 10px;
}

.atlt-dashboard-info .logo  a:focus{
    box-shadow: none;
}

.atlt-dashboard-info-links a {
    color: #0073aa;
    text-decoration: none;
}

.atlt-dashboard-social-icons a:focus{
     text-decoration: underline;
}

.atlt-dashboard-social-icons img{
    margin-top: 10px;
}
.atlt-dashboard-social-icons svg {
    width: 32px;  
    height: 32px; 
}
@media (max-width: 488px) and  (min-width:100px){
	.atlt-dashboard-ai-translations .atlt-dashboard-ai-translations-container .header,
	.atlt-dashboard-free-vs-pro-container .header,
	.atlt-dashboard-settings-container .header,
	.atlt-dashboard-license-container .header{
    flex-direction: column;
    align-items: start;
	padding-bottom: 10px;
}
	.atlt-dashboard-ai-translations .atlt-dashboard-translations {
    flex-direction: column;
}
}
@media (max-width: 767px) {

	.atlt-dashboard-wrapper .tab-content{
    flex-direction: column;
	gap: 0px;
}
	.atlt-dashboard-sidebar {
    width: auto;
}
	.atlt-dashboard-header {
    padding: 0px 11px 11px 20px;
    height: 145px;
	flex-direction: column;
    gap: 0px;
	top: 47px;
}
	.atlt-dashboard-welcome {
    flex-direction: column;
}
.atlt-dashboard-welcome-text {
    width: calc(100% - 0px);
}
.atlt-dashboard-welcome-video {
    width: 100%;
}
	.atlt-dashboard-wrapper{
	padding:60px 12px 0px 0px;
	}
	.atlt-dashboard-header-right {
    gap: 6px;
}
}
@media (max-width: 766px) and  (min-width:601px){
	.atlt-dashboard-header {
	top: 0px;
}
}
	
@media (max-width: 767px) and  (min-width:375px){
	.atlt-dashboard-header {
    height: 110px;
}
}
	
	
@media (max-width: 1024px) {
.atlt-dashboard-header-left svg {
    max-width: 120px;
}
	.atlt-dashboard-header {
	gap: 15px;
}

	.atlt-dashboard-header-left img{
	max-width: 125px;
}
	.atlt-dashboard-btn {
    padding: 8px 6px;
}
	.atlt-dashboard-header-right {
    gap: 13px;
}
	.atlt-dashboard-ai-translations .atlt-dashboard-translations {
    gap: 7px;
}
}
