{"name": "<PERSON><PERSON>", "version": "1.0.0", "main": "index.js", "scripts": {"build": "wp-scripts build", "start": "wp-scripts start"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@babel/plugin-proposal-class-properties": "^7.13.0", "@babel/preset-env": "^7.14.1", "@babel/preset-react": "^7.13.13", "@types/wordpress__block-editor": "^11.5.16", "@types/wordpress__blocks": "^12.5.17", "@wordpress/scripts": "^26.19.0", "ts-loader": "^9.5.2"}, "description": "", "dependencies": {"@automattic/isolated-block-editor": "^2.29.0", "@wordpress/block-editor": "^14.11.0", "@wordpress/blocks": "^14.5.0", "@wordpress/components": "^29.2.0", "@wordpress/data": "^10.16.0", "@wordpress/element": "^5.25.0", "@wordpress/i18n": "^4.48.0", "react": "^18.2.0", "react-select": "^5.7.0", "sass": "^1.69.5", "typescript": "^5.8.2"}}