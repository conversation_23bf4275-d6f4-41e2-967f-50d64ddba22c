<?php
/**
 * Batch enroll specific candidate users into Tutor LMS courses based on their chosen path.
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
    die;
}

/**
 * Handles the batch enrollment process.
 */
function panapana_process_batch_candidate_enrollment() {
    // 1. Security Check: Verify Nonce
    if ( ! isset( $_POST['batch_enroll_candidates_nonce'] ) || ! wp_verify_nonce( sanitize_key( $_POST['batch_enroll_candidates_nonce'] ), 'batch_enroll_candidates_action' ) ) {
        wp_die( 'Security check failed. Invalid nonce.', 'Error', array('response' => 403) );
    }

    // 2. Capability Check
    if ( ! current_user_can( 'manage_options' ) ) { // Or a more specific capability
        wp_die( 'You do not have sufficient permissions to perform this action.', 'Error', array('response' => 403) );
    }

    // 3. Ensure Tutor LMS functions are available
    if ( ! function_exists( 'tutor_utils' ) || ! method_exists( tutor_utils(), 'do_enroll' ) ) {
        wp_die( 'Tutor LMS core functions (tutor_utils or do_enroll) are not available. Is Tutor LMS active?', 'Error' );
    }

    // 4. Define User Data and Course Mappings
    $users_to_enroll_raw = "
249	programador
533	designer
244	trafego
304	programador
539	programador
321	designer
442	programador
531	trafego
217	designer
532	programador
179	programador
470	editor
213	programador
522	designer
279	programador
545	trafego
550	programador
435	designer
516	programador
507	wordpress
535	trafego
458	programador
288	programador
334	designer
395	trafego
521	wordpress
256	designer
478	programador
200	designer
263	trafego
322	programador
552	designer
328	designer
342	programador
439	designer
534	designer
258	programador
293	trafego
401	designer
415	designer
289	wordpress
266	designer
525	programador
248	designer
553	trafego
409	trafego
426	trafego
272	trafego
218	programador
551	designer
468	programador
492	programador
506	programador
399	designer
394	programador
436	designer
250	designer
203	programador
500	trafego
429	trafego
302	designer
240	editor
529	editor
280	wordpress
554	designer
170	designer
275	programador
402	programador
282	designer
228	programador
463	programador
375	programador
443	designer
243	trafego
548	designer
483	designer
371	programador
537	programador
254	programador
208	programador
301	editor
216	programador
323	designer
383	programador
259	trafego
314	programador
513	programador
260	trafego
494	programador
495	programador
286	designer
398	programador
561	trafego
390	trafego
412	editor
465	trafego
    ";

    $path_to_course_id = array(
        'wordpress'   => 6946,
        'designer'    => 3145,
        'programador' => 2999,
        'editor'      => 3962,
        'trafego'     => 5339,
    );

    $users_data = array();
    $lines = explode( "\n", trim( $users_to_enroll_raw ) );
    foreach ( $lines as $line ) {
        $parts = preg_split( '/\s+/', trim( $line ) ); // Handles multiple spaces/tabs
        if ( count( $parts ) === 2 && is_numeric( $parts[0] ) ) {
            $users_data[] = array( 'user_id' => (int) $parts[0], 'path' => trim( $parts[1] ) );
        }
    }

    // 5. Process Enrollments
    $results = array(
        'success' => array(),
        'already_enrolled' => array(),
        'failed' => array(),
        'no_course_for_path' => array(),
        'invalid_user' => array(),
    );

    foreach ( $users_data as $user_entry ) {
        $user_id = $user_entry['user_id'];
        $path    = strtolower( $user_entry['path'] ); // Normalize path to lowercase

        if ( ! get_user_by( 'ID', $user_id ) ) {
            $results['invalid_user'][] = "User ID {$user_id} (Path: {$path}) does not exist.";
            continue;
        }

        if ( ! isset( $path_to_course_id[$path] ) ) {
            $results['no_course_for_path'][] = "User ID {$user_id}: No course ID found for path '{$path}'.";
            continue;
        }
        $course_id = $path_to_course_id[$path];

        if ( tutor_utils()->is_enrolled( $course_id, $user_id ) ) {
            $results['already_enrolled'][] = "User ID {$user_id} already enrolled in Course ID {$course_id} (Path: {$path}).";
        } else {
            $enrollment_success = tutor_utils()->do_enroll( $course_id, 0, $user_id );
            if ( $enrollment_success ) {
                $results['success'][] = "User ID {$user_id} successfully enrolled in Course ID {$course_id} (Path: {$path}).";
            } else {
                $results['failed'][] = "User ID {$user_id} FAILED to enroll in Course ID {$course_id} (Path: {$path}).";
            }
        }
    }

    // 6. Display Results
    // For a better UX with shortcodes, it's often better to redirect back to the page
    // with the shortcode and display results using admin notices or a transient.
    // However, for a one-off powerful tool, direct output after admin-post can be acceptable.
    // We'll stick to direct output for now to keep it simpler unless you want to implement redirection.
    
    ob_start(); // Start output buffering for the results page
    ?>
    <div class="wrap" style="padding: 20px; font-family: sans-serif;">
        <h1><span class="dashicons dashicons-yes-alt" style="color: green;"></span> Resultados do Processamento de Inscrição em Lote</h1>
        <p><em>Processamento concluído. Verifique os resultados abaixo.</em></p>
        <hr>

        <?php if ( ! empty( $results['success'] ) ) : ?>
            <div style="margin-bottom: 20px; padding: 10px; border-left: 4px solid #4CAF50; background-color: #f0fff0;">
                <h2 style="margin-top: 0; color: #4CAF50;">Inscrições Bem-sucedidas (<?php echo count( $results['success'] ); ?>):</h2>
                <ul style="list-style-type: disc; margin-left: 20px;">
                    <?php foreach ( $results['success'] as $message ) : ?>
                        <li><?php echo esc_html( $message ); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if ( ! empty( $results['already_enrolled'] ) ) : ?>
            <div style="margin-bottom: 20px; padding: 10px; border-left: 4px solid #2196F3; background-color: #e3f2fd;">
                <h2 style="margin-top: 0; color: #2196F3;">Já Inscritos (<?php echo count( $results['already_enrolled'] ); ?>):</h2>
                <ul style="list-style-type: disc; margin-left: 20px;">
                    <?php foreach ( $results['already_enrolled'] as $message ) : ?>
                        <li><?php echo esc_html( $message ); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <?php if ( ! empty( $results['failed'] ) ) : ?>
            <div style="margin-bottom: 20px; padding: 10px; border-left: 4px solid #f44336; background-color: #ffebee;">
                <h2 style="margin-top: 0; color: #f44336;">Falhas na Inscrição (<?php echo count( $results['failed'] ); ?>):</h2>
                <ul style="list-style-type: disc; margin-left: 20px;">
                    <?php foreach ( $results['failed'] as $message ) : ?>
                        <li><?php echo esc_html( $message ); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if ( ! empty( $results['no_course_for_path'] ) ) : ?>
            <div style="margin-bottom: 20px; padding: 10px; border-left: 4px solid #ff9800; background-color: #fff3e0;">
                <h2 style="margin-top: 0; color: #ff9800;">Caminho sem Curso Correspondente (<?php echo count( $results['no_course_for_path'] ); ?>):</h2>
                <ul style="list-style-type: disc; margin-left: 20px;">
                    <?php foreach ( $results['no_course_for_path'] as $message ) : ?>
                        <li><?php echo esc_html( $message ); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if ( ! empty( $results['invalid_user'] ) ) : ?>
             <div style="margin-bottom: 20px; padding: 10px; border-left: 4px solid #607d8b; background-color: #eceff1;">
                <h2 style="margin-top: 0; color: #607d8b;">Usuários Inválidos/Não Encontrados (<?php echo count( $results['invalid_user'] ); ?>):</h2>
                <ul style="list-style-type: disc; margin-left: 20px;">
                    <?php foreach ( $results['invalid_user'] as $message ) : ?>
                        <li><?php echo esc_html( $message ); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <hr>
        <p><a href="<?php echo esc_url( admin_url( 'admin.php?page=coordenador-dashboard' ) ); ?>" class="button button-secondary">Voltar ao Painel do Coordenador</a></p> <?php // Adjust link as needed ?>
    </div>
    <?php
    echo ob_get_clean(); // Output the results page
    exit; // Important to stop further WordPress execution
}
// Hook to admin_post action
add_action( 'admin_post_panapana_batch_enroll_candidates', 'panapana_process_batch_candidate_enrollment' );
// No nopriv hook needed for such a powerful admin tool.

/**
 * Shortcode to display the button for triggering the batch enrollment.
 * Usage: [batch_enroll_candidates_button]
 */
function panapana_batch_enroll_candidates_button_shortcode() {
    // Check if the current user has the capability
    if ( ! current_user_can( 'manage_options' ) ) { // Or your specific capability
        return '<p>Você não tem permissão para acessar esta funcionalidade.</p>';
    }

    ob_start();
    ?>
    <div class="panapana-batch-enroll-section" style="margin: 20px 0; padding: 20px; border: 1px solid #ccd0d4; background-color: #fff; border-left: 5px solid #ffba00; box-shadow: 0 1px 1px rgba(0,0,0,.04);">
        <h3 style="margin-top:0; color: #d9534f;"><span class="dashicons dashicons-warning" style="color: #d9534f;"></span> Ação em Lote: Inscrever Candidatos em Cursos</h3>
        <p style="font-weight: bold; color: #d9534f;">ATENÇÃO: Esta ação irá processar a lista de candidatos (pré-definida no código do plugin) e tentar inscrevê-los nos cursos correspondentes. Use com extremo cuidado e idealmente apenas uma vez. Faça um backup do banco de dados antes de prosseguir.</p>
        <form method="post" action="<?php echo esc_url( admin_url('admin-post.php') ); ?>" onsubmit="return confirm('TEM CERTEZA ABSOLUTA que deseja prosseguir com a inscrição em lote de candidatos? Esta ação é poderosa e pode ter efeitos significativos. Faça um backup antes!');">
            <input type="hidden" name="action" value="panapana_batch_enroll_candidates">
            <?php wp_nonce_field( 'batch_enroll_candidates_action', 'batch_enroll_candidates_nonce' ); ?>
            <?php submit_button( 'Processar Inscrição em Lote de Candidatos AGORA', 'primary', 'panapana_submit_batch_enroll', true, array('style' => 'background-color: #d9534f; border-color: #d43f3a; box-shadow: none; text-shadow: none;') ); ?>
        </form>
        <p class="description"><em>Isso irá inscrever os usuários da lista pré-definida (aqueles que terminaram a prova e parecem não ter se registrado em cursos) nos seus respectivos cursos escolhidos. A lista de usuários e cursos está configurada diretamente no arquivo <code>batch_enroll_processor.php</code>.</em></p>
    </div>
    <?php
    return ob_get_clean();
}
add_shortcode( 'batch_enroll_candidates_button', 'panapana_batch_enroll_candidates_button_shortcode' );