<?php
function prova_completa_email() {
    $user = wp_get_current_user();
    $user_id = $user->ID;
    $email_sent = get_user_meta($user_id, 'prova_completa_email_sent', true);
    $status_candidatura = get_user_meta($user_id, 'status_candidatura', true);

    // Verifica se o e-mail já foi enviado ou se o status_candidatura está vazio
    if ($email_sent || empty($status_candidatura)) {
        return; // Se já foi enviado ou status_candidatura está vazio, encerra a função
    }

    $to = $user->user_email;
    $first_name = $user->first_name;
    $subject = "Inscrição Realizada com Sucesso no Alagoas Tech!";
    $headers = [
        'Content-Type: text/html; charset=UTF-8',
        'From: Instituto Panapaná <<EMAIL>>'
    ];
    
    $message = '<html><body>';
    $message .= '            <img style="max-width: 150px; height: auto;" src="https://cursos.institutopanapana.org.br/wp-content/uploads/2023/06/LOGO-insituto-panapana-org-hr.png" alt="Instituto Panapaná Logo" data-mce-src="https://cursos.institutopanapana.org.br/ugyrgems/2023/06/LOGO-insituto-panapana-org-hr.png" data-mce-style="max-width: 150px; height: auto;" data-mce-selected="1">';
    $message .= '<p>Olá ' . $first_name . '!</p>';
    $message .= '<p>Parabéns! Você finalizou todas as etapas da inscrição para o Alagoas Tech. Seu empenho e determinação são admiráveis.</p>';
    $message .= '<p>Aguarde para receber a notícia da sua aprovação! Os resultados estarão disponíveis no site a partir de 17 de Agosto.</p>';
    $message .= '<p>Enquanto isso, que tal acompanhar nossas redes sociais para ficar por dentro das novidades?</p>';
    $message .= '<p><a href="https://www.instagram.com/institutopanapanaoficial/" style="background-color: #FF7300; border: none; color: white; padding: 15px 32px; text-align: center; text-decoration: none; display: inline-block; font-size: 16px; margin: 4px 2px; cursor: pointer;">Instagram</a></p>';
    $message .= '<p>Agradecemos seu interesse e participação. Estamos torcendo por você!</p>';
    $message .= '<p>Até breve,<br>Equipe Instituto Panapaná</p>';
    $message .= '</body></html>';
    
    $email_sent = wp_mail($to, $subject, $message, $headers);

    // Se o e-mail for enviado com sucesso, marca como enviado no meta do usuário
    if ($email_sent) {
        update_user_meta($user_id, 'prova_completa_email_sent', true);
    }
}
add_shortcode('prova_completa', 'prova_completa_email');