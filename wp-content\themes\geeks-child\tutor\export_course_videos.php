<?php

/**
 * Panapaná Tutor LMS Course Exporter
 *
 * This file contains all functionality for exporting course data and manages
 * the custom permissions required to do so.
 *
 * This file is included by functions.php.
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}


// =============================================================================
//  STEP 1: REGISTER THE CUSTOM CAPABILITY AND ADD IT TO THE 'GESTOR' ROLE
// =============================================================================

/**
 * Adds the custom capability 'export_course_data' to the 'gestor' role.
 *
 * This function runs once when WordPress loads. It ensures the permission exists
 * so we can check for it later. This is the correct, standard WordPress way.
 */
function panapana_add_custom_export_capability() {
	// Get the 'gestor' role object.
	$role = get_role( 'gestor' );

	// IMPORTANT: Check if the role actually exists before trying to modify it.
	if ( ! empty( $role ) ) {
		// Add our custom capability to the role.
		// WordPress is smart and will only add it once.
		$role->add_cap( 'export_course_data', true );
	}
}
add_action( 'init', 'panapana_add_custom_export_capability' );


// =============================================================================
//  STEP 2: USE THE NEW CAPABILITY IN YOUR SHORTCODE
// =============================================================================

/**
 * Creates a shortcode [tutor_course_exporter] to display a list of courses for export.
 *
 * The output is only visible to users with the 'export_course_data' capability.
 *
 * @return string The HTML for the course list, or an error message.
 */
function panapana_course_exporter_shortcode_ui() {
	// Check if the current user has our custom capability or is an administrator.
	// This works for Administrators (who have manage_options) and for 'gestor' (who we gave export_course_data to).
	if ( ! current_user_can( 'export_course_data' ) && ! current_user_can( 'manage_options' ) ) {
		return '<p>Você não tem permissão para acessar esta funcionalidade.</p>';
	}

	// Get all published courses.
	$courses = get_posts(
		array(
			'post_type'      => 'courses',
			'post_status'    => 'publish',
			'posts_per_page' => -1,
			'orderby'        => 'title',
			'order'          => 'ASC',
		)
	);

	if ( empty( $courses ) ) {
		return '<p>Nenhum curso encontrado.</p>';
	}

	ob_start();
	?>
	<div class="tutor-course-exporter-wrapper">
		<h3>Exportador de Vídeos do Curso</h3>
		<p>Clique em um botão para exportar a lista de módulos e aulas de um curso.</p>
		<ul style="list-style-type: none; padding-left: 0;">
			<?php foreach ( $courses as $course ) : ?>
				<li style="padding: 10px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center;">
					<span><?php echo esc_html( $course->post_title ); ?></span>
					<a href="<?php echo esc_url( add_query_arg( 'export_tutor_course', $course->ID ) ); ?>" class="tutor-btn tutor-btn-primary tutor-btn-sm" target="_blank">
						Exportar Links das Aulas
					</a>
				</li>
			<?php endforeach; ?>
		</ul>
	</div>
	<?php
	return ob_get_clean();
}
add_shortcode( 'tutor_course_exporter', 'panapana_course_exporter_shortcode_ui' );


// =============================================================================
//  STEP 3: USE THE NEW CAPABILITY IN YOUR EXPORT TRIGGER
// =============================================================================

/**
 * Exports course data when triggered by a URL parameter.
 *
 * Only users with the 'export_course_data' capability can run this export.
 */
function panapana_export_course_videos_on_trigger() {
	if ( ! isset( $_GET['export_tutor_course'] ) ) {
		return;
	}

	// Security: Check for the custom capability or administrator access.
	if ( ! current_user_can( 'export_course_data' ) && ! current_user_can( 'manage_options' ) ) {
		wp_die( 'Você não tem permissão para acessar esta página.' );
	}

	$course_id = intval( $_GET['export_tutor_course'] );
	if ( ! $course_id ) {
		wp_die( 'ID de curso inválido.' );
	}

	$course = get_post( $course_id );
	if ( ! $course || 'courses' !== $course->post_type ) {
		wp_die( 'O ID fornecido não é um curso válido do Tutor LMS.' );
	}

	// --- The rest of your export logic ---
	header( 'Content-Type: text/plain; charset=utf-8' );
	echo "Exportando dados para o Curso: \"{$course->post_title}\" (ID: {$course_id})\n";
	echo "===================================================================\n\n";

	$topics = get_posts(
		array(
			'post_type'      => 'topics',
			'post_parent'    => $course_id,
			'posts_per_page' => -1,
			'orderby'        => 'menu_order',
			'order'          => 'ASC',
		)
	);

	if ( ! $topics ) {
		echo "Nenhum tópico (módulo) encontrado para este curso.\n";
		die();
	}

	foreach ( $topics as $topic ) {
		echo "Módulo: {$topic->post_title}\n";
		echo "----------------------------------------\n";
		$lessons_and_quizzes = get_posts(
			array(
				'post_type'      => array( 'lesson', 'tutor_quiz' ),
				'post_parent'    => $topic->ID,
				'posts_per_page' => -1,
				'orderby'        => 'menu_order',
				'order'          => 'ASC',
			)
		);
		if ( ! $lessons_and_quizzes ) {
			echo "  Nenhuma aula ou questionário encontrado neste módulo.\n\n";
			continue;
		}
		foreach ( $lessons_and_quizzes as $item ) {
			if ( 'lesson' === $item->post_type ) {
				$video_meta = get_post_meta( $item->ID, '_video', true );
				$video_url  = 'N/A';
				if ( is_array( $video_meta ) ) {
					if ( ! empty( $video_meta['source_shortcode'] ) && preg_match( '/src="([^"]+)"/', $video_meta['source_shortcode'], $matches ) ) {
						$video_url = $matches[1];
					} elseif ( ! empty( $video_meta['source_youtube'] ) ) {
						$video_url = $video_meta['source_youtube'];
					}
				}
				echo "  - Aula: {$item->post_title}\n";
				echo "    Link: {$video_url}\n\n";
			} elseif ( 'tutor_quiz' === $item->post_type ) {
				echo "  - Questionário: {$item->post_title}\n\n";
			}
		}
	}
	die();
}
add_action( 'init', 'panapana_export_course_videos_on_trigger' );

// =============================================================================
//  MASTER DEBUGGING FUNCTION
// =============================================================================

/**
 * Creates a shortcode [panapana_master_debug] to diagnose permission issues.
 *
 * This will show us exactly what roles and capabilities the current user has,
 * and if the 'gestor' role itself has the capability we are trying to add.
 */
function panapana_master_debug_shortcode_ui() {
	if ( ! is_user_logged_in() ) {
		return '<p style="color: red;"><strong>DEBUG: User is not logged in.</strong></p>';
	}

	$user = wp_get_current_user();
	$role_object = get_role( 'gestor' );

	ob_start();
	?>
	<div class="panapana-debug-box" style="border: 3px solid red; padding: 20px; margin: 20px 0; background: #fff;">
		<h3 style="margin-top:0;">Panapaná Permission Debug</h3>
		
		<h4>Current User Information</h4>
		<ul>
			<li><strong>User ID:</strong> <?php echo esc_html( $user->ID ); ?></li>
			<li><strong>Username:</strong> <?php echo esc_html( $user->user_login ); ?></li>
			<li><strong>User Roles:</strong> <?php echo esc_html( implode( ', ', (array) $user->roles ) ); ?></li>
		</ul>

		<h4>Capability Check for THIS User</h4>
		<ul>
			<li>Does user have capability '<strong>export_course_data</strong>'? 
				<strong style="color: <?php echo current_user_can( 'export_course_data' ) ? 'green' : 'red'; ?>;">
					<?php echo current_user_can( 'export_course_data' ) ? 'YES' : 'NO'; ?>
				</strong>
			</li>
             <li>Does user have capability '<strong>manage_options</strong>' (Admin)? 
				<strong style="color: <?php echo current_user_can( 'manage_options' ) ? 'green' : 'red'; ?>;">
					<?php echo current_user_can( 'manage_options' ) ? 'YES' : 'NO'; ?>
				</strong>
			</li>
		</ul>

		<h4>'gestor' Role Object Check</h4>
		<?php if ( empty( $role_object ) ) : ?>
			<p style="color: red;"><strong>CRITICAL ERROR: The role 'gestor' does not exist! Check the role name slug.</strong></p>
		<?php else : ?>
			<ul>
				<li>Does the '<strong>gestor</strong>' role object have the '<strong>export_course_data</strong>' capability?
					<strong style="color: <?php echo $role_object->has_cap( 'export_course_data' ) ? 'green' : 'red'; ?>;">
						<?php echo $role_object->has_cap( 'export_course_data' ) ? 'YES' : 'NO'; ?>
					</strong>
				</li>
			</ul>
			<p><em>(If this is NO, it means our `add_cap` function is not working or something is removing the capability after we add it.)</em></p>
		<?php endif; ?>
	</div>
	<?php
	return ob_get_clean();
}
add_shortcode( 'panapana_master_debug', 'panapana_master_debug_shortcode_ui' );

// =============================================================================
//  HELLO WORLD COURSE CREATOR - PROOF OF CONCEPT
// =============================================================================

/**
 * Creates a shortcode [panapana_hello_world_course] to create a test course.
 *
 * This is a proof-of-concept script that demonstrates how to programmatically
 * create a complete Tutor LMS course with topics and lessons.
 */
function panapana_hello_world_course_shortcode() {
	// Check permissions - allow both custom capability and administrators
	if ( ! current_user_can( 'export_course_data' ) && ! current_user_can( 'manage_options' ) ) {
		return '<p>Você não tem permissão para acessar esta funcionalidade.</p>';
	}

	// Check if course creation was triggered
	if ( isset( $_POST['create_hello_world_course'] ) && wp_verify_nonce( $_POST['_wpnonce'], 'create_hello_world_course' ) ) {
		$result = panapana_create_hello_world_course();
		if ( is_wp_error( $result ) ) {
			return '<div class="notice notice-error"><p>Erro: ' . esc_html( $result->get_error_message() ) . '</p></div>';
		} else {
			return '<div class="notice notice-success"><p>Curso "Hello World" criado com sucesso! <a href="' . esc_url( get_permalink( $result ) ) . '" target="_blank">Ver Curso</a></p></div>';
		}
	}

	// Display the creation form
	ob_start();
	?>
	<div class="hello-world-course-creator">
		<h3>Criar Curso "Hello World" - Prova de Conceito</h3>
		<p>Este botão criará um curso de teste completo com:</p>
		<ul>
			<li>1 Curso: "Hello World - Curso de Teste"</li>
			<li>1 Tópico: "Módulo 1: Introdução"</li>
			<li>1 Aula de Vídeo: "Aula 1: Bem-vindos"</li>
			<li>1 Aula de E-book: "E-book: Manual de Introdução"</li>
			<li>1 Quiz: "Quiz: Teste seus conhecimentos"</li>
		</ul>
		<form method="post">
			<?php wp_nonce_field( 'create_hello_world_course' ); ?>
			<input type="submit" name="create_hello_world_course" value="Criar Curso de Teste" class="button button-primary" />
		</form>
	</div>
	<?php
	return ob_get_clean();
}
add_shortcode( 'panapana_hello_world_course', 'panapana_hello_world_course_shortcode' );

/**
 * Creates a complete "Hello World" course with all components.
 *
 * @return int|WP_Error Course ID on success, WP_Error on failure.
 */
function panapana_create_hello_world_course() {
	// Step 1: Create the main course
	$course_data = array(
		'post_title'   => 'Hello World - Curso de Teste',
		'post_content' => '<p>Este é um curso de teste criado automaticamente para demonstrar a funcionalidade de criação programática de cursos no Tutor LMS.</p><p>O curso inclui vídeos, e-books e quizzes para mostrar todas as funcionalidades disponíveis.</p>',
		'post_status'  => 'publish',
		'post_type'    => 'courses',
		'post_author'  => get_current_user_id(),
		'menu_order'   => 0,
	);

	$course_id = wp_insert_post( $course_data );
	if ( is_wp_error( $course_id ) ) {
		return $course_id;
	}

	// Set course metadata
	update_post_meta( $course_id, '_tutor_course_price_type', 'free' );
	update_post_meta( $course_id, '_tutor_course_settings', array(
		'maximum_students'        => 0,
		'enable_content_drip'     => 0,
		'content_drip_type'       => '',
		'enrollment_expiry'       => '',
		'enable_tutor_bp'         => 0,
		'course_enrollment_period' => 'no',
		'enrollment_starts_at'    => '',
		'enrollment_ends_at'      => '',
		'pause_enrollment'        => 'no',
	) );

	// Step 2: Create a topic (module)
	$topic_data = array(
		'post_title'   => 'Módulo 1: Introdução',
		'post_content' => 'Este módulo introduz os conceitos básicos do curso.',
		'post_status'  => 'publish',
		'post_type'    => 'topics',
		'post_parent'  => $course_id,
		'post_author'  => get_current_user_id(),
		'menu_order'   => 1,
	);

	$topic_id = wp_insert_post( $topic_data );
	if ( is_wp_error( $topic_id ) ) {
		return $topic_id;
	}

	// Step 3: Create a video lesson
	$video_lesson_data = array(
		'post_title'   => 'Aula 1: Bem-vindos',
		'post_content' => '<p>Nesta aula de boas-vindas, você será introduzido aos conceitos fundamentais que serão abordados ao longo do curso.</p>',
		'post_status'  => 'publish',
		'post_type'    => 'lesson',
		'post_parent'  => $topic_id,
		'post_author'  => get_current_user_id(),
		'menu_order'   => 1,
	);

	$video_lesson_id = wp_insert_post( $video_lesson_data );
	if ( is_wp_error( $video_lesson_id ) ) {
		return $video_lesson_id;
	}

	// Set video lesson metadata (example with custom shortcode)
	$video_meta = array(
		'source'              => 'shortcode',
		'source_video_id'     => '',
		'poster'              => '',
		'source_external_url' => '',
		'source_shortcode'    => '[custom_video_with_vtt src="https://www.youtube.com/watch?v=dQw4w9WgXcQ" width="640" height="360" vtt="https://cursos.institutopanapana.org.br/wp-content/uploads/legendas/dQw4w9WgXcQ.pt.vtt"]',
		'source_youtube'      => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
		'source_vimeo'        => '',
		'source_embedded'     => '',
		'runtime'             => array(
			'hours'   => '00',
			'minutes' => '03',
			'seconds' => '32',
		),
		'duration_sec'        => '212',
		'playtime'            => '3:32',
	);
	update_post_meta( $video_lesson_id, '_video', $video_meta );

	// Step 4: Create an e-book lesson
	$ebook_lesson_data = array(
		'post_title'   => 'E-book: Manual de Introdução',
		'post_content' => '<p><iframe style="width: 100%;height: 700px" src="https://docs.google.com/gview?url=https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf&embedded=true" frameborder="0"></iframe></p><p style="text-align: center"><strong>Este é um e-book de exemplo para demonstrar a funcionalidade.</strong></p>',
		'post_status'  => 'publish',
		'post_type'    => 'lesson',
		'post_parent'  => $topic_id,
		'post_author'  => get_current_user_id(),
		'menu_order'   => 2,
	);

	$ebook_lesson_id = wp_insert_post( $ebook_lesson_data );
	if ( is_wp_error( $ebook_lesson_id ) ) {
		return $ebook_lesson_id;
	}

	// Step 5: Create a quiz
	$quiz_data = array(
		'post_title'   => 'Quiz: Teste seus conhecimentos',
		'post_content' => 'Este quiz testa o que você aprendeu no módulo de introdução.',
		'post_status'  => 'publish',
		'post_type'    => 'tutor_quiz',
		'post_parent'  => $topic_id,
		'post_author'  => get_current_user_id(),
		'menu_order'   => 3,
	);

	$quiz_id = wp_insert_post( $quiz_data );
	if ( is_wp_error( $quiz_id ) ) {
		return $quiz_id;
	}

	// Add quiz questions (this would require direct database insertion for full functionality)
	// For now, we'll just create the quiz post - questions can be added manually or via future automation

	return $course_id;
}

// =============================================================================
//  CSV COURSE PARSER - PHASE 2 AUTOMATION
// =============================================================================

/**
 * Creates a shortcode [panapana_csv_course_creator] for CSV-based course creation.
 *
 * This allows users to upload a CSV file and automatically create complete courses
 * with all content types: video lessons, e-books, and quizzes.
 */
function panapana_csv_course_creator_shortcode() {
	// Check permissions - allow both custom capability and administrators
	if ( ! current_user_can( 'export_course_data' ) && ! current_user_can( 'manage_options' ) ) {
		return '<p>Você não tem permissão para acessar esta funcionalidade.</p>';
	}

	// Handle CSV upload and processing
	if ( isset( $_POST['create_courses_from_csv'] ) && wp_verify_nonce( $_POST['_wpnonce'], 'create_courses_from_csv' ) ) {
		$result = panapana_process_csv_upload();
		if ( is_wp_error( $result ) ) {
			return '<div class="notice notice-error"><p>Erro: ' . esc_html( $result->get_error_message() ) . '</p></div>';
		} else {
			return '<div class="notice notice-success"><p>' . esc_html( $result ) . '</p></div>';
		}
	}

	// Display the upload form
	ob_start();
	?>
	<div class="csv-course-creator">
		<h3>Criador de Cursos via CSV - Automação Completa</h3>
		<p>Faça upload de um arquivo CSV para criar cursos automaticamente com:</p>
		<ul>
			<li>✅ Cursos com múltiplos módulos</li>
			<li>✅ Aulas de vídeo com shortcodes personalizados</li>
			<li>✅ E-books com visualizador PDF</li>
			<li>✅ Quizzes com perguntas e respostas</li>
		</ul>

		<h4>Formato do CSV:</h4>
		<p>O arquivo deve conter as colunas: <code>row_type, course_title, topic_title, item_title, item_type, content, description, order, youtube_url, vtt_filename, pdf_url, quiz_settings, question_type, question_points, answer_text, is_correct, answer_order</code></p>

		<form method="post" enctype="multipart/form-data">
			<?php wp_nonce_field( 'create_courses_from_csv' ); ?>
			<table class="form-table">
				<tr>
					<th scope="row">Arquivo CSV</th>
					<td>
						<input type="file" name="csv_file" accept=".csv" required />
						<p class="description">Selecione o arquivo CSV com a estrutura dos cursos.</p>
					</td>
				</tr>
			</table>
			<p class="submit">
				<input type="submit" name="create_courses_from_csv" value="Criar Cursos do CSV" class="button button-primary" />
			</p>
		</form>

		<h4>Exemplo de Estrutura CSV:</h4>
		<pre style="background: #f1f1f1; padding: 10px; font-size: 12px; overflow-x: auto;">
course,"Meu Curso","","","","","Descrição do curso",1
topic,"Meu Curso","Módulo 1","","","","Primeiro módulo",1
lesson,"Meu Curso","Módulo 1","Aula 1",video,"","Primeira aula",1,"https://youtube.com/watch?v=abc","abc.pt.vtt"
lesson,"Meu Curso","Módulo 1","E-book 1",ebook,"","Manual PDF",2,"","","https://example.com/manual.pdf"
quiz,"Meu Curso","Módulo 1","Quiz 1","","","Teste",3,"","","","time_limit:15,passing_score:70"
		</pre>
	</div>
	<?php
	return ob_get_clean();
}
add_shortcode( 'panapana_csv_course_creator', 'panapana_csv_course_creator_shortcode' );

/**
 * Processes the uploaded CSV file and creates courses.
 *
 * @return string|WP_Error Success message or error object.
 */
function panapana_process_csv_upload() {
	// Validate file upload
	if ( ! isset( $_FILES['csv_file'] ) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK ) {
		return new WP_Error( 'upload_error', 'Erro no upload do arquivo CSV.' );
	}

	$file_path = $_FILES['csv_file']['tmp_name'];
	$file_name = $_FILES['csv_file']['name'];

	// Validate file extension
	if ( pathinfo( $file_name, PATHINFO_EXTENSION ) !== 'csv' ) {
		return new WP_Error( 'invalid_file', 'Por favor, envie apenas arquivos CSV.' );
	}

	// Parse CSV and create courses
	$result = panapana_parse_csv_and_create_courses( $file_path );

	return $result;
}

/**
 * Main CSV parsing function that creates courses from CSV data.
 *
 * @param string $csv_file_path Path to the uploaded CSV file.
 * @return string|WP_Error Success message or error object.
 */
function panapana_parse_csv_and_create_courses( $csv_file_path ) {
	// Read and parse CSV file
	$csv_data = panapana_read_csv_file( $csv_file_path );
	if ( is_wp_error( $csv_data ) ) {
		return $csv_data;
	}

	// Group data by course for processing
	$courses_data = panapana_group_csv_data_by_course( $csv_data );
	if ( is_wp_error( $courses_data ) ) {
		return $courses_data;
	}

	$created_courses = array();
	$total_items = 0;

	// Process each course
	foreach ( $courses_data as $course_title => $course_data ) {
		$result = panapana_create_course_from_data( $course_data );
		if ( is_wp_error( $result ) ) {
			return new WP_Error( 'course_creation_error',
				"Erro ao criar curso '{$course_title}': " . $result->get_error_message() );
		}

		$created_courses[] = $course_title;
		$total_items += count( $course_data['topics'] );
	}

	$course_count = count( $created_courses );
	$course_list = implode( ', ', $created_courses );

	return "Sucesso! {$course_count} curso(s) criado(s): {$course_list}. Total de {$total_items} itens processados.";
}

/**
 * Reads and validates CSV file structure.
 *
 * @param string $file_path Path to CSV file.
 * @return array|WP_Error Array of CSV rows or error object.
 */
function panapana_read_csv_file( $file_path ) {
	if ( ! file_exists( $file_path ) || ! is_readable( $file_path ) ) {
		return new WP_Error( 'file_error', 'Arquivo CSV não encontrado ou não legível.' );
	}

	$csv_data = array();
	$headers = array();
	$row_number = 0;

	if ( ( $handle = fopen( $file_path, 'r' ) ) !== FALSE ) {
		while ( ( $row = fgetcsv( $handle, 1000, ',' ) ) !== FALSE ) {
			$row_number++;

			if ( $row_number === 1 ) {
				// First row should be headers
				$headers = array_map( 'trim', $row );

				// Validate required columns
				$required_columns = array( 'row_type', 'course_title' );
				foreach ( $required_columns as $required_col ) {
					if ( ! in_array( $required_col, $headers ) ) {
						fclose( $handle );
						return new WP_Error( 'invalid_csv', "Coluna obrigatória '{$required_col}' não encontrada no CSV." );
					}
				}
				continue;
			}

			// Skip empty rows
			if ( empty( array_filter( $row ) ) ) {
				continue;
			}

			// Create associative array with headers
			$row_data = array();
			foreach ( $headers as $index => $header ) {
				$row_data[ $header ] = isset( $row[ $index ] ) ? trim( $row[ $index ] ) : '';
			}

			// Validate row type
			$valid_row_types = array( 'course', 'topic', 'lesson', 'quiz', 'question', 'answer' );
			if ( ! in_array( $row_data['row_type'], $valid_row_types ) ) {
				fclose( $handle );
				return new WP_Error( 'invalid_row_type',
					"Tipo de linha inválido '{$row_data['row_type']}' na linha {$row_number}. Tipos válidos: " . implode( ', ', $valid_row_types ) );
			}

			$csv_data[] = $row_data;
		}
		fclose( $handle );
	} else {
		return new WP_Error( 'file_open_error', 'Não foi possível abrir o arquivo CSV.' );
	}

	if ( empty( $csv_data ) ) {
		return new WP_Error( 'empty_csv', 'Arquivo CSV está vazio ou não contém dados válidos.' );
	}

	return $csv_data;
}

/**
 * Groups CSV data by course for organized processing.
 *
 * @param array $csv_data Array of CSV rows.
 * @return array|WP_Error Grouped course data or error object.
 */
function panapana_group_csv_data_by_course( $csv_data ) {
	$courses = array();

	foreach ( $csv_data as $row ) {
		$course_title = $row['course_title'];

		if ( empty( $course_title ) ) {
			return new WP_Error( 'missing_course_title', 'course_title é obrigatório para todas as linhas.' );
		}

		// Initialize course structure if not exists
		if ( ! isset( $courses[ $course_title ] ) ) {
			$courses[ $course_title ] = array(
				'course_info' => null,
				'topics' => array(),
				'current_topic' => null,
				'current_quiz' => null,
				'current_question' => null,
			);
		}

		$course = &$courses[ $course_title ];

		switch ( $row['row_type'] ) {
			case 'course':
				$course['course_info'] = $row;
				break;

			case 'topic':
				$topic_title = $row['topic_title'];
				if ( empty( $topic_title ) ) {
					return new WP_Error( 'missing_topic_title', 'topic_title é obrigatório para linhas do tipo topic.' );
				}

				$course['topics'][ $topic_title ] = array(
					'topic_info' => $row,
					'lessons' => array(),
					'quizzes' => array(),
				);
				$course['current_topic'] = $topic_title;
				break;

			case 'lesson':
				if ( empty( $course['current_topic'] ) ) {
					return new WP_Error( 'lesson_without_topic', 'Aula encontrada sem tópico definido.' );
				}
				$course['topics'][ $course['current_topic'] ]['lessons'][] = $row;
				break;

			case 'quiz':
				if ( empty( $course['current_topic'] ) ) {
					return new WP_Error( 'quiz_without_topic', 'Quiz encontrado sem tópico definido.' );
				}
				$quiz_title = $row['item_title'];
				$course['topics'][ $course['current_topic'] ]['quizzes'][ $quiz_title ] = array(
					'quiz_info' => $row,
					'questions' => array(),
				);
				$course['current_quiz'] = $quiz_title;
				break;

			case 'question':
				if ( empty( $course['current_quiz'] ) ) {
					return new WP_Error( 'question_without_quiz', 'Pergunta encontrada sem quiz definido.' );
				}
				$question_text = $row['content'];
				$course['topics'][ $course['current_topic'] ]['quizzes'][ $course['current_quiz'] ]['questions'][] = array(
					'question_info' => $row,
					'answers' => array(),
				);
				$course['current_question'] = count( $course['topics'][ $course['current_topic'] ]['quizzes'][ $course['current_quiz'] ]['questions'] ) - 1;
				break;

			case 'answer':
				if ( $course['current_question'] === null ) {
					return new WP_Error( 'answer_without_question', 'Resposta encontrada sem pergunta definida.' );
				}
				$course['topics'][ $course['current_topic'] ]['quizzes'][ $course['current_quiz'] ]['questions'][ $course['current_question'] ]['answers'][] = $row;
				break;
		}
	}

	// Validate that each course has required data
	foreach ( $courses as $course_title => $course_data ) {
		if ( empty( $course_data['course_info'] ) ) {
			return new WP_Error( 'missing_course_info', "Informações do curso '{$course_title}' não encontradas." );
		}
		if ( empty( $course_data['topics'] ) ) {
			return new WP_Error( 'missing_topics', "Curso '{$course_title}' não possui tópicos definidos." );
		}
	}

	return $courses;
}

/**
 * Creates a complete course from grouped CSV data.
 *
 * @param array $course_data Grouped course data from CSV.
 * @return int|WP_Error Course ID on success, error object on failure.
 */
function panapana_create_course_from_data( $course_data ) {
	$course_info = $course_data['course_info'];

	// Step 1: Create the main course
	$course_post_data = array(
		'post_title'   => $course_info['course_title'],
		'post_content' => ! empty( $course_info['description'] ) ? $course_info['description'] : 'Curso criado automaticamente via CSV.',
		'post_status'  => 'publish',
		'post_type'    => 'courses',
		'post_author'  => get_current_user_id(),
		'menu_order'   => ! empty( $course_info['order'] ) ? intval( $course_info['order'] ) : 0,
	);

	$course_id = wp_insert_post( $course_post_data );
	if ( is_wp_error( $course_id ) ) {
		return $course_id;
	}

	// Set course metadata
	update_post_meta( $course_id, '_tutor_course_price_type', 'free' );
	update_post_meta( $course_id, '_tutor_course_settings', array(
		'maximum_students'        => 0,
		'enable_content_drip'     => 0,
		'content_drip_type'       => '',
		'enrollment_expiry'       => '',
		'enable_tutor_bp'         => 0,
		'course_enrollment_period' => 'no',
		'enrollment_starts_at'    => '',
		'enrollment_ends_at'      => '',
		'pause_enrollment'        => 'no',
	) );

	// Step 2: Create topics and their content
	foreach ( $course_data['topics'] as $topic_title => $topic_data ) {
		$topic_result = panapana_create_topic_with_content( $course_id, $topic_data );
		if ( is_wp_error( $topic_result ) ) {
			return new WP_Error( 'topic_creation_error',
				"Erro ao criar tópico '{$topic_title}': " . $topic_result->get_error_message() );
		}
	}

	return $course_id;
}

/**
 * Creates a topic and all its lessons and quizzes.
 *
 * @param int   $course_id   Parent course ID.
 * @param array $topic_data  Topic data with lessons and quizzes.
 * @return int|WP_Error Topic ID on success, error object on failure.
 */
function panapana_create_topic_with_content( $course_id, $topic_data ) {
	$topic_info = $topic_data['topic_info'];

	// Create the topic
	$topic_post_data = array(
		'post_title'   => $topic_info['topic_title'],
		'post_content' => ! empty( $topic_info['description'] ) ? $topic_info['description'] : '',
		'post_status'  => 'publish',
		'post_type'    => 'topics',
		'post_parent'  => $course_id,
		'post_author'  => get_current_user_id(),
		'menu_order'   => ! empty( $topic_info['order'] ) ? intval( $topic_info['order'] ) : 0,
	);

	$topic_id = wp_insert_post( $topic_post_data );
	if ( is_wp_error( $topic_id ) ) {
		return $topic_id;
	}

	// Create lessons
	foreach ( $topic_data['lessons'] as $lesson_data ) {
		$lesson_result = panapana_create_lesson_from_data( $topic_id, $lesson_data );
		if ( is_wp_error( $lesson_result ) ) {
			return new WP_Error( 'lesson_creation_error',
				"Erro ao criar aula '{$lesson_data['item_title']}': " . $lesson_result->get_error_message() );
		}
	}

	// Create quizzes
	foreach ( $topic_data['quizzes'] as $quiz_title => $quiz_data ) {
		$quiz_result = panapana_create_quiz_from_data( $topic_id, $quiz_data );
		if ( is_wp_error( $quiz_result ) ) {
			return new WP_Error( 'quiz_creation_error',
				"Erro ao criar quiz '{$quiz_title}': " . $quiz_result->get_error_message() );
		}
	}

	return $topic_id;
}

/**
 * Creates a lesson based on its type (video or ebook).
 *
 * @param int   $topic_id    Parent topic ID.
 * @param array $lesson_data Lesson data from CSV.
 * @return int|WP_Error Lesson ID on success, error object on failure.
 */
function panapana_create_lesson_from_data( $topic_id, $lesson_data ) {
	$item_type = $lesson_data['item_type'];
	$content = '';
	$video_meta = null;

	// Generate content based on lesson type
	switch ( $item_type ) {
		case 'video':
			$youtube_url = ! empty( $lesson_data['youtube_url'] ) ? $lesson_data['youtube_url'] : '';
			$vtt_filename = ! empty( $lesson_data['vtt_filename'] ) ? $lesson_data['vtt_filename'] : '';

			if ( empty( $youtube_url ) ) {
				return new WP_Error( 'missing_youtube_url', 'URL do YouTube é obrigatória para aulas de vídeo.' );
			}

			// Generate video shortcode with configurable VTT path
			$vtt_url = '';
			if ( ! empty( $vtt_filename ) ) {
				// Use local path for development, production path for live site
				$base_url = ( wp_get_environment_type() === 'local' || strpos( home_url(), 'localhost' ) !== false )
					? home_url( '/wp-content/uploads/legendas/' )
					: 'https://cursos.institutopanapana.org.br/wp-content/uploads/legendas/';
				$vtt_url = $base_url . $vtt_filename;
			}

			$shortcode = '[custom_video_with_vtt src="' . esc_attr( $youtube_url ) . '" width="640" height="360"';
			if ( ! empty( $vtt_url ) ) {
				$shortcode .= ' vtt="' . esc_attr( $vtt_url ) . '"';
			}
			$shortcode .= ']';

			$content = '<p>' . $lesson_data['description'] . '</p>';

			// Prepare video metadata
			$video_meta = array(
				'source'              => 'shortcode',
				'source_video_id'     => '',
				'poster'              => '',
				'source_external_url' => '',
				'source_shortcode'    => $shortcode,
				'source_youtube'      => $youtube_url,
				'source_vimeo'        => '',
				'source_embedded'     => '',
				'runtime'             => array( 'hours' => '00', 'minutes' => '00', 'seconds' => '00' ),
				'duration_sec'        => '0',
				'playtime'            => '0:00',
			);
			break;

		case 'ebook':
			$pdf_url = ! empty( $lesson_data['pdf_url'] ) ? $lesson_data['pdf_url'] : '';

			if ( empty( $pdf_url ) ) {
				return new WP_Error( 'missing_pdf_url', 'URL do PDF é obrigatória para aulas de e-book.' );
			}

			// Generate iframe content
			$iframe_src = 'https://docs.google.com/gview?url=' . urlencode( $pdf_url ) . '&embedded=true';
			$content = '<p>' . $lesson_data['description'] . '</p>';
			$content .= '<p><iframe style="width: 100%;height: 700px" src="' . esc_attr( $iframe_src ) . '" frameborder="0"></iframe></p>';
			break;

		default:
			return new WP_Error( 'invalid_lesson_type', "Tipo de aula inválido: '{$item_type}'. Use 'video' ou 'ebook'." );
	}

	// Create the lesson post
	$lesson_post_data = array(
		'post_title'   => $lesson_data['item_title'],
		'post_content' => $content,
		'post_status'  => 'publish',
		'post_type'    => 'lesson',
		'post_parent'  => $topic_id,
		'post_author'  => get_current_user_id(),
		'menu_order'   => ! empty( $lesson_data['order'] ) ? intval( $lesson_data['order'] ) : 0,
	);

	$lesson_id = wp_insert_post( $lesson_post_data );
	if ( is_wp_error( $lesson_id ) ) {
		return $lesson_id;
	}

	// Add video metadata if it's a video lesson
	if ( $video_meta ) {
		update_post_meta( $lesson_id, '_video', $video_meta );
	}

	return $lesson_id;
}

/**
 * Creates a quiz with questions and answers from CSV data.
 *
 * @param int   $topic_id   Parent topic ID.
 * @param array $quiz_data  Quiz data with questions and answers.
 * @return int|WP_Error Quiz ID on success, error object on failure.
 */
function panapana_create_quiz_from_data( $topic_id, $quiz_data ) {
	global $wpdb;

	$quiz_info = $quiz_data['quiz_info'];

	// Create the quiz post
	$quiz_post_data = array(
		'post_title'   => $quiz_info['item_title'],
		'post_content' => ! empty( $quiz_info['description'] ) ? $quiz_info['description'] : '',
		'post_status'  => 'publish',
		'post_type'    => 'tutor_quiz',
		'post_parent'  => $topic_id,
		'post_author'  => get_current_user_id(),
		'menu_order'   => ! empty( $quiz_info['order'] ) ? intval( $quiz_info['order'] ) : 0,
	);

	$quiz_id = wp_insert_post( $quiz_post_data );
	if ( is_wp_error( $quiz_id ) ) {
		return $quiz_id;
	}

	// Parse quiz settings from CSV
	$quiz_settings = panapana_parse_quiz_settings( $quiz_info['quiz_settings'] );

	// Set quiz metadata with default settings
	$quiz_option = array(
		'time_limit'                => array(
			'time_type' => 'minutes',
			'time_limit' => ! empty( $quiz_settings['time_limit'] ) ? intval( $quiz_settings['time_limit'] ) : 0,
		),
		'hide_quiz_time_display'    => '',
		'quiz_auto_start'           => '',
		'question_layout_view'      => 'single_question',
		'questions_order'           => 'rand',
		'hide_question_number_overview' => '',
		'max_questions_for_answer'  => 10,
		'max_attempts_allowed'      => ! empty( $quiz_settings['max_attempts'] ) ? intval( $quiz_settings['max_attempts'] ) : 0,
		'passing_grade'             => ! empty( $quiz_settings['passing_score'] ) ? intval( $quiz_settings['passing_score'] ) : 80,
		'max_grade'                 => 100,
		'feedback_mode'             => 'default',
		'attempts_allowed'          => ! empty( $quiz_settings['max_attempts'] ) ? intval( $quiz_settings['max_attempts'] ) : 0,
		'quiz_auto_start'           => 0,
	);

	update_post_meta( $quiz_id, 'tutor_quiz_option', $quiz_option );

	// Create questions and answers
	foreach ( $quiz_data['questions'] as $question_order => $question_data ) {
		$question_result = panapana_create_quiz_question( $quiz_id, $question_data, $question_order );
		if ( is_wp_error( $question_result ) ) {
			return new WP_Error( 'question_creation_error',
				"Erro ao criar pergunta '{$question_data['question_info']['content']}': " . $question_result->get_error_message() );
		}
	}

	return $quiz_id;
}

/**
 * Parses quiz settings from CSV format.
 *
 * @param string $settings_string Settings in format "key1:value1,key2:value2"
 * @return array Parsed settings array.
 */
function panapana_parse_quiz_settings( $settings_string ) {
	$settings = array();

	if ( empty( $settings_string ) ) {
		return $settings;
	}

	$pairs = explode( ',', $settings_string );
	foreach ( $pairs as $pair ) {
		$parts = explode( ':', trim( $pair ), 2 );
		if ( count( $parts ) === 2 ) {
			$key = trim( $parts[0] );
			$value = trim( $parts[1] );
			$settings[ $key ] = $value;
		}
	}

	return $settings;
}

/**
 * Creates a quiz question with its answers.
 *
 * @param int   $quiz_id        Parent quiz ID.
 * @param array $question_data  Question data with answers.
 * @param int   $question_order Question order.
 * @return int|WP_Error Question ID on success, error object on failure.
 */
function panapana_create_quiz_question( $quiz_id, $question_data, $question_order ) {
	global $wpdb;

	$question_info = $question_data['question_info'];
	$question_type = $question_info['question_type'];
	$question_points = ! empty( $question_info['question_points'] ) ? floatval( $question_info['question_points'] ) : 1.00;

	// Prepare question settings
	$question_settings = array(
		'question_type' => $question_type,
		'question_mark' => number_format( $question_points, 2 ),
	);

	// Insert question into wp_tutor_quiz_questions table
	$question_table = $wpdb->prefix . 'tutor_quiz_questions';
	$question_insert_result = $wpdb->insert(
		$question_table,
		array(
			'quiz_id'             => $quiz_id,
			'question_title'      => $question_info['content'],
			'question_description' => '',
			'answer_explanation'  => '',
			'question_type'       => $question_type,
			'question_mark'       => $question_points,
			'question_settings'   => serialize( $question_settings ),
			'question_order'      => $question_order,
		),
		array( '%d', '%s', '%s', '%s', '%s', '%f', '%s', '%d' )
	);

	if ( false === $question_insert_result ) {
		return new WP_Error( 'question_insert_failed', 'Falha ao inserir pergunta no banco de dados: ' . $wpdb->last_error );
	}

	$question_id = $wpdb->insert_id;

	// Create answers for this question
	foreach ( $question_data['answers'] as $answer_data ) {
		$answer_result = panapana_create_quiz_answer( $question_id, $answer_data, $question_type );
		if ( is_wp_error( $answer_result ) ) {
			return new WP_Error( 'answer_creation_error',
				"Erro ao criar resposta '{$answer_data['answer_text']}': " . $answer_result->get_error_message() );
		}
	}

	return $question_id;
}

/**
 * Creates a quiz answer.
 *
 * @param int    $question_id   Parent question ID.
 * @param array  $answer_data   Answer data from CSV.
 * @param string $question_type Type of question (single_choice, true_false, etc.).
 * @return int|WP_Error Answer ID on success, error object on failure.
 */
function panapana_create_quiz_answer( $question_id, $answer_data, $question_type ) {
	global $wpdb;

	$is_correct = ! empty( $answer_data['is_correct'] ) ? intval( $answer_data['is_correct'] ) : 0;
	$answer_order = ! empty( $answer_data['answer_order'] ) ? intval( $answer_data['answer_order'] ) : 0;

	// For true_false questions, set special values
	$answer_two_gap_match = '';
	if ( 'true_false' === $question_type ) {
		$answer_two_gap_match = ( 1 === $is_correct && stripos( $answer_data['answer_text'], 'verdadeiro' ) !== false ) ? 'true' : 'false';
	}

	// Insert answer into wp_tutor_quiz_question_answers table
	$answer_table = $wpdb->prefix . 'tutor_quiz_question_answers';
	$answer_insert_result = $wpdb->insert(
		$answer_table,
		array(
			'belongs_question_id'   => $question_id,
			'belongs_question_type' => $question_type,
			'answer_title'          => $answer_data['answer_text'],
			'is_correct'            => $is_correct,
			'image_id'              => null,
			'answer_two_gap_match'  => $answer_two_gap_match,
			'answer_view_format'    => 'text',
			'answer_settings'       => '',
			'answer_order'          => $answer_order,
		),
		array( '%d', '%s', '%s', '%d', '%d', '%s', '%s', '%s', '%d' )
	);

	if ( false === $answer_insert_result ) {
		return new WP_Error( 'answer_insert_failed', 'Falha ao inserir resposta no banco de dados: ' . $wpdb->last_error );
	}

	return $wpdb->insert_id;
}