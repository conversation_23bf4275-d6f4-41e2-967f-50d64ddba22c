<?php
/**
 * Test page for CSV Course Automation
 * 
 * This file can be accessed directly via browser to test the CSV course creation functionality.
 * URL: http://localhost/test-csv-automation.php
 */

// Load WordPress
define('WP_USE_THEMES', false);
require_once('wp-load.php');

// Check if user is logged in and has permissions
if (!is_user_logged_in()) {
    wp_die('You must be logged in to access this page.');
}

if (!current_user_can('export_course_data') && !current_user_can('manage_options')) {
    wp_die('You do not have permission to access this functionality.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>CSV Course Automation Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 3px; cursor: pointer; text-decoration: none; display: inline-block; }
        .button:hover { background: #005a87; }
    </style>
</head>
<body>
    <div class="container">
        <h1>CSV Course Automation Test</h1>
        
        <div class="test-section info">
            <h2>System Status</h2>
            <p><strong>WordPress Version:</strong> <?php echo get_bloginfo('version'); ?></p>
            <p><strong>Current User:</strong> <?php echo wp_get_current_user()->display_name; ?> (ID: <?php echo get_current_user_id(); ?>)</p>
            <p><strong>User Capabilities:</strong> 
                <?php if (current_user_can('export_course_data')): ?>
                    ✅ export_course_data
                <?php else: ?>
                    ❌ export_course_data
                <?php endif; ?>
                
                <?php if (current_user_can('manage_options')): ?>
                    ✅ manage_options
                <?php else: ?>
                    ❌ manage_options
                <?php endif; ?>
            </p>
        </div>

        <div class="test-section">
            <h2>Function Availability Test</h2>
            <?php
            $functions_to_test = [
                // Core functions
                'panapana_user_can_access_automation',
                'panapana_process_csv_upload',
                'panapana_read_csv_file',
                'panapana_group_csv_data_by_course',
                'panapana_create_course_from_data',
                'panapana_create_topic_with_content',
                'panapana_create_lesson_from_data',
                'panapana_create_quiz_from_data',
                'panapana_create_quiz_question',
                'panapana_create_quiz_answer',
                'panapana_parse_quiz_settings',
                // Shortcode functions
                'panapana_csv_course_creator_shortcode',
                'panapana_course_exporter_shortcode',
                'panapana_hello_world_course_shortcode',
                'panapana_master_debug_shortcode'
            ];

            echo '<ul>';
            foreach ($functions_to_test as $function) {
                $exists = function_exists($function);
                echo '<li>' . ($exists ? '✅' : '❌') . ' ' . $function . '</li>';
            }
            echo '</ul>';
            ?>
        </div>

        <div class="test-section">
            <h2>Shortcode Registration Test</h2>
            <?php
            global $shortcode_tags;
            $shortcodes_to_test = [
                'panapana_csv_course_creator',
                'panapana_hello_world_course',
                'tutor_course_exporter',
                'panapana_master_debug'
            ];
            
            echo '<ul>';
            foreach ($shortcodes_to_test as $shortcode) {
                $registered = isset($shortcode_tags[$shortcode]);
                echo '<li>' . ($registered ? '✅' : '❌') . ' [' . $shortcode . ']</li>';
            }
            echo '</ul>';
            ?>
        </div>

        <div class="test-section">
            <h2>Database Tables Test</h2>
            <?php
            global $wpdb;
            $tables_to_test = [
                $wpdb->prefix . 'tutor_quiz_questions',
                $wpdb->prefix . 'tutor_quiz_question_answers',
                $wpdb->prefix . 'posts'
            ];
            
            echo '<ul>';
            foreach ($tables_to_test as $table) {
                $exists = $wpdb->get_var("SHOW TABLES LIKE '$table'") == $table;
                echo '<li>' . ($exists ? '✅' : '❌') . ' ' . $table . '</li>';
            }
            echo '</ul>';
            ?>
        </div>

        <div class="test-section">
            <h2>Sample CSV File Test</h2>
            <?php
            $sample_csv_path = '../backlog/sample-course.csv';
            if (file_exists($sample_csv_path)) {
                echo '<p>✅ Sample CSV file found at: ' . $sample_csv_path . '</p>';
                echo '<p><strong>File size:</strong> ' . filesize($sample_csv_path) . ' bytes</p>';
                echo '<p><strong>First 5 lines:</strong></p>';
                echo '<pre>';
                $lines = file($sample_csv_path, FILE_IGNORE_NEW_LINES);
                for ($i = 0; $i < min(5, count($lines)); $i++) {
                    echo htmlspecialchars($lines[$i]) . "\n";
                }
                echo '</pre>';
            } else {
                echo '<p>❌ Sample CSV file not found at: ' . $sample_csv_path . '</p>';
            }
            ?>
        </div>

        <div class="test-section">
            <h2>System Information</h2>
            <?php
            if (function_exists('panapana_get_automation_info')) {
                $system_info = panapana_get_automation_info();
                echo '<ul>';
                echo '<li><strong>Automation Version:</strong> ' . $system_info['version'] . '</li>';
                echo '<li><strong>WordPress Version:</strong> ' . $system_info['wordpress_version'] . '</li>';
                echo '<li><strong>Tutor LMS Active:</strong> ' . ($system_info['tutor_active'] ? '✅ Yes' : '❌ No') . '</li>';
                echo '<li><strong>Database Tables:</strong></li>';
                echo '<ul>';
                foreach ($system_info['required_tables'] as $table => $exists) {
                    echo '<li>' . ($exists ? '✅' : '❌') . ' ' . $table . '</li>';
                }
                echo '</ul>';
                echo '</ul>';
            } else {
                echo '<p>❌ System info function not available</p>';
            }
            ?>
        </div>

        <div class="test-section">
            <h2>CSV Course Creator Shortcode Test</h2>
            <p>This will render the actual shortcode form:</p>
            <div style="border: 1px solid #ccc; padding: 15px; background: #f9f9f9;">
                <?php
                if (function_exists('panapana_csv_course_creator_shortcode')) {
                    echo do_shortcode('[panapana_csv_course_creator]');
                } else {
                    echo '<p class="error">❌ Shortcode function not available</p>';
                }
                ?>
            </div>
        </div>

        <div class="test-section">
            <h2>Quick Actions</h2>
            <p>
                <a href="wp-admin/" class="button">Go to WordPress Admin</a>
                <a href="wp-admin/edit.php?post_type=courses" class="button">View Courses</a>
                <a href="wp-admin/edit.php?post_type=tutor_quiz" class="button">View Quizzes</a>
            </p>
        </div>

        <div class="test-section info">
            <h2>Next Steps</h2>
            <ol>
                <li>If all functions show ✅, the implementation is ready for testing</li>
                <li>Use the CSV upload form above to test with the sample CSV file</li>
                <li>Check the WordPress admin to see if courses are created successfully</li>
                <li>Verify that videos, e-books, and quizzes are properly structured</li>
            </ol>
        </div>
    </div>
</body>
</html>
