(()=>{"use strict";const e=window.wp.blocks,t=window.wp.i18n,n=window.wp.blockEditor,a=window.wp.components,l=function(e){var a=e.attributes,l=e.setAttributes,r=a.label,i=a.hide_label,c=a.required,o=a.instructions;return React.createElement("div",{className:"acf-field"},React.createElement("div",{className:"acf-label"},React.createElement("label",null,!i&&React.createElement(n.RichText,{tagName:"label",onChange:function(e){return l({label:e})},withoutInteractiveFormatting:!0,placeholder:(0,t.__)("Text Field","acf-frontend-form-element"),value:r}),c&&React.createElement("span",{className:"acf-required"},"*"))),React.createElement("div",{className:"acf-input"},o&&React.createElement(n.RichText,{tagName:"p",className:"description",onChange:function(e){return l({instructions:e})},withoutInteractiveFormatting:!0,value:o}),React.createElement("div",{className:"acf-input-wrap",style:{display:"flex",width:"100%"}},e.children)))},r=window.React;var i="acf-frontend-form-element";const c=function(e){var l=e.attributes,c=e.setAttributes,o=l.label,u=l.hide_label,d=l.required,s=l.instructions,p=function(e){return e.toLowerCase().replace(/[^a-z0-9 _]/g,"").replace(/\s+/g,"_")};return(0,r.useEffect)((function(){"field_key"in l&&!l.field_key&&c({field_key:Math.random().toString(36).substring(2,10)})}),[]),React.createElement(n.InspectorControls,{field_key:"fea-inspector-controls"},React.createElement(a.PanelBody,{title:(0,t.__)("General",i),initialOpen:!0},React.createElement(a.TextControl,{label:(0,t.__)("Label",i),value:o,onChange:function(e){return c({label:e})}}),React.createElement(a.ToggleControl,{label:(0,t.__)("Hide Label",i),checked:u,onChange:function(e){return c({hide_label:e})}}),"name"in l&&React.createElement(a.TextControl,{label:(0,t.__)("Name",i),value:l.name||p(o),onChange:function(e){return c({name:p(e)})}}),"field_key"in l&&React.createElement(a.TextControl,{label:(0,t.__)("Field Key",i),value:l.field_key,readOnly:!0,onChange:function(e){}}),React.createElement(a.TextareaControl,{label:(0,t.__)("Instructions",i),rows:"3",value:s,onChange:function(e){return c({instructions:e})}}),React.createElement(a.ToggleControl,{label:(0,t.__)("Required",i),checked:d,onChange:function(e){return c({required:e})}}),e.children))};var o="acf-frontend-form-element";const u=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":2,"name":"frontend-admin/time-field","title":"Time Field","description":"Displays a time field.","category":"frontend-admin","textdomain":"frontend-admin","icon":"list-view","supports":{"align":["wide"]},"attributes":{"name":{"type":"string","default":""},"label":{"type":"string","default":"Text Field"},"hide_label":{"type":"boolean","default":""},"required":{"type":"boolean","default":""},"default_value":{"type":"string","default":""},"placeholder":{"type":"string","default":""},"instructions":{"type":"string","default":""},"prepend":{"type":"string","default":""},"append":{"type":"string","default":""}},"editorScript":"file:../../time/index.js"}');(0,e.registerBlockType)(u,{edit:function(e){var r=e.attributes,i=e.setAttributes,u=r.default_value,d=r.placeholder,s=r.prepend,p=r.append,f=(0,n.useBlockProps)();return React.createElement("div",f,React.createElement(c,e,React.createElement(a.TextControl,{type:"time",label:(0,t.__)("Default Value",o),value:u,onChange:function(e){return i({default_value:e})}}),React.createElement(a.TextControl,{label:(0,t.__)("Placeholder",o),value:d,onChange:function(e){return i({placeholder:e})}}),React.createElement(a.TextControl,{label:(0,t.__)("Prepend",o),value:s,onChange:function(e){return i({prepend:e})}}),React.createElement(a.TextControl,{label:(0,t.__)("Append",o),value:p,onChange:function(e){return i({append:e})}})),React.createElement(l,e,s&&React.createElement("span",{className:"acf-input-prepend"},s),React.createElement("input",{type:"time",placeholder:d,value:u,onChange:function(e){i({default_value:e.target.value})},style:{width:"auto",flexGrow:1}}),p&&React.createElement("span",{className:"acf-input-append"},p)))},save:function(){return null}})})();