# Setup New Environment Script
# Complete setup for new PC/environment

param(
    [string]$WordPressPath = "public"
)

Write-Host "🏗️  Setting up Course Creator on new environment..." -ForegroundColor Green

# Check if we're in the right directory
if (-not (Test-Path "themes\geeks-child")) {
    Write-Host "❌ Not in course_creator repository root!" -ForegroundColor Red
    Write-Host "📍 Run this script from the repository root directory" -ForegroundColor Yellow
    exit 1
}

# Check WordPress installation
if (-not (Test-Path "$WordPressPath\wp-config.php")) {
    Write-Host "❌ WordPress not found at: $WordPressPath" -ForegroundColor Red
    Write-Host "📍 Make sure WordPress is installed and path is correct" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ Repository and WordPress found!" -ForegroundColor Green

# Deploy theme
Write-Host ""
Write-Host "📦 Deploying theme..." -ForegroundColor Cyan
& ".\scripts\deploy-theme.ps1" -WordPressPath $WordPressPath

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Theme deployment failed!" -ForegroundColor Red
    exit 1
}

# Check Tutor LMS
Write-Host ""
Write-Host "🔍 Checking WordPress setup..." -ForegroundColor Cyan

$PluginsPath = "$WordPressPath\wp-content\plugins"
$TutorPath = "$PluginsPath\tutor"

if (Test-Path $TutorPath) {
    Write-Host "✅ Tutor LMS plugin found" -ForegroundColor Green
} else {
    Write-Host "⚠️  Tutor LMS plugin not found!" -ForegroundColor Yellow
    Write-Host "📍 Install Tutor LMS plugin before proceeding" -ForegroundColor Yellow
}

# Check parent theme
$ParentThemePath = "$WordPressPath\wp-content\themes\geeks"
if (Test-Path $ParentThemePath) {
    Write-Host "✅ Geeks parent theme found" -ForegroundColor Green
} else {
    Write-Host "⚠️  Geeks parent theme not found!" -ForegroundColor Yellow
    Write-Host "📍 Install Geeks theme before activating child theme" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎯 Setup Complete!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Manual Steps Required:" -ForegroundColor Cyan
Write-Host "1. Go to WordPress Admin → Appearance → Themes" -ForegroundColor White
Write-Host "2. Activate 'Geeks Child' theme" -ForegroundColor White
Write-Host "3. Go to WordPress Admin → Plugins" -ForegroundColor White
Write-Host "4. Activate 'Tutor LMS' plugin (if not active)" -ForegroundColor White
Write-Host "5. Create a test page with: [panapana_master_debug]" -ForegroundColor White
Write-Host "6. Verify all functions show as 'EXISTS'" -ForegroundColor White
Write-Host ""
Write-Host "🧪 Test the system:" -ForegroundColor Cyan
Write-Host "- Upload backlog/sample-course.csv using [panapana_csv_course_creator]" -ForegroundColor White
Write-Host "- Check if course 'Git - Iniciante ao Avançado' is created" -ForegroundColor White
