<?php
/**
 * <PERSON><PERSON> login form template (from Geeks Parent Theme)
 *
 * OVER<PERSON>DDEN IN GEEKS CHILD THEME FOR TEXT TRANSLATION
 *
 * @package <PERSON><PERSON>\Templates
 * <AUTHOR> <<EMAIL>>
 * @link https://themeum.com
 * @since 2.0.1
 */

use TUTOR\Ajax;

$lost_pass = apply_filters( 'tutor_lostpassword_url', wp_lostpassword_url() );
$value_remember =false;
/**
 * Get login validation errors & print
 *
 * @since 2.1.3
 */
$login_errors = get_transient( Ajax::LOGIN_ERRORS_TRANSIENT_KEY ) ? get_transient( Ajax::LOGIN_ERRORS_TRANSIENT_KEY ) : array();
foreach ( $login_errors as $login_error ) {
    ?>
    <div class="tutor-alert alert alert-warning tutor-mb-12" style="display:block; grid-gap: 0px 10px;">
        <?php
        echo wp_kses(
            $login_error,
            array(
                'strong' => true,
                'a'      => array(
                    'href'  => true,
                    'class' => true,
                    'id'    => true,
                ),
                'p'      => array(
                    'class' => true,
                    'id'    => true,
                ),
                'div'    => array(
                    'class' => true,
                    'id'    => true,
                ),
            )
        );
        ?>
    </div>
    <?php
}
do_action( 'tutor_before_login_form' );
?>

<form id="tutor-login-form" method="post">
    <?php if ( is_single_course() ): ?>
        <input type="hidden" name="tutor_course_enroll_attempt" value="<?php echo esc_attr( get_the_ID() ); ?>">
    <?php endif; ?>
    <?php tutor_nonce_field(); ?>
    <input type="hidden" name="tutor_action" value="tutor_user_login" />
    <input type="hidden" name="redirect_to" value="<?php echo esc_url( apply_filters( 'tutor_after_login_redirect_url', tutor()->current_url ) ); ?>" />

    <p class="login-username mb-3">
        <label class="form-label fs-5" for="<?php echo esc_attr( 'user_login' ); ?>"><?php esc_html_e( 'Username', 'geeks' ); ?></label>
        <input type="text" placeholder="<?php esc_html_e('Username or Email Address', 'geeks');?>" name="log" id="<?php echo esc_attr( 'user_login' ); ?>" class="input form-control" value="" size="20" required/>
    </p>

    <p class="login-password mb-3">
        <label class="form-label fs-5" for="<?php echo esc_attr( 'user_pass' ); ?>"><?php esc_html_e( 'Password', 'geeks' ); ?></label>
        <input type="password" placeholder="<?php echo esc_html__( 'Password', 'geeks' ); ?>" name="pwd" id="<?php echo esc_attr( 'user_pass' ); ?>" class="input form-control" value="" size="20"required/>
    </p>

    <div class="tutor-login-error">

    </div>
    <?php
        do_action("tutor_login_form_middle");
        do_action("login_form");
        apply_filters("login_form_middle", '', '');
    ?>

    <div class="tutor-login-rememeber-wrap d-lg-flex justify-content-between align-items-center mb-4">
                        
        <p class="login-remember form-check mb-0">
            <input name="rememberme" class="form-check-input" type="checkbox" id="<?php echo esc_attr( 'rememberme' ); ?>" 
                value="forever"
                <?php $value_remember ? 'checked' : ''; ?>>
            <label class="form-check-label fs-5" for="<?php echo esc_attr( 'rememberme' ); ?>">
                <?php echo esc_html__( 'Remember Me', 'geeks' ); ?>
            </label>
        </p>
        
        <div>
            <a class="fs-5" href="<?php echo esc_url( $lost_pass ); ?>">
                 Esqueceu a senha?
            </a>
        </div>
    </div>


    <?php do_action("tutor_login_form_end"); ?>

    <p class="login-submit d-grid mb-0">
        <button type="submit" class="btn btn-primary tutor-is-block">
            <?php esc_html_e('Sign In', 'geeks'); ?>
        </button>
    </p>
   <?php do_action( 'tutor_after_sign_in_button' ); ?>
</form>
<?php 
do_action( 'tutor_after_login_form' );

if ( ! tutor_utils()->is_tutor_frontend_dashboard() ) : ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        var { __ } = wp.i18n;
        var loginModal = document.querySelector('.tutor-modal.tutor-login-modal');
        var errors = <?php echo wp_json_encode( $login_errors ); ?>;
        if (loginModal && errors.length) {
            loginModal.classList.add('tutor-is-active');
        }
    });
</script>
<?php endif; ?>
<?php delete_transient( Ajax::LOGIN_ERRORS_TRANSIENT_KEY ); ?> 