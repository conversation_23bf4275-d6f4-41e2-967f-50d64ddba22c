# Translation of Plugins - WPCode &#8211; Insert Headers and Footers + Custom Code Snippets &#8211; WordPress Code Manager - Stable (latest release) in Portuguese (Brazil)
# This file is distributed under the same license as the Plugins - WPCode &#8211; Insert Headers and Footers + Custom Code Snippets &#8211; WordPress Code Manager - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-01-15 17:13:20+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: pt_BR\n"
"Project-Id-Version: Plugins - WPCode &#8211; Insert Headers and Footers + Custom Code Snippets &#8211; WordPress Code Manager - Stable (latest release)\n"

#. Translators: %s: the number of items.
#: includes/admin/pages/class-wpcode-code-snippets-table.php:995
msgid "%s item"
msgid_plural "%s items"
msgstr[0] "%s item"
msgstr[1] "%s itens"

#: includes/admin/pages/class-wpcode-code-snippets-table.php:966
msgid "Current Page"
msgstr "Página atual"

#: includes/admin/pages/class-wpcode-code-snippets-table.php:964
msgid "of"
msgstr "de"

#: includes/conditional-logic/class-wpcode-conditional-page.php:119
msgid "Post meta"
msgstr "Metadados de post"

#: includes/conditional-logic/class-wpcode-conditional-page.php:120
msgid "Target specific posts based on custom post meta values."
msgstr "Direcione posts específicos com base em valores de metadados de post personalizados."

#: includes/conditional-logic/class-wpcode-conditional-page.php:124
msgid "Post Meta rules are a Pro feature"
msgstr "As regras de metadados de post são um recurso profissional"

#: includes/conditional-logic/class-wpcode-conditional-user.php:71
msgid "User Meta"
msgstr "Metadados do usuário"

#: includes/conditional-logic/class-wpcode-conditional-user.php:72
msgid "Target users based on user meta values."
msgstr "Destino usuários com base em valores meta do usuário."

#: includes/conditional-logic/class-wpcode-conditional-user.php:76
msgid "User Meta is a Pro Feature"
msgstr "Os metadados do usuário são um recurso da versão Pro"

#: includes/conditional-logic/class-wpcode-conditional-user.php:77
msgid "Get access to User Meta conditional logic rules by upgrading to PRO today."
msgstr "Obtenha acesso às regras de lógica condicional dos metadados do usuário atualizando agora para a versão Pro."

#: includes/admin/admin-scripts.php:50
msgid "Already Purchased?"
msgstr "Já comprou?"

#: includes/admin/admin-scripts.php:52
msgid "Bonus"
msgstr "Bônus"

#: includes/admin/admin-scripts.php:53
msgid "$50 off"
msgstr "US$ 50 de desconto"

#: includes/admin/admin-scripts.php:54
msgid "regular price, automatically applied at checkout."
msgstr "o preço regular, aplicado automaticamente na finalização de compra."

#: includes/admin/admin-scripts.php:55
msgid "WPCode Lite users get"
msgstr "Os usuários do WPCode Lite obtêm"

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:506
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1765
msgid "SCSS snippets is a Pro Feature"
msgstr "Snippets SCSS são um recurso da versão Pro"

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:507
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1766
msgid "Upgrade to PRO today and unlock editing snippets using SCSS code with optimized compiling directly in the WordPress admin."
msgstr "Faça o upgrade para a versão Pro hoje mesmo e acesse a edição de snippets usando código SCSS com compilação otimizada diretamente no administrador do WordPress."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:199
msgid "Your changes are saved but your snippet was deactivated due to an error, please check the syntax and try again."
msgstr "Suas alterações foram salvas, mas o snippet foi desativado devido a um erro. Verifique a sintaxe e tente novamente."

#: includes/class-wpcode-snippet-execute.php:157
msgid "SCSS Snippet (PRO)"
msgstr "Snippet SCSS (Pro)"

#: includes/class-wpcode-snippet-execute.php:159
msgid "Write SCSS styles directly in WPCode and easily customize how your website looks."
msgstr "Escreva estilos SCSS diretamente no WPCode e personalize facilmente a aparência do seu site."

#: includes/lite/conditional-logic/class-wpcode-conditional-edd.php:63
msgid "Confirmation Page"
msgstr "Página de confirmação"

#: includes/admin/class-wpcode-code-editor.php:67
msgid "The PHP constant DISALLOW_UNFILTERED_HTML is currently defined, which is preventing WPCode from saving code accurately. Please contact your webmaster for assistance in disabling this restriction."
msgstr "A constante PHP DISALLOW_UNFILTERED_HTML está definida no momento, o que impede que o WPCode salve o código de maneira precisa. Entre em contato com o seu webmaster para obter ajuda para desativar essa restrição."

#: includes/admin/class-wpcode-suggested-plugins.php:102
msgid "The easiest way to sell digital products with WordPress."
msgstr "A maneira mais fácil de vender produtos digitais com o WordPress."

#: includes/admin/class-wpcode-suggested-plugins.php:112
msgid "Raise more money for your cause with the most powerful WordPress donation and fundraising plugin."
msgstr "Arrecade mais dinheiro para sua causa com o plugin de doação e arrecadação de recursos mais avançado do WordPress."

#: includes/admin/class-wpcode-suggested-plugins.php:122
msgid "Start accepting one-time and recurring payments on your WordPress site without setting up a shopping cart."
msgstr "Comece a aceitar pagamentos únicos e recorrentes em seu site WordPress sem configurar um carrinho de compras."

#: includes/admin/pages/class-wpcode-admin-page-duplicator.php:88
msgid "Easy, Fast and Secure WordPress Backups and Website Migration. Join 1,500,000+ professionals who trust Duplicator. No Code Required."
msgstr "Backups fáceis, rápidos e seguros do WordPress e migração de sites. Junte-se a mais de 1.500.000 profissionais que confiam no Duplicator. Não é necessário saber programação."

#: includes/admin/pages/class-wpcode-admin-page-duplicator.php:93
msgid "Duplicator Screenshot"
msgstr "Captura de tela do Duplicator"

#: includes/admin/pages/class-wpcode-admin-page-duplicator.php:99
msgid "Secure Backups."
msgstr "Backups seguros."

#: includes/admin/pages/class-wpcode-admin-page-duplicator.php:100
msgid "Website Cloning."
msgstr "Clonagem de site."

#: includes/admin/pages/class-wpcode-admin-page-duplicator.php:101
msgid "Cloud Storage."
msgstr "Armazenamento em nuvem."

#: includes/admin/pages/class-wpcode-admin-page-duplicator.php:102
msgid "1-Click Restore."
msgstr "Restauração com um clique."

#: includes/admin/pages/class-wpcode-admin-page-duplicator.php:146
msgid "Please ask your website administrator to install Duplicator."
msgstr "Peça ao administrador do seu site para instalar o Duplicator."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:378
msgid "Snippet Generators"
msgstr "Geradores de snippets"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:381
msgid "Plugin Snippets"
msgstr "Snippets de plugin"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:413
msgid "Create custom snippets for your website easily by filling out a form. You can also use this form to update your snippets later, without having to write or edit any code."
msgstr "Crie facilmente snippets personalizados para seu site preenchendo um formulário. Você também pode usar esse formulário para atualizar seus snippets quando desejar, sem precisar escrever ou editar nenhum código."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:443
msgid "Easily add code snippets that extend other plugins on your site from a library maintained by the plugin authors."
msgstr "Adicione facilmente snippets de código que incrementam outros plugins em seu site a partir de uma biblioteca dos autores do plugin."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:453
msgid "You don't have any plugins installed that extend the WPCode library, install any of the plugins below to start expanding the WPCode snippet library:"
msgstr "Você não tem nenhum plugin instalado que estenda a biblioteca WPCode , instale qualquer um dos plugins abaixo para iniciar a expandir a biblioteca de snippets do WPCode :"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:562
msgid "Select the code type for your snippet:"
msgstr "Selecione o tipo de código para seu snippet:"

#: includes/admin/pages/trait-wpcode-my-library-markup.php:241
msgid "Save your snippets to your library"
msgstr "Salve seus snippets em sua biblioteca"

#: includes/admin/pages/trait-wpcode-my-library-markup.php:242
msgid "1-click import snippets from you library"
msgstr "Importação de snippets da sua biblioteca com um clique"

#: includes/admin/pages/trait-wpcode-my-library-markup.php:243
msgid "Deploy new snippets from your account"
msgstr "Instale novos snippets a partir de sua conta"

#: includes/admin/pages/trait-wpcode-my-library-markup.php:244
msgid "Update snippets across all your sites"
msgstr "Atualize snippets em todos os seus sites"

#: includes/class-wpcode-snippet-execute.php:132
msgid "Easily insert scripts from other sites or build custom elements using HTML."
msgstr "Insira facilmente scripts de outros sites ou crie elementos personalizados usando HTML."

#: includes/class-wpcode-snippet-execute.php:139
msgid "Create reusable text snippets that you can visually format in a familiar editor."
msgstr "Crie snippets de texto reutilizáveis que você pode formatar visualmente em um editor conhecido."

#: includes/class-wpcode-snippet-execute.php:146
msgid "Use the Block Editor to create components that you can insert anywhere in your site."
msgstr "Use o editor de blocos para criar componentes que você pode inserir em qualquer lugar do seu site."

#: includes/class-wpcode-snippet-execute.php:152
msgid "Write CSS styles directly in WPCode and easily customize how your website looks."
msgstr "Escreva estilos CSS diretamente no WPCode e personalize facilmente a aparência do seu site."

#: includes/class-wpcode-snippet-execute.php:165
msgid "Add custom JavaScript code to your site to add interactivity or integrate with other services."
msgstr "Adicione código JavaScript personalizado ao seu site para adicionar interatividade ou integrar com outros serviços."

#: includes/class-wpcode-snippet-execute.php:171
msgid "Extend or add functionality using PHP code with full control on where it's executed"
msgstr "Amplie ou adicione funcionalidades usando código PHP com controle total sobre o local de execução"

#: includes/class-wpcode-snippet-execute.php:177
msgid "Start writing HTML and add PHP code like you would in a .php file with Universal snippets."
msgstr "Comece a escrever HTML e adicione código PHP como faria em um arquivo .php com snippets universais."

#: includes/conditional-logic/class-wpcode-conditional-page.php:125
#: includes/conditional-logic/class-wpcode-conditional-page.php:136
msgid "Upgrade today to create conditional logic rules for specific pages or posts."
msgstr "Atualize hoje para criar regras de lógica condicional para páginas ou posts específicos."

#: includes/conditional-logic/class-wpcode-conditional-page.php:141
msgid "Page Template"
msgstr "Modelo de página"

#: includes/conditional-logic/class-wpcode-conditional-page.php:142
msgid "Load the snippet only on pages with a specific template."
msgstr "Carregue o snippet somente em páginas com um modelo específico."

#: includes/conditional-logic/class-wpcode-conditional-page.php:146
msgid "Page Template rules are a Pro feature"
msgstr "As regras do modelo de página são um recurso da versão Pro"

#: includes/conditional-logic/class-wpcode-conditional-page.php:153
msgid "Load the snippet only on pages with a specific author."
msgstr "Carregue o snippet somente em páginas com um autor específico."

#: includes/conditional-logic/class-wpcode-conditional-page.php:157
msgid "Post Author rules are a Pro feature"
msgstr "As regras do autor de post são um recurso da versão Pro"

#: includes/conditional-logic/class-wpcode-conditional-page.php:158
msgid "Upgrade today to create conditional logic rules based on the page/post author."
msgstr "Atualize hoje para criar regras de lógica condicional com base no autor da página/post."

#: includes/lite/auto-insert/class-wpcode-auto-insert-content.php:78
msgid "Insert in the Middle of the Content"
msgstr "Inserir no meio do conteúdo"

#: includes/lite/auto-insert/class-wpcode-auto-insert-content.php:79
msgid "Insert snippet in the middle of the post content."
msgstr "Insira um snippet no meio do conteúdo do post."

#: includes/lite/auto-insert/class-wpcode-auto-insert-content.php:82
msgid "Insert after first Quarter (25%)"
msgstr "Inserir após o primeiro quarto (25%)"

#: includes/lite/auto-insert/class-wpcode-auto-insert-content.php:83
msgid "Insert snippet after the first quarter of the post content."
msgstr "Insira um snippet após o primeiro quarto do conteúdo do post."

#: includes/lite/auto-insert/class-wpcode-auto-insert-content.php:86
msgid "Insert after 3rd Quarter (75%)"
msgstr "Inserir após o terceiro quarto (75%)"

#: includes/lite/auto-insert/class-wpcode-auto-insert-content.php:87
msgid "Insert snippet after the third quarter of the post content."
msgstr "Insira o snippet após o terceiro quarto do conteúdo do post."

#: includes/lite/auto-insert/class-wpcode-auto-insert-content.php:90
msgid "Insert after first Third (33%)"
msgstr "Inserir após o primeiro terço (33%)"

#: includes/lite/auto-insert/class-wpcode-auto-insert-content.php:91
msgid "Insert snippet after the first third of the post content."
msgstr "Insira um snippet após o primeiro terço do conteúdo do post."

#: includes/lite/auto-insert/class-wpcode-auto-insert-content.php:94
msgid "Insert after second Third (66%)"
msgstr "Inserir após o segundo quarto (66%)"

#: includes/lite/auto-insert/class-wpcode-auto-insert-content.php:95
msgid "Insert snippet after the second third of the post content."
msgstr "Insira o snippet após o segundo terço do conteúdo do post."

#: includes/lite/auto-insert/class-wpcode-auto-insert-content.php:98
msgid "Insert after 80%"
msgstr "Inserir após 80%"

#: includes/lite/auto-insert/class-wpcode-auto-insert-content.php:99
msgid "Insert snippet after 80% of the post content."
msgstr "Insira um snippet após 80% do conteúdo do post."

#: includes/admin/class-wpcode-suggested-plugins.php:42
msgid "Easy, Fast and Secure WordPress and Website Migration."
msgstr "Migração fácil, rápida e segura de sites e do WordPress."

#: includes/admin/class-wpcode-suggested-plugins.php:52
msgid "Replace text across your database or media uploads in a single plugin."
msgstr "Substitua o texto em seu banco de dados ou uploads de mídia em um único plugin."

#: includes/admin/class-wpcode-suggested-plugins.php:62
msgid "Making Email Deliverability Easy for WordPress"
msgstr "Facilitando a capacidade de entrega de e-mails para o WordPress"

#: includes/admin/class-wpcode-suggested-plugins.php:72
msgid "Powerful SEO Plugin to Boost SEO Rankings & Increase Traffic."
msgstr "Avançado plugin de SEO para impulsionar as classificações de SEO e aumentar o tráfego."

#: includes/admin/class-wpcode-suggested-plugins.php:82
msgid "The Best Drag & Drop WordPress Form Builder."
msgstr "O melhor editor de formulários do tipo arrastar e soltar para o WordPress."

#: includes/admin/class-wpcode-suggested-plugins.php:92
msgid "Connect your WordPress plugins together and create automated workflows."
msgstr "Conecte seus plugins do WordPress e crie fluxos de trabalho automatizados."

#: includes/admin/class-wpcode-suggested-plugins.php:360
msgid "Enjoying WPCode? Check out some of our other top-rated FREE plugins:"
msgstr "Está gostando do WPCode? Conheça alguns de nossos outros plugins GRATUITOS mais bem avaliados:"

#: includes/admin/class-wpcode-suggested-plugins.php:370
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:463
msgid "Install Plugin"
msgstr "Instalar plugin"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:718
msgid "Who (visitor)"
msgstr "Quem (visitante)"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:722
msgid "Where (page)"
msgstr "Onde (página)"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:730
msgid "Advanced"
msgstr "Avançado"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:740
msgid "Search Options"
msgstr "Opções de pesquisa"

#: includes/conditional-logic/class-wpcode-conditional-page.php:49
msgid "Choose a WordPress-specific type of page for your rule."
msgstr "Escolha um tipo de página específico do WordPress para sua regra."

#: includes/conditional-logic/class-wpcode-conditional-page.php:85
msgid "Target by post type: posts, pages or custom post types."
msgstr "Segmente por tipo de post: posts, páginas ou tipos de post personalizados."

#: includes/conditional-logic/class-wpcode-conditional-page.php:92
msgid "Use the page referrer/last visited page url as a condition."
msgstr "Use o URL da página de referência/última página visitada como uma condição."

#: includes/conditional-logic/class-wpcode-conditional-page.php:98
msgid "Load only on pages for a specific category/taxonomy."
msgstr "Carregar apenas nas páginas de uma categoria/taxonomia específica."

#: includes/conditional-logic/class-wpcode-conditional-page.php:105
msgid "Choose category/taxonomy terms to target for single or archive pages."
msgstr "Escolha os termos de categoria/taxonomia a ser direcionados para páginas únicas ou de arquivo."

#: includes/conditional-logic/class-wpcode-conditional-page.php:114
msgid "Use the page URL to limit where this snippet is loaded."
msgstr "Use o URL da página para limitar onde esse snippet é carregado."

#: includes/conditional-logic/class-wpcode-conditional-page.php:131
msgid "Pick specific posts or pages to load the snippet on."
msgstr "Escolha posts ou páginas específicas para carregar o snippet."

#: includes/conditional-logic/class-wpcode-conditional-user.php:49
msgid "Check if your site visitor is logged in."
msgstr "Verifique se o visitante do seu site está logado."

#: includes/conditional-logic/class-wpcode-conditional-user.php:65
msgid "Target a specific user role."
msgstr "Segmente uma função de usuário específica."

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:49
msgid "Target either desktop or mobile devices."
msgstr "Segmente computadores ou dispositivos móveis."

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:71
msgid "Target specific visitor web browsers."
msgstr "Segmente navegadores da web de visitantes específicos."

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:81
msgid "Target operating systems like Windows, Mac OS or Linux."
msgstr "Destino sistemas operacionais como Windows, Mac OS ou Linux."

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:91
msgid "Load or hide a snippet by cookie name."
msgstr "Carregue ou oculte um snippet por nome de cookie."

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:101
msgid "Load or hide a snippet by cookie value."
msgstr "Carregue ou oculte um snippet por valor de cookie."

#: includes/lite/conditional-logic/class-wpcode-conditional-edd.php:49
msgid "Load the snippet on specific Easy Digital Downloads pages."
msgstr "Carregue o snippet em páginas específicas do Easy Digital Downloads."

#: includes/lite/conditional-logic/class-wpcode-conditional-location.php:41
msgid "Limit loading the snippet based on the visitor's country."
msgstr "Limite o carregamento do snippet com base no país do visitante."

#: includes/lite/conditional-logic/class-wpcode-conditional-location.php:52
msgid "Target entire continents with ease."
msgstr "Segmente continentes inteiros com facilidade."

#: includes/lite/conditional-logic/class-wpcode-conditional-memberpress.php:50
msgid "Load the snippet on specific MemberPress pages."
msgstr "Carregar o snippet em páginas específicas do MemberPress."

#: includes/lite/conditional-logic/class-wpcode-conditional-memberpress.php:78
msgid "Check if the current user has a specific MemberPress subscription active."
msgstr "Verificar se o usuário atual tem uma assinatura específica do MemberPress ativa."

#: includes/lite/conditional-logic/class-wpcode-conditional-schedule.php:49
msgid "Check whether today is before or after a date."
msgstr "Marcar se hoje é antes ou depois de um encontro."

#: includes/lite/conditional-logic/class-wpcode-conditional-schedule.php:54
msgid "Get more specific by also including a specific time."
msgstr "Seja mais específico incluindo também um horário específico."

#: includes/lite/conditional-logic/class-wpcode-conditional-schedule.php:59
msgid "Load the snippet on specific days of the week."
msgstr "Carregue o snippet em dias específicos da semana."

#: includes/lite/conditional-logic/class-wpcode-conditional-schedule.php:65
msgid "Check whether it's before or after a specific time"
msgstr "Verificar se é antes ou depois de um horário específico"

#: includes/lite/conditional-logic/class-wpcode-conditional-snippet.php:49
msgid "Load this snippet based on another snippet being loaded."
msgstr "Carregue esse snippet com base em outro snippet que está sendo carregado."

#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:49
msgid "Load the snippet on specific WooCommerce pages."
msgstr "Carregar o snippet em páginas específicas do WooCommerce."

#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:101
msgid "Load the snippet based on the WooCommerce Cart Contents."
msgstr "Carregar o snippet com base no conteúdo do carrinho WooCommerce."

#: includes/admin/class-wpcode-suggested-plugins.php:229
msgid "Plugin already installed and activated."
msgstr "Plugin já instalado e ativado."

#: includes/admin/class-wpcode-suggested-plugins.php:239
#: includes/admin/class-wpcode-suggested-plugins.php:248
msgid "Plugin activated."
msgstr "Plugin ativado."

#: includes/admin/class-wpcode-suggested-plugins.php:277
msgid "Plugin installed and activated."
msgstr "Plugin instalado e ativado."

#: includes/admin/pages/class-wpcode-admin-page-search-replace.php:87
msgid "Confidently replace any text in your database and update images without duplicating uploads, all with a single plugin."
msgstr "Substitua com confiança qualquer texto em seu banco de dados e atualize imagens sem duplicar uploads, tudo com um único plugin."

#: includes/admin/pages/class-wpcode-admin-page-search-replace.php:92
msgid "Search & Replace Everything Screenshot"
msgstr "Captura de tela do Search & Replace Everything"

#: includes/admin/pages/class-wpcode-admin-page-search-replace.php:98
msgid "Replace Text Across Your Whole Database."
msgstr "Substitua o texto em todo o seu banco de dados."

#: includes/admin/pages/class-wpcode-admin-page-search-replace.php:99
msgid "Preview Changes Every Time."
msgstr "Pré-visualize sempre as alterações."

#: includes/admin/pages/class-wpcode-admin-page-search-replace.php:100
msgid "Replace Image Sources to Clear Up Space."
msgstr "Substitua as fontes de imagem para liberar espaço."

#: includes/admin/pages/class-wpcode-admin-page-search-replace.php:101
msgid "Support for Serialized Data."
msgstr "Suporte a dados serializados."

#. translators: %s is the plugin name.
#: includes/admin/pages/class-wpcode-admin-page-duplicator.php:115
#: includes/admin/pages/class-wpcode-admin-page-search-replace.php:114
msgid "Install and Activate %s"
msgstr "Instalar e ativar o %s"

#. translators: %s is the plugin name.
#: includes/admin/pages/class-wpcode-admin-page-duplicator.php:124
#: includes/admin/pages/class-wpcode-admin-page-search-replace.php:123
msgid "Install %s from the WordPress.org plugin repository."
msgstr "Instale %s do repositório de plugin WordPress.org."

#. translators: %s is the plugin name.
#: includes/admin/pages/class-wpcode-admin-page-duplicator.php:137
#: includes/admin/pages/class-wpcode-admin-page-search-replace.php:136
msgid "Install %s"
msgstr "Instalar o %s"

#: includes/admin/pages/class-wpcode-admin-page-search-replace.php:145
msgid "Please ask your website administrator to install Search & Replace Everything."
msgstr "Solicite ao administrador do site que instale o Search & Replace Everything."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:338
msgid "Add Custom Snippet"
msgstr "Adicionar snippet personalizado"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:342
msgid "Generate snippet using AI"
msgstr "Gerar snippet usando IA"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:343
msgid "Generate a new snippet specific to your needs leveraging the power of WPCode's AI integration."
msgstr "Gere um novo snippet específico para suas necessidades, aproveitando o poder da integração de IA do WPCode."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:351
msgid "Generate Snippet"
msgstr "Gerar snippet"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1799
msgid "AI Snippet Generation is a Pro Feature"
msgstr "A geração de snippets de IA é um recurso da versão Pro"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1800
msgid "Improving Snippets with AI is a Pro Feature"
msgstr "Aprimoramento de snippets com IA é um recurso da versão Pro"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1801
msgid "Upgrade today to access the WPCode AI code generator, which allows you to create code snippets simply by describing their functionality using the power of AI."
msgstr "Atualize hoje para acessar o gerador de código de IA do WPCode, que permite criar snippets de código simplesmente descrevendo sua funcionalidade usando o poder da IA."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2488
msgid "Use AI to improve your snippet by describing the changes you want"
msgstr "Use IA para melhorar seu snippet, descrevendo as alterações desejadas"

#: includes/auto-insert/class-wpcode-auto-insert-admin.php:53
msgid "Admin header"
msgstr "Cabeçalho do administrador"

#: includes/auto-insert/class-wpcode-auto-insert-admin.php:54
msgid "Insert snippet in the wp-admin header area."
msgstr "Insira o snippet na área de cabeçalho do wp-admin."

#: includes/auto-insert/class-wpcode-auto-insert-admin.php:57
msgid "Admin footer"
msgstr "Rodapé do administrador"

#: includes/auto-insert/class-wpcode-auto-insert-admin.php:58
msgid "Insert snippet in the wp-admin footer."
msgstr "Insira o trecho no rodapé do wp-admin."

#: includes/lite/conditional-logic/class-wpcode-conditional-location.php:40
msgid "Country"
msgstr "País"

#: includes/lite/conditional-logic/class-wpcode-conditional-location.php:45
#: includes/lite/conditional-logic/class-wpcode-conditional-location.php:56
msgid "Location Rules are a Pro Feature"
msgstr "As regras de localização são um recurso da versão Pro"

#: includes/lite/conditional-logic/class-wpcode-conditional-location.php:46
#: includes/lite/conditional-logic/class-wpcode-conditional-location.php:57
msgid "Get access to location-based conditional logic rules by upgrading to PRO today."
msgstr "Obtenha acesso a regras lógicas condicionais baseadas em localização atualizando para a versão Pro hoje mesmo."

#: includes/lite/conditional-logic/class-wpcode-conditional-location.php:51
msgid "Continent"
msgstr "Continente"

#: includes/auto-insert/class-wpcode-auto-insert-admin.php:42
msgid "Admin Area"
msgstr "Área do administrador"

#: includes/auto-insert/class-wpcode-auto-insert-site-wide.php:49
msgid "Site Wide (Frontend)"
msgstr "Site inteiro (interface)"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:198
msgid "Snippet updated & executed."
msgstr "Snippet atualizado e executado."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1231
msgid "Execute Snippet Now"
msgstr "Executar o snippet agora"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1284
msgid "Restricted Code Detected"
msgstr "Código restrito detectado"

#: includes/auto-insert/class-wpcode-auto-insert-everywhere.php:86
msgid "On Demand"
msgstr "Sob demanda"

#: includes/auto-insert/class-wpcode-auto-insert-everywhere.php:87
msgid "Execute this snippet on demand or programmatically just when you need it."
msgstr "Execute esse snippet sob demanda ou de forma automática quando precisar."

#: includes/class-wpcode-smart-tags.php:92
msgid "Permalink"
msgstr "Link permanente"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1770
msgid "Load as file is a Pro Feature"
msgstr "Carregar como arquivo é um recurso da versão Pro"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1771
msgid "Upgrade to PRO today and unlock loading your CSS and JS snippets as files for better performance and improved compatibility with caching plugins."
msgstr "Atualize para a versão PRO hoje mesmo e acesse o carregamento de seus snippets CSS e JS como arquivos para obter melhor desempenho e compatibilidade aprimorada com plugins de cache."

#. Translators: Link to the documentation article for files as snippets. %1$s
#. is the opening anchor tag, %2$s is the closing anchor tag.
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2013
msgid "If enabled, this snippet will be loaded as a physical file instead of being inserted in the source of the page. %1$sLearn more%2$s."
msgstr "Se habilitado, este snippet será carregado como um arquivo físico em vez de ser inserido na origem da página. %1$sSaiba mais%2$s."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2035
msgid "Load as file"
msgstr "Carregar como arquivo"

#. Translators: The name of the user that is currently editing the snippet is
#. appended at the end.
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1798
msgid "This snippet is currently being edited by "
msgstr "Esse snippets está sendo editado no momento por "

#. Translators: The placeholder gets replaced with the display name of the user
#. currently editing the snippet.
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2331
msgid "Notice: %1$s is also editing this snippet. Please be aware that your changes could be overwritten."
msgstr "Aviso: %1$s também está editando este trecho. Esteja ciente de que suas alterações podem ser substituídas."

#. translators: %s: User display name
#: includes/admin/pages/class-wpcode-code-snippets-table.php:337
msgid "%s is currently editing"
msgstr "%s está editando no momento"

#: includes/conditional-logic/class-wpcode-conditional-page.php:77
msgid "Blog home"
msgstr "Página inicial do blog"

#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:310
msgid "Settings Saved. Please don't forget to clear the site cache if you are using a cache plugin, so that the changes will be reflected for all users."
msgstr "Configurações salvas. Não se esqueça de limpar o cache do site se estiver usando um plugin de cache, para que as alterações sejam refletidas para todos os usuários."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:648
msgid "minimum number of words"
msgstr "número mínimo de palavras"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:649
msgid "number of words"
msgstr "número de palavras"

#: includes/conditional-logic/class-wpcode-conditional-page.php:130
msgid "Post/Page"
msgstr "Post/Página"

#: includes/conditional-logic/class-wpcode-conditional-page.php:135
msgid "Post specific rules are a Pro feature"
msgstr "Regras específicas do post são um recurso da versão Pro"

#: includes/lite/auto-insert/class-wpcode-auto-insert-content.php:70
msgid "Insert After # Words"
msgstr "Inserir após # de palavras"

#: includes/lite/auto-insert/class-wpcode-auto-insert-content.php:71
msgid "Insert snippet after a minimum number of words."
msgstr "Insira o snippet após um número mínimo de palavras."

#: includes/lite/auto-insert/class-wpcode-auto-insert-content.php:74
msgid "Insert Every # Words"
msgstr "Insira a cada # palavras"

#: includes/lite/auto-insert/class-wpcode-auto-insert-content.php:75
msgid "Insert snippet every # number of words."
msgstr "Insira o snippet a cada # números de palavras."

#: includes/lite/auto-insert/class-wpcode-auto-insert-content.php:110
msgid "Word-based content locations are a PRO feature"
msgstr "Locais de conteúdo baseados em palavras são um recurso da versão Pro"

#: includes/lite/auto-insert/class-wpcode-auto-insert-content.php:111
msgid "Upgrade to PRO today and get access to automatic word-count based insert locations."
msgstr "Atualize para PRO hoje e tenha acesso a locais de inserção baseados em contagem automática de palavras."

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:37
msgid "Device"
msgstr "Dispositivo"

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:70
msgid "Browser Type"
msgstr "Tipo de navegador"

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:74
msgid "Browser Type Rules are a Pro Feature"
msgstr "As regras de tipo de navegador são um recurso da versão Pro"

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:75
msgid "Get access to advanced device conditional logic rules by upgrading to PRO today."
msgstr "Obtenha acesso a regras avançadas de lógica condicional de dispositivos atualizando para a versão Pro hoje mesmo."

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:80
msgid "Operating System"
msgstr "Sistema operacional"

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:84
msgid "Operating System Rules are a Pro Feature"
msgstr "As regras do sistema operacional são um recurso da versão Pro"

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:85
msgid "Get access to advanced operating system conditional logic rules by upgrading to PRO today."
msgstr "Obtenha acesso a regras avançadas de lógica condicional do sistema operacional atualizando para a versão Pro hoje mesmo."

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:90
msgid "Cookie Name"
msgstr "Nome do cookie"

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:94
#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:104
msgid "Cookie-based Rules are a Pro Feature"
msgstr "Regras baseadas em cookies são um recurso da versão Pro"

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:95
#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:105
msgid "Get access to advanced cookie conditional logic rules by upgrading to PRO today."
msgstr "Obtenha acesso a regras lógicas condicionais de cookies avançadas atualizando para a versão Pro hoje mesmo."

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:100
msgid "Cookie Value"
msgstr "Valor do cookie"

#: includes/lite/conditional-logic/class-wpcode-conditional-snippet.php:37
msgid "Snippet"
msgstr "Snippet"

#: includes/admin/admin-ajax-handlers.php:442
#: includes/lite/conditional-logic/class-wpcode-conditional-snippet.php:48
msgid "WPCode Snippet"
msgstr "Snippet do WPCode"

#: includes/lite/conditional-logic/class-wpcode-conditional-snippet.php:53
msgid "WPCode Snippet Loaded Rules are a Pro Feature"
msgstr "Regras carregadas de snippet do WPCode são um recurso da versão Pro"

#: includes/lite/conditional-logic/class-wpcode-conditional-snippet.php:54
msgid "Upgrade today and use conditional logic rules based on other WPCode snippets being loaded."
msgstr "Atualize hoje e use regras lógicas condicionais com base em outros snippets WPCode sendo carregados."

#: includes/conditional-logic/class-wpcode-conditional-page.php:147
msgid "Upgrade today to create conditional logic rules for specific page templates."
msgstr "Atualize hoje para criar regras de lógica condicional para modelos de página específicos."

#: includes/admin/pages/class-wpcode-admin-page-settings.php:52
msgid "WPCode Settings"
msgstr "Configurações do WPCode"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:217
msgid "Dark Mode"
msgstr "Modo escuro"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:221
msgid "Enable Dark Mode across WPCode."
msgstr "Ativar o modo escuro no WPCode."

#: includes/admin/pages/class-wpcode-admin-page-settings.php:478
msgid "Email Notifications"
msgstr "Notificações por e-mail"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:481
msgid "Receive email notifications when snippets throw errors or are automatically deactivated."
msgstr "Receba notificações por e-mail quando snippets gerarem erros ou forem desativados automaticamente."

#: includes/admin/pages/class-wpcode-admin-page-settings.php:493
msgid "Email Notifications is a Pro Feature"
msgstr "Notificações por e-mail são um recurso da versão PRO"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:494
msgid "Do you want to get email notifications the moment your snippets throw an error or are automatically deactivated? Upgrade today and improve your workflow with WPCode Error Email Notifications."
msgstr "Deseja receber notificações por e-mail quando os seus snippets apresentarem um erro ou forem desativados automaticamente? Atualize hoje mesmo e melhore seu fluxo de trabalho com as notificações por e-mail de erros do WPCode."

#: includes/admin/pages/class-wpcode-admin-page-settings.php:512
msgid "Error Notifications"
msgstr "Notificações de erro"

#. Translators: %1$s: opening anchor tag, %2$s: closing anchor tag. Link to
#. docs about error notifications.
#: includes/admin/pages/class-wpcode-admin-page-settings.php:518
msgid "Send email notifications when snippets throw errors? %1$sLearn more%2$s"
msgstr "Deseja enviar notificações por e-mail quando os snippets gerarem erros? %1$sSaiba mais%2$s"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:528
#: includes/admin/pages/class-wpcode-admin-page-settings.php:569
msgid "Send To"
msgstr "Enviar para"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:532
msgid "Enter a comma separated list of email addresses to receive error notifications. Defaults to the admin email address."
msgstr "Insira uma lista de endereços de e-mail separados por vírgula para receber notificações de erro. O padrão é o endereço de e-mail do administrador."

#: includes/admin/pages/class-wpcode-admin-page-settings.php:548
#: includes/admin/pages/class-wpcode-admin-page-settings.php:589
msgid "Preview Email"
msgstr "Pré-visualizar e-mail"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:553
msgid "Deactivation Notifications"
msgstr "Notificações de desativação"

#. Translators: %1$s: opening anchor tag, %2$s: closing anchor tag. Link to
#. docs about error notifications.
#: includes/admin/pages/class-wpcode-admin-page-settings.php:559
msgid "Send an email when a snippet gets automatically deactivated? %1$sLearn more%2$s"
msgstr "Deseja enviar um e-mail quando um snippet for desativado automaticamente? %1$sSaiba mais%2$s"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:573
msgid "Enter a comma separated list of email addresses to receive deactivation notifications. Defaults to the admin email address."
msgstr "Insira uma lista de endereços de e-mail separados por vírgula para receber notificações de desativação. O padrão é o endereço de e-mail do administrador."

#: includes/admin/pages/class-wpcode-admin-page-settings.php:768
msgid "Make Sure Important Emails Reach Your Inbox"
msgstr "Certifique-se de que os e-mails importantes cheguem à sua caixa de entrada"

#. Translators: %1$s: opening anchor tag, %2$s: closing anchor tag.
#: includes/admin/pages/class-wpcode-admin-page-settings.php:773
msgid "Solve common email deliverability issues for good. %1$sGet WP Mail SMTP%2$s!"
msgstr "Resolva em definitivo os problemas frequentes de capacidade de entrega de e-mail. %1$sAdquira o WP Mail SMTP%2$s!"

#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:338
msgid "Upgrade to WPCode Pro today and start tracking revisions and see exactly who, when and which changes were made to global Headers & Footers scripts."
msgstr "Atualize para o WPCode Pro hoje mesmo e comece a monitorar as revisões e veja exatamente quem, quando e quais alterações foram feitas nos scripts globais de cabeçalhos e rodapés."

#: includes/admin/pages/class-wpcode-admin-page-settings.php:179
msgid "Remove ALL WPCode data upon plugin deletion."
msgstr "Excluir TODOS os dados do WPCode após a exclusão do plugin."

#: includes/admin/pages/class-wpcode-admin-page-settings.php:184
msgid "All WPCode snippets & scripts will be unrecoverable."
msgstr "Todos os snippets e scripts do WPCode não poderão mais ser recuperados."

#: includes/admin/pages/class-wpcode-admin-page-settings.php:188
msgid "Delete All Data on Uninstall"
msgstr "Excluir todos os dados na desinstalação"

#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:316
msgid "Upgrade to WPCode Pro today and start tracking revisions and see exactly who, when and which changes were made to your page scripts."
msgstr "Atualize para o WPCode Pro hoje mesmo e comece a monitorar as revisões e veja exatamente quem, quando e quais alterações foram feitas em seus scripts de página."

#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:353
msgid "Easily switch back to a previous version of your global scripts."
msgstr "Volte facilmente para uma versão anterior de seus scripts globais."

#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:309
msgid "As you make changes to your page scripts and save, you will get a list of previous versions with all the changes made in each revision. You can compare revisions to the current version or see changes as they have been saved by going through each revision. Any of the revisions can then be restored as needed without interfering with your post/page."
msgstr "Ao fazer e salvar alterações nos scripts de página, você obterá uma lista de versões anteriores com todas as mudanças feitas em cada revisão. Você pode comparar as revisões com a versão atual ou ver as alterações à medida que foram salvas, passando por cada revisão. As revisões podem ser restauradas conforme necessário, sem interferir em seu post/página."

#: includes/admin/admin-scripts.php:58
msgid "Testing Mode is a Premium Feature"
msgstr "O Modo de teste é um recurso premium"

#: includes/admin/admin-scripts.php:59
msgid "Upgrade to PRO today and make changes to your snippets, Header & Footer scripts or Page Scripts without affecting your live site. You choose when and what to publish to your visitors."
msgstr "Atualize para a versão PRO hoje mesmo e faça alterações em seus snippets, scripts de cabeçalho e rodapé ou de página sem afetar seu site em funcionamento. Você escolhe quando e o que publicar para seus visitantes."

#: includes/admin/admin-scripts.php:62
msgid "Learn more about Testing Mode"
msgstr "Saiba mais sobre o Modo de teste"

#: includes/admin/pages/class-wpcode-admin-page.php:438
msgid "Testing Mode"
msgstr "Modo de teste"

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:439
msgid "This view lists your snippets that threw errors. Some of the snippets may have also been automatically disabled due to potentially preventing you from accessing the admin."
msgstr "Essa exibição apresenta uma lista dos snippets que geraram erros. Alguns deles também podem ter sido desativados automaticamente devido à possibilidade de impedir você de acessar o administrador."

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:448
msgid "In order to get more info about the errors please consider enabling error logging."
msgstr "Para mais informações sobre os erros, considere ativar o registro de erros."

#. Translators: The placeholder gets replaced with the time passed since the
#. snippet was deactivated.
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2382
msgid "This snippet first threw an error at %1$s on %2$s (%3$s ago)."
msgstr "Este snippet gerou primeiro um erro às %1$s em %2$s (%3$s atrás)."

#. Translators: The placeholders make the text bold and add the line number.
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2402
msgid "The error occurred on %1$sline %3$d%2$s of this snippet's code (highlighted below)."
msgstr "O erro ocorreu na %1$slinha %3$d%2$s do código desse snippet (destacado abaixo)."

#. Translators: The placeholder is replaced with the URL where the error
#. happened.
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2416
msgid "The error was triggered at the following URL: %1$s"
msgstr "O erro foi gerado no seguinte URL: %1$s"

#. Translators: The placeholders are for the link to the error logs.
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2427
msgid "You can %1$sview the error log%2$s to get more details about the error that caused this. The error will also be in a snippet-specific log whose name starts with snippet-%3$d (the id of this snippet)."
msgstr "Você pode %1$svisualizar o registro de erros%2$s para saber mais detalhes sobre o problema que causou isso. O erro também estará em um registro específico de snippet cujo nome começa com snippet-%3$d (a id desse snippet)."

#: includes/admin/pages/class-wpcode-code-snippets-table.php:274
msgid "This snippet threw an error, you can see more details when editing the snippet."
msgstr "Este snippet gerou um erro. Você pode ver mais detalhes ao editar o snippet."

#: includes/class-wpcode-admin-bar-info.php:156
msgid "Snippets With Errors"
msgstr "Snippets com erros"

#: includes/class-wpcode-install.php:151
msgid "Snippet was deactivated due to an error."
msgstr "O snippet foi desativado devido a um erro."

#: includes/class-wpcode-snippet-execute.php:586
msgid "View Snippets With Errors"
msgstr "Visualizar snippets com erros"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1794
msgid "The snippet has been recently deactivated due to an error on this line"
msgstr "O snippet foi desativado recentemente devido a um erro nesta linha"

#: includes/class-wpcode-admin-bar-info.php:286
msgid "Gutenberg Block"
msgstr "Bloco Gutenberg"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:64
msgid "General Settings"
msgstr "Configurações gerais"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:66
#: includes/admin/pages/class-wpcode-admin-page-settings.php:624
msgid "Access Control"
msgstr "Controle de acesso"

#. Translators: %1$s - Opening anchor tag. %2$s - Closing anchor tag.
#: includes/admin/pages/class-wpcode-admin-page-settings.php:629
msgid "Select the user roles that are allowed to manage different types of snippets or parts of WPCode. By default, all permissions are provided only to administrator users. Please see our %1$sAccess Control documentation%2$s for more details."
msgstr "Selecione as funções de usuário com permissão para gerenciar diferentes tipos de snippets ou partes do WPCode. Por padrão, todas as permissões são atribuídas somente aos usuários administradores. Para mais detalhes, consulte a documentação do %1$sControle de acesso%2$s."

#: includes/admin/pages/class-wpcode-admin-page-settings.php:671
msgid "Access Control is a PRO Feature"
msgstr "O Controle de acesso é um recurso da versão PRO"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:679
msgid "Save time and improve website management with your team"
msgstr "Economize tempo e melhore o gerenciamento do site com sua equipe"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:680
msgid "Delegate snippet management to other users with full control"
msgstr "Delegue o gerenciamento de snippets a outros usuários para que tenham controle total"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:681
msgid "Enable other users to set up ads & 3rd party services"
msgstr "Permita que outros usuários configurem anúncios e serviços de terceiros"

#. translators: %1$s and %2$s are <u> tags.
#: includes/admin/pages/class-wpcode-admin-page-settings.php:665
msgid "Improve the way you and your team manage your snippets with the WPCode Access Control settings. Enable other users on your site to manage different types of snippets or configure Conversion Pixels settings and update configuration files. This feature is available on the %1$sWPCode Pro%2$s plan or higher."
msgstr "Melhore a maneira como você e sua equipe gerenciam seus snippets com as configurações do Controle de acesso do WPCode. Permita que outros usuários do seu site gerenciem diferentes tipos de snippets ou definam as configurações dos Pixels de conversão e atualizem os arquivos de configuração. Esse recurso está disponível no plano %1$sWPCode Pro%2$s ou superior."

#: includes/admin/pages/class-wpcode-admin-page-settings.php:682
msgid "Choose if PHP snippets should be enabled on the site"
msgstr "Defina se os snippets de PHP devem ser ativados no site"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:697
msgid "Disable PHP snippets"
msgstr "Desativar snippets de PHP"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:701
msgid "This option will completely disable PHP snippets execution and the option to edit or add new PHP snippets."
msgstr "Essa opção desabilitará completamente a execução de snippets de PHP e a possibilidade de editar ou adicionar novos snippets de PHP."

#: includes/admin/pages/class-wpcode-admin-page.php:119
msgid "You do not have permission to access this page."
msgstr "Você não tem permissão para acessar esta página."

#: includes/capabilities.php:51
msgid "Edit Text/Blocks Snippets"
msgstr "Editar snippets de texto/blocos"

#: includes/capabilities.php:52
msgid "This enables users to edit just text & blocks snippets, no HTML code is allowed."
msgstr "Permite que os usuários editem apenas snippets de texto e blocos, não sendo permitido nenhum código HTML."

#: includes/capabilities.php:55
msgid "Edit HTML, JavaScript & CSS Snippets"
msgstr "Editar snippets de HTML, JavaScript e CSS"

#: includes/capabilities.php:56
msgid "This enables users to add and manage HTML, JavaScript & CSS snippets but also Text & Blocks snippets."
msgstr "Permite que os usuários adicionem e gerenciem snippets de HTML, JavaScript e CSS, além de snippets de texto e blocos."

#: includes/capabilities.php:59
msgid "Edit PHP Snippets"
msgstr "Editar snippets de PHP"

#: includes/capabilities.php:60
msgid "This enables users to add and manage PHP snippets and all the other types of snippets."
msgstr "Permite que os usuários adicionem e gerenciem snippets de PHP e todos os demais tipos de snippets."

#: includes/capabilities.php:64
msgid "This enables users to manage the conversion pixels settings."
msgstr "Permite que os usuários gerenciem as configurações dos Pixels de conversão."

#: includes/capabilities.php:68
msgid "This enables users to use the file editor."
msgstr "Permite que os usuários usem o editor de arquivos."

#: includes/capabilities.php:63
msgid "Manage Conversion Pixels Settings"
msgstr "Gerenciar configurações dos pixels de conversão"

#: includes/capabilities.php:67
msgid "Use the File Editor"
msgstr "Usar o editor de arquivos"

#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:38
msgid "File Editor"
msgstr "Editor de arquivos"

#. Translators: %s is the name of the file.
#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:81
msgid "%s Editor"
msgstr "Editor de %s"

#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:91
msgid "Enable file output"
msgstr "Ativar saída de arquivo"

#. Translators: %s is the name of the file.
#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:107
msgid "Use this area to edit your %s file."
msgstr "Use esta área para editar seu arquivo %s."

#. Translators: %1$s is the opening link tag, %2$s is the closing link tag.
#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:116
msgid "The file contents above will be used to generated a dynamic file. There is no physical file created on your server. %1$sLearn more%2$s."
msgstr "O conteúdo do arquivo acima será usado para gerar um arquivo dinâmico. Não há nenhum arquivo físico criado em seu servidor. %1$sSaiba mais%2$s."

#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:137
msgid "View File"
msgstr "Visualizar arquivo"

#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:202
msgid "Content added here will be appended to the robots.txt content generated by WordPress and other plugins."
msgstr "O conteúdo adicionado aqui será anexado ao conteúdo do robots.txt gerado pelo WordPress e outros plugins."

#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:240
msgid "Simplify your website management with the WPCode File Editor! Say goodbye to the hassle of manually editing files on your server."
msgstr "Simplifique o gerenciamento do seu site com o Editor de arquivos do WPCode! Acabe com o transtorno de editar arquivos manualmente em seu servidor."

#. translators: %1$s and %2$s are <u> tags.
#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:244
msgid "With this powerful tool, you can easily customize crucial files like %1$sads.txt%2$s, %1$sapp-ads.txt%2$s, %1$srobots.txt%2$s, and %1$sservice-worker.js%2$s right from your WordPress admin."
msgstr "Com essa ferramenta avançada, você pode personalizar facilmente arquivos essenciais, como %1$sads.txt%2$s, %1$sapp-ads.txt%2$s, %1$srobots.txt%2$s e %1$sservice-worker.js%2$s, diretamente do seu administrador do WordPress."

#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:251
msgid "File Editor is a PRO Feature"
msgstr "Editor de arquivos é um recurso da versão PRO"

#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:258
msgid "Learn More about the File Editor"
msgstr "Saiba mais sobre o Editor de arquivos"

#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:262
msgid "No manual coding, no FTP"
msgstr "Sem necessidade de programação, sem FTP"

#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:263
msgid "Effortless integrations setup"
msgstr "Configuração de integrações sem esforço"

#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:264
msgid "Reduce the number of plugins"
msgstr "Diminua a quantidade de plugins"

#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:265
msgid "Prevent advertising fraud"
msgstr "Evitar fraudes de anúncios"

#: includes/admin/pages/class-wpcode-code-snippets-table.php:446
msgid "Duplicate this snippet"
msgstr "Duplicar este snippet"

#: includes/admin/pages/class-wpcode-code-snippets-table.php:447
msgid "Duplicate"
msgstr "Duplicar"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:228
msgid "Admin Bar Info"
msgstr "Informações da barra de administração"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:232
msgid "Enable the admin bar menu that shows info about which snippets & scripts are loaded on the current page."
msgstr "Ativar o menu da barra de administração que mostra informações sobre os snippets e scripts que estão carregados na página atual."

#: includes/class-wpcode-admin-bar-info.php:175
msgid "Loaded on this page"
msgstr "Carregado nesta página"

#. translators: %d is the total number of global scripts.
#: includes/class-wpcode-admin-bar-info.php:196
msgid "Global Scripts (%d)"
msgstr "Scripts globais (%d)"

#: includes/class-wpcode-admin-bar-info.php:348
msgid "Global Header"
msgstr "Cabeçalho global"

#: includes/class-wpcode-admin-bar-info.php:353
msgid "Global Body"
msgstr "Corpo global"

#: includes/class-wpcode-admin-bar-info.php:357
msgid "Global Footer"
msgstr "Rodapé global"

#: includes/class-wpcode-admin-bar-info.php:360
msgid "Disabled via Page Scripts"
msgstr "Desativado por meio de scripts de página"

#: includes/class-wpcode-admin-bar-info.php:396
msgid "+ Add Snippet"
msgstr "+ Adicionar snippet"

#: includes/class-wpcode-admin-bar-info.php:442
msgid "Help Docs"
msgstr "Documentos de ajuda"

#: includes/lite/class-wpcode-admin-bar-info-lite.php:29
msgid "Page Scripts"
msgstr "Scripts de página"

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:469
msgid "Number of snippets per page:"
msgstr "Número de snippets por página:"

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:532
msgid "Order Snippets By"
msgstr "Ordenar snippets por"

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:534
msgid "Order snippets by"
msgstr "Ordenar snippets por"

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:540
#: includes/admin/pages/class-wpcode-code-snippets-table.php:704
msgid "Last Updated"
msgstr "Última atualização"

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:547
msgid "Ascending"
msgstr "Crescente"

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:548
msgid "Descending"
msgstr "Decrescente"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:80
msgid "Works with Facebook, Google Ads, Pinterest, TikTok and Snapchat"
msgstr "Compatível com Facebook, Google Ads, Pinterest, TikTok e Snapchat"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:98
msgid "Snapchat"
msgstr "Snapchat"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:99
msgid "Click Tracking"
msgstr "Monitoramento de cliques"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:379
msgid "Snapchat Pixel"
msgstr "Pixel do Snapchat"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:382
msgid "Snap Pixel ID"
msgstr "ID do pixel do Snapchat"

#. translators: %1$s and %2$s are the opening and closing anchor tags.
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:388
msgid "You can find your Snapchat Pixel ID in the Snapchat Ads Manager. %1$sRead our step by step directions%2$s. "
msgstr "Você pode encontrar sua ID do pixel do Snapchat no Gerenciador de anúncios do Snapchat. %1$sLeia nossas instruções passo a passo%2$s. "

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:407
msgid "Snapchat Pixel Events"
msgstr "Eventos do pixel do Snapchat"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:413
msgid "Enable the PAGE_VIEW event to track and record page visits on all the pages of your website using the Snapchat Pixel."
msgstr "Ative o evento PAGE_VIEW para monitorar e registrar visitas a todas as páginas do seu site usando o pixel do Snapchat."

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:687
msgid "View Content Event"
msgstr "Evento Visualização de conteúdo"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:696
msgid "Turn on the \"ADD_CART\" event to track when items are added to a shopping cart on your website."
msgstr "Ative o evento \"ADD_CART\" para monitorar quando os itens são adicionados a um carrinho de compras no seu site."

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:701
msgid "Start Checkout Event"
msgstr "Evento Início de finalização de compra"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:724
msgid "Custom Click Tracking"
msgstr "Monitoramento de cliques personalizados"

#. Translators: %1$s is the opening link tag, %2$s is the closing link tag
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:729
msgid "Use this section to add custom click events to your site. You can add as many as you like. Each event can have multiple pixels and each pixel can have a custom event name and value. Read more about how to configure these settings in %1$sthis article%2$s"
msgstr "Use esta seção para adicionar eventos de cliques personalizados ao seu site. Você pode incluir a quantidade que desejar. Cada evento pode ter vários pixels e cada pixel pode ter um nome e um valor de evento personalizado. Leia mais sobre a definição dessas configurações %1$sneste artigo%2$s"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:739
msgid "CSS Selector"
msgstr "Seletor CSS"

#. Translators: %1$s is an opening anchor tag, %2$s is a closing anchor tag.
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:745
msgid "Define the HTML element that triggers the event upon clicking (button, link, etc). Input the appropriate CSS selector here. %1$sLearn more%2$s"
msgstr "Defina o elemento HTML que aciona o evento ao ser clicado (botão, link etc.). Insira o seletor CSS correto aqui. %1$sSaiba mais%2$s"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:753
msgid "Event Name"
msgstr "Nome do evento"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:757
msgid "Assign a unique identifier to your event for easy recognition and categorization. "
msgstr "Atribua um identificador exclusivo ao seu evento para fácil reconhecimento e categorização. "

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:761
msgid "Event Value"
msgstr "Valor do evento"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:765
msgid "Input a numerical value for your event. This helps in quantifying user interactions for your tracking needs. "
msgstr "Insira um valor numérico para seu evento. Isso ajuda a quantificar as interações do usuário para suas necessidades de monitoramento. "

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:804
msgid "Add New Click Event"
msgstr "Adicionar novo evento de clique"

#: includes/lite/auto-insert/class-wpcode-auto-insert-anywhere.php:85
msgid "Replace HTML Element"
msgstr "Substituir elemento HTML"

#: includes/lite/auto-insert/class-wpcode-auto-insert-anywhere.php:86
msgid "Completely replace the HTML element specified by the CSS selector with the output of this snippet."
msgstr "Substitua completamente o elemento HTML especificado pelo seletor CSS pela saída desse snippet."

#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:100
msgid "WooCommerce Cart"
msgstr "Carrinho WooCommerce"

#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:105
msgid "WooCommerce Cart Contents Rule is a Pro Feature"
msgstr "A regra de conteúdo do carrinho WooCommerce é um recurso da versão Pro"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:689
msgid "Turn on the \"VIEW_CONTENT\" event to track views of product pages on your website."
msgstr "Ative o evento \"Visualização de conteúdo\" para monitorar visualizações de páginas de produtos em seu site."

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:703
msgid "Turn on the \"START_CHECKOUT\" event to track when a user reaches the checkout page on your website."
msgstr "Ative o evento \"Início de finalização de compra\" para monitorar quando um usuário chega à página de finalização de compra do seu site."

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:710
msgid "Turn on the \"PURCHASE\" event to track successful purchases on your website."
msgstr "Ative o evento \"Compra\" para monitorar compras bem-sucedidas em seu site."

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:411
msgid "Pave View Event"
msgstr "Evento Visualização de página"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:244
msgid "Don't forget to activate your snippet using the toggle next to the \"Update\" button when you are ready to start using it."
msgstr "Não se esqueça de ativar seu snippets usando o botão de alternância ao lado do botão \"Atualizar\" quando quiser começar a usá-lo."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1769
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1935
msgid "Shortcode Attributes"
msgstr "Atributos de shortcode"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1909
msgid "Add&nbsp;Attribute"
msgstr "Adicionar&nbsp;atributo"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1913
msgid "Attribute name"
msgstr "Nome do atributo"

#: includes/class-wpcode-smart-tags.php:43
msgid "Content ID"
msgstr "ID do conteúdo"

#: includes/class-wpcode-smart-tags.php:47
msgid "Content title"
msgstr "Título do conteúdo"

#: includes/class-wpcode-smart-tags.php:51
msgid "Content Categories"
msgstr "Categorias de conteúdo"

#: includes/class-wpcode-smart-tags.php:55
msgid "User's email"
msgstr "E-mail do usuário"

#: includes/class-wpcode-smart-tags.php:59
msgid "User's first name"
msgstr "Nome do usuário"

#: includes/class-wpcode-smart-tags.php:63
msgid "User's last name"
msgstr "Sobrenome do usuário"

#: includes/class-wpcode-smart-tags.php:67
msgid "Custom Field (meta)"
msgstr "Campo personalizado (meta)"

#: includes/class-wpcode-smart-tags.php:72
msgid "Author ID"
msgstr "ID do autor"

#: includes/class-wpcode-smart-tags.php:76
msgid "Author Name"
msgstr "Nome do autor"

#: includes/class-wpcode-smart-tags.php:80
msgid "Author URL"
msgstr "URL do autor"

#: includes/class-wpcode-smart-tags.php:84
msgid "Login URL"
msgstr "URL de login"

#: includes/class-wpcode-smart-tags.php:88
msgid "Logout URL"
msgstr "URL de logout"

#: includes/class-wpcode-smart-tags.php:99
msgid "Order number"
msgstr "Número do pedido"

#: includes/class-wpcode-smart-tags.php:103
msgid "Order subtotal"
msgstr "Subtotal do pedido"

#: includes/class-wpcode-smart-tags.php:107
msgid "Order total"
msgstr "Total de pedidos"

#: includes/class-wpcode-smart-tags.php:196
msgid "Hide Smart Tags"
msgstr "Ocultar tags inteligentes"

#: includes/lite/class-wpcode-smart-tags-lite.php:20
msgid "Smart Tags are a Premium feature"
msgstr "Tags inteligentes são um recurso premium"

#: includes/lite/class-wpcode-smart-tags-lite.php:21
msgid "Upgrade to PRO today and simplify the way you write advanced snippets using smart tags without having to write any PHP code."
msgstr "Atualize para a versão Pro hoje mesmo e simplifique a maneira como você escreve snippets avançados usando tags inteligentes sem precisar criar nenhum código PHP."

#: includes/class-wpcode-smart-tags.php:227
msgid "Learn more about Smart Tags"
msgstr "Saiba mais sobre tags inteligentes"

#. Translators: %1$s is the opening <code> tag, %2$s is the closing </code>
#. tag.
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1920
msgid "Use this field to define the attribute name for your shortcode and click Add Attribute. Attributes added here will be available to use as smart tags and as variables inside snippets. E.g. an attribute named \"keyword\" will be available in a PHP snippet as %1$s$keyword%2$s. %3$sLearn more%4$s."
msgstr "Use esse campo para definir o nome do atributo para seu shortcode e clique em Adicionar atributo. Os atributos adicionados aqui estarão disponíveis para uso como tags inteligentes e como variáveis dentro de snippets. Por exemplo, um atributo chamado \"keyword\" (palavra-chave) estará disponível em um snippet PHP como %1$s$keyword%2$s. %3$sSaiba mais%4$s."

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:427
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2354
msgid "Enable Error Logging"
msgstr "Ativar registro de erros"

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:441
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2445
msgid "Learn More"
msgstr "Saiba mais"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:79
msgid "Seamless integration with WooCommerce, Easy Digital Downloads and MemberPress"
msgstr "Integração perfeita com WooCommerce, Easy Digital Downloads e MemberPress"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2345
msgid "View Error Logs"
msgstr "Visualizar registros de erros"

#. Translators: The placeholder gets replaced with the time passed since the
#. snippet was deactivated.
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2370
msgid "This snippet was automatically deactivated due to an error at %1$s on %2$s (%3$s ago)."
msgstr "Este snippet foi desativado automaticamente devido a um erro às %1$s em %2$s (%3$s atrás)."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2433
msgid "You can enable error logging to get more details about the error that caused this."
msgstr "Você pode ativar o registro de erros para obter mais detalhes sobre o erro responsável por isso."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2438
msgid "This message will disappear when the snippet is updated."
msgstr "Esta mensagem desaparecerá quando o snippet for atualizado."

#. Translators: %1$s is the time since the snippet was deactivated, %2$s is the
#. date and time of deactivation.
#: includes/admin/pages/class-wpcode-code-snippets-table.php:266
msgid "This snippet was automatically deactivated because of a fatal error at %2$s on %3$s (%1$s ago)"
msgstr "Este snippet foi desativado automaticamente devido a um erro fatal às %2$s em %3$s (%1$s atrás)"

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:69
msgid "Before the Registration Form"
msgstr "Antes do formulário de cadastro"

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:70
msgid "Insert snippet before the MemberPress registration form used for checkout."
msgstr "Insira o snippet antes do formulário de cadastro do MemberPress usado para finalização de compra."

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:73
msgid "Before Checkout Submit Button"
msgstr "Antes do botão de enviar da finalização da compra"

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:74
msgid "Insert snippet right before the MemberPress checkout submit button."
msgstr "Insira o snippet antes do botão de enviar da finalização de compra do MemberPress."

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:77
msgid "Before Checkout Coupon Field"
msgstr "Antes do campo de cupom da finalização da compra"

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:78
msgid "Insert snippet before the MemberPress checkout coupon field."
msgstr "Insira o snippet antes do campo de cupom da finalização de compra do MemberPress."

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:81
msgid "Before Account First Name"
msgstr "Antes do nome da conta"

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:82
msgid "Insert snippet to the Home tab of the MemberPress Account page before First Name field."
msgstr "Insira o snippet na guia Página inicial da página da conta do MemberPress antes do campo Nome."

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:85
msgid "Before Subscriptions Content"
msgstr "Antes do conteúdo das assinaturas"

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:86
msgid "Insert snippet at the beginning of the Subscriptions tab on the MemberPress Account page."
msgstr "Insira o snippet no início da aba Assinaturas na página da conta do MemberPress."

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:89
msgid "Before Login Form Submit"
msgstr "Antes do envio do formulário de login"

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:90
msgid "Insert snippet before the Remember Me checkbox on the MemberPress Login page."
msgstr "Insira o snippet antes da caixa de seleção Lembrar de mim na página de login do MemberPress."

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:94
msgid "Insert a snippet before the notice that access to the content is unauthorized. "
msgstr "Insira um snippet antes do aviso de que o acesso ao conteúdo não é autorizado. "

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:98
msgid "Insert a snippet after the notice that access to the content is unauthorized. "
msgstr "Insira um snippet após o aviso de que o acesso ao conteúdo não é autorizado. "

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:109
msgid "MemberPress Locations are a PRO feature"
msgstr "Locais MemberPress são um recurso da versão Pro"

#: includes/lite/conditional-logic/class-wpcode-conditional-memberpress.php:49
msgid "MemberPress Page"
msgstr "Página MemberPress"

#: includes/lite/conditional-logic/class-wpcode-conditional-memberpress.php:53
msgid "MemberPress Page Rules is a Pro Feature"
msgstr "As regras da página MemberPress são um recurso da versão Pro"

#: includes/lite/conditional-logic/class-wpcode-conditional-memberpress.php:54
#: includes/lite/conditional-logic/class-wpcode-conditional-memberpress.php:83
msgid "Get access to advanced conditional logic rules for MemberPress by upgrading to PRO today."
msgstr "Obtenha acesso a regras lógicas condicionais avançadas para o MemberPress atualizando para a versão Pro hoje mesmo."

#: includes/lite/conditional-logic/class-wpcode-conditional-memberpress.php:59
msgid "Registration Page"
msgstr "Página de registro"

#: includes/lite/conditional-logic/class-wpcode-conditional-memberpress.php:67
msgid "Account Page"
msgstr "Página da conta"

#: includes/lite/conditional-logic/class-wpcode-conditional-memberpress.php:71
msgid "Login Page"
msgstr "Página de login"

#: includes/lite/conditional-logic/class-wpcode-conditional-memberpress.php:77
msgid "User active membership"
msgstr "Associação ativa do usuário"

#: includes/lite/conditional-logic/class-wpcode-conditional-memberpress.php:82
msgid "MemberPress Active Membership Rules is a Pro Feature"
msgstr "As regras de associação ativa do MemberPress são um recurso da versão Pro"

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:93
msgid "Before the Unauthorized Message"
msgstr "Antes da mensagem de não autorização"

#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:97
msgid "After the Unauthorized Message"
msgstr "Após a mensagem não autorizada"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:153
msgid "Allow Usage Tracking"
msgstr "Permitir rastreamento de uso"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:157
msgid "By allowing us to track usage data, we can better help you, as we will know which WordPress configurations, themes, and plugins we should test."
msgstr "Ao nos permitir rastrear os dados de uso, podemos ajudar você melhor, pois saberemos quais configurações, temas e plugins do WordPress devemos testar."

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:502
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1761
msgid "Blocks snippets is a Pro Feature"
msgstr "Snippets de blocos são um recurso da versão Pro"

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:503
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1762
msgid "Upgrade to PRO today and unlock building snippets using the Gutenberg Block Editor. Create templates using blocks and use the full power of WPCode to insert them in your site."
msgstr "Atualize para a versão Pro hoje mesmo e descubra como criar snippets usando o bloco editor Gutenberg. Crie modelos usando blocos e use todo o poder do WPCode para inseri-los em seu site."

#: includes/class-wpcode-snippet-execute.php:144
msgid "Blocks Snippet (PRO)"
msgstr "Snippet de blocos (Pro)"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:136
msgid "License Key"
msgstr "Chave da licença"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:394
msgid "You're using WPCode Lite - no license needed. Enjoy!"
msgstr "Você está usando o WPCode Lite: não é necessário licença. Aproveite!"

#. Translators: %1$s - Opening anchor tag, do not translate. %2$s - Closing
#. anchor tag, do not translate.
#: includes/admin/pages/class-wpcode-admin-page-settings.php:401
msgid "To unlock more features consider %1$supgrading to PRO%2$s."
msgstr "Para desbloquear mais recursos, considere %1$satualizar para PRO%2$s."

#: includes/admin/pages/class-wpcode-admin-page-settings.php:408
msgid "Already purchased? Simply enter your license key below to enable WPCode PRO!"
msgstr "Já comprou? Então é só digitar sua chave de licença abaixo para ativar o WPCode PRO!"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:410
msgid "Paste license key here"
msgstr "Cole a chave de licença aqui"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:412
msgid "Verify Key"
msgstr "Verificar chave"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:429
msgid "Oops!"
msgstr "Opa!"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:431
msgid "Almost Done"
msgstr "Quase concluído"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:433
msgid "Unfortunately there was a server connection error."
msgstr "Infelizmente, ocorreu um erro de conexão do servidor."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:644
msgid "before paragraph number"
msgstr "antes do número do parágrafo"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:645
msgid "after paragraph number"
msgstr "após o número do parágrafo"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:646
msgid "before post number"
msgstr "antes do número do post"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:647
msgid "after post number"
msgstr "após o número do post"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:812
msgid "Search locations"
msgstr "Locais de pesquisa"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:756
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:820
msgid "Selected"
msgstr "Selecionado"

#: includes/auto-insert/class-wpcode-auto-insert-archive.php:62
msgid "Insert snippet above post summary."
msgstr "Insira o snippet acima do resumo do post."

#: includes/auto-insert/class-wpcode-auto-insert-archive.php:66
msgid "Insert snippet below post summary."
msgstr "Insira o snippet abaixo do resumo do post."

#: includes/auto-insert/class-wpcode-auto-insert-archive.php:70
msgid "Insert snippet between multiple posts."
msgstr "Insira o snippet entre várias posts."

#: includes/auto-insert/class-wpcode-auto-insert-archive.php:74
#: includes/auto-insert/class-wpcode-auto-insert-single.php:72
msgid "Insert snippet at the beginning of a post."
msgstr "Insira o snippet no início de um post."

#: includes/auto-insert/class-wpcode-auto-insert-archive.php:78
#: includes/auto-insert/class-wpcode-auto-insert-single.php:76
msgid "Insert snippet at the end of a post."
msgstr "Insira o snippet no final de um post."

#: includes/auto-insert/class-wpcode-auto-insert-everywhere.php:71
msgid "Snippet gets executed everywhere on your website."
msgstr "O snippet é executado em qualquer lugar do seu site."

#: includes/auto-insert/class-wpcode-auto-insert-everywhere.php:75
msgid "Snippet gets executed only in the frontend of the website."
msgstr "O snippet é executado somente na interface do site."

#: includes/auto-insert/class-wpcode-auto-insert-everywhere.php:79
msgid "The snippet only gets executed in the wp-admin area."
msgstr "O snippet é executado somente na área do administrador do wp."

#: includes/auto-insert/class-wpcode-auto-insert-everywhere.php:83
msgid "Ideal for running the snippet later with conditional logic rules in the frontend."
msgstr "Ideal para executar o snippet posteriormente com regras de lógica condicional na interface."

#: includes/auto-insert/class-wpcode-auto-insert-single.php:80
msgid "Insert snippet at the beginning of the post content."
msgstr "Insira o snippet no início do conteúdo do post."

#: includes/auto-insert/class-wpcode-auto-insert-single.php:84
msgid "Insert snippet at the end of the post content."
msgstr "Insira o snippet no final do conteúdo do post."

#: includes/auto-insert/class-wpcode-auto-insert-single.php:88
msgid "Insert snippet before paragraph # of the post content."
msgstr "Insira o snippet antes do n.º do parágrafo do conteúdo do post."

#: includes/auto-insert/class-wpcode-auto-insert-single.php:92
msgid "Insert snippet after paragraph # of the post content."
msgstr "Insira o snippet após o n.º do parágrafo do conteúdo do post."

#: includes/auto-insert/class-wpcode-auto-insert-site-wide.php:61
msgid "Insert snippet between the head tags of your website on all pages."
msgstr "Insira o snippet entre as tags do cabeçalho de seu site em todas as páginas."

#: includes/auto-insert/class-wpcode-auto-insert-site-wide.php:65
msgid "Insert the snippet just after the opening body tag."
msgstr "Insira o snippet logo após a tag de abertura do corpo."

#: includes/auto-insert/class-wpcode-auto-insert-site-wide.php:69
msgid "Insert the snippet in the footer just before the closing body tag."
msgstr "Insira o snippet no rodapé antes da tag de encerramento do corpo."

#: includes/class-wpcode-auto-insert.php:53
msgid "Global"
msgstr "Global"

#: includes/class-wpcode-auto-insert.php:54
msgid "Page-Specific"
msgstr "Específico da página"

#. translators: %s: Snippet title and ID used in the error log for clarity.
#: includes/class-wpcode-snippet.php:756
msgid "Snippet %s was automatically deactivated due to a fatal error."
msgstr "O snippet %s foi desativado automaticamente devido a um erro fatal."

#: includes/lite/admin/class-wpcode-connect.php:67
msgid "You are not allowed to install plugins."
msgstr "Você não tem autorização para instalar plugins."

#: includes/lite/admin/class-wpcode-connect.php:73
msgid "Please enter your license key to connect."
msgstr "Insira sua chave de licença para conectar."

#: includes/lite/admin/class-wpcode-connect.php:77
msgid "Only the Lite version can be upgraded."
msgstr "Somente a versão Lite pode atualizar."

#: includes/lite/admin/class-wpcode-connect.php:95
msgid "WPCode Pro is installed but not activated."
msgstr "O WPCode Pro está instalado, mas não ativado."

#: includes/lite/admin/class-wpcode-connect.php:148
msgid "There was an error while installing an upgrade. Please download the plugin from wpcode.com and install it manually."
msgstr "Ocorreu um erro ao instalar uma atualização. Baixe o plugin em wpcode.com e instale-o manualmente."

#: includes/lite/admin/class-wpcode-connect.php:181
#: includes/lite/admin/class-wpcode-connect.php:194
#: includes/lite/admin/class-wpcode-connect.php:255
msgid "Plugin installed & activated."
msgstr "Plugin instalado e ativado."

#: includes/lite/admin/class-wpcode-connect.php:202
msgid "There was an error while installing an upgrade. Please check file system permissions and try again. Also, you can download the plugin from wpcode.com and install it manually."
msgstr "Ocorreu um erro ao instalar uma atualização. Verifique as permissões do sistema de arquivos e tente novamente. Além disso, você pode baixar o plugin de wpcode.com e instalá-lo manualmente."

#: includes/lite/admin/class-wpcode-connect.php:229
msgid "No key provided."
msgstr "Nenhuma chave fornecida."

#: includes/lite/admin/class-wpcode-connect.php:259
msgid "Pro version installed but needs to be activated on the Plugins page inside your WordPress admin."
msgstr "Versão Pro instalada, mas precisa ser ativada na página Plugins, dentro do seu administrador do WordPress."

#: includes/lite/auto-insert/class-wpcode-auto-insert-anywhere.php:58
msgid "Anywhere (CSS Selector)"
msgstr "Em qualquer lugar (seletor CSS)"

#: includes/lite/auto-insert/class-wpcode-auto-insert-anywhere.php:69
msgid "Before HTML Element"
msgstr "Antes do elemento HTML"

#: includes/lite/auto-insert/class-wpcode-auto-insert-anywhere.php:70
msgid "Insert snippet before the HTML element specified by the CSS selector."
msgstr "Insira o snippet antes do elemento HTML especificado pelo seletor CSS."

#: includes/lite/auto-insert/class-wpcode-auto-insert-anywhere.php:73
msgid "After HTML Element"
msgstr "Após o elemento HTML"

#: includes/lite/auto-insert/class-wpcode-auto-insert-anywhere.php:74
msgid "Insert snippet after the HTML element specified by the CSS selector."
msgstr "Insira o snippet após o elemento HTML especificado pelo seletor CSS."

#: includes/lite/auto-insert/class-wpcode-auto-insert-anywhere.php:77
msgid "At the start of HTML Element"
msgstr "No início do elemento HTML"

#: includes/lite/auto-insert/class-wpcode-auto-insert-anywhere.php:78
msgid "Insert snippet before the content of the HTML element specified by CSS selector."
msgstr "Insira o snippet antes do conteúdo do elemento HTML especificado pelo seletor CSS."

#: includes/lite/auto-insert/class-wpcode-auto-insert-anywhere.php:81
msgid "At the end of HTML Element"
msgstr "Ao final do elemento HTML"

#: includes/lite/auto-insert/class-wpcode-auto-insert-anywhere.php:82
msgid "Insert snippet after the content of the HTML element specified by CSS selector."
msgstr "Insira o snippet após o conteúdo do elemento HTML especificado pelo seletor CSS."

#: includes/lite/auto-insert/class-wpcode-auto-insert-anywhere.php:97
msgid "Insert Anywhere by CSS Selector is a Premium feature"
msgstr "\"Inserir em qualquer lugar\" pelo seletor CSS é um recurso Premium"

#: includes/lite/auto-insert/class-wpcode-auto-insert-anywhere.php:98
msgid "Upgrade to PRO today and insert snippets anywhere on your site using CSS selectors to target any HTML element."
msgstr "Atualize para a versão Pro hoje mesmo e insira snippets em qualquer lugar de seu site usando seletores CSS para direcionar qualquer elemento HTML."

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:70
msgid "Insert snippet before the EDD purchase button."
msgstr "Insira o snippet antes do botão de compra EDD."

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:74
msgid "Insert snippet after the EDD purchase button."
msgstr "Insira o snippet após o botão de compra EDD."

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:78
msgid "Insert snippet before the single EDD download content."
msgstr "Insira o snippet antes do conteúdo de download do EDD único."

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:82
msgid "Insert snippet after the single EDD download content."
msgstr "Insira o snippet após o conteúdo de download do EDD único."

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:86
msgid "Insert snippet before the EDD cart."
msgstr "Insira o snippet antes do carrinho EDD."

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:90
msgid "Insert snippet after the EDD cart."
msgstr "Insira o snippet após o carrinho EDD."

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:94
msgid "Insert snippet before the EDD cart on the checkout page."
msgstr "Insira o snippet antes do carrinho EDD na página de finalização de compra."

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:98
msgid "Insert snippet after the EDD cart on the checkout page."
msgstr "Insira o snippet após o carrinho EDD na página de finalização de compra."

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:102
msgid "Insert snippet before the EDD checkout form on the checkout page."
msgstr "Insira o snippet antes do formulário de finalização de compra EDD na página de finalização de compra."

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:106
msgid "Insert snippet after the EDD checkout form on the checkout page"
msgstr "Insira o snippet após o formulário de finalização de compra EDD na página de finalização de compra"

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:71
msgid "Insert snippet before the list of products on a WooCommerce page."
msgstr "Insira o snippet antes da lista de produtos em uma página WooCommerce."

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:75
msgid "Insert snippet after the list of products on a WooCommerce page."
msgstr "Insira o snippet após a lista de produtos em uma página WooCommerce."

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:79
msgid "Insert snippet before the content on the single WooCommerce product page."
msgstr "Insira o snippet antes do conteúdo na página de produto único WooCommerce."

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:83
msgid "Insert snippet after the content on the single WooCommerce product page."
msgstr "Insira o snippet após o conteúdo na página de produto único WooCommerce."

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:87
msgid "Insert snippet before the product summary on the single WooCommerce product page."
msgstr "Insira o snippet antes do resumo do produto na página de produto único WooCommerce."

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:91
msgid "Insert snippet after the product summary on the single WooCommerce product page."
msgstr "Insira o snippet após o resumo do produto na página de produto único WooCommerce."

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:95
msgid "Insert snippet before the cart on WooCommerce pages."
msgstr "Insira o snippet antes do carrinho nas páginas WooCommerce."

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:99
msgid "Insert snippet after the cart on WooCommerce pages."
msgstr "Insira o snippet após o carrinho nas páginas do WooCommerce."

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:103
msgid "Insert snippet before the checkout form on the WooCommerce checkout page."
msgstr "Insira o snippet antes do formulário de finalização de compra na página de finalização de compra WooCommerce."

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:107
msgid "Insert snippet after the checkout form on the WooCommerce checkout page."
msgstr "Insira o snippet após o formulário de finalização de compra na página de finalização de compra WooCommerce."

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:111
msgid "Insert snippet before the checkout payment button on the WooCommerce checkout page."
msgstr "Insira o snippet antes do botão de pagamento da finalização da compra na página de finalização da compra WooCommerce."

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:115
msgid "Insert snippet after the checkout payment button on the WooCommerce checkout page."
msgstr "Insira o snippet após o botão de pagamento de finalização da compra na página de finalização da compra WooCommerce."

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:118
msgid "Before the Thank You Page"
msgstr "Antes da página de agradecimento"

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:119
msgid "Insert snippet before the thank you page content for WooCommerce."
msgstr "Insira o snippet antes do conteúdo da página de agradecimento do WooCommerce."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:726
#: includes/class-wpcode-auto-insert.php:55
msgid "eCommerce"
msgstr "Comércio eletrônico"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:601
msgid "Error Logging"
msgstr "Registro de erros"

#. Translators: %1$s: opening anchor tag, %2$s: closing anchor tag.
#: includes/admin/pages/class-wpcode-admin-page-settings.php:607
msgid "Log errors thrown by snippets? %1$sView Logs%2$s"
msgstr "Erros de registro gerados por snippets? %1$sVisualizar registros%2$s"

#. translators: %s: Error message.
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:211
msgid "Error message: %s"
msgstr "Mensagem de erro: %s"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1212
msgid "Snippet Status:"
msgstr "Status do snippet:"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1626
msgid "Add another \"AND\" rules row."
msgstr "Adicionar outra linha de regra \"E\"."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1758
msgid "Scheduling snippets is a Pro Feature"
msgstr "Agendar snippets é um recurso da versão Pro"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1759
msgid "Upgrade to PRO today and unlock powerful scheduling options to limit when your snippet is active on the site."
msgstr "Atualize para a versão PRO hoje mesmo e acesse opções avançadas de agendamento para limitar quando seu snippet estará ativo no site."

#. Translators: %1$s Opening anchor tag. %2$s Closing anchor tag.
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1778
msgid "For better results using conditional logic with PHP snippets we automatically switched the auto-insert location to \"Frontend Conditional Logic\" that runs later. If you want to run the snippet earlier please switch back to \"Run Everywhere\" but note not all conditional logic options will be available. %1$sRead more%2$s"
msgstr "Para melhores resultados usando a lógica condicional com snippets de PHP, mudamos automaticamente o local de inserção automática para \"Lógica condicional de interface\", que é executado posteriormente. Se quiser executar o snippet primeiro, mude novamente para \"Executar em todos os lugares\", mas observe que nem todas as opções de lógica condicional estarão disponíveis. %1$sLeia mais%2$s"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1990
msgid "Schedule snippet"
msgstr "Agendar snippet"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2097
msgid "Clear end date"
msgstr "Limpar data de término"

#. Translators: %1$s and %2$s are HTML tags for a link to the documentation
#. article.
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2106
msgid "Looking for more scheduling options? %1$sClick here%2$s to read more about all the available options."
msgstr "Procurando mais opções de agendamento? %1$sClique aqui%2$s para saber mais sobre todas as opções disponíveis."

#: includes/admin/pages/class-wpcode-admin-page-tools.php:495
#: includes/class-wpcode-admin-bar-info.php:422
msgid "Logs"
msgstr "Registros"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:968
msgid "You do not have sufficient permissions to view logs."
msgstr "Você não tem as permissões necessárias para visualizar os registros."

#. translators: %1$s: opening anchor tag, %2$s: closing anchor tag.
#: includes/admin/pages/class-wpcode-admin-page-tools.php:979
msgid "No logs found. You can enable logging from the %1$ssettings panel%2$s."
msgstr "Nenhum registro encontrado. Você pode ativar o registro no %1$spainel de configurações%2$s."

#: includes/admin/pages/class-wpcode-admin-page-tools.php:1017
msgid "Delete log"
msgstr "Excluir registro"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:1027
msgid "View"
msgstr "Visualizar"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:1035
msgid "Log is empty."
msgstr "O registro está vazio."

#: includes/admin/pages/class-wpcode-admin-page-tools.php:1055
msgid "Link expired. Please refresh the page and retry."
msgstr "O link expirou. Atualize a página e tente novamente."

#: includes/admin/pages/class-wpcode-admin-page-tools.php:1059
msgid "You do not have sufficient permissions to delete logs."
msgstr "Você não tem as permissões necessárias para excluir registros."

#: includes/admin/pages/class-wpcode-admin-page-tools.php:1078
msgid "Are you sure you want to delete this log?"
msgstr "Quer mesmo excluir este registro?"

#. translators: %s is the title of the metabox.
#: includes/admin/pages/class-wpcode-admin-page.php:607
msgid "Collapse Metabox %s"
msgstr "Recolher Metabox %s"

#: includes/admin/pages/class-wpcode-code-snippets-table.php:254
msgid "Toggle Snippet Status"
msgstr "Alternar status do snippet"

#: includes/class-wpcode-snippet-execute.php:600
msgid "View error logs"
msgstr "Visualizar registros de erros"

#: includes/class-wpcode-snippet-execute.php:609
msgid "Enable error logging"
msgstr "Ativar registro de erros"

#: includes/helpers.php:184
msgid "Is Before"
msgstr "É antes"

#: includes/helpers.php:185
msgid "Is After"
msgstr "É depois"

#: includes/helpers.php:186
msgid "Is on or Before"
msgstr "É até"

#: includes/helpers.php:187
msgid "Is on or After"
msgstr "É a partir de"

#: includes/lite/conditional-logic/class-wpcode-conditional-schedule.php:48
msgid "Date"
msgstr "Data"

#: includes/lite/conditional-logic/class-wpcode-conditional-schedule.php:53
msgid "Date & Time"
msgstr "Data e hora"

#: includes/lite/conditional-logic/class-wpcode-conditional-schedule.php:58
msgid "Day of the Week"
msgstr "Dia da semana"

#: includes/lite/conditional-logic/class-wpcode-conditional-schedule.php:64
msgid "Current time"
msgstr "Hora atual"

#: includes/lite/conditional-logic/class-wpcode-conditional-schedule.php:72
msgid "Scheduling rules are a Pro Feature"
msgstr "As regras de agendamento são um recurso da versão Pro"

#: includes/lite/conditional-logic/class-wpcode-conditional-schedule.php:73
msgid "Upgrade today and get access to advanced scheduling conditional logic rules."
msgstr "Atualize hoje mesmo e tenha acesso a regras lógicas condicionais de agendamento avançadas."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2086
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2087
msgid "Start Date"
msgstr "Data inicial"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2088
msgid "Clear start date"
msgstr "Limpar data inicial"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2095
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2096
msgid "End Date"
msgstr "Data final"

#: includes/auto-insert/class-wpcode-auto-insert-everywhere.php:82
msgid "Frontend Conditional Logic"
msgstr "Lógica condicional de interface"

#. Translators: Support link tag starts with url and Support link tag ends.
#: includes/admin/class-wpcode-skin-legacy.php:92
#: includes/admin/class-wpcode-skin.php:92
msgid "There was an error installing the addon. Please try again. If you are still having issues, please %1$scontact our support%2$s team."
msgstr "Ocorreu um erro ao instalar o complemento. Tente novamente. Se ainda estiver com problemas, %1$sentre em contato com nossa equipe de suporte%2$s."

#. Translators: The name of the addon that can't be installed, Support link tag
#. starts with url and Support link tag ends.
#: includes/admin/class-wpcode-skin-legacy.php:105
#: includes/admin/class-wpcode-skin.php:106
msgid "There was an error installing the addon, %1$s. Please try again. If you are still having issues, please %2$scontact our support%3$s team. "
msgstr "Ocorreu um erro ao instalar o complemento %1$s. Tente novamente. Se ainda estiver com problemas, %2$sentre em contato com nossa equipe de suporte%3$s. "

#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:254
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:74
#: includes/admin/pages/class-wpcode-admin-page-settings.php:496
#: includes/admin/pages/class-wpcode-admin-page-settings.php:674
msgid "Upgrade to WPCode PRO"
msgstr "Atualize para o WPCode Pro"

#. Translators: gets replaced with the snippet title.
#: includes/admin/pages/class-wpcode-admin-page-generator.php:101
msgid "You are now editing the generated snippet: \"%s\". Updating the snippet will override any edits you made to the code."
msgstr "Agora você está editando o snippet gerado: \"%s\". A atualização do snippet substituirá todas as edições que você fez no código."

#: includes/admin/pages/class-wpcode-admin-page-generator.php:221
msgid "Update Snippet"
msgstr "Atualizar snippet"

#. translators: %1$s and %2$s are <u> tags.
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:65
msgid "While you can always add pixels manually using code snippets, our Conversion Pixels addon helps you %1$ssave time%2$s while %1$sreducing errors%2$s. It lets you properly implement Facebook, Google, Pinterest, TikTok and Snapchat ads tracking with deep integrations for eCommerce events, interaction measurement, and more. This addon is available on WPCode Plus plan or higher."
msgstr "Você sempre pode adicionar pixels manualmente usando snippets de código, mas nosso complemento Pixels de conversão ajuda você a %1$spoupar tempo%2$s e ainda %1$sreduzir o número de erros%2$s. Ele permite que você implemente corretamente o monitoramento de anúncios do Facebook, Google, Pinterest, TikTok e Snapchat com integrações avançadas para eventos de comércio eletrônico, medição de interação e muito mais. Esse complemento está disponível no plano WPCode Plus ou superior."

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:71
msgid "Conversion Pixels Addon is a PRO Feature"
msgstr "O complemento Pixels de conversão é um recurso da versão PRO"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:81
msgid "No coding required"
msgstr "Não é necessário programação"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:82
msgid "1-click setup for conversion tracking"
msgstr "Configuração com um clique para monitoramento de conversões"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:94
msgid "Facebook"
msgstr "Facebook"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:95
msgid "Google"
msgstr "Google"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:96
msgid "Pinterest"
msgstr "Pinterest"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:97
msgid "TikTok"
msgstr "TikTok"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:130
msgid "Facebook Pixel"
msgstr "Pixel do Facebook"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:133
msgid "Facebook Pixel ID"
msgstr "ID do pixel do Facebook"

#. translators: %1$s and %2$s are the opening and closing anchor tags.
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:139
msgid "You can find your Facebook Pixel ID in the Facebook Ads Manager. %1$sRead our step by step directions%2$s. "
msgstr "Você pode encontrar sua ID do pixel do Facebook no Gerenciador de anúncios do Facebook. %1$sLeia nossas instruções passo a passo%2$s. "

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:147
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:397
msgid "Conversions API Token"
msgstr "Token da API de conversões"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:151
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:401
msgid "The Conversions API token allows you to send API events that are more reliable and can improve audience targeting."
msgstr "O token da API de conversões permite que você envie eventos da API que são mais confiáveis e podem melhorar a segmentação do público-alvo."

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:157
msgid "Facebook Pixel Events"
msgstr "Eventos do pixel do Facebook"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:170
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:247
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:311
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:361
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:421
msgid "eCommerce Events Tracking"
msgstr "Monitoramento de eventos de comércio eletrônico"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:188
msgid "Google Analytics & Ads Tracking"
msgstr "Google Analytics e monitoramento de anúncios"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:191
msgid "Google Analytics ID"
msgstr "ID do Google Analytics"

#. translators: %1$s and %2$s are the opening and closing anchor tags.
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:197
msgid "You can find your Google Analytics ID in the Google Analytics Admin panel. %1$sRead our step by step directions%2$s. "
msgstr "Você pode encontrar sua ID do Google Analytics no painel de administração do Google Analytics. %1$sLeia nossas instruções passo a passo%2$s. "

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:205
msgid "Google Ads Tag ID"
msgstr "ID da tag do Google Ads"

#. translators: %1$s and %2$s are the opening and closing anchor tags.
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:211
msgid "You can find your Google Ads Tag ID in the Google Ads Settings under Google Tag. %1$sRead our step by step directions%2$s. "
msgstr "Você pode encontrar a ID da tag do Google Ads nas configurações do Google Ads, em Tag do Google. %1$sLeia nossas instruções passo a passo%2$s. "

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:219
msgid "Ads Conversion Label"
msgstr "Rótulo de conversão de anúncios"

#. translators: %1$s and %2$s are the opening and closing anchor tags.
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:225
msgid "Add your Google Ads Conversion Label for tracking conversion events. %1$sLearn More%2$s."
msgstr "Adicione seu rótulo de conversão do Google Ads para monitorar eventos de conversão. %1$sSaiba mais%2$s."

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:233
msgid "Google Events"
msgstr "Eventos do Google"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:265
msgid "Pinterest Tag"
msgstr "Tag do Pinterest"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:268
msgid "Tag ID"
msgstr "ID da tag"

#. translators: %1$s and %2$s are the opening and closing anchor tags.
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:274
msgid "You can find your Tag id in your Pinterest Business account. %1$sRead our step by step directions%2$s. "
msgstr "Você pode encontrar a ID da sua tag na sua conta do Pinterest Business. %1$sLeia nossas instruções passo a passo%2$s. "

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:282
msgid "Ad Account ID"
msgstr "ID da conta de anúncio"

#. translators: %1$s and %2$s are the opening and closing anchor tags.
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:288
msgid "You can find your Ad Account ID in your Pinterest Business account. %1$sRead more%2$s. "
msgstr "Você pode encontrar a ID da sua conta de anúncio na sua conta do Pinterest Business. %1$sLeia mais%2$s. "

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:296
msgid "Conversion Access Token"
msgstr "Token de acesso de conversão"

#. translators: %1$s and %2$s are the opening and closing anchor tags.
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:302
msgid "You can find your Conversion Access Token under Ads > Conversions > Conversion access token. %1$sRead more%2$s. "
msgstr "Você pode encontrar seu token de acesso de conversão em Anúncios > Conversões > Token de acesso de conversão. %1$sLeia mais%2$s. "

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:329
msgid "TikTok Pixel"
msgstr "Pixel do TikTok"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:332
msgid "Pixel ID"
msgstr "ID do pixel"

#. translators: %1$s and %2$s are the opening and closing anchor tags.
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:338
msgid "You can find your Pixel id in your TikTok Business Account. %1$sRead our step by step directions%2$s."
msgstr "Você pode encontrar sua ID do Pixel em sua conta comercial TikTok. %1$sLeia nossas instruções passo a passo%2$s."

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:346
msgid "Events API Access Token"
msgstr "Token de acesso à API de eventos"

#. translators: %1$s and %2$s are the opening and closing anchor tags.
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:352
msgid "You can generate an access token in the Pixel Settings under Access Token Generation. %1$sRead more%2$s"
msgstr "Você pode gerar um token de acesso nas Configurações do Pixel, em Geração de token de acesso. %1$sLeia mais%2$s"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:461
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:499
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:708
msgid "Purchase Event"
msgstr "Evento Compra"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:463
msgid "Turn on the \"Purchase\" event to track successful purchases on your website."
msgstr "Ative o evento \"Compra\" para monitorar compras bem-sucedidas em seu site."

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:478
msgid "View Item Event"
msgstr "Evento Visualização de item"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:485
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:530
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:568
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:694
msgid "Add to Cart Event"
msgstr "Evento Adicionar ao carrinho"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:487
msgid "Send the Add to Cart event when a product is added to the cart."
msgstr "Envie o evento Adicionar ao carrinho quando um produto for adicionado ao carrinho."

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:492
msgid "Begin Checkout Event"
msgstr "Evento Início de finalização de compra"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:494
msgid "Send the Begin Checkout event when the user sees the checkout page."
msgstr "Envie o evento Início de finalização de compra quando o usuário visualizar a página de finalização de compra."

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:501
msgid "Send the Purchase event when the user completes a purchase."
msgstr "Envie o evento Compra quando o usuário concluir uma compra."

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:506
msgid "Conversion Event"
msgstr "Evento Conversão"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:508
msgid "Send the conversion event with the Google Ads label set above on a successful purchase."
msgstr "Envie o evento Conversão com o rótulo do Google Ads indicado acima em uma compra bem-sucedida."

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:523
msgid "PageVisit Product Event"
msgstr "Evento Visita à página de produtos"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:537
msgid "Checkout PageVisit Event"
msgstr "Evento Visita à página de finalização de compra"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:544
msgid "Checkout Event"
msgstr "Evento Finalização de compra"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:546
msgid "Turn on the \"Checkout\" event to track successful purchases on your website."
msgstr "Ative o evento “Finalização de compra” para monitorar compras bem-sucedidas em seu site."

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:561
msgid "ViewContent Product Event"
msgstr "Evento Visualização de conteúdo de produto"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:582
msgid "PlaceAnOrder Event"
msgstr "Evento Realização de pedido"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:584
msgid "Turn on the \"PlaceAnOrder\" event to track successful purchases on your website."
msgstr "Ative o evento \"Realização de pedido\" para monitorar compras bem-sucedidas em seu site."

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:660
msgid "Disabled, no eCommerce Platform Detected"
msgstr "Desativado, nenhuma plataforma de comércio eletrônico detectada"

#. translators: %s is the name of the eCommerce provider.
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:664
msgid "%s Tracking Enabled"
msgstr "Monitoramento ativado do %s"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1141
msgid "Update Generated Snippet"
msgstr "Atualizar snippet gerado"

#: includes/class-wpcode-library-auth.php:101
#: includes/class-wpcode-library-auth.php:171
msgid "You do not have permissions to connect WPCode to the library."
msgstr "Você não tem permissão para conectar o WPCode à biblioteca."

#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:143
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:286
#: includes/lite/class-wpcode-admin-bar-info-lite.php:92
msgid "While you can always use global snippets, in the PRO version you can easily add page-specific scripts and snippets directly from the post edit screen."
msgstr "Você sempre pode usar snippets globais, mas na versão PRO é possível adicionar facilmente scripts e snippets específicos da página diretamente da tela de edição do post."

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:161
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:237
msgid "PageView Event"
msgstr "Evento Visualização de página"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:163
msgid "Enable the PageView event to track and record page visits on all the pages of your website using the Facebook Pixel."
msgstr "Ative o evento \"Visualização de página\" para monitorar e registrar visitas a todas as páginas do seu site usando o pixel do Facebook."

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:440
msgid "ViewContent Event"
msgstr "Evento Visualização de conteúdo"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:447
msgid "AddtoCart Event"
msgstr "Evento Adicionar ao carrinho"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:449
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:570
msgid "Turn on the \"AddToCart\" event to track when items are added to a shopping cart on your website."
msgstr "Ative o evento \"Adicionar ao carrinho\" para monitorar quando os itens são adicionados a um carrinho de compras no seu site."

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:454
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:575
msgid "InitiateCheckout Event"
msgstr "Evento Iniciar finalização de compra"

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:456
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:577
msgid "Turn on the \"InitiateCheckout\" event to track when a user reaches the checkout page on your website."
msgstr "Ative o evento \"Iniciar finalização de compra\" para monitorar quando um usuário chega à página de finalização de compra do seu site."

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:480
msgid "Send the View Item event to track views of product pages on your website."
msgstr "Envie o evento Visualização de item para monitorar as visualizações de páginas de produtos em seu site."

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:525
msgid "Turn on the \"PageVisit\" event to track views of product pages on your website."
msgstr "Ative o evento \"Visita à página\" para monitorar visualizações de páginas de produtos em seu site."

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:532
msgid "Turn on the Add to Cart event to track when items are added to a shopping cart on your website."
msgstr "Ative o evento \"Adicionar ao carrinho\" para monitorar quando os itens são adicionados a um carrinho de compras em seu site."

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:539
msgid "Enable the Checkout PageVisit event to track when a user reaches the checkout page on your website."
msgstr "Ative o evento \"Visita à página de finalização da compra\" para monitorar quando um usuário chega à página de finalização da compra em seu site."

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:239
msgid "Enable PageView event on all pages."
msgstr "Ativar o evento Visualização de página em todas as páginas."

#: includes/admin/pages/class-wpcode-admin-page-pixel.php:442
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:563
msgid "Turn on the \"ViewContent\" event to track views of product pages on your website."
msgstr "Ative o evento \"Visualização de conteúdo\" para monitorar as visualizações de páginas de produtos em seu site."

#. translators: %s a html break.
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:670
msgid "Advanced eCommerce tracking is available for WooCommerce, Easy Digital Downloads and MemberPress. %s These plugins are detected automatically and when available you can toggle individual events using the options below."
msgstr "O rastreamento avançado de comércio eletrônico está disponível para WooCommerce, Easy Digital Downloads e MemberPress. %s Esses plugins são detectados automaticamente e, quando disponíveis, você pode alternar eventos individuais usando as opções abaixo."

#: includes/admin/pages/class-wpcode-admin-page-click.php:45
msgid "1-Click"
msgstr "Um clique"

#: includes/admin/pages/class-wpcode-admin-page-click.php:98
msgid "You're almost there! To finish installing the snippet, you need to connect your site to your account on the WPCode Library. This will allow you to install snippets directly to your site in the future."
msgstr "Falta pouco! Para concluir a instalação do snippet, conecte seu site à sua conta na Biblioteca do WPCode. Isso permitirá que você instale no futuro os snippets diretamente em seu site."

#: includes/admin/pages/class-wpcode-admin-page-click.php:99
msgid "You'll also get access to tens of free expert-curated snippets that can be installed with 1-click from inside the plugin."
msgstr "Além disso, você terá acesso a dezenas de snippets gratuitos selecionados por especialistas que podem ser instalados com um clique dentro do plugin."

#: includes/admin/pages/class-wpcode-admin-page-click.php:109
msgid "Your site is already connected to the  WPCode Library using another account"
msgstr "Seu site já está conectado à Biblioteca do WPCode usando outra conta"

#: includes/admin/pages/class-wpcode-admin-page-click.php:111
msgid "In order to continue installing the snippet from the WPCode library you have to either login to the Library with the same account used to connect this site to the WPCode library initially or disconnect this site from the WPCode library and connect using your own account."
msgstr "Para continuar instalando o snippet da Biblioteca do WPCode, faça login na biblioteca com a mesma conta usada para conectar este site à biblioteca inicialmente ou desconecte este site da biblioteca e conecte-se usando sua própria conta."

#: includes/admin/pages/class-wpcode-admin-page-click.php:115
msgid "Disconnect Site From Library"
msgstr "Desconectar site da Biblioteca"

#: includes/admin/pages/class-wpcode-admin-page-click.php:118
msgid "Login with another user on the WPCode Library"
msgstr "Faça login com outro usuário na Biblioteca do WPCode"

#: includes/admin/pages/class-wpcode-admin-page-click.php:127
msgid "Congratulations, your site is now connected!"
msgstr "Parabéns! Seu site já está conectado."

#: includes/admin/pages/class-wpcode-admin-page-click.php:128
msgid "Your site is now connected to the WPCode Library and you can install snippets directly from the library. Please click the button below to resume installing the snippet you were viewing."
msgstr "Seu site agora está conectado à Biblioteca do WPCode e você pode instalar snippets diretamente dela. Clique no botão abaixo para retomar a instalação do snippet que você estava visualizando."

#: includes/admin/pages/class-wpcode-admin-page-click.php:131
msgid "Resume Snippet Installation"
msgstr "Retomar instalação do snippet"

#: includes/admin/pages/class-wpcode-admin-page-click.php:138
msgid "Your site is not connected to the WPCode library."
msgstr "Seu site não está conectado à Biblioteca do WPCode."

#: includes/admin/pages/class-wpcode-admin-page-click.php:139
msgid "Connect now to enable installing public snippets from the WPCode library with 1-click and also get access to tens of expert-curated snippets that you can install from inside the plugin."
msgstr "Conecte-se agora para habilitar a instalação de snippets públicos da Biblioteca do WPCode com um clique e obtenha acesso a dezenas de snippets selecionados por especialistas que você pode instalar a partir do plugin."

#: includes/admin/pages/class-wpcode-admin-page-click.php:150
msgid "No snippet provided."
msgstr "Nenhum snippet fornecido."

#: includes/admin/pages/class-wpcode-admin-page-click.php:157
msgid "Missing authentication token, please click the link in the WPCode Library again."
msgstr "Token de autenticação ausente. Clique novamente no link da Biblioteca do WPCode."

#: includes/admin/pages/class-wpcode-admin-page-click.php:170
msgid "We encountered an error loading your snippet, please try again in a few minutes"
msgstr "Encontramos um erro ao carregar seu snippet. Tente novamente em alguns minutos"

#. translators: %s: The error message from the API.
#: includes/admin/pages/class-wpcode-admin-page-click.php:175
msgid "We encountered the following error loading your snippet: %s"
msgstr "Encontramos o seguinte erro ao carregar seu snippet: %s"

#: includes/admin/pages/class-wpcode-admin-page-click.php:186
msgid "Go back to the library"
msgstr "Volte para a Biblioteca"

#: includes/admin/pages/class-wpcode-admin-page-click.php:194
msgid "Library Snippet Preview"
msgstr "Pré-visualizar snippet da Biblioteca"

#: includes/admin/pages/class-wpcode-admin-page-click.php:195
msgid "Please review the snippet below and confirm that you would like to install it."
msgstr "Confira o snippet abaixo e confirme que deseja instalá-lo."

#. Translators: %s: The snippet name.
#: includes/admin/pages/class-wpcode-admin-page-click.php:199
msgid "Snippet title: %s"
msgstr "Título do snippet: %s"

#: includes/admin/pages/class-wpcode-admin-page-click.php:205
msgid "Code preview"
msgstr "Pré-visualizar código"

#: includes/admin/pages/class-wpcode-admin-page-click.php:230
msgid "Confirm & Install Snippet"
msgstr "Confirmar e instalar o snippet"

#: includes/admin/pages/class-wpcode-admin-page-click.php:255
msgid "Confirm Snippet Installation"
msgstr "Confirmar a instalação do snippet"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:211
msgid "Editor Height"
msgstr "Altura do editor"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:376
msgid "Auto height"
msgstr "Altura automática"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:379
msgid "Set the editor height in pixels or enable auto-grow so the code editor automatically grows in height with the code."
msgstr "Define a altura do editor em pixels ou ativa o crescimento automático para que o editor de código cresça na mesma altura que o código."

#: includes/class-wpcode-library-auth.php:61
msgid "Your WordPress Site"
msgstr "Seu site WordPress"

#: includes/admin/class-wpcode-upgrade-welcome.php:270
msgid "Store Snippets in Cloud"
msgstr "Armazenar snippets na nuvem"

#. Translators: Placeholders add a link to the suggestions page.
#: includes/admin/class-wpcode-upgrade-welcome.php:276
msgid "This feature is now available in the %1$sPRO version of the plugin%2$s along with other powerful features."
msgstr "Esse recurso agora está disponível na versão %1$sPRO do plugin%2$s, além de outros recursos avançados."

#: includes/admin/pages/class-wpcode-admin-page-click.php:102
#: includes/admin/pages/class-wpcode-admin-page-click.php:142
#: includes/admin/pages/class-wpcode-admin-page.php:1300
msgid "Connect to Library"
msgstr "Conectar-se à biblioteca"

#. translators: %d - Activated snippets count.
#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:384
msgid "%d snippet was successfully activated."
msgid_plural "%d snippets were successfully activated."
msgstr[0] "O snippet %d foi ativado com sucesso."
msgstr[1] "Os snippets %d foram ativados com sucesso."

#. translators: %d - Failed to activate snippets count.
#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:393
msgid "%d snippet was not activated due to an error."
msgid_plural "%d snippets were not activated due to errors."
msgstr[0] "O snippet %d não foi ativado devido a um erro."
msgstr[1] "Os snippets %d não foram ativados devido a erros."

#. translators: %d - Deactivated snippets count.
#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:402
msgid "%d snippet was successfully deactivated."
msgid_plural "%d snippets were successfully deactivated."
msgstr[0] "O snippet %d foi desativado com sucesso."
msgstr[1] "Os snippets %d foram desativados com sucesso."

#: includes/admin/pages/class-wpcode-admin-page-settings.php:432
#: includes/admin/pages/class-wpcode-code-snippets-table.php:469
msgid "Activate"
msgstr "Ativar"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1755
msgid "Device Type is a Pro Feature"
msgstr "O tipo de dispositivo é um recurso da versão Pro"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1756
msgid "Upgrade to PRO today and unlock one-click device targeting for your snippets."
msgstr "Atualize para a versão Pro hoje mesmo e aproveite a segmentação por dispositivo para seus snippets com apenas um clique."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2229
msgid "Limit where you want this snippet to be loaded by device type. By default, snippets are loaded on all devices."
msgstr "Restrinja onde você deseja que esse snippet seja carregado por tipo de dispositivo. Por padrão, os snippets são carregados em todos os dispositivos."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2235
#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:48
msgid "Device Type"
msgstr "Tipo de dispositivo"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2249
msgid "Any device type"
msgstr "Qualquer tipo de dispositivo"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2250
msgid "Desktop only"
msgstr "Somente desktop"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2251
msgid "Mobile only"
msgstr "Somente dispositivos móveis"

#. translators: %d - snippets count.
#: includes/admin/pages/class-wpcode-admin-page.php:1291
msgid "Get Access to Our Library of %d FREE Snippets"
msgstr "Obtenha acesso à nossa Biblioteca de snippets GRATUITOS %d"

#: includes/admin/pages/class-wpcode-admin-page.php:1296
msgid "Connect your website with WPCode Library and get instant access to FREE code snippets written by our experts. Snippets can be installed with just 1-click from inside the plugin and come automatically-configured to save you time."
msgstr "Conecte seu site à Biblioteca do WPCode e obtenha acesso instantâneo a snippets de código GRATUITOS criados por nossos especialistas. Os snippets podem ser instalados com apenas um clique a partir do plugin e são configurados automaticamente para economizar seu tempo."

#: includes/admin/pages/class-wpcode-code-snippets-table.php:470
msgid "Deactivate"
msgstr "Desativar"

#: includes/lite/admin/class-wpcode-admin-page-loader-lite.php:35
#: includes/lite/admin/class-wpcode-admin-page-loader-lite.php:36
#: includes/lite/class-wpcode-admin-bar-info-lite.php:71
msgid "Upgrade to Pro"
msgstr "Atualizar para a versão Pro"

#. Translators: more here is used in the sense of "get access to more snippets"
#. and gets replaced with the number of snippets if the library items are
#. loaded correctly.
#: includes/lite/admin/notices.php:37
msgid "more"
msgstr "mais"

#. Translators: %1$s and %2$s add a link to the upgrade page. %3$s and %4$s
#. make the text bold.
#: includes/lite/admin/notices.php:88
msgid "%3$sYou're using WPCode Lite%4$s. To unlock more features consider %1$supgrading to Pro%2$s."
msgstr "%3$sVocê está usando o WPCode Lite%4$s. Para acessar mais recursos, %1$satualize para a versão Pro%2$s."

#: includes/lite/admin/notices.php:116
msgid "Get WPCode Pro and Unlock all the Powerful Features"
msgstr "Obtenha o WPCode Pro e acesse todos os recursos avançados"

#: includes/lite/admin/notices.php:119
msgid "Save & Reuse snippets in your private Cloud Library"
msgstr "Salve e reutilize snippets em sua biblioteca privada na nuvem"

#: includes/lite/admin/notices.php:120
msgid "Add page-specific scripts when editing a post/page."
msgstr "Adicione scripts específicos da página ao editar um post/página."

#: includes/lite/admin/notices.php:121
msgid "Track all snippet changes with Advanced Code Revisions"
msgstr "Monitore todas as alterações de snippets com revisões avançadas de código"

#: includes/lite/admin/notices.php:124
msgid "Load snippets by device (mobile/desktop) with 1-click."
msgstr "Carregue snippets por dispositivo (dispositivo móvel/desktop) com apenas um clique."

#: includes/lite/admin/notices.php:125
msgid "Easily insert and reuse content with Custom Shortcodes."
msgstr "Insira e reutilize facilmente o conteúdo com shortcodes personalizados."

#: includes/lite/admin/notices.php:126
msgid "Precisely track eCommerce conversions for WooCommerce and EDD."
msgstr "Monitore com precisão as conversões de comércio eletrônico para WooCommerce e EDD."

#: includes/lite/admin/notices.php:132
msgid "Get WPCode Pro Today and Unlock all the Powerful Features »"
msgstr "Obtenha o WPCode Pro hoje mesmo e aproveite todos os recursos avançados »"

#. Translators: Placeholders make the text bold.
#: includes/lite/admin/notices.php:137
msgid "%1$sBonus:%2$s WPCode Lite users get %3$s$50 off regular price%4$s, automatically applied at checkout"
msgstr "%1$sBônus:%2$s Os usuários do WPCode Lite ganham %3$s$50 de desconto sobre o preço normal%4$s, aplicado automaticamente a finalização da compra"

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:52
msgid "Device Type Rules are a Pro Feature"
msgstr "As regras de tipo de dispositivo são um recurso da versão Pro"

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:53
msgid "Get access to advanced device type conditional logic rules by upgrading to PRO today."
msgstr "Obtenha acesso a regras avançadas de lógica condicional de tipo de dispositivo atualizando para a versão Pro hoje mesmo."

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:58
msgid "Desktop"
msgstr "Desktop"

#: includes/lite/conditional-logic/class-wpcode-conditional-device.php:63
msgid "Mobile"
msgstr "Dispositivo móvel"

#: includes/admin/class-wpcode-review.php:128
msgid "Are you enjoying WPCode?"
msgstr "Você está gostando do WPCode?"

#: includes/admin/class-wpcode-review.php:130
msgid "Not Really"
msgstr "Não muito"

#: includes/admin/class-wpcode-review.php:134
msgid "We're sorry to hear you aren't enjoying WPCode. We would love a chance to improve. Could you take a minute and let us know what we can do better?"
msgstr "Lamentamos saber que você não está gostando do WPCode. Gostaríamos muito de poder melhorar. Você poderia reservar um minuto para nos dizer o que podemos fazer melhor?"

#: includes/admin/class-wpcode-review.php:136
#: includes/admin/class-wpcode-review.php:142
msgid "Give Feedback"
msgstr "Dar opinião"

#: includes/admin/class-wpcode-review.php:136
#: includes/admin/class-wpcode-review.php:142
msgid "No thanks"
msgstr "Não"

#: includes/admin/class-wpcode-review.php:140
msgid "That's awesome! Could you please do us a BIG favor and give it a 5-star rating on WordPress to help us spread the word and boost our motivation?"
msgstr "Isso é maravilhoso! Você poderia nos fazer um GRANDE favor e dar uma avaliação de 5 estrelas no WordPress para divulgar o nosso trabalho e aumentar nossa motivação?"

#. Plugin Name of the plugin
#: ihaf.php
msgid "WPCode Lite"
msgstr "WPCode Lite"

#: ihaf.php:125
msgid "Heads up!"
msgstr "Atenção!"

#: ihaf.php:126
msgid "Your site already has WPCode Pro activated. If you want to switch to WPCode Lite, please first go to Plugins → Installed Plugins and deactivate WPCode. Then, you can activate WPCode Lite."
msgstr "Seu site já tem o WPCode Pro ativado. Se você quiser mudar para o WPCode Lite, primeiro vá para Plugins → Plugins instalados e desative o WPCode. Em seguida, você poderá ativar o WPCode Lite."

#: includes/admin/admin-scripts.php:48
msgid "Please wait."
msgstr "Aguarde."

#: includes/admin/admin-scripts.php:49
#: includes/admin/pages/class-wpcode-admin-page-settings.php:430
msgid "OK"
msgstr "Tudo bem"

#: includes/admin/admin-scripts.php:56 includes/admin/admin-scripts.php:60
#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:505
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1804
#: includes/lite/class-wpcode-smart-tags-lite.php:22
msgid "Upgrade to PRO"
msgstr "Atualize para Pro"

#: includes/admin/class-wpcode-admin-page-loader.php:220
msgid "Upgrade to WPCode Pro"
msgstr "Atualize para o WPCode Pro"

#: includes/admin/class-wpcode-admin-page-loader.php:221
msgid "Get WPCode Pro"
msgstr "Obtenha o WPCode Pro"

#: includes/admin/class-wpcode-metabox-snippets.php:54
msgid "WPCode Page Scripts"
msgstr "Scripts de página do WPCode"

#: includes/admin/class-wpcode-metabox-snippets.php:66
msgid "Custom Code Snippet"
msgstr "Snippet de código personalizado"

#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:335
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2300
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:313
msgid "Code Revisions is a Pro Feature"
msgstr "Análises de código são um recurso da versão Pro"

#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:341
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2306
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:319
msgid "Upgrade to Pro and Unlock Revisions"
msgstr "Atualize para a versão Pro e acesse as revisões"

#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:345
#: includes/admin/pages/class-wpcode-admin-page-library.php:219
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2310
#: includes/admin/pages/trait-wpcode-my-library-markup.php:237
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:150
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:293
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:323
msgid "Learn more about all the features"
msgstr "Saiba mais sobre todos os recursos"

#: includes/admin/pages/class-wpcode-admin-page-library.php:63
msgid "Snippets"
msgstr "Snippets"

#: includes/admin/pages/class-wpcode-admin-page-library.php:64
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:384
msgid "My Library"
msgstr "Minha biblioteca"

#: includes/admin/pages/class-wpcode-admin-page-library.php:65
msgid "My Favorites"
msgstr "Meus favoritos"

#: includes/admin/pages/trait-wpcode-my-library-markup.php:230
msgid "My Library is a PRO Feature"
msgstr "Minha biblioteca é um recurso da versão PRO"

#: includes/admin/pages/trait-wpcode-my-library-markup.php:233
msgid "Upgrade to PRO and Unlock \"My Library\""
msgstr "Atualize para a versão PRO e acesse a \"Minha biblioteca\""

#: includes/admin/pages/class-wpcode-admin-page-library.php:225
msgid "Save your snippets to the WPCode Library"
msgstr "Salve seus snippets na Biblioteca do WPCode"

#: includes/admin/pages/class-wpcode-admin-page-library.php:224
msgid "Import snippets from the WPCode Library"
msgstr "Importe snippets da Biblioteca do WPCode"

#: includes/admin/pages/class-wpcode-admin-page-library.php:226
#: includes/admin/pages/trait-wpcode-my-library-markup.php:245
msgid "Set up new websites faster"
msgstr "Configure novos sites com mais rapidez"

#: includes/admin/pages/class-wpcode-admin-page-library.php:227
msgid "Easily implement features on multiple sites"
msgstr "Implemente recursos com facilidade em vários sites"

#: includes/admin/pages/class-wpcode-admin-page-library.php:228
#: includes/admin/pages/trait-wpcode-my-library-markup.php:246
msgid "Edit snippets in the WPCode Library"
msgstr "Edite snippets na Biblioteca do WPCode"

#: includes/admin/pages/class-wpcode-admin-page-library.php:223
msgid "Load favorite snippets in the plugin"
msgstr "Carregue seus snippets favoritos no plugin"

#: includes/admin/pages/class-wpcode-admin-page-library.php:212
msgid "My Favorites is a PRO Feature"
msgstr "Meus favoritos é um recurso da versão PRO"

#: includes/admin/pages/class-wpcode-admin-page-library.php:213
msgid "Upgrade to WPCode PRO today and see the snippets you starred in the WPCode Library directly in the plugin."
msgstr "Atualize para o WPCode PRO hoje mesmo e veja os snippets que você destacou na Biblioteca do WPCode diretamente no plugin."

#: includes/admin/pages/class-wpcode-admin-page-library.php:215
msgid "Upgrade to PRO and Unlock \"My Favorites\""
msgstr "Atualize para a versão PRO e acesse os \"Meus favoritos\""

#: includes/admin/pages/class-wpcode-admin-page-settings.php:206
msgid "WPCode Library Connection"
msgstr "Conexão da Biblioteca do WPCode"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:248
msgid "Connect to the WPCode Library"
msgstr "Conectar-se à Biblioteca do WPCode"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:251
msgid "Disconnect from the WPCode Library"
msgstr "Desconectar-se da Biblioteca do WPCode"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1261
msgid "Save to Library"
msgstr "Salvar na Biblioteca"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1749
msgid "Save to Library is a Pro Feature"
msgstr "Salvar na Biblioteca é um recurso da versão Pro"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1750
msgid "Upgrade to PRO today and save your private snippets to the WPCode library for easy access. You can also share your snippets with other users or load them on other sites."
msgstr "Atualize para a versão PRO hoje mesmo e salve seus snippets particulares na Biblioteca do WPCode para facilitar o acesso. Além disso, você pode compartilhar seus snippets com outros usuários ou carregá-los em outros sites."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1752
msgid "Custom Shortcode is a Pro Feature"
msgstr "Shortcode personalizado é um recurso da versão Pro"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1753
msgid "Upgrade today to use a custom shortcode and nerver worry about changing snippet ids again, even when importing your snippets to another site. You'll also get access to a private library that makes setting up new sites a lot easier."
msgstr "Atualize hoje para usar um shortcode personalizado e não se preocupe mais em alterar as IDs de snippets, mesmo ao importar seus snippets para outro site. Você também terá acesso a uma biblioteca privada que facilita a configuração de novos sites."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1887
#: includes/class-wpcode-admin-bar-info.php:289
msgid "Custom Shortcode"
msgstr "Shortcode personalizado"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1890
msgid "Shortcode name"
msgstr "Nome do shortcode"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1895
msgid "Use this field to define a custom shortcode name instead of the id-based one."
msgstr "Use este campo para definir um nome de shortcode personalizado no lugar do nome baseado em ID."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2210
msgid "As you make changes to your snippet and save, you will get a list of previous versions with all the changes made in each revision. You can compare revisions to the current version or see changes as they have been saved by going through each revision. Any of the revisions can then be restored as needed."
msgstr "Ao fazer alterações em seu snippet e salvá-lo, você obterá uma lista de versões anteriores com todas as alterações feitas em cada revisão. Você pode comparar as revisões com a versão atual ou ver as alterações conforme foram salvas acessando cada revisão. Todas as revisões podem ser restauradas, se necessário."

#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:351
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2215
msgid "Code Revisions"
msgstr "Revisões de código"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2217
msgid "Easily switch back to a previous version of your snippet."
msgstr "Volte facilmente para uma versão anterior de seu snippet."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2303
msgid "Upgrade to WPCode Pro today and start tracking revisions and see exactly who, when and which changes were made to your snippet."
msgstr "Atualize para a versão WPCode Pro hoje mesmo e comece a monitorar as revisões e veja exatamente quem, quando e quais alterações foram feitas em seu snippet."

#: includes/admin/pages/class-wpcode-admin-page.php:763
msgid "Connect to library to unlock (Free)"
msgstr "Conecte-se à biblioteca para acessar (gratuito)"

#. Translators: time since the revision has been updated.
#: includes/admin/pages/trait-wpcode-revisions-display.php:106
msgid "Updated %s ago"
msgstr "Atualizado %s atrás"

#. Translators: The placeholder gets replaced with the extra number of
#. revisions available.
#: includes/admin/pages/trait-wpcode-revisions-display.php:129
msgid "%d Other Revisions"
msgstr "Outras revisões %d"

#: includes/class-wpcode-library-auth.php:131
msgid "Authentication successfully completed"
msgstr "Autenticação concluída com sucesso"

#: includes/class-wpcode-library-auth.php:132
msgid "Reloading page, please wait."
msgstr "Recarregando a página, aguarde."

#: includes/class-wpcode-snippet-execute.php:151
msgid "CSS Snippet"
msgstr "Snippet CSS"

#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:64
msgid "Choose the snippets you want to run on this page. Please note: only active snippets will be executed."
msgstr "Escolha os snippets que deseja executar nesta página. Observação: somente os snippets ativos serão executados."

#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:69
msgid "Select snippets"
msgstr "Selecionar snippets"

#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:75
msgid "Search snippets"
msgstr "Pesquisar snippets"

#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:81
msgid "Load more snippets"
msgstr "Carregar mais snippets"

#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:88
msgid "+ Choose Snippet"
msgstr "+ Escolher snippet"

#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:140
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:283
#: includes/lite/class-wpcode-admin-bar-info-lite.php:91
msgid "Page Scripts is a Pro Feature"
msgstr "Scripts de página são um recurso da versão Pro"

#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:146
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:289
#: includes/lite/class-wpcode-admin-bar-info-lite.php:93
msgid "Upgrade to Pro and Unlock Page Scripts"
msgstr "Atualize para a versão Pro e acesse scripts de página"

#. Translators: placeholder for the name of the section (header or footer).
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:171
msgid "Add scripts below to the %s section of this page."
msgstr "Adicione os scripts abaixo à seção %s desta página."

#. Translators: placeholder for the name of the section (header or footer).
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:182
msgid "Disable global %s scripts on this page"
msgstr "Desativar scripts globais de %s nesta página"

#. Translators: placeholder for the name of the section (header or footer).
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:193
msgid "%s - any device type"
msgstr "%s - qualquer tipo de dispositivo"

#. Translators: %1$s and %2$s add a link to the settings page. %3$s and %4$s
#. make the text bold. %6$s is replaced with the number of snippets and %5$s
#. adds a "new" icon.
#: includes/lite/admin/notices.php:42
msgid "%5$s%1$sConnect to the WPCode Library%2$s to get access to %3$s%6$s FREE snippets%4$s!"
msgstr "%5$s%1$sConecte-se à Biblioteca do WPCode%2$s para obter acesso a %3$s%6$s snippets GRATUITOS%4$s!"

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:69
msgid "Before the Purchase Button"
msgstr "Antes do botão de compra"

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:73
msgid "After the Purchase Button"
msgstr "Após o botão de compra"

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:77
msgid "Before the Single Download"
msgstr "Antes do download único"

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:81
msgid "After the Single Download"
msgstr "Após o download único"

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:85
#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:94
msgid "Before the Cart"
msgstr "Antes do carrinho"

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:89
#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:98
msgid "After the Cart"
msgstr "Após o carrinho"

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:93
msgid "Before the Checkout Cart"
msgstr "Antes do carrinho de finalização de compra"

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:97
msgid "After the Checkout Cart"
msgstr "Após o carrinho de finalização de compra"

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:101
#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:102
msgid "Before the Checkout Form"
msgstr "Antes do formulário de finalização de compra"

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:105
#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:106
msgid "After the Checkout Form"
msgstr "Após o formulário de finalização de compra"

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:117
msgid "Easy Digital Downloads Locations are a PRO feature"
msgstr "Os locais Easy Digital Downloads são um recurso da versão Pro"

#: includes/lite/auto-insert/class-wpcode-auto-insert-edd.php:118
#: includes/lite/auto-insert/class-wpcode-auto-insert-memberpress.php:110
#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:131
msgid "Upgrade to PRO today and get access to advanced eCommerce auto-insert locations and conditional logic rules for your needs."
msgstr "Atualize para a versão Pro hoje mesmo e tenha acesso a locais avançados de inserção automática de comércio eletrônico e regras de lógica condicional adequadas às suas necessidades."

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:70
msgid "Before the List of Products"
msgstr "Antes da lista de produtos"

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:74
msgid "After the List of Products"
msgstr "Após a lista de produtos"

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:78
msgid "Before the Single Product"
msgstr "Antes do produto único"

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:82
msgid "After the Single Product"
msgstr "Após o produto único"

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:86
msgid "Before the Single Product Summary"
msgstr "Antes do resumo do produto único"

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:90
msgid "After the Single Product Summary"
msgstr "Após o resumo do produto único"

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:110
msgid "Before Checkout Payment Button"
msgstr "Antes do botão de pagamento da finalização da compra"

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:114
msgid "After Checkout Payment Button"
msgstr "Após o botão de pagamento da finalização da compra"

#: includes/lite/auto-insert/class-wpcode-auto-insert-woocommerce.php:130
msgid "WooCommerce Locations are a PRO feature"
msgstr "Os locais WooCommerce são um recurso da versão Pro"

#: includes/lite/conditional-logic/class-wpcode-conditional-edd.php:48
msgid "EDD Page"
msgstr "Página EDD"

#: includes/lite/conditional-logic/class-wpcode-conditional-edd.php:52
msgid "Easy Digital Downloads Page Rules is a Pro Feature"
msgstr "As regras da página Easy Digital Downloads são um recurso da versão Pro"

#: includes/lite/conditional-logic/class-wpcode-conditional-edd.php:53
msgid "Get access to advanced conditional logic rules for Easy Digital Downloads by upgrading to PRO today."
msgstr "Obtenha acesso a regras avançadas de lógica condicional para o Easy Digital Downloads atualizando para a versão Pro hoje mesmo."

#: includes/lite/conditional-logic/class-wpcode-conditional-edd.php:58
#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:58
msgid "Checkout Page"
msgstr "Página de confirmação"

#: includes/lite/conditional-logic/class-wpcode-conditional-edd.php:68
msgid "Single Download Page"
msgstr "Página de download único"

#: includes/lite/conditional-logic/class-wpcode-conditional-edd.php:73
msgid "Download Category Page"
msgstr "Página de categoria de download"

#: includes/lite/conditional-logic/class-wpcode-conditional-edd.php:78
msgid "Download Tag Page"
msgstr "Página de tag de download"

#: includes/lite/conditional-logic/class-wpcode-conditional-memberpress.php:63
#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:63
msgid "Thank You Page"
msgstr "Página de agradecimento"

#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:48
msgid "WooCommerce Page"
msgstr "Página WooCommerce"

#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:52
msgid "WooCommerce Page Rules is a Pro Feature"
msgstr "As regras da página WooCommerce são um recurso da versão Pro"

#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:53
#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:106
msgid "Get access to advanced conditional logic rules for WooCommerce by upgrading to PRO today."
msgstr "Obtenha acesso a regras avançadas de lógica condicional para o WooCommerce atualizando para a versão Pro hoje mesmo."

#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:68
msgid "Cart Page"
msgstr "Página do carrinho"

#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:73
msgid "Single Product Page"
msgstr "Página de produto único"

#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:78
msgid "Shop Page"
msgstr "Página de compra"

#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:83
msgid "Product Category Page"
msgstr "Página de categoria de produto"

#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:88
msgid "Product Tag Page"
msgstr "Página de tag do produto"

#: includes/lite/conditional-logic/class-wpcode-conditional-woocommerce.php:93
msgid "My Account Page"
msgstr "Página Minha conta"

#: includes/class-wpcode-smart-tags.php:193
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:202
msgid "Show Smart Tags"
msgstr "Mostrar tags inteligentes"

#: includes/admin/pages/trait-wpcode-my-library-markup.php:231
msgid "Upgrade to WPCode PRO today and save your snippets in your private library directly from the plugin and import them with 1-click on other sites."
msgstr "Atualize para o WPCode PRO hoje mesmo e salve seus snippets em sua biblioteca particular diretamente do plugin e importe-os com um clique em outros sites."

#. Plugin URI of the plugin
#. Author URI of the plugin
#: ihaf.php
msgid "https://www.wpcode.com/"
msgstr "https://www.wpcode.com/"

#. Description of the plugin
#: ihaf.php
msgid "Easily add code snippets in WordPress. Insert scripts to the header and footer, add PHP code snippets with conditional logic, insert ads pixel, custom content, and more."
msgstr "Adicione facilmente snippets de código no WordPress. Insira scripts no cabeçalho e no rodapé, adicione snippets de código PHP com lógica condicional, insira pixels de anúncios, conteúdo personalizado e muito mais."

#. Author of the plugin
#: ihaf.php
msgid "WPCode"
msgstr "WPCode"

#: includes/admin/admin-ajax-handlers.php:41
#: includes/class-wpcode-snippet.php:484
msgid "You are not allowed to change snippet status, please contact your webmaster."
msgstr "Você não tem permissão para alterar o status do snippet. Entre em contato com o seu webmaster."

#. Translators: %2$s is the action that they were trying to perform, either
#. activated or deactivated. %1$s is the error message why the action failed.
#: includes/admin/admin-ajax-handlers.php:55
msgid "Snippet not %2$s, the following error was encountered: %1$s"
msgstr "Snippet não %2$s, o seguinte erro foi encontrado: %1$s"

#: includes/admin/admin-ajax-handlers.php:57
msgctxt "Snippet status change"
msgid "activated"
msgstr "ativado"

#: includes/admin/admin-ajax-handlers.php:57
msgctxt "Snippet status change"
msgid "deactivated"
msgstr "desativado"

#. Translators: this an auto-generated title for when a snippet is saved from
#. the generator.
#: includes/admin/admin-ajax-handlers.php:226
msgid "Generated Snippet %s"
msgstr "Snippet gerado %s"

#: includes/admin/admin-ajax-handlers.php:281
msgid "Success! Your server can make SSL connections."
msgstr "Sucesso! Seu servidor pode fazer conexões SSL."

#: includes/admin/admin-ajax-handlers.php:288
msgid "There was an error and the connection failed. Please contact your web host with the technical details below."
msgstr "Houve um erro e a conexão falhou. Entre em contato com seu servidor web com os detalhes técnicos abaixo."

#: includes/admin/class-wpcode-admin-page-loader.php:139
#: includes/admin/class-wpcode-admin-page-loader.php:140
#: includes/admin/class-wpcode-admin-page-loader.php:197
#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:32
#: includes/class-wpcode-admin-bar-info.php:226
msgid "Code Snippets"
msgstr "Snippets de código"

#. Translators: Placeholder for the category name.
#: includes/admin/class-wpcode-docs.php:164
msgid "View All %s Docs"
msgstr "Visualizar todos os %s documentos"

#: includes/admin/class-wpcode-metabox-snippets.php:56
#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:167
#: includes/generator/class-wpcode-generator-script.php:193
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:23
msgid "Header"
msgstr "Cabeçalho"

#: includes/admin/class-wpcode-metabox-snippets.php:57
#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:171
#: includes/generator/class-wpcode-generator-script.php:192
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:36
msgid "Footer"
msgstr "Rodapé"

#: includes/admin/class-wpcode-metabox-snippets.php:63
#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:169
#: includes/lite/admin/class-wpcode-metabox-snippets-lite.php:49
msgid "Body"
msgstr "Corpo"

#: includes/admin/class-wpcode-metabox-snippets.php:67
#: includes/generator/class-wpcode-generator-post-type.php:384
msgid "Revisions"
msgstr "Revisões"

#. Translators: Human-Readable time to display.
#: includes/admin/class-wpcode-notifications.php:287
msgid "%1$s ago"
msgstr "%1$s atrás"

#: includes/admin/class-wpcode-review.php:130
#: includes/generator/class-wpcode-generator-post-status.php:170
#: includes/generator/class-wpcode-generator-post-status.php:195
#: includes/generator/class-wpcode-generator-post-type.php:399
#: includes/generator/class-wpcode-generator-post-type.php:551
#: includes/generator/class-wpcode-generator-post-type.php:757
#: includes/generator/class-wpcode-generator-query.php:136
#: includes/generator/class-wpcode-generator-query.php:379
#: includes/generator/class-wpcode-generator-query.php:436
#: includes/generator/class-wpcode-generator-script.php:211
#: includes/generator/class-wpcode-generator-script.php:228
#: includes/generator/class-wpcode-generator-style.php:212
#: includes/generator/class-wpcode-generator-style.php:229
#: includes/generator/class-wpcode-generator-taxonomy.php:428
#: includes/generator/class-wpcode-generator-taxonomy.php:505
msgid "Yes"
msgstr "Sim"

#: includes/admin/class-wpcode-upgrade-welcome.php:34
#: includes/admin/class-wpcode-upgrade-welcome.php:35
msgid "Welcome to WPCode"
msgstr "Boas-vindas ao WPCode"

#: includes/admin/class-wpcode-upgrade-welcome.php:135
msgid "Header & Footer Scripts"
msgstr "Scripts de cabeçalho e rodapé"

#: includes/admin/class-wpcode-upgrade-welcome.php:136
msgid "Effortlessly manage global headers & footers in a familiar interface."
msgstr "Gerencie cabeçalhos e rodapés globais tranquilamente em uma interface familiar."

#: includes/admin/class-wpcode-upgrade-welcome.php:140
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:38
msgid "Conversion Pixels"
msgstr "Pixels de conversão"

#: includes/admin/class-wpcode-upgrade-welcome.php:141
msgid "Easily target specific pages to track conversions reliably."
msgstr "Selecione facilmente páginas específicas para monitorar conversões de forma confiável."

#: includes/admin/class-wpcode-upgrade-welcome.php:145
#: includes/admin/pages/class-wpcode-admin-page-settings.php:694
msgid "PHP Snippets"
msgstr "Snippets PHP"

#: includes/admin/class-wpcode-upgrade-welcome.php:146
msgid "Add or remove features with full confidence that your site will not break."
msgstr "Adicione ou elimine recursos com plena confiança de que seu site não sofrerá interrupções."

#: includes/admin/class-wpcode-upgrade-welcome.php:150
msgid "Conditional Logic"
msgstr "Lógica condicional"

#: includes/admin/class-wpcode-upgrade-welcome.php:151
msgid "Create advanced conditional logic rules in an easy-to-use interface."
msgstr "Crie regras avançadas de lógica condicional em uma interface fácil de usar."

#: includes/admin/class-wpcode-upgrade-welcome.php:155
#: includes/admin/pages/class-wpcode-admin-page-settings.php:65
msgid "Error Handling"
msgstr "Gerenciamento de erros"

#: includes/admin/class-wpcode-upgrade-welcome.php:156
msgid "Unique error handling capabilities ensure you will not get locked out of your site."
msgstr "Os recursos exclusivos de gerenciamento de erros garantem que você não fique sem acesso ao seu site."

#: includes/admin/class-wpcode-upgrade-welcome.php:160
msgid "Snippets Library"
msgstr "Biblioteca de snippets"

#: includes/admin/class-wpcode-upgrade-welcome.php:161
msgid "One-click install from our extensive library of commonly-used snippets."
msgstr "Instalação com um clique a partir de nossa extensa biblioteca de snippets mais usados."

#. Translators: This simply adds the plugin name before the logo text.
#: includes/admin/class-wpcode-upgrade-welcome.php:166
msgid "%s logo"
msgstr "Logotipo do %s"

#: includes/admin/class-wpcode-upgrade-welcome.php:175
msgid "Insert Headers and Footers is now WPCode"
msgstr "O Insert Headers and Footers agora se chama WPCode"

#: includes/admin/class-wpcode-upgrade-welcome.php:176
msgid "When we first built Insert Headers and Footers over a decade ago, it was meant to do one very simple thing: add header and footer scripts to your site without editing theme files."
msgstr "Quando desenvolvemos o Insert Headers and Footers, há mais de uma década, o objetivo era criar um recurso muito simples: adicionar scripts de cabeçalho e rodapé ao seu site sem editar os arquivos do tema."

#: includes/admin/class-wpcode-upgrade-welcome.php:177
msgid "Since then, the plugin has grown to over 1 million active installs with an amazing user base. We have continued to receive feature requests to add more options like controlling which pages the scripts get loaded, allowing more types of code snippets, etc."
msgstr "Desde então, o plugin cresceu e atingiu mais de 1 milhão de instalações ativas, com uma base de usuários incríveis. Continuamos a receber solicitações de para adicionar mais opções de recursos, como o controle das páginas em que os scripts são carregados, permitindo mais tipos de snippets de código etc."

#: includes/admin/class-wpcode-upgrade-welcome.php:178
msgid "We listened to your feedback, and we are excited to present WPCode, the next generation of Insert Headers and Footers. We chose a new name because it was only fair considering the plugin is now 10x more powerful. Aside from adding global headers and footer snippets, you can also add multiple other types of code snippets, have granular control of where the snippets are output with conditional logic, and a whole lot more."
msgstr "Levamos em conta seus comentários e temos a satisfação de apresentar o WPCode, a próxima geração do Insert Headers and Footers. Escolhemos um novo nome porque era mais do que razoável, considerando que o plugin agora está dez vezes mais poderoso. Além de adicionar cabeçalhos globais e snippets de rodapé, você também pode incluir vários outros tipos de snippets de código, ter controle granular de onde os snippets são exibidos com lógica condicional e muito mais."

#. Translators: Placeholders 1 & 2 add a link to scroll down the page and 3 & 4
#. add a link to the suggestions form.
#: includes/admin/class-wpcode-upgrade-welcome.php:183
msgid "Please see the full list of features %1$sbelow%2$s and let us know what you'd like us to add next by %3$ssharing your feedback%4$s."
msgstr "Consulte a lista completa de recursos %1$sabaixo%2$s e conte-nos o que você gostaria que adicionássemos no futuro, %3$scompartilhando suas opiniões%4$s."

#. Translators: Placeholders add link to the details about settings.
#: includes/admin/class-wpcode-upgrade-welcome.php:198
msgid "For those of you who want to limit the functionality and switch back to the old interface, you can do so with one click. %1$sSee details here%2$s."
msgstr "Se você deseja limitar a funcionalidade e voltar à interface antiga, é só clicar. %1$sVeja os detalhes aqui%2$s."

#: includes/admin/class-wpcode-upgrade-welcome.php:207
msgid "We have an exciting roadmap ahead of us since you have shared tons of great ideas with us over the last several years. We truly appreciate your continued support and thank you for being an awesome user."
msgstr "Temos um caminho promissor pela frente, pois você compartilhou muitas ideias incríveis conosco nos últimos anos. Valorizamos muito o seu apoio contínuo e agradecemos por ser um usuário incrível."

#: includes/admin/class-wpcode-upgrade-welcome.php:208
msgid "We truly appreciate your continued support and thank you for using WPCode."
msgstr "Agradecemos muito seu apoio contínuo e por usar o WPCode."

#. Translators: Placeholder for "WPBeginner".
#: includes/admin/class-wpcode-upgrade-welcome.php:219
msgid "Founder of %s"
msgstr "Fundador da %s"

#: includes/admin/class-wpcode-upgrade-welcome.php:231
msgid "Lead Developer"
msgstr "Desenvolvedor-chefe"

#: includes/admin/class-wpcode-upgrade-welcome.php:237
msgid "What’s New in WPCode (Features & Highlights)"
msgstr "Novidades do WPCode (recursos e destaques)"

#: includes/admin/class-wpcode-upgrade-welcome.php:257
#: includes/admin/class-wpcode-upgrade-welcome.php:307
msgid "WPCode Generator Screen capture"
msgstr "Captura de tela do gerador do WPCode"

#: includes/admin/class-wpcode-upgrade-welcome.php:260
msgid "Snippet Generator"
msgstr "Gerador de snippets"

#: includes/admin/class-wpcode-upgrade-welcome.php:261
msgid "WPCode now includes a snippet generator directly in the plugin."
msgstr "O WPCode agora tem um gerador de snippet diretamente no plugin."

#: includes/admin/class-wpcode-upgrade-welcome.php:262
msgid "Using the built-in generators, you can quickly add custom post types, custom post statuses, widgets, menus, build complex WP Queries and much more."
msgstr "Usando os geradores integrados, você pode adicionar rapidamente tipos de posts personalizados, conhecer seus status, widgets, menus, criar consultas WP complexas e muito mais."

#: includes/admin/class-wpcode-upgrade-welcome.php:263
msgid "Simply fill in the fields in our guided wizard to generate a custom ready-to-use snippet for your website with 1 click. Try WordPress Snippet Generator."
msgstr "É só preencher os campos em nosso assistente guiado para gerar um snippet personalizado pronto para uso em seu site com um clique. Experimente o gerador de snippets do WordPress."

#: includes/admin/class-wpcode-upgrade-welcome.php:271
msgid "A lot of you requested the ability to save and re-use snippets on multiple websites."
msgstr "Muitos usuários pediram a possibilidade de salvar e reutilizar snippets em vários sites."

#. Translators: Placeholders add a link to the suggestions page.
#: includes/admin/class-wpcode-upgrade-welcome.php:289
msgid "If you have specific ideas or feature requests, please let us know by %1$sfilling out this form%2$s."
msgstr "Se você tiver sugestões específicas ou solicitações de recursos, entre em contato conosco %1$spreenchendo este formulário%2$s."

#: includes/admin/class-wpcode-upgrade-welcome.php:300
msgid "WPCode Cloud Screen capture"
msgstr "Captura de tela da nuvem do WPCode"

#: includes/admin/class-wpcode-upgrade-welcome.php:310
msgid "Not ready for the new interface?"
msgstr "Ainda não quer usar a nova interface?"

#: includes/admin/class-wpcode-upgrade-welcome.php:311
msgid "If you are not ready to switch to the new interface, or you simply want to use the plugin just for inserting headers and footers we've got you covered."
msgstr "Se não quiser mudar agora para a nova interface ou se preferir usar o plugin apenas para inserir cabeçalhos e rodapés, sem problemas, pois temos tudo o que você precisa."

#. Translators: Placeholders add a link to the settings page.
#: includes/admin/class-wpcode-upgrade-welcome.php:316
msgid "You can switch to the simple Headers & Footers interface at any time from the %1$ssettings page%2$s."
msgstr "Você pode mudar para a interface simples de Cabeçalhos e Rodapés a qualquer momento na página de configurações %1$s%2$s."

#: includes/admin/class-wpcode-upgrade-welcome.php:325
msgid "And if you change your mind later and want to give the full plugin a shot, you can always switch back with just 2 clicks using the option at the top of the page."
msgstr "E se mudar de ideia mais tarde e quiser usar o plugin completo, você sempre poderá voltar com apenas dois cliques usando a opção na parte superior da página."

#: includes/admin/class-wpcode-upgrade-welcome.php:330
msgid "Add Your First Snippet"
msgstr "Adicione seu primeiro snippet"

#: includes/admin/importers/class-wpcode-importer-code-snippets.php:94
#: includes/admin/importers/class-wpcode-importer-simple-custom-css-and-js.php:85
#: includes/admin/importers/class-wpcode-importer-simple-custom-css-and-js.php:97
#: includes/admin/importers/class-wpcode-importer-woody.php:91
msgid "Unknown Snippet"
msgstr "Snippet desconhecido"

#: includes/admin/importers/class-wpcode-importer-code-snippets.php:95
#: includes/admin/importers/class-wpcode-importer-simple-custom-css-and-js.php:86
#: includes/admin/importers/class-wpcode-importer-simple-custom-css-and-js.php:98
#: includes/admin/importers/class-wpcode-importer-woody.php:92
msgid "The snippet you are trying to import does not exist."
msgstr "O snippet que você está tentando importar não existe."

#: includes/admin/pages/class-wpcode-admin-page-click.php:209
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:547
#: includes/admin/pages/class-wpcode-code-snippets-table.php:707
msgid "Code Type"
msgstr "Tipo de código"

#: includes/admin/admin-ajax-handlers.php:114
#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:215
#: includes/admin/pages/class-wpcode-admin-page.php:941
msgid "Search Snippets"
msgstr "Pesquisar snippets"

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:252
#: includes/admin/pages/class-wpcode-admin-page.php:941
msgid "All Snippets"
msgstr "Todos os snippets"

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:340
#: includes/generator/class-wpcode-generator-post-type.php:231
msgid "Add New"
msgstr "Adicionar novo"

#. Translators: %d - Trashed snippets count.
#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:358
msgid "%d snippet was successfully moved to Trash."
msgid_plural "%d snippets were successfully moved to Trash."
msgstr[0] "O snippet %d foi movido com sucesso para a Lixeira."
msgstr[1] "Os snippets %d foram movidos com sucesso para Lixeira."

#. translators: %d - Restored from trash snippets count.
#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:366
msgid "%d snippet was successfully restored."
msgid_plural "%d snippet were successfully restored."
msgstr[0] "O snippet %d foi restaurado com sucesso."
msgstr[1] "Os snippets %d foram restaurados com sucesso."

#. translators: %d - Deleted snippets count.
#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:374
msgid "%d snippet was successfully permanently deleted."
msgid_plural "%d snippets were successfully permanently deleted."
msgstr[0] "%d snippet foi excluído permanentemente com sucesso."
msgstr[1] "Os snippets %d foram excluídos definitivamente com sucesso."

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:538
#: includes/admin/pages/class-wpcode-code-snippets-table.php:700
#: includes/generator/class-wpcode-generator-post-status.php:128
#: includes/generator/class-wpcode-generator-post-type.php:136
#: includes/generator/class-wpcode-generator-sidebar.php:130
msgid "Name"
msgstr "Nome"

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:539
#: includes/admin/pages/class-wpcode-code-snippets-table.php:703
msgid "Created"
msgstr "Criado"

#: includes/admin/pages/class-wpcode-admin-page-code-snippets.php:541
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1065
#: includes/admin/pages/class-wpcode-code-snippets-table.php:708
#: includes/generator/class-wpcode-generator-hooks.php:1360
msgid "Priority"
msgstr "Prioridade"

#: includes/admin/pages/class-wpcode-admin-page-file-editor.php:134
#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:233
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:176
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:253
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:317
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:367
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:427
#: includes/admin/pages/class-wpcode-admin-page-pixel.php:810
#: includes/admin/pages/class-wpcode-admin-page-settings.php:168
#: includes/admin/pages/class-wpcode-admin-page-settings.php:463
#: includes/admin/pages/class-wpcode-admin-page-settings.php:652
msgid "Save Changes"
msgstr "Salvar alterações"

#: includes/admin/pages/class-wpcode-admin-page-generator.php:73
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1069
msgid "Generator"
msgstr "Gerador"

#. Translators: gets replaced with the generator name.
#: includes/admin/pages/class-wpcode-admin-page-generator.php:114
msgid "%s Generator"
msgstr "Gerador %s"

#: includes/admin/pages/class-wpcode-admin-page-generator.php:140
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:416
msgid "All Generators"
msgstr "Todos os geradores"

#: includes/admin/pages/class-wpcode-admin-page-generator.php:155
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:432
msgid "Generate"
msgstr "Gerar"

#: includes/admin/pages/class-wpcode-admin-page-generator.php:207
msgid "Update code"
msgstr "Atualizar código"

#: includes/admin/pages/class-wpcode-admin-page-generator.php:217
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:510
#: includes/admin/pages/class-wpcode-admin-page.php:1120
msgid "Code Preview"
msgstr "Pré-visualizar código"

#: includes/admin/pages/class-wpcode-admin-page-generator.php:226
msgctxt "Copy to clipboard"
msgid "Copy Code"
msgstr "Copiar código"

#: includes/admin/pages/class-wpcode-admin-page-generator.php:258
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1695
#: includes/generator/class-wpcode-generator-type.php:212
msgid "Remove Row"
msgstr "Excluir linha"

#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:57
msgid "Header & Footer"
msgstr "Cabeçalho e rodapé"

#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:109
msgid "Headers & Footers mode activated. Use the toggle next to the Save Changes button to disable it at any time."
msgstr "Modo Cabeçalhos e rodapés ativado. Use o botão de alternância ao lado do botão Salvar alterações para desativar a qualquer momento."

#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:110
msgid "Headers & Footers mode deactivated, if you wish to switch back please use the option on the settings page."
msgstr "Modo Cabeçalhos e rodapés desativado; para voltar a usá-lo, use a opção na página de configurações."

#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:228
msgid "Global Header and Footer"
msgstr "Cabeçalho e rodapé globais"

#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:251
msgid "Simple mode"
msgstr "Modo simples"

#: includes/admin/pages/class-wpcode-admin-page-library.php:52
msgid "Library"
msgstr "Biblioteca"

#: includes/admin/pages/class-wpcode-admin-page-library.php:181
msgid "We encountered an error while trying to load the snippet data. Please try again."
msgstr "Encontramos um erro ao tentar carregar os dados do snippet. Tente novamente."

#: includes/admin/pages/class-wpcode-admin-page-settings.php:125
msgid "This allows you to disable all Code Snippets functionality and have a single \"Headers & Footers\" item under the settings menu."
msgstr "Permite que você desative todas as funcionalidades dos Snippets de código e tenha um único item \"Cabeçalhos e rodapés\" no menu de configurações."

#. Translators: Placeholders make the text bold.
#: includes/admin/pages/class-wpcode-admin-page-settings.php:130
msgid "%1$sNOTE:%2$s Please use this setting with caution. It will disable all custom snippets that you add using the new snippet management interface."
msgstr "%1$sOBSERVAÇÃO:%2$s Tenha cuidado ao usar essa configuração. Ela desativará todos os snippets personalizados que você adicionar usando a nova interface de gerenciamento de snippets."

#: includes/admin/pages/class-wpcode-admin-page-settings.php:141
msgid "Headers & Footers mode"
msgstr "Modo Cabeçalhos e rodapés"

#. Translators: This adds the name of the plugin "WPCode".
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:85
msgid "Add %s Snippet"
msgstr "Adicionar snippet %s"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:86
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:139
msgid "Add Snippet"
msgstr "Adicionar snippet"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:132
msgid "Save Snippet"
msgstr "Salvar snippet"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:133
msgid "Create Custom Snippet"
msgstr "Criar snippet personalizado"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:135
msgid "Edit Snippet"
msgstr "Editar snippet"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:136
msgid "Update"
msgstr "Atualizar"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:168
msgid "You cannot edit this snippet because it is in the Trash. Please restore it and try again."
msgstr "Você não pode editar este snippet porque ele está na Lixeira. Restaure-o e tente novamente."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:194
msgid "Snippet updated."
msgstr "Snippet atualizado."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:195
msgid "Snippet created & Saved."
msgstr "Snippet criado e salvo."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:196
msgid "We encountered an error activating your snippet, please check the syntax and try again."
msgstr "Encontramos um erro ao ativar seu snippet. Verifique a sintaxe e tente novamente."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:197
msgid "Sorry, you are not allowed to change the status of the snippet."
msgstr "Desculpe, mas você não tem permissão para alterar o status do snippet."

#. Translators: this changes the edit page title to show the snippet title.
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:278
msgid "Edit snippet \"%s\""
msgstr "Editar snippet \"%s\""

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:325
msgid "Add Your Custom Code (New Snippet)"
msgstr "Adicionar seu código personalizado (snippet novo)"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:493
msgid "Add title for snippet"
msgstr "Adicionar título ao snippet"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:599
msgid "Insertion"
msgstr "Inserção"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:605
msgid "Choose \"Auto Insert\" if you want the snippet to be automatically executed in one of the locations available. In \"Shortcode\" mode, the snippet will only be executed where the shortcode is inserted."
msgstr "Escolha \"Inserção automática\" se quiser que o snippet seja executado automaticamente em um dos locais disponíveis. No modo \"Shortcode\", o snippet só será executado onde o shortcode for inserido."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:608
msgid "Insert Method"
msgstr "Método de inserção"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:610
#: includes/admin/pages/class-wpcode-code-snippets-table.php:702
#: includes/lite/conditional-logic/class-wpcode-conditional-location.php:70
msgid "Location"
msgstr "Localização"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:614
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:674
#: includes/admin/pages/class-wpcode-code-snippets-table.php:706
#: includes/class-wpcode-admin-bar-info.php:283
msgid "Shortcode"
msgstr "Shortcode"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:633
msgid "Your snippet can be either automatically executed or only used as a shortcode. When using the \"Auto Insert\" option you can choose the location where your snippet will be placed automatically."
msgstr "Seu snippet pode ser executado automaticamente ou usado apenas como um shortcode. Ao usar a opção \"Inserção automática\", você pode escolher o local onde o snippet será colocado automaticamente."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:670
msgid "Auto&nbsp;Insert"
msgstr "Inserção&nbsp;automática"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:972
msgid "Please save the snippet first"
msgstr "Salvar o snippet primeiro"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1064
msgid "Tag"
msgstr "Tag"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1066
#: includes/admin/pages/class-wpcode-code-snippets-table.php:709
msgid "Note"
msgstr "Observação"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1073
msgid "Basic info"
msgstr "Informações básicas"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1087
msgid "Using conditional logic you can limit the pages where you want the snippet to be auto-inserted."
msgstr "Usando a lógica condicional, você pode limitar as páginas nas quais deseja que o snippet seja inserido automaticamente."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1091
msgid "Enable Logic"
msgstr "Ativar lógica"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1092
msgid "Conditions"
msgstr "Condições"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1097
msgid "Smart Conditional Logic"
msgstr "Lógica condicional inteligente"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1099
msgid "Enable logic to add rules and limit where your snippets are inserted automatically. Use multiple groups for different sets of rules."
msgstr "Ative a lógica para adicionar regras e limitar onde seus snippets são inseridos automaticamente. Use vários grupos para diferentes conjuntos de regras."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1215
#: includes/admin/pages/class-wpcode-admin-page-tools.php:369
#: includes/admin/pages/class-wpcode-code-snippets-table.php:1076
msgid "Active"
msgstr "Ativo"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1218
#: includes/admin/pages/class-wpcode-admin-page-tools.php:370
#: includes/admin/pages/class-wpcode-code-snippets-table.php:1079
msgid "Inactive"
msgstr "Inativo"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1477
msgid "+ Add new group"
msgstr "+ Adicionar novo grupo"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1494
msgid "Show"
msgstr "Mostrar"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1495
msgid "Hide"
msgstr "Ocultar"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1625
msgctxt "Conditional logic add another \"and\" rules row."
msgid "AND"
msgstr "E"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1728
msgctxt "Conditional logic \"or\" another rule"
msgid "OR"
msgstr "OU"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:2393
#: includes/class-wpcode-snippet-execute.php:615
msgid "Error message:"
msgstr "Mensagem de erro:"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:59
msgid "Tools"
msgstr "Ferramentas"

#. Translators: Adds a link to the snippets list in the admin.
#: includes/admin/pages/class-wpcode-admin-page-tools.php:94
msgid "Import was successfully finished. You can go and check %1$syour snippets%2$s."
msgstr "A importação foi concluída com sucesso. Você pode conferir %1$seus snippets%2$s."

#: includes/admin/pages/class-wpcode-admin-page-tools.php:140
msgid "WPCode Snippet Import"
msgstr "Importação de snippet do WPCode"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:141
msgid "Select a WPCode export file"
msgstr "Selecione um arquivo de exportação do WPCode"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:147
msgid "No file chosen"
msgstr "Nenhum arquivo selecionado"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:151
msgid "Choose a file&hellip;"
msgstr "Escolha um arquivo…"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:160
#: includes/admin/pages/class-wpcode-admin-page-tools.php:193
#: includes/admin/pages/class-wpcode-admin-page-tools.php:315
#: includes/admin/pages/class-wpcode-admin-page-tools.php:491
msgid "Import"
msgstr "Importar"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:166
msgid "Import from Other Code Plugins"
msgstr "Importar de outros plugins de código"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:167
msgid "WPCode makes it easy for you to switch by allowing you import your third-party snippet plugins with a single click."
msgstr "O WPCode facilita a troca e permite que você importe seus plugins de snippets de terceiros com um único clique."

#: includes/admin/pages/class-wpcode-admin-page-tools.php:170
msgid "Select previous Code plugin"
msgstr "Selecione o plugin de código anterior"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:176
msgid "Not Installed"
msgstr "Não instalado"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:178
msgid "Not Active"
msgstr "Não ativo"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:208
msgid "Export Code Snippets"
msgstr "Exportar snippets de código"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:212
#: includes/admin/pages/class-wpcode-code-snippets-table.php:712
#: includes/generator/class-wpcode-generator-post-status.php:111
msgid "Status"
msgstr "Status"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:217
msgid "Code type"
msgstr "Tipo de código"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:222
#: includes/admin/pages/class-wpcode-code-snippets-table.php:705
msgid "Tags"
msgstr "Tags"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:229
msgid "Export Snippets"
msgstr "Exportar snippets"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:243
msgid "System Information"
msgstr "Informações do sistema"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:248
msgid "Test SSL Connections"
msgstr "Testar Conexões SSL"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:249
msgid "Click the button below to verify your web server can perform SSL connections successfully."
msgstr "Clique no botão abaixo para verificar se seu servidor da Web pode realizar conexões SSL com sucesso."

#: includes/admin/pages/class-wpcode-admin-page-tools.php:251
msgid "Test Connection"
msgstr "Conexão de teste"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:274
msgid "Snippets import"
msgstr "Importação de snippets"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:278
msgid "Select the Snippets you would like to import."
msgstr "Selecione os snippets que deseja importar."

#: includes/admin/pages/class-wpcode-admin-page-tools.php:282
#: includes/admin/pages/class-wpcode-admin-page.php:1024
msgid "Available Snippets"
msgstr "Snippets disponíveis"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:287
#: includes/admin/pages/class-wpcode-code-snippets-table.php:512
msgid "No snippets found."
msgstr "Nenhum snippet encontrado."

#: includes/admin/pages/class-wpcode-admin-page-tools.php:301
msgid "Select All"
msgstr "Selecionar Todos"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:306
msgid "Snippets to Import"
msgstr "Snippets para importar"

#. Translators: These add markup to display which snippet out of the total from
#. the provider name.
#: includes/admin/pages/class-wpcode-admin-page-tools.php:327
msgid "Importing %1$s of %2$s snippets from %3$s."
msgstr "Importando %1$s de %2$s snippets de %3$s."

#. Translators: this adds the total snippets count that have been completed.
#: includes/admin/pages/class-wpcode-admin-page-tools.php:345
msgid "Congrats, the import process has finished! We have successfully imported %s snippets. You can review the results below."
msgstr "Parabéns, o processo de importação foi concluído! Importamos %s snippets com sucesso. Você pode ver os resultados abaixo."

#: includes/admin/pages/class-wpcode-admin-page-tools.php:368
#: includes/admin/pages/class-wpcode-code-snippets-table.php:1073
msgid "All"
msgstr "Todos"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:402
msgid "No snippets available to export."
msgstr "Nenhum snippet disponível para exportação."

#: includes/admin/pages/class-wpcode-admin-page-tools.php:427
msgid "No tags available."
msgstr "Nenhuma tag disponível."

#: includes/admin/pages/class-wpcode-admin-page-tools.php:492
msgid "Export"
msgstr "Exportar"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:493
msgid "System Info"
msgstr "Informações do sistema"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:494
msgid "Importer"
msgstr "Importador"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:606
msgid "Please upload a valid .json snippets export file."
msgstr "Carregue um arquivo de exportação de snippets.json válido."

#: includes/admin/pages/class-wpcode-admin-page-tools.php:607
#: includes/admin/pages/class-wpcode-admin-page-tools.php:620
msgid "Error"
msgstr "Erro"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:619
msgid "Snippets data cannot be imported."
msgstr "Os dados de snippets não podem ser importados."

#: includes/admin/pages/class-wpcode-admin-page-tools.php:680
#: includes/admin/pages/class-wpcode-code-snippets-table.php:407
msgid "Edit"
msgstr "Editar"

#: includes/admin/pages/class-wpcode-admin-page-tools.php:696
msgid "Testing"
msgstr "Testando"

#: includes/admin/pages/class-wpcode-admin-page.php:257
msgid "Search docs"
msgstr "Pesquisar documentos"

#: includes/admin/pages/class-wpcode-admin-page.php:261
#: includes/admin/pages/class-wpcode-code-snippets-table.php:1017
msgid "Clear"
msgstr "Limpar"

#: includes/admin/pages/class-wpcode-admin-page.php:268
msgid "No docs found"
msgstr "Nenhum documento encontrado"

#: includes/admin/pages/class-wpcode-admin-page.php:283
msgid "View Documentation"
msgstr "Visualizar documentação"

#: includes/admin/pages/class-wpcode-admin-page.php:284
msgid "Browse documentation, reference material, and tutorials for WPCode."
msgstr "Procurar documentação, material de referência e tutoriais para o WPCode."

#: includes/admin/pages/class-wpcode-admin-page.php:285
msgid "View All Documentation"
msgstr "Ver toda a documentação"

#: includes/admin/pages/class-wpcode-admin-page.php:289
msgid "Get Support"
msgstr "Obter suporte"

#: includes/admin/pages/class-wpcode-admin-page.php:290
msgid "Submit a ticket and our world class support team will be in touch soon."
msgstr "Envie um tíquete e nossa equipe de suporte de padrão internacional entrará em contato em breve."

#: includes/admin/pages/class-wpcode-admin-page.php:291
msgid "Submit a Support Ticket"
msgstr "Enviar uma solicitação de suporte"

#. Translators: Placeholder for the number of active notifications.
#: includes/admin/pages/class-wpcode-admin-page.php:303
msgid "New Notifications (%s)"
msgstr "Notificações novas (%s)"

#. Translators: Placeholder for the number of dismissed notifications.
#: includes/admin/pages/class-wpcode-admin-page.php:314
msgid "Notifications (%s)"
msgstr "Notificações (%s)"

#: includes/admin/pages/class-wpcode-admin-page.php:321
msgid "Dismissed Notifications"
msgstr "Notificações dispensadas"

#: includes/admin/pages/class-wpcode-admin-page.php:324
msgid "Active Notifications"
msgstr "Notificações Ativas"

#: includes/admin/pages/class-wpcode-admin-page.php:347
msgid "Dismiss all"
msgstr "Dispensar tudo"

#: includes/admin/pages/class-wpcode-admin-page.php:389
msgid "Dismiss"
msgstr "Dispensar"

#: includes/admin/pages/class-wpcode-admin-page.php:451
msgid "Help"
msgstr "Ajuda"

#: includes/admin/pages/class-wpcode-admin-page.php:726
msgid "Use snippet"
msgstr "Usar snippet"

#: includes/admin/pages/class-wpcode-admin-page.php:739
msgid "Edit snippet"
msgstr "Editar snippet"

#: includes/admin/pages/class-wpcode-admin-page.php:740
msgid "Used"
msgstr "Usado"

#: includes/admin/pages/class-wpcode-admin-page.php:947
msgid "We encountered a problem loading the Snippet Library items, please try again later."
msgstr "Encontramos um problema ao carregar os itens da Biblioteca de snippets. Tente novamente mais tarde."

#: includes/admin/pages/class-wpcode-admin-page.php:1116
msgid "Preview Snippet"
msgstr "Pré-visualizar snippet"

#. Translators: This is the format for displaying the date in the admin list,
#. [date] at [time].
#: includes/admin/pages/class-wpcode-code-snippets-table.php:145
#: includes/admin/pages/class-wpcode-code-snippets-table.php:154
msgid "%1$s at %2$s"
msgstr "%1$s em %2$s"

#. Translators: The tag by which to filter the list of snippets in the admin.
#: includes/admin/pages/class-wpcode-code-snippets-table.php:187
msgid "Filter snippets by tag: %s"
msgstr "Filtrar snippets por tag: %s"

#: includes/admin/pages/class-wpcode-code-snippets-table.php:325
#: includes/admin/pages/class-wpcode-code-snippets-table.php:406
msgid "Edit This Snippet"
msgstr "Editar este snippet"

#: includes/admin/pages/class-wpcode-code-snippets-table.php:379
msgid "Restore this snippet"
msgstr "Restaurar este snippet"

#: includes/admin/pages/class-wpcode-code-snippets-table.php:380
#: includes/admin/pages/class-wpcode-code-snippets-table.php:463
msgid "Restore"
msgstr "Restaurar"

#: includes/admin/pages/class-wpcode-code-snippets-table.php:396
msgid "Delete this snippet permanently"
msgstr "Excluir este snippet definitivamente"

#: includes/admin/pages/class-wpcode-code-snippets-table.php:397
#: includes/admin/pages/class-wpcode-code-snippets-table.php:464
msgid "Delete Permanently"
msgstr "Excluir permanentemente"

#: includes/admin/pages/class-wpcode-code-snippets-table.php:426
msgid "Move this snippet to trash"
msgstr "Mover este snippet para a lixeira"

#: includes/admin/pages/class-wpcode-code-snippets-table.php:427
#: includes/admin/pages/class-wpcode-code-snippets-table.php:468
#: includes/admin/pages/class-wpcode-code-snippets-table.php:1082
msgid "Trash"
msgstr "Lixeira"

#: includes/admin/pages/class-wpcode-code-snippets-table.php:699
#: includes/generator/class-wpcode-generator-query.php:298
msgid "ID"
msgstr "ID"

#: includes/admin/pages/class-wpcode-code-snippets-table.php:701
#: includes/conditional-logic/class-wpcode-conditional-page.php:152
#: includes/generator/class-wpcode-generator-post-type.php:378
#: includes/generator/class-wpcode-generator-query.php:225
#: includes/generator/class-wpcode-generator-query.php:299
msgid "Author"
msgstr "Autor"

#: includes/admin/pages/class-wpcode-code-snippets-table.php:1013
#: includes/generator/class-wpcode-generator-hooks.php:1337
msgid "Filter"
msgstr "Filtro"

#: includes/admin/pages/class-wpcode-code-snippets-table.php:1049
msgid "Filter by location"
msgstr "Filtrar por localização"

#: includes/admin/pages/class-wpcode-code-snippets-table.php:1051
msgid "All locations"
msgstr "Todos os locais"

#: includes/admin/pages/trait-wpcode-revisions-display.php:96
#: includes/generator/class-wpcode-generator-query.php:514
msgid "Compare"
msgstr "Comparar"

#: includes/auto-insert/class-wpcode-auto-insert-archive.php:50
msgid "Categories, Archives, Tags, Taxonomies"
msgstr "Categorias, arquivos, tags, taxonomias"

#: includes/auto-insert/class-wpcode-auto-insert-archive.php:61
msgid "Insert Before Excerpt"
msgstr "Inserir antes do resumo"

#: includes/auto-insert/class-wpcode-auto-insert-archive.php:65
msgid "Insert After Excerpt"
msgstr "Inserir após o resumo"

#: includes/auto-insert/class-wpcode-auto-insert-archive.php:69
msgid "Between Posts"
msgstr "Entre posts"

#: includes/auto-insert/class-wpcode-auto-insert-archive.php:73
msgid "Before Post"
msgstr "Antes do post"

#: includes/auto-insert/class-wpcode-auto-insert-archive.php:77
msgid "After Post"
msgstr "Depois do post"

#: includes/auto-insert/class-wpcode-auto-insert-everywhere.php:59
msgid "PHP Snippets Only"
msgstr "Somente snippets de PHP"

#: includes/auto-insert/class-wpcode-auto-insert-everywhere.php:70
msgid "Run Everywhere"
msgstr "Executar em qualquer lugar"

#: includes/auto-insert/class-wpcode-auto-insert-everywhere.php:78
msgid "Admin Only"
msgstr "Somente administrador"

#: includes/auto-insert/class-wpcode-auto-insert-single.php:71
msgid "Insert Before Post"
msgstr "Inserir antes do post"

#: includes/auto-insert/class-wpcode-auto-insert-single.php:75
msgid "Insert After Post"
msgstr "Inserir após post"

#: includes/auto-insert/class-wpcode-auto-insert-single.php:79
msgid "Insert Before Content"
msgstr "Inserir antes do conteúdo"

#: includes/auto-insert/class-wpcode-auto-insert-single.php:83
msgid "Insert After Content"
msgstr "Inserir após o conteúdo"

#: includes/auto-insert/class-wpcode-auto-insert-single.php:87
msgid "Insert Before Paragraph"
msgstr "Inserir antes do parágrafo"

#: includes/auto-insert/class-wpcode-auto-insert-single.php:91
msgid "Insert After Paragraph"
msgstr "Inserir após o parágrafo"

#: includes/auto-insert/class-wpcode-auto-insert-site-wide.php:60
msgid "Site Wide Header"
msgstr "Cabeçalho em todo o site"

#: includes/auto-insert/class-wpcode-auto-insert-site-wide.php:64
msgid "Site Wide Body"
msgstr "Corpo de todo o site"

#: includes/class-wpcode-generator.php:94
msgid "Admin"
msgstr "Administrador"

#: includes/class-wpcode-generator.php:95
#: includes/lite/auto-insert/class-wpcode-auto-insert-content.php:59
msgid "Content"
msgstr "Conteúdo"

#: includes/class-wpcode-generator.php:96
msgid "Core"
msgstr "Arquivos básicos"

#: includes/class-wpcode-generator.php:97
msgid "Design"
msgstr "Desenho"

#: includes/class-wpcode-generator.php:98
#: includes/generator/class-wpcode-generator-post-type.php:539
msgid "Query"
msgstr "Consulta"

#: includes/class-wpcode-install.php:218
msgid "Display a message after the 1st paragraph of posts"
msgstr "Exibir uma mensagem após o primeiro parágrafo dos posts"

#: includes/class-wpcode-install.php:230
msgid "Completely Disable Comments"
msgstr "Desativar completamente os comentários"

#: includes/class-wpcode-snippet-execute.php:131
msgid "HTML Snippet"
msgstr "Snippet de HTML"

#: includes/class-wpcode-snippet-execute.php:138
msgid "Text Snippet"
msgstr "Snippet de texto"

#: includes/class-wpcode-snippet-execute.php:164
msgid "JavaScript Snippet"
msgstr "Snippet de JavaScript"

#: includes/class-wpcode-snippet-execute.php:170
msgid "PHP Snippet"
msgstr "Snippet de PHP"

#: includes/class-wpcode-snippet-execute.php:176
msgid "Universal Snippet"
msgstr "Snippet Universal"

#: includes/class-wpcode-snippet-execute.php:565
msgid "Snippet has not been activated due to an error."
msgstr "O snippet não foi ativado devido a um erro."

#: includes/class-wpcode-snippet-execute.php:569
msgid "Please click the back button in the browser to update the snippet."
msgstr "Clique no botão voltar no navegador para atualizar o snippet."

#: includes/class-wpcode-snippet-execute.php:572
msgid "WPCode has detected an error in one of the snippets which has now been automatically deactivated."
msgstr "WPCode detectou um erro em um dos snippets que agora foi desativado automaticamente."

#: includes/class-wpcode-snippet.php:707
msgid "Untitled Snippet"
msgstr "Snippet sem título"

#: includes/conditional-logic/class-wpcode-conditional-page.php:37
msgid "Page"
msgstr "Página"

#: includes/conditional-logic/class-wpcode-conditional-page.php:48
msgid "Type of page"
msgstr "Tipo de página"

#: includes/conditional-logic/class-wpcode-conditional-page.php:53
msgid "Homepage"
msgstr "Página inicial"

#: includes/conditional-logic/class-wpcode-conditional-page.php:65
msgid "Search page"
msgstr "Pesquisar página"

#: includes/conditional-logic/class-wpcode-conditional-page.php:69
msgid "404 page"
msgstr "Página 404"

#: includes/conditional-logic/class-wpcode-conditional-page.php:73
msgid "Author page"
msgstr "Página do autor"

#: includes/conditional-logic/class-wpcode-conditional-page.php:84
#: includes/generator/class-wpcode-generator-query.php:204
msgid "Post type"
msgstr "Tipo de post"

#: includes/conditional-logic/class-wpcode-conditional-page.php:91
msgid "Referrer"
msgstr "Indicador"

#: includes/conditional-logic/class-wpcode-conditional-page.php:97
msgid "Taxonomy page"
msgstr "Página de taxonomia"

#: includes/conditional-logic/class-wpcode-conditional-page.php:104
msgid "Taxonomy term"
msgstr "Termo de taxonomia"

#: includes/conditional-logic/class-wpcode-conditional-page.php:113
msgid "Page URL"
msgstr "URL da página"

#: includes/conditional-logic/class-wpcode-conditional-user.php:37
msgid "User"
msgstr "Usuário"

#: includes/conditional-logic/class-wpcode-conditional-user.php:48
msgid "Logged-in"
msgstr "Logado"

#: includes/conditional-logic/class-wpcode-conditional-user.php:53
msgid "True"
msgstr "Verdadeiro"

#: includes/conditional-logic/class-wpcode-conditional-user.php:57
msgid "False"
msgstr "Falso"

#: includes/conditional-logic/class-wpcode-conditional-user.php:64
msgid "User Role"
msgstr "Função do usuário"

#: includes/generator/class-wpcode-generator-admin-bar.php:35
msgid "Admin Bar Menu"
msgstr "Menu da barra de administração"

#: includes/generator/class-wpcode-generator-admin-bar.php:36
msgid "Add a custom admin bar menu with links or content."
msgstr "Adicione um menu personalizado da barra de administração com links ou conteúdo."

#: includes/generator/class-wpcode-generator-admin-bar.php:47
#: includes/generator/class-wpcode-generator-contact-methods.php:47
#: includes/generator/class-wpcode-generator-cronjob.php:47
#: includes/generator/class-wpcode-generator-hooks.php:1279
#: includes/generator/class-wpcode-generator-menu.php:47
#: includes/generator/class-wpcode-generator-post-status.php:47
#: includes/generator/class-wpcode-generator-post-type.php:47
#: includes/generator/class-wpcode-generator-query.php:72
#: includes/generator/class-wpcode-generator-script.php:47
#: includes/generator/class-wpcode-generator-sidebar.php:47
#: includes/generator/class-wpcode-generator-style.php:47
#: includes/generator/class-wpcode-generator-taxonomy.php:47
#: includes/generator/class-wpcode-generator-widget.php:47
msgid "Info"
msgstr "Informação"

#: includes/generator/class-wpcode-generator-admin-bar.php:55
msgid "Generate a snippet to add a custom menu to the admin bar by filling in a simple form."
msgstr "Gere um snippet para adicionar um menu personalizado à barra de administração preenchendo um formulário simples."

#: includes/generator/class-wpcode-generator-admin-bar.php:63
#: includes/generator/class-wpcode-generator-contact-methods.php:63
#: includes/generator/class-wpcode-generator-cronjob.php:68
#: includes/generator/class-wpcode-generator-hooks.php:1304
#: includes/generator/class-wpcode-generator-menu.php:68
#: includes/generator/class-wpcode-generator-post-status.php:63
#: includes/generator/class-wpcode-generator-post-type.php:63
#: includes/generator/class-wpcode-generator-query.php:93
#: includes/generator/class-wpcode-generator-script.php:63
#: includes/generator/class-wpcode-generator-sidebar.php:68
#: includes/generator/class-wpcode-generator-style.php:63
#: includes/generator/class-wpcode-generator-taxonomy.php:63
#: includes/generator/class-wpcode-generator-widget.php:63
msgid "Usage"
msgstr "Uso"

#: includes/generator/class-wpcode-generator-admin-bar.php:65
#: includes/generator/class-wpcode-generator-contact-methods.php:65
msgid "Fill in the forms sections using the menu on the left."
msgstr "Preencha as seções dos formulários usando o menu à esquerda."

#: includes/generator/class-wpcode-generator-admin-bar.php:78
msgid "You could add a new admin bar menu for links you use often, a list of posts, a site you often visit when in the admin, etc."
msgstr "Você pode adicionar um novo menu na barra de administração para os links mais usados, uma lista de posts, um site visitado com frequência quando está no administrador etc."

#: includes/generator/class-wpcode-generator-admin-bar.php:90
#: includes/generator/class-wpcode-generator-contact-methods.php:90
#: includes/generator/class-wpcode-generator-cronjob.php:95
#: includes/generator/class-wpcode-generator-menu.php:95
#: includes/generator/class-wpcode-generator-post-status.php:90
#: includes/generator/class-wpcode-generator-post-type.php:90
#: includes/generator/class-wpcode-generator-script.php:95
#: includes/generator/class-wpcode-generator-sidebar.php:95
#: includes/generator/class-wpcode-generator-style.php:95
#: includes/generator/class-wpcode-generator-taxonomy.php:90
msgid "Function name"
msgstr "Nome da função"

#: includes/generator/class-wpcode-generator-admin-bar.php:66
#: includes/generator/class-wpcode-generator-contact-methods.php:66
#: includes/generator/class-wpcode-generator-cronjob.php:71
#: includes/generator/class-wpcode-generator-hooks.php:1307
#: includes/generator/class-wpcode-generator-menu.php:71
#: includes/generator/class-wpcode-generator-post-status.php:66
#: includes/generator/class-wpcode-generator-post-type.php:66
#: includes/generator/class-wpcode-generator-query.php:96
#: includes/generator/class-wpcode-generator-script.php:66
#: includes/generator/class-wpcode-generator-sidebar.php:71
#: includes/generator/class-wpcode-generator-style.php:66
#: includes/generator/class-wpcode-generator-taxonomy.php:66
#: includes/generator/class-wpcode-generator-widget.php:66
msgid "Click the \"Update Code\" button."
msgstr "Clique no botão \"Atualizar código\"."

#: includes/generator/class-wpcode-generator-admin-bar.php:67
#: includes/generator/class-wpcode-generator-contact-methods.php:67
#: includes/generator/class-wpcode-generator-cronjob.php:72
#: includes/generator/class-wpcode-generator-hooks.php:1308
#: includes/generator/class-wpcode-generator-menu.php:72
#: includes/generator/class-wpcode-generator-post-status.php:67
#: includes/generator/class-wpcode-generator-post-type.php:67
#: includes/generator/class-wpcode-generator-query.php:97
#: includes/generator/class-wpcode-generator-script.php:67
#: includes/generator/class-wpcode-generator-sidebar.php:72
#: includes/generator/class-wpcode-generator-style.php:67
#: includes/generator/class-wpcode-generator-taxonomy.php:67
#: includes/generator/class-wpcode-generator-widget.php:67
msgid "Click on \"Use Snippet\" to create a new snippet with the generated code."
msgstr "Clique em “Usar snippet” para criar um novo snippet com o código gerado."

#: includes/generator/class-wpcode-generator-admin-bar.php:68
#: includes/generator/class-wpcode-generator-contact-methods.php:68
#: includes/generator/class-wpcode-generator-cronjob.php:73
#: includes/generator/class-wpcode-generator-hooks.php:1309
#: includes/generator/class-wpcode-generator-menu.php:73
#: includes/generator/class-wpcode-generator-post-status.php:68
#: includes/generator/class-wpcode-generator-post-type.php:68
#: includes/generator/class-wpcode-generator-query.php:98
#: includes/generator/class-wpcode-generator-script.php:68
#: includes/generator/class-wpcode-generator-sidebar.php:73
#: includes/generator/class-wpcode-generator-style.php:68
#: includes/generator/class-wpcode-generator-taxonomy.php:68
#: includes/generator/class-wpcode-generator-widget.php:68
msgid "Activate and save the snippet and you're ready to go"
msgstr "Ative e salve o snippet e comece a trabalhar"

#: includes/generator/class-wpcode-generator-admin-bar.php:77
#: includes/generator/class-wpcode-generator-contact-methods.php:77
#: includes/generator/class-wpcode-generator-cronjob.php:82
#: includes/generator/class-wpcode-generator-hooks.php:1318
#: includes/generator/class-wpcode-generator-menu.php:82
#: includes/generator/class-wpcode-generator-post-status.php:77
#: includes/generator/class-wpcode-generator-post-type.php:77
#: includes/generator/class-wpcode-generator-query.php:107
#: includes/generator/class-wpcode-generator-script.php:77
#: includes/generator/class-wpcode-generator-sidebar.php:82
#: includes/generator/class-wpcode-generator-style.php:77
#: includes/generator/class-wpcode-generator-taxonomy.php:77
#: includes/generator/class-wpcode-generator-widget.php:77
msgid "Examples"
msgstr "Exemplos"

#: includes/generator/class-wpcode-generator-admin-bar.php:84
#: includes/generator/class-wpcode-generator-contact-methods.php:84
#: includes/generator/class-wpcode-generator-cronjob.php:89
#: includes/generator/class-wpcode-generator-menu.php:89
#: includes/generator/class-wpcode-generator-post-status.php:84
#: includes/generator/class-wpcode-generator-post-type.php:84
#: includes/generator/class-wpcode-generator-query.php:114
#: includes/generator/class-wpcode-generator-script.php:89
#: includes/generator/class-wpcode-generator-sidebar.php:89
#: includes/generator/class-wpcode-generator-style.php:89
#: includes/generator/class-wpcode-generator-taxonomy.php:84
#: includes/generator/class-wpcode-generator-widget.php:84
msgid "General"
msgstr "Geral"

#: includes/generator/class-wpcode-generator-admin-bar.php:91
#: includes/generator/class-wpcode-generator-cronjob.php:96
#: includes/generator/class-wpcode-generator-menu.php:96
#: includes/generator/class-wpcode-generator-post-status.php:91
#: includes/generator/class-wpcode-generator-post-type.php:91
#: includes/generator/class-wpcode-generator-script.php:96
#: includes/generator/class-wpcode-generator-sidebar.php:96
#: includes/generator/class-wpcode-generator-style.php:96
#: includes/generator/class-wpcode-generator-taxonomy.php:91
msgid "Make this unique to avoid conflicts with other snippets"
msgstr "Personalize-o para evitar conflitos com outros snippets"

#: includes/generator/class-wpcode-generator-admin-bar.php:101
msgid "Position"
msgstr "Posição"

#: includes/generator/class-wpcode-generator-admin-bar.php:102
msgid "Select where you want the menu item to be displayed on the admin bar."
msgstr "Selecione o local em que você deseja que o item de menu seja exibido na barra de administração."

#: includes/generator/class-wpcode-generator-admin-bar.php:106
msgid "Last item on the left"
msgstr "Último item à esquerda"

#: includes/generator/class-wpcode-generator-admin-bar.php:107
msgid "Before Site Name"
msgstr "Antes do nome do site"

#: includes/generator/class-wpcode-generator-admin-bar.php:108
msgid "After Site Name"
msgstr "Após o nome do site"

#: includes/generator/class-wpcode-generator-admin-bar.php:117
msgid "Menu Structure"
msgstr "Estrutura do menu"

#: includes/generator/class-wpcode-generator-admin-bar.php:123
msgid "Menu ID"
msgstr "ID do menu"

#: includes/generator/class-wpcode-generator-admin-bar.php:124
msgid "Unique menu id for the admin bar menu."
msgstr "ID de menu exclusivo para o menu da barra de administração."

#: includes/generator/class-wpcode-generator-admin-bar.php:131
msgid "Menu Title"
msgstr "Título do menu"

#: includes/generator/class-wpcode-generator-admin-bar.php:132
msgid "Text or HTML that will show up in the admin bar top-level item. Use HTML if you want to display an image."
msgstr "Texto ou HTML que será exibido no item de nível superior da barra de administração. Use HTML se quiser exibir uma imagem."

#: includes/generator/class-wpcode-generator-admin-bar.php:139
msgid "Menu item link"
msgstr "Link do item do menu"

#: includes/generator/class-wpcode-generator-admin-bar.php:140
msgid "If left empty, the top level menu item will not be a link, just text."
msgstr "Se deixado em branco, o item de menu de nível superior não será um link, apenas texto."

#: includes/generator/class-wpcode-generator-admin-bar.php:147
msgid "Menu item target"
msgstr "Destino do item de menu"

#: includes/generator/class-wpcode-generator-admin-bar.php:148
msgid "The menu item is a link use this field to set the target attribute. Use \"_blank\" to open the link in a new tab."
msgstr "Se o item de menu for um link, use esse campo para definir o atributo de destino. Use \"_blank\" para abrir o link em uma nova guia."

#: includes/generator/class-wpcode-generator-admin-bar.php:158
msgid "Submenu Item Title"
msgstr "Título do item do submenu"

#: includes/generator/class-wpcode-generator-admin-bar.php:159
msgid "Text or HTML for the submenu item."
msgstr "Texto ou HTML para o item do submenu."

#: includes/generator/class-wpcode-generator-admin-bar.php:167
msgid "Submenu item link"
msgstr "Link do item do submenu"

#: includes/generator/class-wpcode-generator-admin-bar.php:168
msgid "If left empty, this menu item will not be a link, just text."
msgstr "Se deixado em branco, este item de menu não será um link, mas um texto."

#: includes/generator/class-wpcode-generator-admin-bar.php:176
msgid "Submenu item target attribute"
msgstr "Atributo de destino do item do submenu"

#: includes/generator/class-wpcode-generator-admin-bar.php:177
msgid "If the menu item is a link use this for the target attribute. Use \"_blank\" to open in a new tab."
msgstr "Se o item de menu for um link, use-o para o atributo de destino. Use \"_blank\" para abrir em uma nova guia."

#: includes/generator/class-wpcode-generator-admin-bar.php:188
msgid "Add more submenu items"
msgstr "Adicione mais itens de submenu"

#: includes/generator/class-wpcode-generator-admin-bar.php:189
msgid "Use the \"Add submenu item\" button below to add multiple submenu items."
msgstr "Use o botão \"Adicionar item de submenu\" abaixo para adicionar vários itens de submenu."

#: includes/generator/class-wpcode-generator-admin-bar.php:193
msgid "Add submenu item"
msgstr "Adicionar item de submenu"

#: includes/generator/class-wpcode-generator-contact-methods.php:35
#: includes/generator/class-wpcode-generator-contact-methods.php:111
msgid "Contact Methods"
msgstr "Métodos de contato"

#: includes/generator/class-wpcode-generator-contact-methods.php:36
msgid "Add additional contact methods to WordPress user profiles."
msgstr "Adicione métodos de contato adicionais aos perfis de usuário do WordPress."

#: includes/generator/class-wpcode-generator-contact-methods.php:55
msgid "Use this generator to create a snippet which adds more contact methods to your WordPress users profiles."
msgstr "Use este gerador para criar um snippet que adiciona mais métodos de contato aos perfis de usuários do WordPress."

#: includes/generator/class-wpcode-generator-contact-methods.php:78
msgid "You can add extra fields for user profiles like their extended address, city, country, phone number, social media profiles (Facebook, Twitter, etc)."
msgstr "Você pode adicionar campos extras para perfis de usuários, como endereço completo, cidade, país, número de telefone, perfis de mídia social (Facebook, Twitter etc.)."

#: includes/generator/class-wpcode-generator-contact-methods.php:91
msgid "Make this unique to avoid conflicts with other snippets."
msgstr "Personalize-a para evitar conflitos com outros snippets."

#: includes/generator/class-wpcode-generator-contact-methods.php:101
#: includes/generator/class-wpcode-generator-cronjob.php:106
#: includes/generator/class-wpcode-generator-menu.php:106
#: includes/generator/class-wpcode-generator-post-status.php:101
#: includes/generator/class-wpcode-generator-post-type.php:101
#: includes/generator/class-wpcode-generator-sidebar.php:106
#: includes/generator/class-wpcode-generator-taxonomy.php:102
#: includes/generator/class-wpcode-generator-widget.php:113
msgid "Text Domain"
msgstr "Domínio de texto"

#: includes/generator/class-wpcode-generator-contact-methods.php:102
#: includes/generator/class-wpcode-generator-cronjob.php:107
#: includes/generator/class-wpcode-generator-menu.php:107
#: includes/generator/class-wpcode-generator-post-status.php:102
#: includes/generator/class-wpcode-generator-post-type.php:102
#: includes/generator/class-wpcode-generator-sidebar.php:107
#: includes/generator/class-wpcode-generator-taxonomy.php:103
msgid "Optional text domain for translations."
msgstr "Domínio de texto opcional para traduções."

#: includes/generator/class-wpcode-generator-contact-methods.php:117
msgid "Contact Method Slug"
msgstr "Slug de método de contato"

#: includes/generator/class-wpcode-generator-contact-methods.php:118
msgid "A lowercase with no spaces slug for usage in the code. For example: \"facebook\" or \"telephone\"."
msgstr "Um slug em letras minúsculas, sem espaços, para uso no código. Por exemplo: \"facebook\" ou \"telefone\"."

#: includes/generator/class-wpcode-generator-contact-methods.php:126
msgid "Contact Method Label"
msgstr "Rótulo do método de contato"

#: includes/generator/class-wpcode-generator-contact-methods.php:127
msgid "This will show up as a label next to the contact method field. For example: \"Facebook URL\" or \"Phone number\"."
msgstr "Isso será exibido como um rótulo ao lado do campo do método de contato. Por exemplo: \"URL do Facebook\" ou \"Número de telefone\"."

#: includes/generator/class-wpcode-generator-contact-methods.php:138
msgid "Add more contact methods"
msgstr "Adicione mais métodos de contato"

#: includes/generator/class-wpcode-generator-contact-methods.php:139
msgid "Use the \"Add contact method\" button below to add as many contact methods as you wish."
msgstr "Use o botão \"Adicionar método de contato\" abaixo para adicionar quantos métodos de contato desejar."

#: includes/generator/class-wpcode-generator-contact-methods.php:143
msgid "Add contact method"
msgstr "Adicionar método de contato"

#: includes/generator/class-wpcode-generator-cronjob.php:35
msgid "Schedule a Cron Job"
msgstr "Agendar um Cron Job"

#: includes/generator/class-wpcode-generator-cronjob.php:36
msgid "Generate a snippet to schedule a recurring event using the WordPress cron."
msgstr "Gere um snippet para agendar um evento recorrente usando o cron do WordPress."

#. Translators: Placeholders add links to the wordpress.org references.
#: includes/generator/class-wpcode-generator-cronjob.php:57
msgid "This generator makes it easy to generate a snippet that will schedule a recurring event using %1$swp_schedule_event%2$s."
msgstr "Este gerador facilita a geração de um snippet que agenda um evento recorrente usando %1$swp_schedule_event%2$s."

#: includes/generator/class-wpcode-generator-cronjob.php:70
#: includes/generator/class-wpcode-generator-hooks.php:1306
#: includes/generator/class-wpcode-generator-menu.php:70
#: includes/generator/class-wpcode-generator-post-status.php:65
#: includes/generator/class-wpcode-generator-post-type.php:65
#: includes/generator/class-wpcode-generator-query.php:95
#: includes/generator/class-wpcode-generator-script.php:65
#: includes/generator/class-wpcode-generator-sidebar.php:70
#: includes/generator/class-wpcode-generator-style.php:65
#: includes/generator/class-wpcode-generator-taxonomy.php:65
#: includes/generator/class-wpcode-generator-widget.php:65
msgid "Fill in the forms using the menu on the left."
msgstr "Preencha os formulários no menu à esquerda."

#: includes/generator/class-wpcode-generator-cronjob.php:83
msgid "You may want to run some code once every hour, day or week, for example you could use this to send an email with the number of published posts every week."
msgstr "Você pode executar um código a cada hora, dia ou semana; por exemplo, você pode enviar um e-mail com o número de posts publicados semanalmente."

#: includes/generator/class-wpcode-generator-cronjob.php:116
#: includes/lite/conditional-logic/class-wpcode-conditional-schedule.php:37
msgid "Schedule"
msgstr "Programar"

#: includes/generator/class-wpcode-generator-cronjob.php:122
msgid "Recurrence"
msgstr "Recorrência"

#: includes/generator/class-wpcode-generator-cronjob.php:123
msgid "Choose how often you want this event to run."
msgstr "Escolha com que frequência você deseja que esse evento seja realizado."

#: includes/generator/class-wpcode-generator-cronjob.php:127
msgid "Hourly"
msgstr "De hora em hora"

#: includes/generator/class-wpcode-generator-cronjob.php:128
msgid "Twice Daily"
msgstr "Duas vezes por dia"

#: includes/generator/class-wpcode-generator-cronjob.php:129
msgid "Daily"
msgstr "Diariamente"

#: includes/generator/class-wpcode-generator-cronjob.php:130
msgid "Custom"
msgstr "Personalizado"

#: includes/generator/class-wpcode-generator-cronjob.php:138
msgid "Custom Recurrence Name"
msgstr "Nome de recorrência personalizado"

#: includes/generator/class-wpcode-generator-cronjob.php:139
msgid "This is the recurrence name slug, lowercase and no space."
msgstr "Esse é o slug do nome da recorrência, em letras minúsculas e sem espaço."

#: includes/generator/class-wpcode-generator-cronjob.php:145
msgid "Custom Recurrence Label"
msgstr "Rótulo de recorrência personalizado"

#: includes/generator/class-wpcode-generator-cronjob.php:148
msgid "This label will be used in a list of cron events, for example."
msgstr "Esse rótulo será usado em uma lista de eventos cron, por exemplo."

#: includes/generator/class-wpcode-generator-cronjob.php:152
msgid "Custom Recurrence Interval"
msgstr "Intervalo de recorrência personalizado"

#: includes/generator/class-wpcode-generator-cronjob.php:155
msgid "The number of seconds of this interval."
msgstr "O número de segundos desse intervalo."

#: includes/generator/class-wpcode-generator-cronjob.php:161
#: includes/generator/class-wpcode-generator-hooks.php:1379
msgid "Code"
msgstr "Código"

#: includes/generator/class-wpcode-generator-cronjob.php:167
#: includes/generator/class-wpcode-generator-hooks.php:1342
msgid "Hook name"
msgstr "Nome do gancho"

#: includes/generator/class-wpcode-generator-cronjob.php:168
msgid "Unique name of your hook used to run when scheduled."
msgstr "Nome exclusivo do gancho usado para ser executado quando agendado."

#: includes/generator/class-wpcode-generator-cronjob.php:177
#: includes/generator/class-wpcode-generator-hooks.php:1385
msgid "PHP Code"
msgstr "Código PHP"

#: includes/generator/class-wpcode-generator-cronjob.php:178
msgid "Custom PHP code that will run when the event is triggered."
msgstr "Código PHP personalizado que será executado quando o evento for acionado."

#: includes/generator/class-wpcode-generator-hooks.php:35
msgid "Hooks"
msgstr "Ganchos"

#: includes/generator/class-wpcode-generator-hooks.php:36
msgid "Generate a snippet for an action or a filter using any available hook."
msgstr "Gere um snippet para uma ação ou filtro usando qualquer gancho disponível."

#: includes/generator/class-wpcode-generator-hooks.php:1319
msgid "You can use this to quickly get started with adding an action or filter."
msgstr "Você pode usá-lo para começar rapidamente a adicionar uma ação ou filtro."

#: includes/generator/class-wpcode-generator-hooks.php:1325
msgid "Hook Details"
msgstr "Detalhes do gancho"

#: includes/generator/class-wpcode-generator-hooks.php:1331
msgid "Hook type"
msgstr "Tipo de gancho"

#: includes/generator/class-wpcode-generator-hooks.php:1332
msgid "Can be either an action or a filter"
msgstr "Pode ser uma ação ou um filtro"

#: includes/generator/class-wpcode-generator-hooks.php:1336
msgid "Action"
msgstr "Ação"

#: includes/generator/class-wpcode-generator-hooks.php:1343
msgid "Input hook name or pick one from the suggested list as you type."
msgstr "Insira o nome do gancho ou escolha um da lista sugerida enquanto você digita."

#: includes/generator/class-wpcode-generator-hooks.php:1353
msgid "Callback function"
msgstr "Função de callback"

#: includes/generator/class-wpcode-generator-hooks.php:1354
msgid "Name of the function you want to add to this hook."
msgstr "Nome da função que você deseja adicionar a este gancho."

#: includes/generator/class-wpcode-generator-hooks.php:1361
msgid "Priority of this hook, by default 10."
msgstr "Por padrão, a prioridade desse gancho é 10."

#: includes/generator/class-wpcode-generator-hooks.php:1370
msgid "Arguments list"
msgstr "Lista de argumentos"

#: includes/generator/class-wpcode-generator-hooks.php:1371
msgid "Add comma-separated custom arguments that will be passed to the callback function depending on the action/filter."
msgstr "Adicione argumentos personalizados separados por vírgulas que serão repassados à função de callback, dependendo da ação/filtro."

#: includes/generator/class-wpcode-generator-hooks.php:1386
msgid "Custom PHP code you want to run in the function, you can also edit this after you create the snippet."
msgstr "Código PHP personalizado que você deseja executar na função; você também pode editá-lo após criar o snippet."

#: includes/generator/class-wpcode-generator-menu.php:35
msgid "Navigation Menu"
msgstr "Menu de navegação"

#: includes/generator/class-wpcode-generator-menu.php:36
msgid "Generate a snippet to register new navigation menus for your website."
msgstr "Gere um snippet para cadastrar novos menus de navegação para seu site."

#. Translators: Placeholders add links to the wordpress.org references.
#: includes/generator/class-wpcode-generator-menu.php:57
msgid "This generator makes it easy to add new navigation menus to your website using the \"register_nav_menus\" function."
msgstr "Esse gerador facilita a criação de novos menus de navegação no seu site usando a função \"register_nav_menus\"."

#: includes/generator/class-wpcode-generator-menu.php:83
msgid "You can add a new navigation menu for your website to display in a flyout menu that is not part of the theme, for example."
msgstr "Você pode adicionar um novo menu de navegação ao seu site para ser exibido em um menu suspenso que não faz parte do tema, por exemplo."

#: includes/generator/class-wpcode-generator-menu.php:116
msgid "Menus"
msgstr "Menus"

#: includes/generator/class-wpcode-generator-menu.php:122
msgid "Menu name"
msgstr "Nome do menu"

#: includes/generator/class-wpcode-generator-menu.php:123
msgid "This is the menu name slug, lowercase and no space."
msgstr "Esse é o slug do nome do menu, em letras minúsculas e sem espaço."

#: includes/generator/class-wpcode-generator-menu.php:130
msgid "Menu label"
msgstr "Rótulo do menu"

#: includes/generator/class-wpcode-generator-menu.php:141
msgid "Add another menu"
msgstr "Adicionar outro menu"

#: includes/generator/class-wpcode-generator-menu.php:142
msgid "Use the \"Add menu\" button below to add as many menu locations as you need."
msgstr "Use o botão \"Adicionar menu\" abaixo para incluir quantos locais de menu você precisar."

#: includes/generator/class-wpcode-generator-menu.php:146
msgid "Add Menu"
msgstr "Adicionar menu"

#: includes/generator/class-wpcode-generator-post-status.php:35
#: includes/generator/class-wpcode-generator-post-status.php:117
msgid "Post Status"
msgstr "Status do post"

#: includes/generator/class-wpcode-generator-post-status.php:36
msgid "Use this tool to generate a custom post status for your posts."
msgstr "Use esta ferramenta para gerar um status de post personalizado para seus posts."

#: includes/generator/class-wpcode-generator-post-status.php:55
msgid "Generate custom post statuses for your posts to improve the way you manage content."
msgstr "Gere status de post personalizado para seus posts e melhore a maneira como você gerencia o conteúdo."

#: includes/generator/class-wpcode-generator-post-status.php:78
msgid "You could add a new status called \"Pending Review\" that your authors can use before the content will be published"
msgstr "Você pode adicionar um novo status chamado \"Revisão pendente\" que seus autores podem usar antes que o conteúdo seja publicado"

#: includes/generator/class-wpcode-generator-post-status.php:118
msgid "Name of status used in the code, lowercase maximum 32 characters."
msgstr "Nome do status usado no código, em letras minúsculas, com no máximo 32 caracteres."

#: includes/generator/class-wpcode-generator-post-status.php:136
#: includes/generator/class-wpcode-generator-post-type.php:144
#: includes/generator/class-wpcode-generator-taxonomy.php:137
msgid "Name (Plural)"
msgstr "Nome (plural)"

#: includes/generator/class-wpcode-generator-post-status.php:146
#: includes/generator/class-wpcode-generator-post-type.php:441
#: includes/generator/class-wpcode-generator-taxonomy.php:309
msgid "Visibility"
msgstr "Visibilidade"

#: includes/generator/class-wpcode-generator-post-status.php:152
#: includes/generator/class-wpcode-generator-post-type.php:447
#: includes/generator/class-wpcode-generator-taxonomy.php:315
msgid "Public"
msgstr "Público"

#: includes/generator/class-wpcode-generator-post-status.php:153
#: includes/generator/class-wpcode-generator-post-status.php:167
msgid "Should the posts with this status be visible in the frontend?"
msgstr "Os posts com esse status devem ficar visíveis na interface?"

#: includes/generator/class-wpcode-generator-post-status.php:157
#: includes/generator/class-wpcode-generator-post-status.php:185
#: includes/generator/class-wpcode-generator-post-type.php:411
#: includes/generator/class-wpcode-generator-post-type.php:426
#: includes/generator/class-wpcode-generator-post-type.php:454
#: includes/generator/class-wpcode-generator-post-type.php:465
#: includes/generator/class-wpcode-generator-post-type.php:479
#: includes/generator/class-wpcode-generator-post-type.php:521
#: includes/generator/class-wpcode-generator-post-type.php:532
#: includes/generator/class-wpcode-generator-post-type.php:552
#: includes/generator/class-wpcode-generator-post-type.php:622
#: includes/generator/class-wpcode-generator-post-type.php:636
#: includes/generator/class-wpcode-generator-post-type.php:647
#: includes/generator/class-wpcode-generator-query.php:137
#: includes/generator/class-wpcode-generator-query.php:342
#: includes/generator/class-wpcode-generator-query.php:437
#: includes/generator/class-wpcode-generator-script.php:210
#: includes/generator/class-wpcode-generator-script.php:229
#: includes/generator/class-wpcode-generator-style.php:211
#: includes/generator/class-wpcode-generator-style.php:230
#: includes/generator/class-wpcode-generator-taxonomy.php:322
#: includes/generator/class-wpcode-generator-taxonomy.php:336
#: includes/generator/class-wpcode-generator-taxonomy.php:347
#: includes/generator/class-wpcode-generator-taxonomy.php:361
#: includes/generator/class-wpcode-generator-taxonomy.php:372
#: includes/generator/class-wpcode-generator-taxonomy.php:417
msgid "No"
msgstr "Não"

#: includes/generator/class-wpcode-generator-post-status.php:166
msgid "Exclude from search results"
msgstr "Excluir dos resultados da pesquisa"

#: includes/generator/class-wpcode-generator-post-status.php:180
msgid "Show in admin all list"
msgstr "Mostrar toda a lista no administrador"

#: includes/generator/class-wpcode-generator-post-status.php:181
msgid "Show statuses in the edit listing of the post."
msgstr "Mostrar status na lista de edição do post."

#: includes/generator/class-wpcode-generator-post-status.php:191
msgid "Show in admin status list"
msgstr "Mostrar na lista de status do administrador"

#: includes/generator/class-wpcode-generator-post-status.php:192
msgid "Show statuses list at the top of the edit listings. e.g. Published (12) Custom Status (2)"
msgstr "Mostrar a lista de status na parte superior das opções de edição. Por exemplo, Publicado (12) Status personalizado (2)"

#: includes/generator/class-wpcode-generator-post-type.php:35
#: includes/generator/class-wpcode-generator-post-type.php:111
msgid "Post Type"
msgstr "Tipo de post"

#: includes/generator/class-wpcode-generator-post-type.php:36
msgid "Use this tool to generate a custom post type for your website."
msgstr "Use esta ferramenta para gerar um tipo de post personalizado para o seu site."

#: includes/generator/class-wpcode-generator-post-type.php:55
msgid "Generate custom post types for your website using a snippet."
msgstr "Gere tipos de post personalizados para o seu site usando um snippet."

#: includes/generator/class-wpcode-generator-post-type.php:78
msgid "You can add custom post types for specific items that are not blog posts, for example, if your site is about music you can have post types for artists, albums or songs."
msgstr "Você pode adicionar tipos de post personalizados para itens específicos que não sejam posts de blog. Por exemplo, se o seu site é de música, você pode ter tipos de post para artistas, álbuns ou músicas."

#: includes/generator/class-wpcode-generator-post-type.php:117
msgid "Post Type Key"
msgstr "Chave de tipo de post"

#: includes/generator/class-wpcode-generator-post-type.php:118
msgid "Name of post type used in the code, lowercase maximum 20 characters."
msgstr "Nome do tipo de post usado no código, em letras minúsculas, com no máximo 20 caracteres."

#: includes/generator/class-wpcode-generator-post-type.php:125
#: includes/generator/class-wpcode-generator-sidebar.php:138
#: includes/generator/class-wpcode-generator-widget.php:150
#: includes/generator/class-wpcode-generator-widget.php:223
msgid "Description"
msgstr "Descrição"

#: includes/generator/class-wpcode-generator-post-type.php:126
msgid "A short description of the post type."
msgstr "Uma breve descrição do tipo de post."

#: includes/generator/class-wpcode-generator-post-type.php:155
msgid "Link To Taxonomies"
msgstr "Link para taxonomias"

#: includes/generator/class-wpcode-generator-post-type.php:156
msgid "Comma-separated list of Taxonomies (e.g. post_tag, category)"
msgstr "Lista de taxonomias separadas por vírgulas (por exemplo, post_tag, category)"

#: includes/generator/class-wpcode-generator-post-type.php:163
#: includes/generator/class-wpcode-generator-taxonomy.php:156
msgid "Hierarchical"
msgstr "Hierárquico"

#: includes/generator/class-wpcode-generator-post-type.php:164
msgid "Hierarchical post types can have parents/children."
msgstr "Os tipos de posts hierárquicos podem ter ascendentes/descendentes."

#: includes/generator/class-wpcode-generator-post-type.php:168
msgid "Yes, like pages"
msgstr "Sim, como páginas"

#: includes/generator/class-wpcode-generator-post-type.php:169
msgid "No, like posts"
msgstr "Não, como posts"

#: includes/generator/class-wpcode-generator-post-type.php:176
#: includes/generator/class-wpcode-generator-taxonomy.php:169
msgid "Labels"
msgstr "Etiquetas"

#: includes/generator/class-wpcode-generator-post-type.php:182
#: includes/generator/class-wpcode-generator-taxonomy.php:175
msgid "Menu Name"
msgstr "Nome do menu"

#: includes/generator/class-wpcode-generator-post-type.php:189
msgid "Admin Bar Name"
msgstr "Nome da barra de administração"

#: includes/generator/class-wpcode-generator-post-type.php:196
msgid "Archives"
msgstr "Arquivos"

#: includes/generator/class-wpcode-generator-post-type.php:203
msgid "Attributes"
msgstr "Atributos"

#: includes/generator/class-wpcode-generator-post-type.php:210
#: includes/generator/class-wpcode-generator-taxonomy.php:189
msgid "Parent Item"
msgstr "Item ascendente"

#: includes/generator/class-wpcode-generator-post-type.php:224
#: includes/generator/class-wpcode-generator-taxonomy.php:210
msgid "Add New Item"
msgstr "Adicionar novo item"

#: includes/generator/class-wpcode-generator-post-type.php:238
msgid "New Item"
msgstr "Novo item"

#: includes/generator/class-wpcode-generator-post-type.php:248
#: includes/generator/class-wpcode-generator-taxonomy.php:220
msgid "Edit Item"
msgstr "Editar item"

#: includes/generator/class-wpcode-generator-post-type.php:255
#: includes/generator/class-wpcode-generator-taxonomy.php:227
msgid "Update Item"
msgstr "Atualizar item"

#: includes/generator/class-wpcode-generator-post-type.php:262
#: includes/generator/class-wpcode-generator-taxonomy.php:234
msgid "View Item"
msgstr "Visualizar item"

#: includes/generator/class-wpcode-generator-post-type.php:269
msgid "View Items"
msgstr "Visualizar itens"

#: includes/generator/class-wpcode-generator-post-type.php:276
msgid "Search Item"
msgstr "Pesquisar item"

#: includes/generator/class-wpcode-generator-post-type.php:283
#: includes/generator/class-wpcode-generator-taxonomy.php:279
msgid "Not Found"
msgstr "Não encontrado"

#: includes/generator/class-wpcode-generator-post-type.php:290
msgid "Not Found in Trash"
msgstr "Não encontrado na lixeira"

#: includes/generator/class-wpcode-generator-post-type.php:297
msgid "Featured Image"
msgstr "Imagem de destaque"

#: includes/generator/class-wpcode-generator-post-type.php:304
msgid "Set featured image"
msgstr "Definir imagem em destaque"

#: includes/generator/class-wpcode-generator-post-type.php:314
msgid "Remove featured image"
msgstr "Excluir imagem em destaque"

#: includes/generator/class-wpcode-generator-post-type.php:321
msgid "Use as featured image"
msgstr "Usar como imagem em destaque"

#: includes/generator/class-wpcode-generator-post-type.php:328
msgid "Insert into item"
msgstr "Inserir no item"

#: includes/generator/class-wpcode-generator-post-type.php:335
msgid "Uploaded to this item"
msgstr "Carregado para este item"

#: includes/generator/class-wpcode-generator-post-type.php:342
#: includes/generator/class-wpcode-generator-taxonomy.php:293
msgid "Items list"
msgstr "Lista de itens"

#: includes/generator/class-wpcode-generator-post-type.php:349
#: includes/generator/class-wpcode-generator-taxonomy.php:300
msgid "Items list navigation"
msgstr "Navegação na lista de itens"

#: includes/generator/class-wpcode-generator-post-type.php:356
msgid "Filter items list"
msgstr "Filtrar lista de itens"

#: includes/generator/class-wpcode-generator-post-type.php:365
#: includes/generator/class-wpcode-generator-widget.php:239
msgid "Options"
msgstr "Opções"

#: includes/generator/class-wpcode-generator-post-type.php:371
msgid "Supports"
msgstr "é compatível"

#: includes/generator/class-wpcode-generator-post-type.php:372
msgid "Select which features this post type should support"
msgstr "Selecione com quais recursos esse tipo de post deve ser compatível"

#: includes/generator/class-wpcode-generator-post-type.php:376
#: includes/generator/class-wpcode-generator-query.php:300
msgid "Title"
msgstr "Título"

#: includes/generator/class-wpcode-generator-post-type.php:377
msgid "Content Editor"
msgstr "Editor de conteúdo"

#: includes/generator/class-wpcode-generator-post-type.php:379
msgid "Featured image"
msgstr "Imagem em destaque"

#: includes/generator/class-wpcode-generator-post-type.php:380
msgid "Excerpt"
msgstr "Resumo"

#: includes/generator/class-wpcode-generator-post-type.php:381
msgid "Trackbacks"
msgstr "Trackbacks"

#: includes/generator/class-wpcode-generator-post-type.php:382
#: includes/generator/class-wpcode-generator-query.php:487
msgid "Custom Fields"
msgstr "Campos personalizados"

#: includes/generator/class-wpcode-generator-post-type.php:383
msgid "Comments"
msgstr "Comentários"

#: includes/generator/class-wpcode-generator-post-type.php:385
msgid "Page Attributes"
msgstr "Atributos da página"

#: includes/generator/class-wpcode-generator-post-type.php:386
msgid "Post Formats"
msgstr "Formatos de post"

#: includes/generator/class-wpcode-generator-post-type.php:394
msgid "Exclude From Search"
msgstr "Excluir da pesquisa"

#: includes/generator/class-wpcode-generator-post-type.php:395
msgid "Exclude the posts of this post type from search results?"
msgstr "Excluir os posts desse tipo de post dos resultados de pesquisa?"

#: includes/generator/class-wpcode-generator-post-type.php:400
#: includes/generator/class-wpcode-generator-post-type.php:758
#: includes/generator/class-wpcode-generator-taxonomy.php:427
#: includes/generator/class-wpcode-generator-taxonomy.php:506
msgid "No - default"
msgstr "Não - padrão"

#: includes/generator/class-wpcode-generator-post-type.php:405
msgid "Enable Export"
msgstr "Habilitar exportação"

#: includes/generator/class-wpcode-generator-post-type.php:406
msgid "Allow exporting posts of this post type in Tools > Export."
msgstr "Permitir a exportação de posts desse tipo de post em Ferramentas > Exportar."

#: includes/generator/class-wpcode-generator-post-type.php:419
msgid "Enable Archives"
msgstr "Habilitar arquivos"

#: includes/generator/class-wpcode-generator-post-type.php:420
msgid "Enables archives for this post type, the post type key is used as default."
msgstr "Ativa os arquivos para esse tipo de post; a chave do tipo de post é usada como padrão."

#: includes/generator/class-wpcode-generator-post-type.php:410
#: includes/generator/class-wpcode-generator-post-type.php:424
#: includes/generator/class-wpcode-generator-post-type.php:453
#: includes/generator/class-wpcode-generator-post-type.php:464
#: includes/generator/class-wpcode-generator-post-type.php:478
#: includes/generator/class-wpcode-generator-post-type.php:520
#: includes/generator/class-wpcode-generator-post-type.php:531
#: includes/generator/class-wpcode-generator-post-type.php:621
#: includes/generator/class-wpcode-generator-post-type.php:635
#: includes/generator/class-wpcode-generator-post-type.php:646
#: includes/generator/class-wpcode-generator-taxonomy.php:321
#: includes/generator/class-wpcode-generator-taxonomy.php:335
#: includes/generator/class-wpcode-generator-taxonomy.php:346
#: includes/generator/class-wpcode-generator-taxonomy.php:360
#: includes/generator/class-wpcode-generator-taxonomy.php:371
#: includes/generator/class-wpcode-generator-taxonomy.php:416
msgid "Yes - default"
msgstr "Sim - padrão"

#: includes/generator/class-wpcode-generator-post-type.php:425
msgid "Yes - using custom slug"
msgstr "Sim - usando slug personalizado"

#: includes/generator/class-wpcode-generator-post-type.php:431
msgid "Custom Archive Slug"
msgstr "Slug de arquivo personalizado"

#: includes/generator/class-wpcode-generator-post-type.php:432
msgid "Custom archive slug (if selected above)."
msgstr "Slug de arquivo personalizado (se selecionado acima)."

#. Translators: Placeholders add a link to the wp.org documentation page.
#: includes/generator/class-wpcode-generator-post-type.php:449
msgid "Should this post type be %1$svisible to authors%2$s?"
msgstr "Este tipo de post deve ser %1$svisível para os autores%2$s?"

#: includes/generator/class-wpcode-generator-post-type.php:459
#: includes/generator/class-wpcode-generator-taxonomy.php:330
msgid "Show UI"
msgstr "Mostrar interface do usuário"

#: includes/generator/class-wpcode-generator-post-type.php:460
msgid "Should this post type be visible in the Admin?"
msgstr "Este tipo de post deve estar visível no Admin?"

#: includes/generator/class-wpcode-generator-post-type.php:473
msgid "Show in Menu?"
msgstr "Mostrar no menu?"

#: includes/generator/class-wpcode-generator-post-type.php:474
msgid "Should this post type be visible in the admin menu?"
msgstr "Este tipo de post deve estar visível no menu de administração?"

#: includes/generator/class-wpcode-generator-post-type.php:484
msgid "Menu position"
msgstr "Posição do menu"

#: includes/generator/class-wpcode-generator-post-type.php:485
msgid "Choose the admin menu position."
msgstr "Escolha a posição do menu de administração."

#: includes/generator/class-wpcode-generator-post-type.php:489
msgid "Below Posts (5)"
msgstr "Abaixo de Posts (5)"

#: includes/generator/class-wpcode-generator-post-type.php:490
msgid "Below Media (10)"
msgstr "Abaixo de Mídia (10)"

#: includes/generator/class-wpcode-generator-post-type.php:491
msgid "Below Pages (20)"
msgstr "Abaixo de Páginas (20)"

#: includes/generator/class-wpcode-generator-post-type.php:492
msgid "Below Comments (30)"
msgstr "Abaixo de Comentários (30)"

#: includes/generator/class-wpcode-generator-post-type.php:493
msgid "Below First Separator (60)"
msgstr "Abaixo de Primeiro separador (60)"

#: includes/generator/class-wpcode-generator-post-type.php:494
msgid "Below Plugins (65)"
msgstr "Abaixo de Plugins (65)"

#: includes/generator/class-wpcode-generator-post-type.php:495
msgid "Below Users (70)"
msgstr "Abaixo de Usuários (70)"

#: includes/generator/class-wpcode-generator-post-type.php:496
msgid "Below Tools (75)"
msgstr "Abaixo de Ferramentas (75)"

#: includes/generator/class-wpcode-generator-post-type.php:497
msgid "Below Settings (80)"
msgstr "Abaixo de Configurações (80)"

#: includes/generator/class-wpcode-generator-post-type.php:498
msgid "Below Second Separator (100)"
msgstr "Abaixo de Segundo separador (100)"

#: includes/generator/class-wpcode-generator-post-type.php:503
msgid "Menu Icon"
msgstr "Ícone do menu"

#. Translators: Placeholder adds a link to the dashicons page.
#: includes/generator/class-wpcode-generator-post-type.php:505
msgid "Icon used next to the post type label in the admin menu. Use either a %1$sdashicon%2$s name or a full URL to an image file."
msgstr "Ícone usado ao lado do rótulo do tipo de post no menu de administração. Use um nome %1$sdashicon%2$s ou um URL completo para um arquivo de imagem."

#: includes/generator/class-wpcode-generator-post-type.php:515
msgid "Show in Admin Bar?"
msgstr "Mostrar na barra de administração?"

#: includes/generator/class-wpcode-generator-post-type.php:516
msgid "Should this post type be visible in the admin bar?"
msgstr "Este tipo de post deve estar visível na barra de administração?"

#: includes/generator/class-wpcode-generator-post-type.php:526
msgid "Show in Navigation Menus?"
msgstr "Mostrar nos menus de navegação?"

#: includes/generator/class-wpcode-generator-post-type.php:527
msgid "Should this post type be available for use in menus (Appearance > Menus)?"
msgstr "Esse tipo de post deve estar disponível para ser usado em menus (Aparência > Menus)?"

#: includes/generator/class-wpcode-generator-post-type.php:545
msgid "Publicly Queryable"
msgstr "Consultável publicamente"

#. Translators: Placeholders add link to wp.org docs.
#: includes/generator/class-wpcode-generator-post-type.php:547
msgid "Enable frontend requests using the query variable. %1$sSee Documentation.%2$s"
msgstr "Ativar solicitações de interface usando a variável de consulta. %1$sAcesse a documentação.%2$s"

#: includes/generator/class-wpcode-generator-post-type.php:560
msgid "Query variable"
msgstr "Variável de consulta"

#. Translators: Placeholders add link to wp.org docs.
#: includes/generator/class-wpcode-generator-post-type.php:562
msgid "Key used for querying posts in the frontend. %1$sSee Documentation.%2$s"
msgstr "Chave usada para consultar posts na interface. %1$sConsulte a documentação.%2$s"

#: includes/generator/class-wpcode-generator-post-type.php:566
#: includes/generator/class-wpcode-generator-post-type.php:598
msgid "Default (post type key)"
msgstr "Padrão (chave de tipo de post )"

#: includes/generator/class-wpcode-generator-post-type.php:567
msgid "Custom variable"
msgstr "Variável personalizada"

#: includes/generator/class-wpcode-generator-post-type.php:575
msgid "Custom Query Variable"
msgstr "Variável de consulta personalizada"

#: includes/generator/class-wpcode-generator-post-type.php:586
#: includes/generator/class-wpcode-generator-taxonomy.php:379
msgid "Permalinks"
msgstr "Links permanentes"

#: includes/generator/class-wpcode-generator-post-type.php:592
msgid "Rewrite Permalinks"
msgstr "Reescrever links permanentes"

#. Translators: Placeholders add link to wp.org docs.
#: includes/generator/class-wpcode-generator-post-type.php:594
msgid "Use the default permalink structure, disable permalinks for this post type or use custom options. %1$sSee Documentation.%2$s"
msgstr "Use a estrutura de link permanente padrão, desative os links permanentes para esse tipo de post ou use opções personalizadas. %1$sVeja a documentação.%2$s"

#: includes/generator/class-wpcode-generator-post-type.php:599
#: includes/generator/class-wpcode-generator-taxonomy.php:391
msgid "Disable permalink rewrites"
msgstr "Desativar reescritas de link permanente"

#: includes/generator/class-wpcode-generator-post-type.php:600
#: includes/generator/class-wpcode-generator-taxonomy.php:392
msgid "Custom permalink structure"
msgstr "Estrutura de link permanente personalizado"

#: includes/generator/class-wpcode-generator-post-type.php:608
#: includes/generator/class-wpcode-generator-taxonomy.php:400
msgid "URL Slug"
msgstr "Slug de URL"

#: includes/generator/class-wpcode-generator-post-type.php:609
msgid "The slug used for this post types base. (for example: artist in www.example.com/artist/ )"
msgstr "O slug usado para essa base de tipos de post. (por exemplo: artista em www.example.com/artista/ )"

#: includes/generator/class-wpcode-generator-post-type.php:616
msgid "Use URL Slug?"
msgstr "Usar slug de URL?"

#: includes/generator/class-wpcode-generator-post-type.php:617
msgid "Use the post type name as URL slug base?"
msgstr "Usar o nome do tipo de post como base de slug de URL?"

#: includes/generator/class-wpcode-generator-post-type.php:630
msgid "Use pagination?"
msgstr "Usar paginação?"

#: includes/generator/class-wpcode-generator-post-type.php:631
msgid "Allow the post type to have pagination?"
msgstr "Permitir que o tipo de post tenha paginação?"

#: includes/generator/class-wpcode-generator-post-type.php:641
msgid "Use feeds?"
msgstr "Usar feeds?"

#: includes/generator/class-wpcode-generator-post-type.php:642
msgid "Allow the post type to have feeds?"
msgstr "Permitir que o tipo de post tenha feeds?"

#: includes/generator/class-wpcode-generator-post-type.php:654
#: includes/generator/class-wpcode-generator-post-type.php:660
#: includes/generator/class-wpcode-generator-taxonomy.php:435
#: includes/generator/class-wpcode-generator-taxonomy.php:441
msgid "Capabilities"
msgstr "Recursos"

#. Translators: Placeholders add link to wp.org docs.
#: includes/generator/class-wpcode-generator-post-type.php:662
msgid "User capabilities in relation to this post type. %1$sSee Documentation.%2$s"
msgstr "Recursos do usuário relacionados a esse tipo de post. %1$sVeja a documentação.%2$s"

#: includes/generator/class-wpcode-generator-post-type.php:666
#: includes/generator/class-wpcode-generator-taxonomy.php:447
msgid "Base capabilities - default"
msgstr "Recursos básicos – padrão"

#: includes/generator/class-wpcode-generator-post-type.php:667
#: includes/generator/class-wpcode-generator-post-type.php:686
#: includes/generator/class-wpcode-generator-taxonomy.php:448
#: includes/generator/class-wpcode-generator-taxonomy.php:453
msgid "Custom Capabilities"
msgstr "Recursos personalizados"

#: includes/generator/class-wpcode-generator-post-type.php:672
msgid "Base Capablities Type"
msgstr "Tipo de recursos básicos"

#: includes/generator/class-wpcode-generator-post-type.php:673
msgid "Use base capabilities from a core post type."
msgstr "Use recursos básicos de um tipo de post principal."

#: includes/generator/class-wpcode-generator-post-type.php:677
msgid "Posts"
msgstr "Posts"

#: includes/generator/class-wpcode-generator-post-type.php:678
msgid "Pages"
msgstr "Páginas"

#: includes/generator/class-wpcode-generator-post-type.php:691
msgid "Read Post"
msgstr "Ler post"

#: includes/generator/class-wpcode-generator-post-type.php:698
msgid "Read Private Posts"
msgstr "Ler posts privados"

#: includes/generator/class-wpcode-generator-post-type.php:705
msgid "Publish Posts"
msgstr "Publicar posts"

#: includes/generator/class-wpcode-generator-post-type.php:715
msgid "Delete Posts"
msgstr "Excluir posts"

#: includes/generator/class-wpcode-generator-post-type.php:722
msgid "Edit Post"
msgstr "Editar post"

#: includes/generator/class-wpcode-generator-post-type.php:729
msgid "Edit Posts"
msgstr "Editar posts"

#: includes/generator/class-wpcode-generator-post-type.php:736
msgid "Edit Others Posts"
msgstr "Editar outros posts"

#: includes/generator/class-wpcode-generator-post-type.php:745
#: includes/generator/class-wpcode-generator-taxonomy.php:494
msgid "Rest API"
msgstr "API Rest"

#: includes/generator/class-wpcode-generator-post-type.php:751
#: includes/generator/class-wpcode-generator-taxonomy.php:500
msgid "Show in Rest API?"
msgstr "Mostrar na API Rest?"

#. Translators: Placeholders add link to wp.org docs.
#: includes/generator/class-wpcode-generator-post-type.php:753
msgid "Add the post type to the WordPress wp-json API. %1$sSee Documentation.%2$s"
msgstr "Adicione o tipo de post à API wp-json do WordPress. %1$sConsulte a documentação.%2$s"

#: includes/generator/class-wpcode-generator-post-type.php:766
#: includes/generator/class-wpcode-generator-taxonomy.php:514
msgid "Rest Base"
msgstr "Base Rest"

#. Translators: Placeholders add link to wp.org docs.
#: includes/generator/class-wpcode-generator-post-type.php:768
msgid "The base slug that this post type will use in the REST API. %1$sSee Documentation.%2$s"
msgstr "O slug base que esse tipo de post usará na API Rest. %1$sConsulte a documentação.%2$s"

#: includes/generator/class-wpcode-generator-post-type.php:777
#: includes/generator/class-wpcode-generator-taxonomy.php:524
msgid "Rest Controller Class"
msgstr "Classe de controlador Rest"

#. Translators: Placeholders add link to wp.org docs.
#: includes/generator/class-wpcode-generator-post-type.php:779
msgid "The name of a custom Rest Controller class instead of WP_REST_Posts_Controller. %1$sSee Documentation.%2$s"
msgstr "O nome de uma classe de controlador Rest personalizada em vez de WP_REST_Posts_Controller. %1$sVeja a documentação.%2$s"

#: includes/generator/class-wpcode-generator-query.php:42
msgid "WP_Query"
msgstr "WP_Query"

#: includes/generator/class-wpcode-generator-query.php:43
msgid "Generate a snippet using WP_Query to load posts from your website."
msgstr "Gere um snippet usando WP_Query para carregar posts de seu site."

#: includes/generator/class-wpcode-generator-query.php:108
msgid "You can use this generator to get quickly started with a query for all the posts of an author and display them using the shortcode functionality of WPCode or automatically displaying the posts using the auto-insert option."
msgstr "Você pode usar esse gerador para começar rapidamente uma consulta de todos os posts de um autor e exibi-las usando a funcionalidade de shortcode do WPCode ou exibindo automaticamente os posts usando a opção de inserção automática."

#: includes/generator/class-wpcode-generator-query.php:120
msgid "Query variable name"
msgstr "Nome da variável de consulta"

#: includes/generator/class-wpcode-generator-query.php:121
msgid "If you want to use something more specific. The leading $ will be automatically added."
msgstr "Se você quiser usar algo mais específico. O $ inicial será adicionado automaticamente."

#: includes/generator/class-wpcode-generator-query.php:131
msgid "Include loop"
msgstr "Incluir loop"

#: includes/generator/class-wpcode-generator-query.php:144
msgid "IDs & Parents"
msgstr "IDs e ascendentes"

#: includes/generator/class-wpcode-generator-query.php:150
msgid "Post ID(s)"
msgstr "ID(s) de post"

#: includes/generator/class-wpcode-generator-query.php:151
msgid "Query a specific post ID or comma-separated list of ids. Cannot be combined with \"Post ID not in\" below."
msgstr "Consulta a uma ID de post específico ou a uma lista de IDs separadas por vírgulas. Não pode ser combinado com \"ID do post não disponível\" abaixo."

#: includes/generator/class-wpcode-generator-query.php:158
msgid "Post ID not in"
msgstr "ID do post não disponível"

#: includes/generator/class-wpcode-generator-query.php:159
msgid "Post ids to exclude from this query. Cannot be combined with \"Post ID(s)\" above."
msgstr "IDs de post a serem excluídas dessa consulta. Não podem ser combinadas com \"ID(s) de post\" acima."

#: includes/generator/class-wpcode-generator-query.php:169
msgid "Post parent ID(s)"
msgstr "ID(s) de post ascendente"

#: includes/generator/class-wpcode-generator-query.php:177
msgid "Post parent not in"
msgstr "Post ascendente não disponível"

#: includes/generator/class-wpcode-generator-query.php:188
msgid "Post slugs"
msgstr "Slugs de post"

#: includes/generator/class-wpcode-generator-query.php:189
msgid "Comma-separated list of post slugs to query by."
msgstr "Lista de slugs de post separados por vírgulas para consulta."

#: includes/generator/class-wpcode-generator-query.php:198
msgid "Type & Status"
msgstr "Tipo e status"

#: includes/generator/class-wpcode-generator-query.php:205
msgid "Post type to query by, start typing to get suggestions."
msgstr "Tipo de post para consulta, comece a digitar para ver sugestões."

#: includes/generator/class-wpcode-generator-query.php:215
msgid "Post status"
msgstr "Status do post"

#: includes/generator/class-wpcode-generator-query.php:216
msgid "Post status to query by."
msgstr "Status do post para consulta."

#: includes/generator/class-wpcode-generator-query.php:231
msgid "Author ID(s)"
msgstr "ID(s) do autor"

#: includes/generator/class-wpcode-generator-query.php:232
msgid "Author ID or comma-separated list of ids."
msgstr "ID do autor ou lista de IDs separados por vírgula."

#: includes/generator/class-wpcode-generator-query.php:238
msgid "Author not in"
msgstr "Autor não disponível"

#: includes/generator/class-wpcode-generator-query.php:239
msgid "Comma-separated list of author ids to exclude from the query."
msgstr "Lista de IDs de autores separadas por vírgulas a serem excluídas da consulta."

#: includes/generator/class-wpcode-generator-query.php:250
msgid "Author name"
msgstr "Nome do autor"

#: includes/generator/class-wpcode-generator-query.php:251
msgid "Use the \"user_nicename\" parameter to query by author."
msgstr "Use o parâmetro \"user_nicename\" para fazer uma consulta por autor."

#: includes/generator/class-wpcode-generator-query.php:259
msgid "Search"
msgstr "Pesquisar"

#: includes/generator/class-wpcode-generator-query.php:265
msgid "Search term"
msgstr "Pesquisar termo"

#: includes/generator/class-wpcode-generator-query.php:266
msgid "Search for posts by this search term."
msgstr "Pesquisar posts por essa pesquisa de termos."

#: includes/generator/class-wpcode-generator-query.php:274
msgid "Order"
msgstr "Ordem"

#: includes/generator/class-wpcode-generator-query.php:280
msgid "Results Order"
msgstr "Ordem dos resultados"

#: includes/generator/class-wpcode-generator-query.php:284
msgid "Descending order (3, 2, 1; c, b, a)"
msgstr "Ordem decrescente (3, 2, 1; c, b, a)"

#: includes/generator/class-wpcode-generator-query.php:285
msgid "Ascending order (1, 2, 3; a, b, c)"
msgstr "Ordem crescente (1, 2, 3; a, b, c)"

#: includes/generator/class-wpcode-generator-query.php:293
msgid "Order by"
msgstr "Ordenar por"

#: includes/generator/class-wpcode-generator-query.php:297
msgid "No order (none)"
msgstr "Sem ordem (nenhuma)"

#: includes/generator/class-wpcode-generator-query.php:301
msgid "Slug (name)"
msgstr "Slug (nome)"

#: includes/generator/class-wpcode-generator-query.php:302
msgid "Post type (type)"
msgstr "Tipo de post (tipo)"

#: includes/generator/class-wpcode-generator-query.php:303
msgid "Date (default)"
msgstr "Data (padrão)"

#: includes/generator/class-wpcode-generator-query.php:304
msgid "Modified date"
msgstr "Data modificada"

#: includes/generator/class-wpcode-generator-query.php:305
msgid "Parent id"
msgstr "ID ascendente"

#: includes/generator/class-wpcode-generator-query.php:306
msgid "Random"
msgstr "Aleatório"

#: includes/generator/class-wpcode-generator-query.php:307
msgid "Comment count"
msgstr "Total de comentários"

#: includes/generator/class-wpcode-generator-query.php:308
msgid "Relevance (for search)"
msgstr "Relevância (para pesquisa)"

#: includes/generator/class-wpcode-generator-query.php:309
msgid "Page Order (menu_order)"
msgstr "Ordem das páginas (menu_order)"

#: includes/generator/class-wpcode-generator-query.php:311
msgid "Meta value"
msgstr "Valor meta"

#: includes/generator/class-wpcode-generator-query.php:312
msgid "Numerical meta value (meta_value_num)"
msgstr "Valor do metadado numérico (meta_value_num)"

#: includes/generator/class-wpcode-generator-query.php:313
msgid "Order of ids in post__in"
msgstr "Ordem das IDs em post__in"

#: includes/generator/class-wpcode-generator-query.php:314
msgid "Order of names in post_name__in"
msgstr "Ordem dos nomes em post_name__in"

#: includes/generator/class-wpcode-generator-query.php:315
msgid "Order of ids in post_parent__in"
msgstr "Ordem das IDs em post_parent__in"

#: includes/generator/class-wpcode-generator-query.php:320
#: includes/generator/class-wpcode-generator-query.php:493
msgid "Meta Key"
msgstr "Chave de metadado"

#: includes/generator/class-wpcode-generator-query.php:321
msgid "Meta key to use if you choose to order by meta value."
msgstr "Chave do metadado a ser usada se você optar por ordenar por valor do metadado."

#: includes/generator/class-wpcode-generator-query.php:331
msgid "Pagination"
msgstr "Paginação"

#: includes/generator/class-wpcode-generator-query.php:337
msgid "Use Pagination"
msgstr "Usar paginação"

#: includes/generator/class-wpcode-generator-query.php:340
msgid "Choose no to display all posts (not recommended)."
msgstr "Escolha a opção não para exibir todos os posts (não recomendado)."

#: includes/generator/class-wpcode-generator-query.php:343
msgid "Yes (default)"
msgstr "Sim (padrão)"

#: includes/generator/class-wpcode-generator-query.php:348
msgid "Page number"
msgstr "Número da página"

#: includes/generator/class-wpcode-generator-query.php:349
msgid "Which page to show."
msgstr "A página a ser exibida."

#: includes/generator/class-wpcode-generator-query.php:358
msgid "Posts per page"
msgstr "Posts por página"

#: includes/generator/class-wpcode-generator-query.php:359
msgid "How many posts should be displayed per page."
msgstr "Quantos posts devem ser exibidos por página."

#: includes/generator/class-wpcode-generator-query.php:365
msgid "Offset"
msgstr "Deslocamento"

#: includes/generator/class-wpcode-generator-query.php:366
msgid "Number of posts to skip."
msgstr "Número de posts a serem ignorados."

#: includes/generator/class-wpcode-generator-query.php:375
msgid "Ignore sticky posts"
msgstr "Ignorar posts fixos"

#: includes/generator/class-wpcode-generator-query.php:380
msgid "No (default)"
msgstr "Não (padrão)"

#: includes/generator/class-wpcode-generator-query.php:387
#: includes/generator/class-wpcode-generator-query.php:393
#: includes/generator/class-wpcode-generator-taxonomy.php:35
#: includes/generator/class-wpcode-generator-taxonomy.php:112
msgid "Taxonomy"
msgstr "Taxonomia"

#: includes/generator/class-wpcode-generator-query.php:394
msgid "Taxonomy slug that you want to query by."
msgstr "Slug de taxonomia que você deseja fazer consulta."

#: includes/generator/class-wpcode-generator-query.php:402
msgid "Field"
msgstr "Campo"

#: includes/generator/class-wpcode-generator-query.php:403
msgid "Select taxonomy term by."
msgstr "Selecione o termo de taxonomia."

#: includes/generator/class-wpcode-generator-query.php:410
msgid "Term Name"
msgstr "Nome do termo"

#: includes/generator/class-wpcode-generator-query.php:411
msgid "Term Slug"
msgstr "Slug do termo"

#: includes/generator/class-wpcode-generator-query.php:412
msgid "Term Taxonomy ID"
msgstr "ID da taxonomia do termo"

#: includes/generator/class-wpcode-generator-query.php:417
msgid "Terms"
msgstr "Termos"

#: includes/generator/class-wpcode-generator-query.php:418
msgid "Comma-separated list of terms to query by."
msgstr "Lista de termos separados por vírgulas para consulta."

#: includes/generator/class-wpcode-generator-query.php:430
msgid "Include Children"
msgstr "Incluir descendentes"

#: includes/generator/class-wpcode-generator-query.php:434
msgid "Whether or not to include children for hierarchical taxonomies."
msgstr "Incluir ou não descendentes em taxonomias hierárquicas."

#: includes/generator/class-wpcode-generator-query.php:443
msgid "Operator"
msgstr "Operador"

#: includes/generator/class-wpcode-generator-query.php:447
msgid "Operator to test relation by."
msgstr "Operador para testar relação."

#: includes/generator/class-wpcode-generator-query.php:465
msgid "Add another taxonomy"
msgstr "Adicionar outra taxonomia"

#: includes/generator/class-wpcode-generator-query.php:466
msgid "Use the \"Add Taxonomy\" button below to query multiple taxonomies."
msgstr "Use o botão \"Adicionar taxonomia\" abaixo para consultar várias taxonomias."

#: includes/generator/class-wpcode-generator-query.php:470
msgid "Add Taxonomy"
msgstr "Adicionar taxonomia"

#: includes/generator/class-wpcode-generator-query.php:475
msgid "Tax Relation"
msgstr "Relação taxonômica"

#: includes/generator/class-wpcode-generator-query.php:479
#: includes/generator/class-wpcode-generator-query.php:579
msgid "AND (default)"
msgstr "E (padrão)"

#: includes/generator/class-wpcode-generator-query.php:480
#: includes/generator/class-wpcode-generator-query.php:580
msgid "OR"
msgstr "OU"

#: includes/generator/class-wpcode-generator-query.php:494
msgid "The key of the custom field."
msgstr "A chave do campo personalizado."

#: includes/generator/class-wpcode-generator-query.php:502
msgid "Meta Value"
msgstr "Valor do metadado"

#: includes/generator/class-wpcode-generator-query.php:503
msgid "Value to query the meta by."
msgstr "Valor para consultar o metadado."

#: includes/generator/class-wpcode-generator-query.php:515
msgid "How to compare the value for querying by meta."
msgstr "Como comparar o valor para consulta por meta."

#: includes/generator/class-wpcode-generator-query.php:542
msgid "Type"
msgstr "Tipo"

#: includes/generator/class-wpcode-generator-query.php:543
msgid "Type of custom field."
msgstr "Tipo de campo personalizado."

#: includes/generator/class-wpcode-generator-query.php:565
msgid "Add another meta query"
msgstr "Adicionar outra consulta de metadado"

#: includes/generator/class-wpcode-generator-query.php:566
msgid "Use the \"Add Meta\" button below to use multiple meta queries."
msgstr "Use o botão \"Adicionar metadado\" abaixo para usar várias consultas de metadados."

#: includes/generator/class-wpcode-generator-query.php:575
msgid "Relation"
msgstr "Relação"

#: includes/generator/class-wpcode-generator-script.php:35
msgid "Register Scripts"
msgstr "Registrar scripts"

#: includes/generator/class-wpcode-generator-script.php:36
msgid "Generate a snippet to load JavaScript scripts using wp_register_script."
msgstr "Gere um snippet para carregar scripts JavaScript usando wp_register_script."

#: includes/generator/class-wpcode-generator-script.php:55
msgid "Using this generator you can create a WordPress function to register and enqueue scripts."
msgstr "Usando esse gerador, você pode criar uma função do WordPress para registrar e enfileirar scripts."

#. Translators: the placeholders add a link to getboostrap.com.
#: includes/generator/class-wpcode-generator-script.php:80
msgid "You can use this to load external scripts or even scripts from a theme or plugin. For example, you could load %1$sbootstrap%2$s from a cdn."
msgstr "Você pode usá-lo para carregar scripts externos ou mesmo scripts de um tema ou plugin. Por exemplo, você poderia carregar %1$sbootstrap%2$s de uma cdn."

#: includes/generator/class-wpcode-generator-script.php:107
#: includes/generator/class-wpcode-generator-style.php:107
msgid "Action (hook)"
msgstr "Ação (gancho)"

#. Translators: placeholders add links to documentation on wordpress.org.
#: includes/generator/class-wpcode-generator-script.php:110
msgid "Hook used to add the scripts: %1$sfrontend%2$s, %3$sadmin%4$s, %5$slogin%6$s or %7$sembed%8$s."
msgstr "Gancho usado para adicionar os scripts: %1$sfrontend%2$s, %3$sadmin%4$s, %5$slogin%6$s ou %7$sembed%8$s."

#. Translators: placeholder adds the hook name.
#: includes/generator/class-wpcode-generator-script.php:124
#: includes/generator/class-wpcode-generator-style.php:124
msgid "Frontend (%s)"
msgstr "Frontend (%s)"

#. Translators: placeholder adds the hook name.
#: includes/generator/class-wpcode-generator-script.php:126
#: includes/generator/class-wpcode-generator-style.php:126
msgid "Admin (%s)"
msgstr "Admin (%s)"

#. Translators: placeholder adds the hook name.
#: includes/generator/class-wpcode-generator-script.php:128
#: includes/generator/class-wpcode-generator-style.php:128
msgid "Login (%s)"
msgstr "Acessar (%s)"

#. Translators: placeholder adds the hook name.
#: includes/generator/class-wpcode-generator-script.php:130
#: includes/generator/class-wpcode-generator-style.php:130
msgid "Embed (%s)"
msgstr "Embed (%s)"

#: includes/generator/class-wpcode-generator-script.php:137
msgid "Scripts"
msgstr "Scripts"

#: includes/generator/class-wpcode-generator-script.php:143
msgid "Script name"
msgstr "Nome do script"

#: includes/generator/class-wpcode-generator-script.php:144
#: includes/generator/class-wpcode-generator-style.php:144
msgid "This will be used as an identifier in the code, should be lowercase with no spaces."
msgstr "Será usado como um identificador no código; deve estar em letras minúsculas, sem espaços."

#: includes/generator/class-wpcode-generator-script.php:153
msgid "Script URL"
msgstr "URL do script"

#: includes/generator/class-wpcode-generator-script.php:154
msgid "The full URL for the script e.g. https://cdn.jsdelivr.net/npm/bootstrap@5.2.0-beta1/dist/js/bootstrap.bundle.min.js."
msgstr "O URL completo do script, por exemplo, https://cdn.jsdelivr.net/npm/bootstrap@5.2.0-beta1/dist/js/bootstrap.bundle.min.js."

#: includes/generator/class-wpcode-generator-script.php:163
#: includes/generator/class-wpcode-generator-style.php:163
msgid "Dependencies"
msgstr "Dependências"

#: includes/generator/class-wpcode-generator-script.php:164
msgid "Comma-separated list of scripts required for this script to load, e.g. jquery"
msgstr "Lista de scripts separados por vírgula necessários para que esse script seja carregado, por exemplo, jquery"

#: includes/generator/class-wpcode-generator-script.php:173
msgid "Script Version"
msgstr "Versão do script"

#: includes/generator/class-wpcode-generator-script.php:174
msgid "The script version."
msgstr "A versão do script."

#: includes/generator/class-wpcode-generator-script.php:186
msgid "Header or Footer?"
msgstr "Cabeçalho ou rodapé?"

#: includes/generator/class-wpcode-generator-script.php:187
msgid "Load the script in the page head or in the footer."
msgstr "Carregue o script no cabeçalho ou rodapé da página."

#: includes/generator/class-wpcode-generator-script.php:199
msgid "Deregister script?"
msgstr "Cancelar registro do script?"

#. Translators: Placeholders for wp.org docs link.
#: includes/generator/class-wpcode-generator-script.php:202
msgid "Should the script be %1$sderegistered%2$s first? (for example, if you are replacing an existing script)."
msgstr "O script deve ser %1$scancelado%2$s primeiro? (por exemplo, se você estiver substituindo um script existente)."

#: includes/generator/class-wpcode-generator-script.php:217
msgid "Enqueue script?"
msgstr "Enfileirar script?"

#. Translators: Placeholders for wp.org docs link.
#: includes/generator/class-wpcode-generator-script.php:220
msgid "Should the script be %1$senqueued%2$s or just registered? (select \"No\" only if you intend enqueueing it later."
msgstr "O script deve ser %1$senfileirado%2$s ou apenas registrado? (selecione \"Não\" somente se você pretende enfileirá-lo posteriormente)."

#: includes/generator/class-wpcode-generator-script.php:241
msgid "Add more scripts"
msgstr "Adicionar mais scripts"

#: includes/generator/class-wpcode-generator-script.php:242
msgid "Use the \"Add script\" button below to add multiple scripts in this snippet."
msgstr "Use o botão \"Adicionar script\" abaixo para incluir vários scripts neste snippet."

#: includes/generator/class-wpcode-generator-script.php:246
msgid "Add script"
msgstr "Adicionar script"

#: includes/generator/class-wpcode-generator-sidebar.php:35
msgid "Sidebar"
msgstr "Barra lateral"

#: includes/generator/class-wpcode-generator-sidebar.php:36
msgid "Generate a snippet to register a sidebar for your widgets."
msgstr "Gere um snippet para permitir uma barra lateral para seus widgets."

#. Translators: Placeholders add links to the wordpress.org references.
#: includes/generator/class-wpcode-generator-sidebar.php:57
msgid "This generator makes it easy to add sidebars to your website using the \"register_sidebar\" function."
msgstr "Esse gerador facilita a inclusão de barras laterais em seu site usando a função \"register_sidebar\"."

#: includes/generator/class-wpcode-generator-sidebar.php:83
msgid "You can add multiple widget areas for your footer or post-type specific sidebars."
msgstr "Você pode adicionar várias áreas de widgets para o rodapé ou barras laterais específicas de cada tipo de post."

#: includes/generator/class-wpcode-generator-sidebar.php:116
msgid "Sidebars"
msgstr "Barras laterais"

#: includes/generator/class-wpcode-generator-sidebar.php:122
msgid "Sidebar Id"
msgstr "ID da barra lateral"

#: includes/generator/class-wpcode-generator-sidebar.php:123
msgid "This is the sidebar unique id, used in the code, lowercase with no spaces."
msgstr "Esse é a ID exclusiva da barra lateral, usada no código, em letras minúsculas e sem espaços."

#: includes/generator/class-wpcode-generator-sidebar.php:131
msgid "Add a descriptive label for this sidebar to be used in the admin."
msgstr "Adicione um rótulo descritivo à barra lateral a ser usada no painel de administração."

#: includes/generator/class-wpcode-generator-sidebar.php:146
#: includes/generator/class-wpcode-generator-widget.php:158
msgid "CSS Class"
msgstr "Classe CSS"

#: includes/generator/class-wpcode-generator-sidebar.php:147
msgid "Use an unique CSS class name for better control over this sidebar's styles in the admin."
msgstr "Use um nome de classe CSS exclusivo para melhor controle sobre os estilos dessa barra lateral no admin."

#: includes/generator/class-wpcode-generator-sidebar.php:157
msgid "Before Title"
msgstr "Antes do título"

#: includes/generator/class-wpcode-generator-sidebar.php:158
msgid "HTML code to add before each widget title."
msgstr "Código HTML a ser adicionado antes de cada título do widget."

#: includes/generator/class-wpcode-generator-sidebar.php:166
msgid "After Title"
msgstr "Após o título"

#: includes/generator/class-wpcode-generator-sidebar.php:167
msgid "HTML code to add after each widget title."
msgstr "Código HTML a ser adicionado após cada título do widget."

#: includes/generator/class-wpcode-generator-sidebar.php:175
msgid "Before Widget"
msgstr "Antes do widget"

#: includes/generator/class-wpcode-generator-sidebar.php:176
msgid "HTML code to add before each widget."
msgstr "Código HTML a ser adicionado antes de cada widget."

#: includes/generator/class-wpcode-generator-sidebar.php:184
msgid "After Widget"
msgstr "Após o widget"

#: includes/generator/class-wpcode-generator-sidebar.php:185
msgid "HTML code to add after each widget."
msgstr "Código HTML a ser adicionado após cada widget."

#: includes/generator/class-wpcode-generator-sidebar.php:199
msgid "Add another sidebar"
msgstr "Adicionar outra barra lateral"

#: includes/generator/class-wpcode-generator-sidebar.php:200
msgid "Use the \"Add Sidebar\" button below to add as many sidebars as you need."
msgstr "Use o botão \"Adicionar barra lateral\" abaixo para adicionar quantas barras laterais forem necessárias."

#: includes/generator/class-wpcode-generator-sidebar.php:204
msgid "Add Sidebar"
msgstr "Adicionar barra lateral"

#: includes/generator/class-wpcode-generator-style.php:35
msgid "Register Stylesheets"
msgstr "Registrar folhas de estilo"

#: includes/generator/class-wpcode-generator-style.php:36
msgid "Generate a snippet to load CSS stylesheets using wp_register_style."
msgstr "Gere um snippet para carregar folhas de estilo CSS usando wp_register_style."

#: includes/generator/class-wpcode-generator-style.php:55
msgid "Using this generator you can create a WordPress function to register and enqueue styles."
msgstr "Usando esse gerador, você pode criar uma função do WordPress para registrar e enfileirar estilos."

#. Translators: the placeholders add a link to getboostrap.com.
#: includes/generator/class-wpcode-generator-style.php:80
msgid "You can use this to load external styles or even styles from a theme or plugin. For example, you could load %1$sfontawesome%2$s from a cdn."
msgstr "Você pode usá-lo para carregar estilos externos ou mesmo estilos de um tema ou plugin. Por exemplo, você pode carregar %1$sfontawesome%2$s de uma cdn."

#. Translators: placeholders add links to documentation on wordpress.org.
#: includes/generator/class-wpcode-generator-style.php:110
msgid "Hook used to add the styles: %1$sfrontend%2$s, %3$sadmin%4$s, %5$slogin%6$s or %7$sembed%8$s."
msgstr "Gancho usado para adicionar os estilos: %1$sfrontend%2$s, %3$sadmin%4$s, %5$slogin%6$s ou %7$sembed%8$s."

#: includes/generator/class-wpcode-generator-style.php:137
msgid "Styles"
msgstr "Estilos"

#: includes/generator/class-wpcode-generator-style.php:143
msgid "Style name"
msgstr "Nome do estilo"

#: includes/generator/class-wpcode-generator-style.php:153
msgid "Stylesheet URL"
msgstr "URL da folha de estilo"

#: includes/generator/class-wpcode-generator-style.php:154
msgid "The full URL for the stylesheet e.g. https://cdn.jsdelivr.net/npm/bootstrap@5.2.0-beta1/dist/css/bootstrap.min.css."
msgstr "O URL completo da folha de estilo, por exemplo, https://cdn.jsdelivr.net/npm/bootstrap@5.2.0-beta1/dist/css/bootstrap.min.css."

#: includes/generator/class-wpcode-generator-style.php:164
msgid "Comma-separated list of styles required for this style to load, e.g. jquery"
msgstr "Lista de estilos, separados por vírgulas, necessários para que esse estilo seja carregado, por exemplo, jquery"

#: includes/generator/class-wpcode-generator-style.php:173
msgid "Style Version"
msgstr "Versão de estilo"

#: includes/generator/class-wpcode-generator-style.php:174
msgid "The style version."
msgstr "A versão de estilo."

#: includes/generator/class-wpcode-generator-style.php:186
msgid "Media"
msgstr "Mídia"

#. Translators: placeholders add a link to the W3.org reference.
#: includes/generator/class-wpcode-generator-style.php:189
msgid "Load the style %1$smedia type%2$s, usually \"all\"."
msgstr "Carregue o estilo %1$stipo de mídia%2$s, geralmente \"all\"."

#: includes/generator/class-wpcode-generator-style.php:200
msgid "Deregister style?"
msgstr "Cancelar registro de estilo?"

#. Translators: Placeholders for wp.org docs link.
#: includes/generator/class-wpcode-generator-style.php:203
msgid "Should the style be %1$sderegistered%2$s first? (for example, if you are replacing an existing style)."
msgstr "O estilo deve ser %1$scancelado%2$s primeiro? (por exemplo, se você estiver substituindo um estilo existente)."

#: includes/generator/class-wpcode-generator-style.php:218
msgid "Enqueue style?"
msgstr "Enfileirar estilo?"

#. Translators: Placeholders for wp.org docs link.
#: includes/generator/class-wpcode-generator-style.php:221
msgid "Should the style be %1$senqueued%2$s or just registered? (select \"No\" only if you intend enqueueing it later."
msgstr "O estilo deve ser %1$senfileirado%2$s ou apenas registrado? (selecione \"Não\" apenas se pretender enfileirá-lo posteriormente)."

#: includes/generator/class-wpcode-generator-style.php:242
msgid "Add more styles"
msgstr "Adicionar mais estilos"

#: includes/generator/class-wpcode-generator-style.php:243
msgid "Use the \"Add style\" button below to add multiple styles in this snippet."
msgstr "Use o botão \"Adicionar estilo\" abaixo para adicionar vários estilos neste snippet."

#: includes/generator/class-wpcode-generator-style.php:247
msgid "Add style"
msgstr "Adicionar estilo"

#: includes/generator/class-wpcode-generator-taxonomy.php:36
msgid "Create a custom taxonomy for your posts using this generator."
msgstr "Crie uma taxonomia personalizada para seus posts usando esse gerador."

#: includes/generator/class-wpcode-generator-taxonomy.php:55
msgid "Use this generator to create custom taxonomies for your WordPress site."
msgstr "Use esse gerador para criar taxonomias personalizadas para seu site WordPress."

#: includes/generator/class-wpcode-generator-taxonomy.php:78
msgid "Use this to add more taxonomies to posts or custom post types. For example, if you used the Post Type generator to create an Artist post type you can use this one to create a Genre taxonomy."
msgstr "Use essa opção para adicionar mais taxonomias a posts ou tipos de post personalizados. Por exemplo, se você usou o gerador Tipo de post para criar um tipo de post Artista, pode usá-lo para criar uma taxonomia de Gênero."

#: includes/generator/class-wpcode-generator-taxonomy.php:118
msgid "Taxonomy Key"
msgstr "Chave de taxonomia"

#: includes/generator/class-wpcode-generator-taxonomy.php:119
msgid "Name of taxonomy used in the code, lowercase maximum 20 characters."
msgstr "Nome da taxonomia usada no código, em letras minúsculas, com no máximo 20 caracteres."

#: includes/generator/class-wpcode-generator-taxonomy.php:129
msgid "Name (Singular)"
msgstr "Nome (singular)"

#: includes/generator/class-wpcode-generator-taxonomy.php:130
msgid "The singular taxonomy name (e.g. Genre, Year)."
msgstr "O nome da taxonomia no singular (por exemplo, Gênero, Ano)."

#: includes/generator/class-wpcode-generator-taxonomy.php:138
msgid "The taxonomy plural name (e.g. Genres, Years)."
msgstr "O nome da taxonomia no plural (por exemplo, Gêneros, Anos)."

#: includes/generator/class-wpcode-generator-taxonomy.php:148
msgid "Link To Post Type(s)"
msgstr "Link para tipo(s) de post"

#: includes/generator/class-wpcode-generator-taxonomy.php:149
msgid "Comma-separated list of Post Types (e.g. post, page)"
msgstr "Lista de tipos de post separados por vírgulas (por exemplo, post, página)"

#: includes/generator/class-wpcode-generator-taxonomy.php:157
msgid "Hierarchical taxonomies can have descendants."
msgstr "Taxonomias hierárquicas podem ter descendentes."

#: includes/generator/class-wpcode-generator-taxonomy.php:161
msgid "No, like tags"
msgstr "Não, como tags"

#: includes/generator/class-wpcode-generator-taxonomy.php:162
msgid "Yes, like categories"
msgstr "Sim, como categorias"

#: includes/generator/class-wpcode-generator-taxonomy.php:203
msgid "New Item Name"
msgstr "Nome do novo item"

#: includes/generator/class-wpcode-generator-taxonomy.php:241
msgid "Separate Items with commas"
msgstr "Separar itens por vírgulas"

#: includes/generator/class-wpcode-generator-taxonomy.php:248
msgid "Add or Remove Items"
msgstr "Adicionar ou excluir itens"

#: includes/generator/class-wpcode-generator-taxonomy.php:265
msgid "Popular Items"
msgstr "Itens populares"

#: includes/generator/class-wpcode-generator-taxonomy.php:272
msgid "Search Items"
msgstr "Pesquisar itens"

#: includes/generator/class-wpcode-generator-taxonomy.php:286
msgid "No items"
msgstr "Nenhum item"

#. Translators: Placeholders add a link to the wp.org documentation page.
#: includes/generator/class-wpcode-generator-taxonomy.php:317
msgid "Should this taxonomy be %1$svisible to authors%2$s?"
msgstr "Essa taxonomia deve ser %1$svisível para os autores%2$s?"

#: includes/generator/class-wpcode-generator-taxonomy.php:331
msgid "Should this taxonomy have an User Interface for managing?"
msgstr "Essa taxonomia deve ter uma interface de usuário para gerenciamento?"

#: includes/generator/class-wpcode-generator-taxonomy.php:341
msgid "Show Admin Column"
msgstr "Mostrar coluna de administração"

#: includes/generator/class-wpcode-generator-taxonomy.php:342
msgid "Should this taxonomy add a column in the list of associated post types?"
msgstr "Essa taxonomia deve adicionar uma coluna na lista de tipos de post associados?"

#: includes/generator/class-wpcode-generator-taxonomy.php:355
msgid "Show Tag Cloud"
msgstr "Mostrar nuvem de tag"

#: includes/generator/class-wpcode-generator-taxonomy.php:356
msgid "Should this taxonomy be visible in the tag cloud widget?"
msgstr "Essa taxonomia deve estar visível no widget da nuvem de tags?"

#: includes/generator/class-wpcode-generator-taxonomy.php:366
msgid "Show in Navigation Menus"
msgstr "Mostrar nos menus de navegação"

#: includes/generator/class-wpcode-generator-taxonomy.php:367
msgid "Should this taxonomy be available in menus (Appearance > Menus)."
msgstr "Essa taxonomia deve estar disponível nos menus (Aparência > Menus)?"

#: includes/generator/class-wpcode-generator-taxonomy.php:385
msgid "Permalink Rewrite"
msgstr "Reescrita de link permanente"

#: includes/generator/class-wpcode-generator-taxonomy.php:386
msgid "Use Default Permalinks, disable automatic rewriting or use custom permalinks."
msgstr "Use links permanentes padrão, desative a reescrita automática ou use links permanentes personalizados."

#: includes/generator/class-wpcode-generator-taxonomy.php:390
msgid "Default (taxonomy key)"
msgstr "Padrão (chave de taxonomia)"

#: includes/generator/class-wpcode-generator-taxonomy.php:401
msgid "If you selected custom permalinks use this field for the rewrite base, e.g. taxonomy in https://yoursite.com/taxonomy"
msgstr "Se você selecionou links permanentes personalizados, use esse campo para a base de reescrita, por exemplo, taxonomia em https://yoursite.com/taxonomy"

#: includes/generator/class-wpcode-generator-taxonomy.php:411
msgid "Prepend permastruct"
msgstr "Anexar permastruct"

#: includes/generator/class-wpcode-generator-taxonomy.php:412
msgid "Should the permastruct be prepended to the url (with_front parameter)."
msgstr "O permastruct deve ser anexado previamente à url (parâmetro with_front)?"

#: includes/generator/class-wpcode-generator-taxonomy.php:422
msgid "Hierarchical URL Slug"
msgstr "Slug de URL hierárquico"

#: includes/generator/class-wpcode-generator-taxonomy.php:423
msgid "For hierarchical taxonomies use the whole hierarchy in the URL?"
msgstr "Para taxonomias hierárquicas, usar toda a hierarquia no URL?"

#. Translators: Placeholders add link to wp.org docs.
#: includes/generator/class-wpcode-generator-taxonomy.php:443
msgid "User capabilities in relation to this taxonomy. %1$sSee Documentation.%2$s"
msgstr "Recursos do usuário em relação a essa taxonomia. %1$sConsulte a documentação.%2$s"

#: includes/generator/class-wpcode-generator-taxonomy.php:461
msgid "Edit Terms"
msgstr "Editar termos"

#: includes/generator/class-wpcode-generator-taxonomy.php:468
msgid "Delete Terms"
msgstr "Excluir termos"

#: includes/generator/class-wpcode-generator-taxonomy.php:478
msgid "Manage Terms"
msgstr "Gerenciar termos"

#: includes/generator/class-wpcode-generator-taxonomy.php:485
msgid "Assign Terms"
msgstr "Atribuir termos"

#: includes/generator/class-wpcode-generator-taxonomy.php:501
msgid "Add the taxonomy to the WordPress wp-json API."
msgstr "Adicione a taxonomia à API wp-json do WordPress."

#: includes/generator/class-wpcode-generator-taxonomy.php:515
msgid "The base slug that this taxonomy will use in the REST API."
msgstr "O slug base que esta taxonomia usará na API REST."

#: includes/generator/class-wpcode-generator-taxonomy.php:525
msgid "The name of a custom Rest Controller class instead of WP_REST_Terms_Controller."
msgstr "O nome de uma classe de controlador Rest personalizada em vez de WP_REST_Terms_Controller."

#: includes/generator/class-wpcode-generator-widget.php:35
#: includes/generator/class-wpcode-generator-widget.php:123
msgid "Widget"
msgstr "Widget"

#: includes/generator/class-wpcode-generator-widget.php:36
msgid "Generate a snippet to register a custom sidebar widget for your website."
msgstr "Gere um snippet para registrar um widget de barra lateral personalizado para o seu site."

#: includes/generator/class-wpcode-generator-widget.php:55
msgid "Using this generator you can easily add a custom sidebar widget with settings."
msgstr "Com esse gerador, você pode adicionar facilmente um widget de barra lateral personalizado com configurações."

#: includes/generator/class-wpcode-generator-widget.php:78
msgid "Sidebar widgets are very useful when you want to display the same content on multiple pages, you can create a widget with contact methods, for example and fields to set a phone number, email, etc."
msgstr "Os widgets da barra lateral são muito úteis quando você deseja exibir o mesmo conteúdo em várias páginas. É possível criar um widget com métodos de contato, por exemplo, e campos para definir um número de telefone, e-mail etc."

#: includes/generator/class-wpcode-generator-widget.php:90
msgid "Class name"
msgstr "Nome da classe"

#: includes/generator/class-wpcode-generator-widget.php:91
msgid "Make this unique to avoid conflicts with other similar snippets."
msgstr "Torne-o único para evitar conflitos com outros snippets semelhantes."

#: includes/generator/class-wpcode-generator-widget.php:102
msgid "Prefix"
msgstr "Prefixo"

#: includes/generator/class-wpcode-generator-widget.php:103
msgid "Used to prefix all the field names."
msgstr "Usado para prefixar todos os nomes de campo."

#: includes/generator/class-wpcode-generator-widget.php:114
msgid "Optional textdomain for translations."
msgstr "Domínio de texto opcional para traduções."

#: includes/generator/class-wpcode-generator-widget.php:129
msgid "Widget ID"
msgstr "ID do widget"

#: includes/generator/class-wpcode-generator-widget.php:130
msgid "Unique id for the widget, used in the code."
msgstr "ID exclusiva do widget, usada no código."

#: includes/generator/class-wpcode-generator-widget.php:138
msgid "Widget Title"
msgstr "Título do widget"

#: includes/generator/class-wpcode-generator-widget.php:139
msgid "The title of the widget (displayed in the admin)."
msgstr "O título do widget (exibido no admin)."

#: includes/generator/class-wpcode-generator-widget.php:151
msgid "Description used in the admin to explain what the widget is used for."
msgstr "Descrição usada no admin para explicar a finalidade do widget."

#: includes/generator/class-wpcode-generator-widget.php:159
msgid "Widget-specific CSS class name."
msgstr "Nome da classe CSS específica do widget."

#: includes/generator/class-wpcode-generator-widget.php:168
msgid "Widget Output Code"
msgstr "Código de saída do widget"

#: includes/generator/class-wpcode-generator-widget.php:169
msgid "PHP Code used for outputting the fields in the frontend. If left empty it will output the fields values in a simple list."
msgstr "Código PHP usado para gerar os campos na interface. Se deixado vazio, ele exibirá os valores dos campos em uma lista simples."

#: includes/generator/class-wpcode-generator-widget.php:177
msgid "Fields"
msgstr "Campos"

#: includes/generator/class-wpcode-generator-widget.php:183
msgid "Field Type"
msgstr "Tipo de campo"

#: includes/generator/class-wpcode-generator-widget.php:184
msgid "Pick the type of field you want to use for this setting."
msgstr "Escolha o tipo de campo que deseja usar para essa configuração."

#: includes/generator/class-wpcode-generator-widget.php:188
msgid "Text"
msgstr "Texto"

#: includes/generator/class-wpcode-generator-widget.php:189
msgid "Email"
msgstr "E-mail"

#: includes/generator/class-wpcode-generator-widget.php:190
msgid "URL"
msgstr "URL"

#: includes/generator/class-wpcode-generator-widget.php:191
msgid "Number"
msgstr "Número"

#: includes/generator/class-wpcode-generator-widget.php:192
msgid "Textarea"
msgstr "Área de texto"

#: includes/generator/class-wpcode-generator-widget.php:193
msgid "Select"
msgstr "Selecionar"

#: includes/generator/class-wpcode-generator-widget.php:194
msgid "Checkboxes"
msgstr "Caixas de seleção"

#: includes/generator/class-wpcode-generator-widget.php:195
msgid "Radio"
msgstr "Opção"

#: includes/generator/class-wpcode-generator-widget.php:201
msgid "Field ID"
msgstr "ID do Campo"

#: includes/generator/class-wpcode-generator-widget.php:202
msgid "Unique id for this field, used in the code."
msgstr "ID exclusiva para esse campo, usada no código."

#: includes/generator/class-wpcode-generator-widget.php:209
msgid "Field Label"
msgstr "Rótulo do campo"

#: includes/generator/class-wpcode-generator-widget.php:210
msgid "The label displayed next to this field in the admin form."
msgstr "O rótulo exibido ao lado desse campo no formulário de administração."

#: includes/generator/class-wpcode-generator-widget.php:231
msgid "Default"
msgstr "Padrão"

#: includes/generator/class-wpcode-generator-widget.php:232
msgid "Set the default value for this field."
msgstr "Defina o valor padrão desse campo."

#: includes/generator/class-wpcode-generator-widget.php:250
msgid "Add another field"
msgstr "Adicionar outro campo"

#: includes/generator/class-wpcode-generator-widget.php:251
msgid "Use the \"Add field\" button below to add as many fields as you need."
msgstr "Use o botão \"Adicionar campo\" abaixo para adicionar os campos necessários."

#: includes/generator/class-wpcode-generator-widget.php:255
msgid "Add field"
msgstr "Adicionar campo"

#: includes/helpers.php:166
msgctxt "Copy to clipboard"
msgid "Copy"
msgstr "Copiar"

#: includes/helpers.php:182
msgid "Is"
msgstr "É"

#: includes/helpers.php:183
msgid "Is not"
msgstr "Não é"

#: includes/helpers.php:180
msgid "Contains"
msgstr "contém"

#: includes/helpers.php:181
msgid "Doesn't Contain"
msgstr "Não contém"

#: includes/safe-mode.php:75
msgid "WPCode is in Safe Mode which means no snippets are getting executed. Please disable any snippets that have caused errors and when done click the button below to exit safe mode."
msgstr "O WPCode está no modo de segurança, o que significa que nenhum snippet está sendo executado. Desative os snippets que causaram erros e, ao terminar, clique no botão abaixo para sair do modo de segurança."

#: includes/safe-mode.php:76
msgid "The link will open in a new window so if you are still encountering issues you safely can return to this tab and make further adjustments"
msgstr "O link será aberto em uma nova janela, portanto, se ainda houver problemas, você poderá retornar a esta guia com segurança e fazer mais ajustes"

#: includes/safe-mode.php:78
msgid "Exit safe mode"
msgstr "Sair do modo de segurança"

#: includes/admin/pages/class-wpcode-admin-page-generator.php:223
#: includes/admin/pages/class-wpcode-admin-page.php:1125
msgid "Use Snippet"
msgstr "Usar snippet"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1075
msgid "Tags: Use tags to make it easier to group similar snippets together. <br />Priority: A lower priority will result in the snippet being executed before others with a higher priority. <br />Note: Add a private note related to this snippet."
msgstr "Tags: use tags para facilitar o agrupamento de snippets semelhantes. <br />Prioridade: a prioridade mais baixa fará com que o snippet seja executado antes de outros com prioridade mais alta. <br />Observação: adicione uma anotação privada relacionada a esse snippet."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:1511
msgid "This code snippet if"
msgstr "este snippet de código se"

#: includes/admin/pages/class-wpcode-admin-page.php:753
msgid "Preview"
msgstr "Pré-visualizar"

#: includes/auto-insert/class-wpcode-auto-insert-everywhere.php:74
msgid "Frontend Only"
msgstr "Somente na interface"

#: includes/auto-insert/class-wpcode-auto-insert-single.php:60
msgid "Page, Post, Custom Post Type"
msgstr "Página, post, tipo de post personalizado"

#: includes/conditional-logic/class-wpcode-conditional-page.php:57
msgid "Archive"
msgstr "Arquivo"

#: includes/conditional-logic/class-wpcode-conditional-page.php:61
msgid "Single post/page"
msgstr "Post/página única"

#. Translators: Placeholders add links to the wordpress.org references.
#: includes/generator/class-wpcode-generator-hooks.php:1289
msgid "Using this generator you can safely add custom %1$shooks%2$s using either %3$sadd_action%4$s or %5$sadd_filter%6$s."
msgstr "Com esse gerador, você pode adicionar com segurança %1$sganchos%2$s personalizados usando %3$sadd_action%4$s ou %5$sadd_filter%6$s."

#: includes/generator/class-wpcode-generator-menu.php:131
msgid "Add a descriptive label for this menu in the admin."
msgstr "Adicione um rótulo descritivo para esse menu no admin."

#: includes/generator/class-wpcode-generator-post-type.php:137
msgid "The singular post type name (e.g. Artist, Album, Song)."
msgstr "O nome do tipo de post no singular (p. ex., artista, álbum, música)."

#: includes/generator/class-wpcode-generator-post-type.php:217
#: includes/generator/class-wpcode-generator-taxonomy.php:182
msgid "All Items"
msgstr "Todos os itens"

#. Translators: Placeholders add links to the wordpress.org references.
#: includes/generator/class-wpcode-generator-query.php:82
msgid "This generator makes it easy for you to create custom queries using %1$sWP_Query%2$s which you can then extend to display posts or similar."
msgstr "Esse gerador facilita a criação de consultas personalizadas usando %1$sWP_Query%2$s, que podem ser utilizadas para exibir posts ou similares."

#: includes/generator/class-wpcode-generator-query.php:132
msgid "Select yes if you want to include an empty loop of the results that you can fill in for output."
msgstr "Selecione \"Sim\" se quiser incluir um loop vazio dos resultados que você pode inserir na saída."

#: includes/generator/class-wpcode-generator-query.php:170
msgid "Comma-separated list of post parent ids if the post type is hierarchical (like pages)."
msgstr "Lista de IDs de post ascendente separadas por vírgulas se o tipo de post for hierárquico (como páginas)."

#: includes/generator/class-wpcode-generator-query.php:178
msgid "Comma-separated list of post parent ids to exclude."
msgstr "Lista de IDs de posts ascendentes separadas por vírgulas a serem excluídas."

#: includes/generator/class-wpcode-generator-query.php:570
msgid "Add Meta"
msgstr "Adicionar metadado"

#: includes/generator/class-wpcode-generator-taxonomy.php:196
msgid "Parent Item colon"
msgstr "Item ascendente com dois pontos"

#: includes/generator/class-wpcode-generator-taxonomy.php:255
msgid "Choose From Most Used"
msgstr "Escolher entre os mais usados"

#: includes/generator/class-wpcode-generator-widget.php:224
msgid "Display a short descriptive text below this field."
msgstr "Exiba um texto descritivo curto abaixo desse campo."

#: includes/generator/class-wpcode-generator-widget.php:240
msgid "Use value|label for each line to add options for select, checkbox or radio."
msgstr "Use valor| rótulo para cada linha para adicionar opções de seleção, caixa de seleção ou opção."

#: includes/admin/pages/class-wpcode-admin-page-generator.php:140
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:416
msgid "Search Generators"
msgstr "Geradores de pesquisa"

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:326
msgid "Choose this blank snippet to start from scratch and paste any custom code or simply write your own."
msgstr "Escolha este snippets em branco para começar do zero e cole qualquer código personalizado ou simplesmente escreva o seu próprio código."

#. Translators: The placeholders add links to create a new custom snippet or
#. the suggest-a-snippet form.
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:400
msgid "To speed up the process you can select from one of our pre-made library, or you can start with a %1$sblank snippet%2$s and %1$screate your own%2$s. Have a suggestion for new snippet? %3$sWe’d love to hear it!%4$s"
msgstr "Para acelerar o processo, você pode selecionar uma de nossas bibliotecas predefinidas ou começar com um %1$ssnippet em branco%2$s e %1$scriar o seu próprio%2$s. Tem sugestão para um novo snippets? %3$sFale conosco!%4$s"

#: includes/auto-insert/class-wpcode-auto-insert-site-wide.php:68
msgid "Site Wide Footer"
msgstr "Rodapé de todo o site"

#: includes/generator/class-wpcode-generator-admin-bar.php:54
#: includes/generator/class-wpcode-generator-contact-methods.php:54
#: includes/generator/class-wpcode-generator-cronjob.php:54
#: includes/generator/class-wpcode-generator-hooks.php:1286
#: includes/generator/class-wpcode-generator-menu.php:54
#: includes/generator/class-wpcode-generator-post-status.php:54
#: includes/generator/class-wpcode-generator-post-type.php:54
#: includes/generator/class-wpcode-generator-query.php:79
#: includes/generator/class-wpcode-generator-script.php:54
#: includes/generator/class-wpcode-generator-sidebar.php:54
#: includes/generator/class-wpcode-generator-style.php:54
#: includes/generator/class-wpcode-generator-taxonomy.php:54
#: includes/generator/class-wpcode-generator-widget.php:54
msgid "Overview"
msgstr "Visão geral"

#: includes/generator/class-wpcode-generator-post-status.php:156
#: includes/generator/class-wpcode-generator-post-status.php:184
msgid "Yes - Default"
msgstr "Sim – padrão"

#: includes/generator/class-wpcode-generator-post-status.php:171
#: includes/generator/class-wpcode-generator-post-status.php:196
msgid "No - Default"
msgstr "Não – padrão"

#: includes/generator/class-wpcode-generator-post-type.php:145
msgid "The post type plural name (e.g. Artists, Albums, Songs)."
msgstr "O nome do tipo de post no plural (p. ex., artistas, álbuns, músicas)."

#. Translators: Placeholder adds a link to the dashicons page.
#: includes/generator/class-wpcode-generator-post-type.php:577
msgid "The custom query variable to use for this post type. %1$sSee documentation%2$s."
msgstr "A variável de consulta personalizada a ser usada para esse tipo de post. %1$sVeja a documentação%2$s."

#: includes/generator/class-wpcode-generator-query.php:409
msgid "Term ID (default)"
msgstr "ID do termo (padrão)"

#: includes/generator/class-wpcode-generator-sidebar.php:139
msgid "A short description for the the admin area."
msgstr "Uma breve descrição da área do administrador."

#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:375
msgid "Snippet Library"
msgstr "Biblioteca de snippets"

#. translators: $1$s - WPCode plugin name; $2$s - WP.org review link; $3$s -
#. WP.org review link.
#: includes/admin/class-wpcode-review.php:193
msgid "Please rate %1$s <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">&#9733;&#9733;&#9733;&#9733;&#9733;</a> on <a href=\"%3$s\" target=\"_blank\" rel=\"noopener\">WordPress.org</a> to help us spread the word. Thank you from the WPCode team!"
msgstr "Avalie %1$s <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">&#9733;&#9733;&#9733;&#9733;&#9733;</a> em <a href=\"%3$s\" target=\"_blank\" rel=\"noopener\">WordPress.org</a> para divulgar nosso trabalho. A equipe do WPCode agradece!"

#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:128
#: includes/admin/pages/class-wpcode-admin-page-snippet-manager.php:182
msgid "Sorry, you only have read-only access to this page. Ask your administrator for assistance editing."
msgstr "Desculpe, você tem acesso somente de leitura a esta página. Peça ajuda ao seu administrador para editar."

#. translators: %s: The `<head>` tag
#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:154
msgid "These scripts will be printed in the %s section."
msgstr "Esses scripts serão impressos na seção %s."

#. translators: %s: The `<head>` tag
#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:159
msgid "These scripts will be printed just below the opening %s tag."
msgstr "Esses scripts serão impressos logo abaixo da tag %s de abertura."

#. translators: %s: The `</body>` tag
#: includes/admin/pages/class-wpcode-admin-page-headers-footers.php:164
msgid "These scripts will be printed above the closing %s tag."
msgstr "Esses scripts serão impressos acima da tag %s de encerramento."

#: includes/admin/class-wpcode-admin-page-loader.php:205
#: includes/admin/pages/class-wpcode-admin-page-settings.php:53
#: includes/class-wpcode-admin-bar-info.php:408
msgid "Settings"
msgstr "Configurações"

#: includes/admin/pages/class-wpcode-admin-page-settings.php:88
msgid "Settings Saved."
msgstr "Configurações salvas."