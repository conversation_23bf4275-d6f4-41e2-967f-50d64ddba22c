<?php

function get_candidate_results($user_id, $quiz_ids) {
    global $wpdb;
    $results = [];
    foreach ($quiz_ids as $quiz_id) {
        $result = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM wp_mlw_results WHERE user = %d AND quiz_id = %d", 
            $user_id, 
            $quiz_id
        ));
        if ($result) {
            $results[] = $result;
        }
    }
    return $results;
}

function recalculate_score($quiz_results, $path_meta) {
    $weights = [
        "wordpress" => ["logica" => 3.0, "matematica" => 3.0, "portugues" => 3.0, "marketing" => 3.0, "audiovisual" => 3.0, "design" => 5.0],
        "designer" => ["logica" => 2.0, "matematica" => 1.0, "portugues" => 3.0, "marketing" => 4.0, "audiovisual" => 5.0, "design" => 5.0],
        "editor" => ["logica" => 2, "matematica" => 1, "portugues" => 3.0, "marketing" => 4.0, "audiovisual" => 5.0, "design" => 5.0],
        "trafego" => ["logica" => 2.0, "matematica" => 4.0, "portugues" => 3.0, "marketing" => 5.0, "audiovisual" => 2.0, "design" => 2.0],
        "programador" => ["logica" => 6.0, "matematica" => 6.0, "portugues" => 3.0, "marketing" => 1.0, "audiovisual" => 1.0, "design" => 3.0]
    ];
    $categories_map = [
        '56' => 'portugues',
        '57' => 'matematica',
        '58' => 'logica',
        '59' => 'marketing',
        '60' => 'audiovisual',
        '61' => 'design'
    ];
    $path_weights = $weights[$path_meta];

    $total_score = 0;
    foreach ($quiz_results as $result) {
        $result = $result->quiz_results;
        $questions = @unserialize(trim($result))[1];
        foreach ($questions as $question) {
            $category_id =  $question['multicategories'][0];
            $category = $categories_map[$category_id];
            $total_score += (float) $path_weights[$category] * $question['points'];
        }
    }
    return $total_score;
}

function adjust_technical_score($final_score, $additional_score) {
    if ($additional_score >= 81 && $additional_score <= 85) {
        $final_score = (float) $final_score * 1.05;
    } elseif ($additional_score >= 86 && $additional_score <= 90) {
        $final_score = (float) $final_score * 1.10;
    } elseif ($additional_score >= 91 && $additional_score <= 95) {
        $final_score = (float) $final_score * 1.15;
    } elseif ($additional_score >= 96 && $additional_score <= 100) {
        $final_score = (float) $final_score * 1.20;
    } elseif ($additional_score >= 101 && $additional_score <= 103) {
        $final_score = (float) $final_score * 1.25;
    }
    return $final_score;
}

function correct_candidate_scores() {
    global $wpdb;
    $candidates = get_users(['role' => 'candidato']);
    $quiz_ids = [4, 6, 7, 9];
    $final_results = [];

    foreach ($candidates as $candidate) {
        $user_id = $candidate->ID;
        $user_role = get_user_meta($user_id, 'wp_capabilities', true);
       // echo $user_role;
        $path_meta = get_user_meta($user_id, 'caminho_selecionado', true);
        
        if(!$path_meta){
            $path_meta = get_user_meta($user_id, 'vocacao_inicial', true);
        }

        // Buscar resultados dos quizzes
        $quiz_results = get_candidate_results($user_id, $quiz_ids);
        if (empty($quiz_results)) {
            $final_results[] = [
                'user_id' => $user_id,
                'name' => get_user_meta($user_id, 'nome', true) . ' ' . get_user_meta($user_id, 'sobrenome', true),
                'final_score' => '-',
                'additional_score' => 0
            ];
            continue;
        }

        // Recalcular a pontuação
        $recalculated_score = recalculate_score($quiz_results, $path_meta);

        // Buscar resultado do quiz de ID 3
        $additional_result = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM wp_mlw_results WHERE user = %d AND quiz_id = 3", 
            $user_id
        ));
        
        if ($additional_result) {
            $additional_score = $additional_result->point_score;
            $final_score = adjust_technical_score($recalculated_score, $additional_score);
        } else {
            $final_score = $recalculated_score;
            $additional_score = 0;
        }
        
        $final_results[] = [
            'user_id' => $user_id,
            'name' => get_user_meta($user_id, 'nome', true) . ' ' . get_user_meta($user_id, 'sobrenome', true),
            'final_score' => (float) $final_score,
            'additional_score' => $additional_score
        ];
    }

    // Ordenar resultados finais
    usort($final_results, function($a, $b) {
        return $b['final_score'] <=> $a['final_score'];
    });
    
    // Gerar tabela HTML
   $output = '<table style="border-collapse: collapse; width: 100%;">';
    $output .= '<tr>
                  <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">Posição</th>
                  <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">Nome</th>
                  <!-- <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">Pontuação</th> -->
                  <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">Status</th>
                </tr>';
    foreach ($final_results as $index=>$result) {
        $position = $index + 1;
        $formatted_score = $result['final_score'] === '-' ? '-' : number_format($result['final_score'], 2, '.', '');
        $output .= "<tr>
                      <td style='border: 1px solid #ddd; padding: 12px;'>{$position}</td>
                      <td style='border: 1px solid #ddd; padding: 12px; text-transform: uppercase;'>{$result['name']}</td>
                      <!-- <td style='border: 1px solid #ddd; padding: 12px;'>{$formatted_score}</td> -->
                      <td style='border: 1px solid #ddd; padding: 12px; color: green;'>APROVADA</td>
                    </tr>";
    }
    $output .= '</table>';

    return $output;
}

add_shortcode('correct_scores', 'correct_candidate_scores');