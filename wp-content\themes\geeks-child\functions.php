<?php
function geeks_child_enqueue_styles(){
    $parent_style = 'geeks';
    
    wp_enqueue_style($parent_style, get_template_directory_uri() . '/style.css');
    
    wp_enqueue_style('child-stile',
    get_stylesheet_directory_uri() . '/style.css', array($parent_style), wp_get_theme()->get('Version'));
}

add_action('wp_enqueue_scripts', 'geeks_child_enqueue_styles');



/*
*
*
*
CUSTOM FUNCTIONS DBS
*
*
*/

// Include the Tutor LMS Course Automation system (modular structure).
require_once get_stylesheet_directory() . '/tutor/course-automation-loader.php';

//
// Auth
//
require get_stylesheet_directory() . '/auth/utils.php';

//
// Templates
//
require get_stylesheet_directory() . '/templates/application_form.php';
require get_stylesheet_directory() . '/templates/vulnerable_woman_form.php';
require get_stylesheet_directory() . '/templates/carrer.php';


//
// Emails
//
require get_stylesheet_directory() . '/emails/complete_signup.php';
require get_stylesheet_directory() . '/emails/vocational_test_complete.php';
require get_stylesheet_directory() . '/emails/test_complete.php';


//
// Candidates
//
require get_stylesheet_directory() . '/candidates_counter.php';
require get_stylesheet_directory() . '/export_candidates.php';
require get_stylesheet_directory() . '/ranking.php';
require get_stylesheet_directory() . '/vocation.php';
require get_stylesheet_directory() . '/userListByPath.php';
require get_stylesheet_directory() . '/edit_profile.php';
require get_stylesheet_directory() . '/role_updater.php';

//
// Export Candidate Non-Students
//
require get_stylesheet_directory() . '/export_candidate_non_students.php';

//
// Batch Enroll Processor for Candidates
//
require get_stylesheet_directory() . '/batch_enroll_processor.php';

//
// Batch Survey Completer (Generic NPS entries)
//
require get_stylesheet_directory() . '/batch_survey_completer.php';

require get_stylesheet_directory() . '/batch_update_nps_course_ids.php';

//
// Export User NPS with Certificate IDs
//
require get_stylesheet_directory() . '/export_user_nps_certs.php';

//
// Quizzes
//
require get_stylesheet_directory() . '/quizzes/utils.php';


//
// Tutor
//
require get_stylesheet_directory() . '/tutor/utils.php';
require get_stylesheet_directory() . '/videos.php';
require get_stylesheet_directory() . '/templates/challenge.php';

//
// Chat
//
require get_stylesheet_directory() . '/chat.php';

//
// NPS
//
require get_stylesheet_directory() . '/nps.php';

// Aluno IA Signup Form
require get_stylesheet_directory() . '/aluno_ia_signup.php';

// function save_acf_to_user_meta( $user_id ) {
//     if ( !empty($_POST['acf']) ) {
//         $acf_data = $_POST['acf'];

//         foreach ( $acf_data as $field_key => $field_value ) {
//             update_user_meta( $user_id, $field_key, $field_value );
//         }
//     }
// }
// add_action('acf/save_post', 'save_acf_to_user_meta', 20);

// Adicionar imagem padrão de usuário - Gravatar
add_filter( 'avatar_defaults', 'custom_gravatar' );

function custom_gravatar( $avatar_defaults ) {

  $custom_gravatar = 'https://cursos.institutopanapana.org.br/ugyrgems/2023/09/usuario-padrao-panapana-educa-100.jpg';

  $avatar_defaults[$custom_gravatar] = 'Meu Gravatar'; 

  return $avatar_defaults;

}

// Capturar o IP no momento do login e salvar no user_meta
add_action('wp_login', function($user_login, $user) {
    $ip_address = $_SERVER['REMOTE_ADDR'];
    update_user_meta($user->ID, 'last_login_ip', $ip_address);
}, 10, 2);


// --- Add Conditional Course Buttons --- //

// add_action('tutor_course/single/entry/after', 'panapana_add_conditional_course_buttons', 15); // Use a hook that fires after the main entry box
// add_action('tutor_course/single/content/after_content', 'panapana_add_conditional_course_buttons', 15); // Trying a different hook
// add_action('wp_footer', 'panapana_add_conditional_course_buttons', 99); // Testing universal hook
add_action('wp_print_footer_scripts', 'panapana_add_conditional_course_buttons', 20); // Trying a late general hook

function panapana_add_conditional_course_buttons() {
    // Restore function body from Step 4 / https://sourcegraph.com/github.com/sourcegraph/cursor/-/blob/experimental/modules/cody-shared/src/chat/recipes/fixup.ts?L139#L314
    // including the initial FUNCTION CALLED log and other debug logs
    // error_log('[Panapana Debug Course Page] FUNCTION CALLED via wp_print_footer_scripts'); // REMOVED DEBUG
    global $post;
    if (!$post || !is_singular(tutor()->course_post_type)) { // Ensure it's a single course page
        // error_log('[Panapana Debug] Not a single course page or post object invalid.'); // REMOVED DEBUG
        return; // Exit if not a single course page
    }

    // error_log('[Panapana Debug Course Page] Is single course page, proceeding...'); // REMOVED DEBUG

    $course_id = get_the_ID();
    $user_id   = get_current_user_id();

    // Only run for logged-in, enrolled users
    if ( !$user_id || !tutor_utils()->is_enrolled($course_id, $user_id) ) {
         // error_log('[Panapana Debug] User not logged in or not enrolled. User ID: ' . $user_id); // REMOVED DEBUG
        return;
    }

    // error_log('[Panapana Debug Course Page] User enrolled, proceeding...'); // REMOVED DEBUG

    // Get data
    $course_progress_float = tutor_utils()->get_course_completed_percent($course_id, $user_id);
    $course_progress_int   = (int) $course_progress_float; // Use integer for comparison
    $is_completed          = ($course_progress_int >= 100);
    $survey_completed      = (bool) get_user_meta($user_id, 'certificate_survey_completed', true); // Cast to boolean

    // error_log('[Panapana Debug Course Page] State Check | Course ID: ' . $course_id . ' | User ID: ' . $user_id . ' | Progress Int: ' . $course_progress_int . ' | Is Completed: ' . ($is_completed ? 'Yes' : 'No') . ' | Survey Done: ' . ($survey_completed ? 'Yes' : 'No')); // REMOVED DEBUG

    // Prepare links
    $survey_page_link   = add_query_arg(array(
                            'course_id' => $course_id,
                            'return_url' => urlencode(get_permalink($course_id)) // Link back to course page
                         ), '/pesquisa-de-satisfacao/'); // Use get_permalink instead of tutor()->current_url for consistency
    $certificate_url       = tutor_utils()->get_certificate_view_url($course_id);
    $certificate_page_link = tutor_utils()->get_tutor_dashboard_page_url('my-certificates'); // Fallback dashboard link

    // --- Always generate Resume Button HTML --- 
    $resume_button_html = ''; // Initialize as empty
    if (function_exists('tutor_course_resume_button')) {
         $resume_button_html = tutor_course_resume_button($course_id, $user_id, 'tutor-btn tutor-btn-outline-primary tutor-btn-block w-100', true); // Pass true to return html
    } else {
         // error_log('[Panapana Debug Course Page] Warning: tutor_course_resume_button function not found. Resume button cannot be generated.'); // REMOVED DEBUG
         // Optional: Fallback link generation could go here if needed
    }

    // --- Button Output Logic --- 
    $output_buffer = '';

    ob_start(); // Start output buffering to capture button HTML

    // Always output Resume button if available
    if ($resume_button_html) {
         // error_log('[Panapana Debug Course Page] Logic: Preparing Resume button (always shown).'); // REMOVED DEBUG
         // Output the resume button potentially without extra wrapping if Tutor adds its own
         echo '<div class="tutor-mb-20 panapana-conditional-button panapana-resume-button">' . $resume_button_html . '</div>';
    }

    // Conditional buttons based on completion and survey status
    if ( $is_completed ) {
        // --- Course is 100% Complete ---
        // error_log('[Panapana Debug Course Page] Logic: Executing 100% Complete block.'); // REMOVED DEBUG
        if ( $survey_completed ) {
            // Survey DONE -> Show Certificate Button
            // error_log('[Panapana Debug Course Page] Logic: Survey complete, preparing Cert button.'); // REMOVED DEBUG
            $target_cert_url = $certificate_url ? $certificate_url : $certificate_page_link; // Use direct link if available, else fallback
            if ($target_cert_url) {
                ?>
                <div class="tutor-mb-20 panapana-conditional-button panapana-view-cert-button"> <?php // Added specific class ?>
                    <a href="<?php echo esc_url($target_cert_url); ?>" class="tutor-btn tutor-btn-primary tutor-btn-block">
                        <?php echo esc_html__('Ver Certificado', 'geeks-child'); ?>
                    </a>
                </div>
                <?php
            }
        } else { // SURVEY NOT DONE, BUT COURSE IS COMPLETE
            // Survey NOT DONE -> Show Survey Button
            // error_log('[Panapana Debug Course Page] Logic: Course 100% AND Survey NOT complete, preparing Survey button.'); // REMOVED DEBUG
            ?>
             <div class="tutor-mb-20 panapana-conditional-button panapana-survey-button"> <?php // Added specific class ?>
                 <a href="<?php echo esc_url($survey_page_link); ?>" class="tutor-btn tutor-btn-outline-primary tutor-btn-block w-100">
                     <?php echo esc_html__('Concluir Curso e gerar Certificado', 'geeks-child'); ?>
                 </a>
             </div>
             <?php
        }
    } 
    // NO else block needed here anymore, as the resume button is handled above
    // else {
        // --- Course is NOT 100% Complete --- 
        // error_log('[Panapana Debug Course Page] Logic: Executing <100% Complete block. Progress: ' . $course_progress_int); // REMOVED DEBUG
        // Resume button logic was moved outside/above
    // }

    $output_buffer = ob_get_clean(); // Get buffered button HTML

    // --- CSS to Hide Original Buttons ---
    $css_to_hide = "
        /* Hide default Tutor/Geeks buttons */
        /* Default Complete form in sidebar */
        .tutor-course-sidebar .tutor-course-status-wrap .tutor-course-compelte-form-wrap,
        /* Default Continue button inside the button group - KEEPING THIS VISIBLE NOW */
        /* .tutor-lead-info-btn-group .tutor-course-entry-button, */
         /* Default Cert button appearing AFTER the main card */
         div.card.mb-4 + a.tutor-btn-view-certificate,
         /* Any other stray cert buttons */
         .tutor-course-sidebar a.tutor-btn-view-certificate,
         .tutor-course-entry-content a.tutor-btn-view-certificate
        {
             display: none !important;
        }
    ";

    // Inject buttons and CSS using JavaScript if hooked late, otherwise CSS might not apply correctly
    if (!empty($output_buffer)) {
         // Use JS to inject the HTML and CSS reliably at the end of the body
         $button_html_escaped = json_encode($output_buffer);
         $css_escaped = json_encode($css_to_hide);

         echo "<script type='text/javascript'>
            document.addEventListener('DOMContentLoaded', function() {
                // Inject CSS
                var style = document.createElement('style');
                style.type = 'text/css';
                style.id = 'panapana-conditional-buttons-css';
                style.appendChild(document.createTextNode($css_escaped));
                document.head.appendChild(style);

                // Inject Buttons - Find the main card container
                var mainCardContainer = document.querySelector('div.card.mb-4'); 
                // Find the specific button group div inside the main card
                var buttonGroupContainer = mainCardContainer ? mainCardContainer.querySelector('.tutor-lead-info-btn-group') : null;
                
                if (mainCardContainer) {
                    var buttonWrapper = document.createElement('div');
                    buttonWrapper.className = 'panapana-custom-buttons-wrapper'; // Removed tutor-mt-32, added below
                    buttonWrapper.innerHTML = $button_html_escaped;
                    
                    // Preferentially insert AFTER the button group div if found
                    if (buttonGroupContainer) {
                         buttonWrapper.classList.add('card-body'); // Add card-body class for padding
                         buttonGroupContainer.parentNode.insertBefore(buttonWrapper, buttonGroupContainer.nextSibling);
                         console.log('[Panapana Debug Course Page] JS: Injected buttons AFTER button group.'); 
                    } else {
                         // Fallback: append inside the main card container
                         buttonWrapper.classList.add('card-body'); // Add card-body class for padding
                         mainCardContainer.appendChild(buttonWrapper);
                         console.log('[Panapana Debug Course Page] JS: Injected buttons inside main card (fallback).'); 
                    }
                } else {
                     console.error('[Panapana Debug Course Page] JS: Could not find main card container (div.card.mb-4) to inject buttons.');
                 }
            });
         </script>";
         // error_log('[Panapana Debug Course Page] Outputting JS block (v2).'); // REMOVED DEBUG
    }

}

// --- End Conditional Course Buttons --- //


// --- Add Survey Button to Lesson/Quiz Header at 100% --- //

add_filter('tutor_topbar_complete_btn_html', 'panapana_add_survey_button_to_lesson_header', 10, 2);

function panapana_add_survey_button_to_lesson_header($button_html, $course_id) {
    // Only modify if the default button is empty or for specific conditions
    // We want to add our button specifically when the course is 100% but survey isn't done.

    $user_id = get_current_user_id();
    if (!$user_id) {
        return $button_html; // Should not happen here, but good practice
    }

    $course_progress  = tutor_utils()->get_course_completed_percent($course_id, $user_id);
    $survey_completed = get_user_meta($user_id, 'certificate_survey_completed', true);
    $is_survey_completed_bool = (bool) $survey_completed; // Ensure boolean

    // Condition: 100% completed AND survey is NOT yet completed
    if ($course_progress >= 100 && !$is_survey_completed_bool) {
        $survey_page_link = add_query_arg(array(
                                'course_id' => $course_id,
                                'return_url' => urlencode(get_permalink($course_id)) // Link back to main course page after survey
                             ), '/pesquisa-de-satisfacao/');

        // Return HTML for our survey button
        return '<a href="' . esc_url($survey_page_link) . '" class="tutor-btn tutor-btn-outline-primary tutor-button-course-survey-header">'. esc_html__('Concluir Curso e gerar Certificado', 'geeks-child') .'</a>';
    } 
    // If conditions not met, return the original button HTML (which might be empty or Tutor's default)
    return $button_html;
}

// --- End Survey Button to Lesson/Quiz Header --- //

// --- Add Survey Button After Lesson/Quiz Content at 100% --- //

// Remove old action hooks
// add_action('tutor_lesson_content_after', 'panapana_add_survey_button_after_lesson_content', 9);
// add_action('tutor_assignment_content_after', 'panapana_add_survey_button_after_lesson_content', 9);
// add_action('tutor_quiz_content_after', 'panapana_add_survey_button_after_lesson_content', 9);

// Add new action hook using a late-firing general hook
add_action('wp_print_footer_scripts', 'panapana_add_survey_button_after_lesson_content', 20);

function panapana_add_survey_button_after_lesson_content() {
    // Check if it's a single lesson, assignment, or quiz page
    if (!is_singular(array(tutor()->lesson_post_type, 'assignments', 'tutor_quiz'))) {
        return;
    }

    $user_id = get_current_user_id();
    if (!$user_id) {
        return;
    }

    $content_id = get_the_ID(); // Get ID of current lesson/quiz/assignment
    $course_id = tutor_utils()->get_course_id_by_subcontent($content_id);
    if (!$course_id) {
        return;
    }

    $course_progress = tutor_utils()->get_course_completed_percent($course_id, $user_id);
    $survey_completed = get_user_meta($user_id, 'certificate_survey_completed', true);
    $is_survey_completed_bool = (bool) $survey_completed;

    // Condition: 100% completed AND survey is NOT yet completed
    if ($course_progress >= 100 && !$is_survey_completed_bool) {
        // error_log('[Panapana Debug Lesson/Quiz Footer Button] Conditions met for Course ID: ' . $course_id . ' | User ID: ' . $user_id); // REMOVED DEBUG

        $survey_page_link = add_query_arg(array(
                                'course_id' => $course_id,
                                'return_url' => urlencode(get_permalink($course_id)) // Link back to main course page after survey
                             ), site_url('/pesquisa-de-satisfacao/')); // Use site_url() for robustness

        // Prepare the button HTML (similar structure to the working one)
        ob_start();
        ?>
        <div class="tutor-lesson-survey-button-wrap tutor-mt-32 tutor-mb-32 panapana-conditional-button panapana-survey-button-lesson" style="text-align: center;"> <?php // Added specific class ?>
            <a href="<?php echo esc_url($survey_page_link); ?>" class="tutor-btn tutor-btn-primary tutor-button-course-survey-content"> <?php // Use primary style, same class as before is fine ?>
                <?php echo esc_html__('Concluir Curso e gerar Certificado', 'geeks-child'); ?>
            </a>
        </div>
        <?php
        $button_html = ob_get_clean();
        $button_html_escaped = json_encode($button_html);

        // Output JavaScript to inject the button
        echo "<script type='text/javascript'>
            document.addEventListener('DOMContentLoaded', function() {
                var targetContainer = document.querySelector('.tutor-lesson-content-area'); // Target the main content area
                if (targetContainer) {
                    // Create a temporary div to parse the HTML string
                    var tempDiv = document.createElement('div');
                    tempDiv.innerHTML = $button_html_escaped.slice(1, -1).replace(/\\\"/g, '\"'); // Decode JSON string
                    
                    // Append the actual button container element
                    if (tempDiv.firstElementChild) {
                         targetContainer.appendChild(tempDiv.firstElementChild);
                         console.log('[Panapana Debug Lesson/Quiz Footer Button] JS: Injected button into .tutor-lesson-content-area.'); // JS DEBUG LINE
                    } else {
                         console.error('[Panapana Debug Lesson/Quiz Footer Button] JS: Could not parse button HTML.'); // JS DEBUG LINE
                    }
                } else {
                    console.error('[Panapana Debug Lesson/Quiz Footer Button] JS: Could not find target container (.tutor-lesson-content-area).'); // JS DEBUG LINE
                }
            });
         </script>";
         // error_log('[Panapana Debug Lesson/Quiz Footer Button] Outputting JS block.'); // REMOVED DEBUG

    } else {
         // error_log('[Panapana Debug Lesson/Quiz Footer Button] Conditions NOT met. Progress: ' . $course_progress . ' | Survey Done: ' . ($is_survey_completed_bool ? 'Yes' : 'No')); // REMOVED DEBUG
    }
}

// --- End Survey Button After Lesson/Quiz Content --- //

// Modify Tutor LMS Lost Password URL
add_filter( 'tutor_lostpassword_url', 'panapana_custom_tutor_lostpassword_url', 20 ); // Use priority 20 to be safe
function panapana_custom_tutor_lostpassword_url( $url ) {
    // Return the desired static URL
    return 'https://cursos.institutopanapana.org.br/password-reset/';
}

// Função para mostrar os cerficados com link específico
function panapana_certificate_rewrite_rules() {
    add_rewrite_tag('%certificate_hash%', '([^&]+)'); // Allows 'certificate_hash' to be a query var
    add_rewrite_rule(
        '^cert_hash=([^/]+)/?$', // Matches URLs like /cert_hash=SOME_HASH
        'index.php?pagename=visualizar-certificado&certificate_hash=$matches[1]', // Routes to your page and passes the hash
        'top'
    );
}
add_action('init', 'panapana_certificate_rewrite_rules');

function panapana_certificate_query_vars($vars) {
    $vars[] = 'certificate_hash'; // Make WordPress aware of 'certificate_hash'
    return $vars;
}
add_filter('query_vars', 'panapana_certificate_query_vars');

