# WordPress Course Creator - Git Ignore Rules

# EXCLUDE ENTIRE WORDPRESS INSTALLATION
/public/

# WordPress Core Files (if not using /public/ exclusion)
/wp-admin/
/wp-includes/
/wp-content/index.php
/wp-content/languages/
/wp-content/upgrade/
/wp-*.php
/xmlrpc.php
/readme.html
/license.txt

# WordPress Configuration
wp-config.php
wp-config-sample.php
.htaccess

# WordPress Uploads (user content)
/wp-content/uploads/
/wp-content/blogs.dir/

# WordPress Cache & Optimization
/wp-content/cache/
/wp-content/w3tc-config/
/wp-content/advanced-cache.php
/wp-content/wp-cache-config.php
/wp-content/object-cache.php

# Third-party Plugins (exclude custom plugins)
/wp-content/plugins/
# Include custom plugins by uncommenting and specifying:
# !/wp-content/plugins/my-custom-plugin/

# Third-party Themes (exclude our child theme)
/wp-content/themes/*/
# Include our child theme
!/wp-content/themes/geeks-child/

# WordPress Updates & Backups
/wp-content/updraft/
/wp-content/backups/
*.sql
*.sql.gz
*.sql.bz2

# Development & IDE Files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Node.js & NPM (if using build tools)
node_modules/
npm-debug.log
yarn-error.log
package-lock.json
yarn.lock

# PHP & Composer
vendor/
composer.lock
.env

# Logs
*.log
/logs/
error_log
debug.log

# Temporary Files
*.tmp
*.temp
*.bak
*.backup

# Database Files (exclude from repo)
*.sql
*.db
*.sqlite
*.sqlite3

# Environment Specific
local-config.php
staging-config.php
production-config.php

# Security
.htpasswd
.htaccess.bak

# WordPress CLI
wp-cli.local.yml
wp-cli.yml

# Deployment Scripts (if sensitive)
deploy-config.php

# OS Generated Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
desktop.ini

# Linux
*~

# Archives
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Exceptions - Files we DO want to track
!.gitkeep
!README.md
!.gitignore

# Custom Exceptions for this project
!/backlog/
!/docs/
!/scripts/
!/tests/
!/database/schema/
!/database/migrations/
