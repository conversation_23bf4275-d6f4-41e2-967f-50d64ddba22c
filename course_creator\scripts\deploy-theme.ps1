# Deploy Theme Script - Fixed Version
param(
    [string]$WordPressPath = "public"
)

Write-Host "Deploying Course Creator Theme..." -ForegroundColor Green

# Check if WordPress path exists
if (-not (Test-Path $WordPressPath)) {
    Write-Host "WordPress path not found: $WordPressPath" -ForegroundColor Red
    exit 1
}

# Check if theme source exists
if (-not (Test-Path "themes\geeks-child")) {
    Write-Host "Theme source not found: themes\geeks-child" -ForegroundColor Red
    exit 1
}

# Create destination directory if it doesn't exist
$DestPath = "$WordPressPath\wp-content\themes\geeks-child"
if (-not (Test-Path $DestPath)) {
    New-Item -ItemType Directory -Path $DestPath -Force | Out-Null
    Write-Host "Created theme directory: $DestPath" -ForegroundColor Yellow
}

# Copy theme files
try {
    Copy-Item "themes\geeks-child\*" -Destination $DestPath -Recurse -Force
    Write-Host "Theme deployed successfully!" -ForegroundColor Green
    Write-Host "Location: $DestPath" -ForegroundColor Cyan
} catch {
    Write-Host "Failed to deploy theme: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Verify deployment
$CoreFiles = @(
    "$DestPath\functions.php",
    "$DestPath\style.css",
    "$DestPath\tutor\course-automation-loader.php"
)

$MissingFiles = @()
foreach ($file in $CoreFiles) {
    if (-not (Test-Path $file)) {
        $MissingFiles += $file
    }
}

if ($MissingFiles.Count -gt 0) {
    Write-Host "Warning: Some core files are missing:" -ForegroundColor Yellow
    $MissingFiles | ForEach-Object { Write-Host "   - $_" -ForegroundColor Yellow }
} else {
    Write-Host "All core files verified!" -ForegroundColor Green
}

Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Cyan
Write-Host "1. Activate Geeks Child theme in WordPress admin" -ForegroundColor White
Write-Host "2. Test shortcodes: panapana_master_debug" -ForegroundColor White
Write-Host "3. Upload CSV using: panapana_csv_course_creator" -ForegroundColor White
