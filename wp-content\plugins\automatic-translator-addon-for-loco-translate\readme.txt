=== Automatic Translate Addon For Loco Translate ===
Contributors: narinder-singh, satindersingh, coolplugins
Donate link: https://paypal.me/CoolPlugins/10USD/
Tags:ai translation, google translate, translate, chatgpt, multilingual
Requires at least:5.0
Tested up to: 6.8
Requires PHP: 7.2
Loco Translate Tested Up to: 2.7.2
Stable tag: 2.4.9
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

The Automatic Translate Addon for Loco Translate is a powerful tool for developers looking to quickly translate their WordPress plugins and themes.

== Description ==

### Automatic Translate Addon For Loco Translate

The Automatic Translate Addon for Loco Translate is a powerful tool for website owners and developers looking to quickly **translate** their WordPress **plugins and themes**.

The plugin integrates with the **Loco Translate** plugin to provide a seamless experience for users. The addon uses **machine translation** to **automatically translate** the plugin and theme strings into the desired language, **saving time and effort** for website owners who need to quickly translate their content into multiple languages.

[How It Works ⇗](https://locoaddon.com/how-it-works/?utm_source=atlt_plugin&utm_medium=readme&utm_campaign=get_pro&utm_content=how_it_works) | [Buy Pro ⇗](https://locoaddon.com/pricing/?utm_source=atlt_plugin&utm_medium=readme&utm_campaign=get_pro&utm_content=buy_pro)

https://www.youtube.com/watch?v=X_1RbSVgnOc


Install this plugin along with the famous **[Loco Translate](https://wordpress.org/plugins/loco-translate/)** plugin (**1 million+ Active Installations**) and automatically machine translates any WordPress plugin or theme translation files into any language.
The Automatic Translate Addon for Loco Translate is a must-have tool for anyone looking for a seamless, efficient, and high-quality solution to translate their WordPress plugins and themes.

### 🤖 No Translate API Required! + Unlimited Translations!

* Yes, it's true, there is no requirement to use any paid translate API key to use this addon for automatic translations, just install it and translate unlimited characters with one click.
* Free version of this addon supports **[Yandex Page Translate Widget]**. By using this translate widget you can unlimitedly translate any plugin or theme translatable plain text strings.
* Pro version provides support for the Google Translate widget, DeepL Doc Translator, and AI translation resources like ChatGPT, Gemini AI, Chrome's built-in AI. You can also use them unlimited times without any paid API key. DeepL Translate provides a limited number of free doc translations daily. It is better than Google, Yandex, or any other machine translation service provider  - **[read a review by TechCrunch](https://techcrunch.com/2017/08/29/deepl-schools-other-online-translators-with-clever-machine-learning/)**

### ⚡ Loco Addon Features

* One-click translate any plugin or theme for all translatable strings. It will only translate plain text strings means if a string will contain HTML or a special character then it will be skipped from automatic translations.
* You can automatically translate **unlimited characters** free of cost without any paid API key. You just need to install this addon and click on the translate button to translate any plugin or theme strings.
* After auto translations, you can manually edit any machine-translated string inside Loco's built-in editor.

> “If you spend too much time thinking about a thing, you'll never get it done. Stop wasting time, complete work smartly & quickly!”

https://www.youtube.com/watch?si=3NfBYELlkhtwnbGL&v=WrM23QBsEs4&feature=youtu.be

https://youtu.be/yNuOVKfwFcE?si=4hQResLF76ntAvzb

https://youtu.be/6k8K6ZU2Rvo?si=UdiJr0n5zTwaDfXR

The Automatic Translate Addon for Loco Translate now includes powerful AI translation options: **ChatGPT, Gemini AI, and Chrome Built-in AI**. These integrations provide advanced, high-quality AI translations for your WordPress plugins and themes, using the latest AI technology.


**Key Benefits**

* **Chrome AI Translation:** We have added the feature to translate plugins or themes using Chrome's built-in AI. This feature enables real-time AI translations directly within the browser, without any paid API services. This means you can easily translate your website content without additional setup, saving time and costs.
* **ChatGPT Translation:** ChatGPT brings conversational AI capabilities to your translation needs. It understands context better and provides more accurate translations, especially for complex phrases and sentences. This ensures your translated content is both natural and coherent.

* **Gemini AI Translation:** Gemini AI offers state-of-the-art translation quality, known for its accuracy and fluency. It uses deep learning algorithms to deliver AI translations that are not only accurate but also contextually relevant, making it ideal for professional-grade translation tasks

* **Enhanced Accuracy:** Both ChatGPT and Gemini provide translations with improved accuracy and context understanding.
* **Natural Language:** The AI models generate translations that sound natural and human-like.
* **Time-Saving:** Automate your translation process with high efficiency and reliability.

### 🌟 FREE v/s Premium Addon Comparison

**Free License**

* ✅ **Yandex Translate Widget Support:** Available
* ✅ **Unlimited Free Translations:** Available (Only via Yandex)
* ✅ **API Key Required:** Not Required (Yandex Widget Support)
* ❌ **Google Translate Widget Support:** Not Available
* ❌ **DeepL Doc Translator Support:** Not Available
* ❌ **Premium Support:** Not Available
* ❌ **ChatGPT Translation Support:** Not Available
* ❌ **Gemini Translation Support:** Not Available

**Premium License**

* ✅ **Chrome Built-in AI Translation Support:** Available
* ✅ **ChatGPT Translation Support:** Available
* ✅ **Gemini Translation Support:** Available
* ✅ **Google Translate Widget Support:** Available
* ✅ **Unlimited Free Translations:** Available
* ✅ **API Key Required:** Not Required
(Chrome Built-in AI, Yandex, Google & DeepL Support)
* ✅ **DeepL Doc Translator Support:** Available
(Limited free docs translations/day or purchase DeepL Pro)

* ✅ **Premium Support:** [Quick Support](https://coolplugins.net/support)
(**Support time:- 24-48 hours**)
* **Premium License:** [Buy Now 🛒](https://locoaddon.com/pricing/?utm_source=atlt_plugin&utm_medium=readme&utm_campaign=get_pro&utm_content=buy_now)

> DeepL Doc Translator provides a limited number of free docs translations per day, you can purchase their pro license to increase this limit.

> “Many people make the mistake of saving money by wasting time.”


In addition to its efficiency and ease of use, the Automatic Translate Addon also provides high-quality translations. Machine translation technology is constantly improving and is able to provide accurate translations for the most common use cases. This makes it a reliable solution for website owners who need to translate their content quickly, without sacrificing quality.

**❗ Important Notice:**  This addon only provides settings to use third-party auto-translation widgets and services(Yandex, Google, DeepL, Gemini, etc.), it does not provide any translation service. So we don’t guarantee 100% correctness, please check all translated text carefully before making it live on your production site. If any auto-translate provider stops providing auto-translation widgets or services in the future, in that case, the plugin will not support that translation provider.

> **Important Notice:-** This plugin works as an auto-translate addon for the Loco Translate official plugin latest version(2.4.4 or higher).

Check out our new [Automatic Translations For Polylang](https://coolplugins.net/product/automatic-translations-for-polylang/?utm_source=atlt_plugin&utm_medium=readme&utm_campaign=atfp_get_pro&utm_content=check_out)

[Automatic Translations For Polylang](https://coolplugins.net/product/automatic-translations-for-polylang/?utm_source=atlt_plugin&utm_medium=readme&utm_campaign=atfp_get_pro&utm_content=check_out)  is a powerful addon that streamlines the process of translating WordPress content. By automatically copying and translating posts, including titles, content, images, and meta fields, this plugin helps you efficiently manage multilingual websites. Compatible with Gutenberg and popular third-party blocks, it preserves all styles and formats, ensuring a consistent look across languages. 

Check out our [Automatic Translate Addon For TranslatePress](https://coolplugins.net/product/automatic-translate-addon-for-translatepress-pro/?utm_source=atlt_plugin&utm_medium=readme&utm_campaign=tpa_get_pro&utm_content=check_out)

Check out our [Automatic Translate Addon For TranslatePress](https://coolplugins.net/product/automatic-translate-addon-for-translatepress-pro/?utm_source=atlt_plugin&utm_medium=readme&utm_campaign=tpa_get_pro&utm_content=check_out) helps you to translate entire page content with one click. You don’t need to translate page content manually one by one.


### 😎 Who's Behind

This plugin is not developed by or affiliated with the "**Loco Translate**" official plugin. It is a third-party addon that provides automatic machine translations to quickly translate your theme or plugin language files.

We(**CoolPlugins.net**) only manage [locoaddon.com](https://locoaddon.com/?utm_source=atlt_plugin&utm_medium=readme&utm_campaign=plugin_page&utm_content=addon_website) (**addon website**), Automatic Translate Addon For Loco Translate(**free plugin**) and its premium version. **[Cool Plugins](https://coolplugins.net/?utm_source=atlt_plugin&utm_medium=readme&utm_campaign=author_page)** is a team of experienced WordPress plugin developers.

* 7+ years of WordPress plugin development experience.
* 20+ free and premium WordPress plugins released.
* 400000+ plugin downloads.
* 70K+ active websites are using our plugins.

> We provide cool solutions to remove famous plugin limitations!

### ✍ Special THANKS!

Special thanks to famous **[Loco Translate](https://wordpress.org/plugins/loco-translate/)** plugin author **Tim Whitlock** for creating an awesome plugin for translations and also thanks to Yandex for providing a translate widget for websites.

All automatic translations will be machine translations, powered by third-party auto-translate providers(Google, Yandex, DeepL, Gemini, ChatGPT or Chrome built-in AI), so we don’t guarantee 100% correctness, please check all translated text carefully before making it live on your production site.


### 🌴 Important Links & Information

* [Yandex Translate Terms](https://yandex.com/legal/translate_termsofuse/)
* [Yandex Privacy Policy](https://yandex.com/legal/confidential/)
* [Google Translate Data Usage Policy](https://cloud.google.com/translate/data-usage)
* [DeepL Privacy Policy](https://www.deepl.com/en/privacy/)
* **DeepL Translate Supports 24 languages**:- English, German, French, Spanish, Portuguese, Portuguese (Brazilian), Italian, Dutch, Polish, Russian, Japanese, Chinese (simplified), Italian, bulgarian, Czech, Danish, Greek, hungarian, Slovak, Swedish, Norwegian Bokmål, Korean, Turkish & Ukrainian
[](http://coderisk.com/wp/plugin/automatic-translator-addon-for-loco-translate/RIPS-FN0AdXlllg)

== Installation ==

1. Install **Automatic Translate Addon For Loco Translate** from the WordPress.org repository or by uploading a plugin-zip unzipped folder to the **/wp-content/plugins** directory. You must also install **[Loco Translate](https://wordpress.org/plugins/loco-translate/)** free plugins if you want to use this addon.

2. Activate the plugin through **Plugins >> Installed Plugin** menu in WordPress

4. Now edit any plugin or theme language file using the Loco build-in editor, you will find an auto translator button at the top to quickly translate all translatable strings with one click using Yandex Translate Widget.

5. If you want to use Google Translate Widget, DeepL Doc Translator, ChatGPT or Gemini for translation then you need to purchase [premium license key](https://locoaddon.com/addon/loco-automatic-translate-premium-license-key/). The pro version also provides better translations using DeepL, Google and AI's advanced machine translation technology.

== Frequently Asked Questions ==

= Can this plugin automatically translate page content ❔ =
 No, this plugin only translates plugins and themes strings automatically. To translate page content automatically, you can try the 
[Automatic Translate Addon For TranslatePress](https://coolplugins.net/product/automatic-translate-addon-for-translatepress-pro/?utm_source=atlt_plugin&utm_medium=readme&utm_campaign=tpa_get_pro&utm_content=faq)
Or
[Automatic Translations For Polylang](https://coolplugins.net/product/automatic-translations-for-polylang/?utm_source=atlt_plugin&utm_medium=readme&utm_campaign=atfp_get_pro&utm_content=faq)

= How it works❔ =
This plugin works as an add-on for **Loco Translate** official plugin. First, you need to install and activate the free version of "Loco Translate" then install this addon and use one-click machine translations (supported by Yandex Translate Widget).

= Are you using any language translation API❔ =

No, there is no requirement for any paid translate API key to use this plugin free or pro version. It uses free page translation widgets and services for unlimited automatic translations. But to use Gemini for translation get free API key.
Free license users can use **[Yandex Translate Widget] but if you are using a premium license then you can also use Google Chrome AI, Google Translate Widget, DeepL Doc Translator, ChatGPT, and Gemini**.
[get free API key](https://ai.google.dev/gemini-api/docs/api-key)


= Is there any translation limit❔ =
There is no limit. Yes, you can translate unlimited characters without any paid API key.

**Free license** users can translate unlimited characters only via the Yandex Page Translate Widget.

**Premium license** Premium users can use Google, DeepL, ChatGPT, Chrome built-in AI, and Gemini AI for translations, with no character limits! Plus, priority support ensures you get help fast if you need it.


> This addon only provides an interface to use third-party translation widgets and services, it does not provide any translation service. Any auto-translate provider can stop providing free translation widgets or services anytime in the future, in that case, the plugin will not support that auto-translate provider.

= What are the benefits of using ChatGPT for translations?❔ =
ChatGPT excels in understanding context and generating natural-sounding translations. It's particularly good at handling complex phrases and maintaining the intended tone and meaning of the original text.

= How does Gemini AI improve translation quality?❔ =
Gemini AI uses advanced deep learning techniques to provide translations that are highly accurate and contextually appropriate. It ensures that the translated text is fluent and professional.

= Do I need any API key to use Gemini AI translations?❔ =

Yes, to access Gemini AI translations, you will need to obtain Free API keys and this key can be entered in the plugin settings.

= Do I need any API key to use ChatGPT translations?❔ =
 No, you can use ChatGPT translations for free by visiting the [ChatGPT website](https://chatgpt.com/).

= How can this addon make my translations easier?
With one-click machine translations, this addon allows you to quickly and efficiently translate your plugins and themes, saving time and effort on multilingual projects!

= Will I get accurate translations?
Yes! With ChatGPT and Gemini AI in the premium version, translations are contextually accurate and natural, ideal for professional-grade results.

= What makes the premium version worth it?
Premium users can use Google Chrome AI ,Google, DeepL, ChatGPT, and Gemini AI for translations, with no character limits! Plus, priority support ensures you get help fast if you need it.

= Can I edit translations easily?
Absolutely! You can refine any translated strings in Loco Translate’s editor, allowing you to perfect your content’s tone and accuracy.

= Do I need to worry about translation limits?
No limits here! Free users can translate unlimited characters with Yandex, while premium users enjoy the same freedom with additional high-quality translation options.

= Is any technical setup required?
Not at all! Just install the addon alongside Loco Translate, and you’re ready to start translating. No API keys required for free users!

= Will my content’s original style and meaning be preserved?
Yes, both ChatGPT and Gemini AI are designed to maintain the original tone and meaning, resulting in natural, coherent translations that match your content perfectly.

= Can I rely on these translations for professional use?
Definitely! The advanced AI in ChatGPT and Gemini AI ensures that your translations are accurate and polished, ideal for professional multilingual sites.

= How can I report security bugs? =

You can report security bugs through the Patchstack Vulnerability Disclosure Program. The Patchstack team help validate, triage and handle any security vulnerabilities. [Report a security vulnerability.](https://patchstack.com/database/vdp/automatic-translator-addon-for-loco-translate)

== Screenshots ==

1. Automatic Translate (No paid API Required)
2. Translate Using Yandex Translate Widget
3. Free License v/s Premium License

== Changelog ==
<strong>Version 2.4.9 |16/04/2025 </strong>
<pre>
Updated: Review notice logo for improved branding.
Updated: Open AI video link.
Fixed: Compatibility conflict with the CMC plugin.
Tested up to: WordPress 6.8
</pre>
<strong>Version 2.4.8 |31/03/2025 </strong>
<pre>
Added: New Dashboard.
Fixed: Bug Fixes.
</pre>
<strong>Version 2.4.7 |27/02/2025 </strong>
<pre>
Added: Review Notice.
Fixed: Fixed Popup issue.
Fixed: Bug Fixes.
Improvements: Translator popup.
</pre>
<strong>Version 2.4.6 |27/01/2025 </strong>
<pre>
Fixed: Fixed string merging issue with Loco Translate 2.7 latest version.
Fixed: Fixed load textdomain issues
</pre>
<strong>Version 2.4.5 |09/10/2024 </strong>
<pre>
Improved: updated utm content in urls
Improved: updated pro features images and add css
Fixed: Fixed security issues
</pre>
<strong>Version 2.4.4 |21/08/2024 </strong>
<pre>
Tweaks:Textual changes in dashboard page and readme file.
</pre>
<strong>Version 2.4.3 |22/07/2024 </strong>
<pre>
Added:Gemini translation button inside popup.
Tested Upto: WordPress 6.6.
</pre>
<strong>Version 2.4.2 |01/05/2024 </strong>
<pre>
Added: Support for Portuguese(Brazil)
Added: CSS for RTL support
</pre>
<strong>Version 2.4.1 |07/02/2024 </strong>
<pre>
Fix: Minor bug fixes
</pre>
<strong>Version 2.4 |28/11/2023 </strong>
<pre>
Improved:Rewrite overall JS code.
Improved:Optimize styles.
Added:AI translation button inside popup.
Tweak:Minor Textual changes.
Improved:Overall code.
</pre>
<strong>Version 2.3.3 |18/05/2023 </strong>
<pre>
Tweak:Minor Textual changes
</pre>
<strong>Version 2.3.2 |24/03/2023 </strong>
<pre>
Tweak:Minor Textual changes
Improved: Tested upto WP version 6.2
Fixed: minor bug fixes
</pre>
<strong>Version 2.3.1 |02/05/2022 </strong>
<pre>
Fixed: scripts not loading in another language
</pre>
<strong>Version 2.3 | 29/04/2022 </strong>
<pre>
Improved: Overall Code improvements.
Removed: No need to activate the free version with PRO anymore.
Fixed: Large string translation merge bug.
</pre>

<strong>Version 2.2.3 | 05/10/2021 </strong>
<pre>
Fixed: License notice issue
</pre>
<strong>Version 2.2.2 | 27/04/2021 </strong>
<pre>
Fixed: Minor ID bug fixes
</pre>
<strong>Version 2.2 | 17/04/2021 </strong>
<pre>
Added: Updated Yandex new translation API
Fixed: unable to translate issue with Yandex
Improved: tested upto WordPress 5.7
</pre>

<strong>Version 2.1.3 | 11 DEC 2020 </strong>
<pre>
Fixed: Compatibility issues with  WordPress 5.6
Fixed: unable to save string issue with WordPress 5.6 version
</pre>
<strong>Version 2.1.2 | 21 NOV 2020</strong>
<pre>
Fixed: Compatibility issues with Loco Translate 2.4.6
</pre>
<strong>Version 2.1.1 | 27 OCT 2020</strong>
<pre>
Fixed: Compatibility issues with Loco Translate 2.4.4
</pre>

<strong>Version 2.1 | 14 OCT 2020</strong>
<pre>
Added: integrated new Auto Translation
Added: Added support for Loco Translate Latest version
Fixed: compatibility issues with Loco Translate 
Improved: removed Extra Code
Improved: improved custom.js
</pre>

<strong>Version 2.0.1 | 07 AUG 2020</strong>
<pre>
Notice Added: Addon is currently compatible with only Loco Translate official plugin version 2.4.0, the new compatible update will be available soon.
</pre>

<strong>Version 2.0 | 31 JULY 2020</strong>
<pre>
Improved: Addon is now compatible with only Loco Translate version 2.4.0
Added: Yandex Page Translate Widget (unlimited translations without API)
Removed: API settings panel (now you can use the plugin without the API key)
Removed: IBM Watson translator support
Removed: Loco Translate Older version notices
Removed: Test API settings
Removed: All APIs 
Improved: code improvements
Updated: Major JS changes
</pre>

<strong>Version 1.9.1 | 9 JULY 2020</strong>
<pre>
Fixed: Minor notice change
</pre>

<strong>Version 1.9 | 23 JUN 2020</strong>
<pre>
Fixed: 404 not found bug with IBM translate.
Fixed: Unable to disable review notice bug.
Updated: Updated settings panel and translate popup content.
Removed: HTML string translation support.
Added: Added some checks in strings.
Improved: Improved IBM translation process.
Improved: Minor JS improvements.
</pre>

<strong>Version 1.8 | 12 JUN 2020</strong>
<pre>
Added: IBM Watson Translator API support.
Added: Notice! Yandex Translate API v1 is deprecated.
Fixed: Bug fixes.
Improved: Endpoint updates.
Improved: Code improvements.
</pre>

<strong>Version 1.7.5 | 27 MAY 2020</strong>
<pre>
Fixed: Notice bug fixed.
Updated: Minor settings updates.
</pre>

<strong>Version 1.7.3 | 15 MAY 2020</strong>
<pre>
Updates: Minor textual changes.
</pre>

<strong>Version 1.7.1 | 28 APR 2020</strong>
<pre>
Fixed: Index per request not working issue.
Updated: Updated new logo and all assets.
</pre>

<strong>Version 1.7 | 13 FEB 2020</strong>
<pre>
Added: Added Chinese languages support.
Added: Supported singular and plural string translation.
Added: Added admin notices.
Added: Added support for the major language.
Added: Added  Punjabi, Kannada, Irish, and Malayalam language support in Microsoft Translator.
Fixed: % s and s % space after special characters problem.
Fixed: Wrong index updates with multiple strings.
Improved: Improved string translation.
Improved: Overall code improvements.
Improved: Optimized settings panel.
Updated: Textual changes.
</pre>

<strong>Version 1.6 | 27 JAN 2020</strong>
<pre>
Added: Integrated Microsoft Translation API support.
Added: Plain text translation support %s,%d placeholders.
Added: Integrated API testing button in the settings panel.
Added: Added error handling messages.
Added: Integrated sweet alert popup.
Added: Supported %s,%d placeholder strings in Google Translate.
Added: Microsoft translation settings.
Improved: Review notice and updated wrong spelling.
</pre>

<strong>Version 1.5 | 14 JAN 2020</strong>
<pre>
Added: Integrated Google Translation API support.
Added: Integrated reset translation strings button.
Added: Integrated characters available characters limit in translation popup.
Added: Google API settings in the settings panel.
Added: Norwegian language support.
Improved: Translation popup.
Improved: Code Improvements.
Improved: Deactivation popup.
Improved: Textual changes.
Fixed: Minor bugs.
</pre>

<strong>Version 1.4.1 | 8 JAN 2020</strong>
<pre>
Fixed: Minor JS bug fixes.
</pre>

<strong>Version 1.4 | 31 DEC 2019</strong>
<pre>
Added: Integrated HTML string translation feature.
Added: Supported Norwegian and other missing languages.
Added: Integrated translation settings popup.
Improved: Optimized code.
Improved: Updated preloader.
Fixed: Unsaved string highlighting the issue.
Fixed: minor JS issues.
Fixed: Wrong characters calculation bug.
</pre>

<strong>Version 1.3.2 | 13 DEC 2019</strong>
<pre>
Added: Integrated URL and link filters in string.
Added: Added string filters.
Improved: JS code.
Improved: Feedback from.
Fixed: Minor issues.
</pre>

<strong>Version 1.3 | 03 DEC 2019</strong>
<pre>
Added: Integrated translated characters stats tables.
Added: Extend characters limit with premium license key.
Added: Integrated namespace.
Added: Not interested button in review popup.
Added: Integrated premium license key manager.
Added: Added security checks in every request.
Added: Integrated nonce in ajax request.
Improved: Improved translation issues.
Improved: Improved overall code.
Improved: Added new screenshots.
Fixed: Minor translation issues.
Fixed: Minor JS errors.
Fixed: Compatibility issues with WordPress 5.3
</pre>

<strong>Version 1.2.1 | 13 SEP 2019</strong>
<pre>
Fixed: Instant review popup notification bug fixed.
</pre>

<strong>Version 1.2 | 02 SEP 2019</strong>
<pre>
Fixed: Mismatch translation strings in Turkish.
Fixed: Minor JS issues.
Improved: Minor textual changes.
Added: Feedback on plugin deactivation.
</pre>

<strong>Version 1.1 | 31 AUG 2019</strong>
<pre>
Added: Integrated automatic translation progress bar popup
Added: Batch translation of all untranslated words in a single click.
Fixed: Issue with HTML translation.
Fixed: Minor javascript issues.
</pre>

<strong>Version 1.0.2 | 08 JUL 2019</strong>
<pre>
Fixed: Translations issues with the Chinese language.
</pre>

<strong>Version 1.0 | 08 JUN 2019</strong>
<pre>
New: Initial plugin release.
</pre>