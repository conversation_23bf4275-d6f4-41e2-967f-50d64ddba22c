<?php
function teste_vocacional_completo_email() {
    $user = wp_get_current_user();
    $user_id = $user->ID;
    $email_sent = get_user_meta($user_id, 'teste_vocacional_email_sent', true);

    if ($email_sent) {
        return;
    }

    $to = $user->user_email;
    $first_name = $user->first_name;
    $subject = "Você está indo muito bem! Próxima etapa: Prova Online";
    $headers = [
        'Content-Type: text/html; charset=UTF-8',
        'From: Instituto Panapaná <<EMAIL>>'
    ];
    $message = '<html><body>';
    $message .= '            <img style="max-width: 150px; height: auto;" src="https://cursos.institutopanapana.org.br/wp-content/uploads/2023/06/LOGO-insituto-panapana-org-hr.png" alt="Instituto Panapaná Logo" data-mce-src="https://cursos.institutopanapana.org.br/ugyrgems/2023/06/LOGO-insituto-panapana-org-hr.png" data-mce-style="max-width: 150px; height: auto;" data-mce-selected="1">';
    $message .= '<p>Olá ' . $first_name . '!</p>';
    $message .= '<p>Parabéns por concluir o Teste Vocacional do Alagoas Tech! Esperamos que tenha sido uma experiência reveladora sobre suas habilidades digitais.</p>';
    $message .= '<p>Agora chegou o momento de demonstrar seus conhecimentos. A próxima e última etapa é a Prova Online.</p>';
    $message .= '<p>Você terá 30 minutos para responder às questões. Reserve um tempo tranquilo, pois terá apenas uma chance.</p>';
    $message .= '<p>Acesse a plataforma para realizar a prova. Boa sorte!</p>';
    $message .= '<p><a href="https://cursos.institutopanapana.org.br/login/" style="background-color: #4CAF50; border: none; color: white; padding: 15px 32px; text-align: center; text-decoration: none; display: inline-block; font-size: 16px; margin: 4px 2px; cursor: pointer;">Realizar Prova</a></p>';
    $message .= '<p>Confiamos no seu potencial,<br>Equipe Instituto Panapaná</p>';
    $message .= '</body></html>';
    
    $email_sent = wp_mail($to, $subject, $message, $headers);

    // Se o e-mail for enviado com sucesso, marca como enviado no meta do usuário
    if ($email_sent) {
        update_user_meta($user_id, 'teste_vocacional_email_sent', true);
    }
}
add_shortcode('teste_vocacional_completo', 'teste_vocacional_completo_email');