<?php
/**
 * Test page for Course Creator Shortcodes
 * 
 * This file can be accessed directly via browser to test the shortcodes.
 * URL: http://localhost/test-shortcodes.php
 */

// Load WordPress
define('WP_USE_THEMES', false);
require_once('wp-load.php');

// Check if user is logged in and has permissions
if (!is_user_logged_in()) {
    wp_die('You must be logged in to access this page. <a href="wp-admin/">Login here</a>');
}

if (!current_user_can('export_course_data') && !current_user_can('manage_options')) {
    wp_die('You do not have permission to access this functionality.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Course Creator Shortcodes Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 1000px; margin: 0 auto; }
        .test-section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .shortcode-output { background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; margin: 10px 0; }
        .button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 3px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .button:hover { background: #005a87; }
        h1 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 10px; }
        h2 { color: #555; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Course Creator Shortcodes Test</h1>
        
        <div class="test-section info">
            <h2>📊 System Status</h2>
            <p><strong>WordPress Version:</strong> <?php echo get_bloginfo('version'); ?></p>
            <p><strong>Current User:</strong> <?php echo wp_get_current_user()->display_name; ?> (ID: <?php echo get_current_user_id(); ?>)</p>
            <p><strong>User Capabilities:</strong> 
                <?php if (current_user_can('export_course_data')): ?>
                    ✅ export_course_data
                <?php else: ?>
                    ❌ export_course_data
                <?php endif; ?>
                
                <?php if (current_user_can('manage_options')): ?>
                    ✅ manage_options
                <?php else: ?>
                    ❌ manage_options
                <?php endif; ?>
            </p>
        </div>

        <div class="test-section">
            <h2>🔍 Debug Information - [panapana_master_debug]</h2>
            <p>This shortcode shows system information and function availability:</p>
            <div class="shortcode-output">
                <?php echo do_shortcode('[panapana_master_debug]'); ?>
            </div>
        </div>

        <div class="test-section">
            <h2>🌍 Hello World Course Creator - [panapana_hello_world_course]</h2>
            <p>This shortcode creates a simple test course with video, e-book, and quiz:</p>
            <div class="shortcode-output">
                <?php echo do_shortcode('[panapana_hello_world_course]'); ?>
            </div>
        </div>

        <div class="test-section">
            <h2>📁 CSV Course Creator - [panapana_csv_course_creator]</h2>
            <p>This shortcode provides a form to upload CSV files and create complete courses:</p>
            <div class="shortcode-output">
                <?php echo do_shortcode('[panapana_csv_course_creator]'); ?>
            </div>
        </div>

        <div class="test-section">
            <h2>📤 Course Exporter - [tutor_course_exporter]</h2>
            <p>This shortcode lists existing courses and provides export functionality:</p>
            <div class="shortcode-output">
                <?php echo do_shortcode('[tutor_course_exporter]'); ?>
            </div>
        </div>

        <div class="test-section info">
            <h2>🔗 Quick Links</h2>
            <p>
                <a href="wp-admin/" class="button">WordPress Admin</a>
                <a href="wp-admin/edit.php?post_type=courses" class="button">View Courses</a>
                <a href="wp-admin/edit.php?post_type=tutor_quiz" class="button">View Quizzes</a>
                <a href="test-csv-automation.php" class="button">System Test Page</a>
            </p>
        </div>

        <div class="test-section success">
            <h2>✅ Testing Instructions</h2>
            <ol>
                <li><strong>Debug Test:</strong> Check that all functions show as "EXISTS" in the debug section</li>
                <li><strong>Hello World Test:</strong> Click "Criar Curso de Teste" to create a sample course</li>
                <li><strong>CSV Test:</strong> Upload the sample CSV file from <code>course_creator/backlog/sample-course.csv</code></li>
                <li><strong>Verification:</strong> Check WordPress admin to see created courses</li>
                <li><strong>Course Content:</strong> Verify videos, e-books, and quizzes work correctly</li>
            </ol>
        </div>

        <div class="test-section">
            <h2>📋 Sample CSV File Information</h2>
            <?php
            $sample_csv_path = 'course_creator/backlog/sample-course.csv';
            if (file_exists($sample_csv_path)) {
                echo '<p>✅ Sample CSV file found: <code>' . $sample_csv_path . '</code></p>';
                echo '<p><strong>File size:</strong> ' . filesize($sample_csv_path) . ' bytes</p>';
                echo '<p><strong>Expected course:</strong> "Git - Iniciante ao Avançado" with 2 modules, 6 lessons, 2 quizzes</p>';
            } else {
                echo '<p>❌ Sample CSV file not found at: <code>' . $sample_csv_path . '</code></p>';
                echo '<p>Please ensure the file exists in the course_creator/backlog/ directory.</p>';
            }
            ?>
        </div>
    </div>
</body>
</html>
