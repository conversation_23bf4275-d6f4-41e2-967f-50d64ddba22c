# 📊 CSV Format Specification

Complete specification for creating courses using CSV files in the Course Creator system.

## 📋 Column Structure

### Required Columns:
```csv
row_type,course_title,topic_title,item_title,lesson_type,content,description,order,video_url,vtt_path,pdf_url,quiz_settings,question_type,question_points,answer_text,is_correct,answer_order
```

## 🏗️ Row Types

### 1. `course` - Course Definition
Creates the main course container.

**Required Fields:**
- `row_type`: `course`
- `course_title`: Name of the course
- `description`: Course description

**Example:**
```csv
course,"Git - Iniciante ao Avançado","","","","","Aprenda Git do básico ao avançado com exemplos práticos.",1,"","","","","","","","",""
```

### 2. `topic` - Course Module/Section
Creates course modules or sections that contain lessons.

**Required Fields:**
- `row_type`: `topic`
- `course_title`: Must match existing course
- `topic_title`: Name of the topic/module
- `description`: Topic description
- `order`: Display order (1, 2, 3...)

**Example:**
```csv
topic,"Git - Iniciante ao Avançado","Módulo 1: Introdução ao Git","","","","Conceitos fundamentais do Git.",1,"","","","","","","","",""
```

### 3. `lesson` - Individual Lesson
Creates lessons within topics. Supports video and ebook types.

#### Video Lesson:
**Required Fields:**
- `row_type`: `lesson`
- `course_title`: Must match existing course
- `topic_title`: Must match existing topic
- `item_title`: Lesson name
- `lesson_type`: `video`
- `video_url`: YouTube URL
- `description`: Lesson description
- `order`: Display order within topic

**Optional Fields:**
- `vtt_path`: Path to VTT subtitle file

**Example:**
```csv
lesson,"Git - Iniciante ao Avançado","Módulo 1: Introdução ao Git","O que é Git?",video,"","Introdução aos conceitos básicos do Git.",1,"https://www.youtube.com/watch?v=4n4I0EqvBk0","4n4I0EqvBk0.pt.vtt","","","","","","",""
```

#### E-book Lesson:
**Required Fields:**
- `row_type`: `lesson`
- `course_title`: Must match existing course
- `topic_title`: Must match existing topic
- `item_title`: Lesson name
- `lesson_type`: `ebook`
- `pdf_url`: Direct URL to PDF file
- `description`: Lesson description
- `order`: Display order within topic

**Example:**
```csv
lesson,"Git - Iniciante ao Avançado","Módulo 1: Introdução ao Git","Manual Git",ebook,"","Manual completo em PDF.",3,"","","https://example.com/git-manual.pdf","","","","","",""
```

### 4. `quiz` - Quiz Definition
Creates quizzes within topics.

**Required Fields:**
- `row_type`: `quiz`
- `course_title`: Must match existing course
- `topic_title`: Must match existing topic
- `item_title`: Quiz name
- `description`: Quiz description
- `order`: Display order within topic
- `quiz_settings`: Quiz configuration (see format below)

**Quiz Settings Format:**
```
key1:value1,key2:value2,key3:value3
```

**Available Settings:**
- `time_limit`: Time limit in minutes (e.g., `15`)
- `passing_score`: Minimum score to pass (e.g., `70`)
- `max_attempts`: Maximum attempts allowed (e.g., `3`)

**Example:**
```csv
quiz,"Git - Iniciante ao Avançado","Módulo 1: Introdução ao Git","Quiz: Fundamentos","","","Teste seus conhecimentos.",4,"","","","time_limit:15,passing_score:70,max_attempts:3","","","","",""
```

### 5. `question` - Quiz Question
Creates questions within quizzes.

**Required Fields:**
- `row_type`: `question`
- `course_title`: Must match existing course
- `topic_title`: Must match existing topic
- `item_title`: Must match existing quiz
- `content`: Question text
- `order`: Question order within quiz
- `question_type`: Type of question
- `question_points`: Points for correct answer

**Question Types:**
- `single_choice`: Multiple choice (one correct answer)
- `true_false`: True/False question

**Example:**
```csv
question,"Git - Iniciante ao Avançado","Módulo 1: Introdução ao Git","Quiz: Fundamentos","","O que é Git?","",1,"","","","",single_choice,1.00,"","",""
```

### 6. `answer` - Question Answer
Creates answer options for questions.

**Required Fields:**
- `row_type`: `answer`
- `course_title`: Must match existing course
- `topic_title`: Must match existing topic
- `item_title`: Must match existing quiz
- `order`: Must match question order
- `answer_text`: Answer option text
- `is_correct`: 1 for correct, 0 for incorrect
- `answer_order`: Display order of answer (1, 2, 3...)

**Example:**
```csv
answer,"Git - Iniciante ao Avançado","Módulo 1: Introdução ao Git","Quiz: Fundamentos","","","",1,"","","","","","","Um sistema de controle de versão",1,1
answer,"Git - Iniciante ao Avançado","Módulo 1: Introdução ao Git","Quiz: Fundamentos","","","",1,"","","","","","","Um editor de texto",0,2
```

## 📝 Complete Example

```csv
row_type,course_title,topic_title,item_title,lesson_type,content,description,order,video_url,vtt_path,pdf_url,quiz_settings,question_type,question_points,answer_text,is_correct,answer_order
course,"Git Basics","","","","","Learn Git from scratch",1,"","","","","","","","",""
topic,"Git Basics","Module 1","","","","Introduction to Git",1,"","","","","","","","",""
lesson,"Git Basics","Module 1","What is Git?",video,"","Basic concepts",1,"https://youtube.com/watch?v=abc","abc.vtt","","","","","","",""
lesson,"Git Basics","Module 1","Git Manual",ebook,"","Reference guide",2,"","","https://example.com/manual.pdf","","","","","",""
quiz,"Git Basics","Module 1","Knowledge Check","","","Test your understanding",3,"","","","time_limit:10,passing_score:80","","","","",""
question,"Git Basics","Module 1","Knowledge Check","","What is Git?","",1,"","","","",single_choice,1.00,"","",""
answer,"Git Basics","Module 1","Knowledge Check","","","",1,"","","","","","","Version control system",1,1
answer,"Git Basics","Module 1","Knowledge Check","","","",1,"","","","","","","Text editor",0,2
question,"Git Basics","Module 1","Knowledge Check","","Git is distributed?","",2,"","","","",true_false,1.00,"","",""
answer,"Git Basics","Module 1","Knowledge Check","","","",2,"","","","","","","True",1,1
answer,"Git Basics","Module 1","Knowledge Check","","","",2,"","","","","","","False",0,2
```

## ✅ Validation Rules

### General Rules:
1. **Header Row**: First row must contain column names
2. **Required Columns**: `row_type` and `course_title` are mandatory
3. **Empty Rows**: Empty rows are ignored
4. **Encoding**: Use UTF-8 encoding
5. **Quotes**: Use double quotes for text containing commas

### Hierarchy Rules:
1. **Course First**: Course must be defined before topics
2. **Topic Before Content**: Topics must exist before lessons/quizzes
3. **Quiz Before Questions**: Quiz must exist before questions
4. **Question Before Answers**: Questions must exist before answers

### Data Validation:
1. **URLs**: Must be valid HTTP/HTTPS URLs
2. **Numbers**: Order, points, and boolean fields must be numeric
3. **File Paths**: VTT and PDF paths should be accessible
4. **Quiz Settings**: Must follow key:value,key:value format

## 🚨 Common Errors

### Format Errors:
- Missing required columns
- Invalid row_type values
- Malformed quiz_settings
- Invalid URL formats

### Hierarchy Errors:
- Lesson without topic
- Question without quiz
- Answer without question
- Topic without course

### Data Errors:
- Empty required fields
- Invalid question_type
- Non-numeric values in numeric fields
- Inaccessible file URLs

## 🛠️ Tips for Success

1. **Start Simple**: Begin with one course, one topic, one lesson
2. **Test Incrementally**: Upload small sections first
3. **Use Sample**: Reference `backlog/sample-course.csv`
4. **Validate URLs**: Ensure all URLs are accessible
5. **Check Encoding**: Save as UTF-8 to avoid character issues
6. **Order Matters**: Maintain logical order values
7. **Backup First**: Always backup before importing large datasets

---

**For more help, see the troubleshooting guide or create a GitHub issue!**
