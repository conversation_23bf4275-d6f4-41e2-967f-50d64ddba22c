<?php
/**
 * <PERSON><PERSON> LMS Course Automation - Course Creator
 *
 * This file handles course, topic, and lesson creation functionality
 * for the course automation system.
 *
 * @package TutorLMS_CourseAutomation
 * @subpackage Core
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Creates a complete course from organized CSV data.
 *
 * @param array $course_data Organized course data from CSV.
 * @return int|WP_Error Course ID on success, error object on failure.
 */
function panapana_create_course_from_data( $course_data ) {
	$course_info = $course_data['course_info'];

	// Create the main course post
	$course_post_data = array(
		'post_title'   => sanitize_text_field( $course_info['course_title'] ),
		'post_content' => wp_kses_post( $course_info['course_description'] ?? '' ),
		'post_status'  => 'publish',
		'post_type'    => 'courses',
		'post_author'  => get_current_user_id(),
	);

	$course_id = wp_insert_post( $course_post_data );
	if ( is_wp_error( $course_id ) ) {
		return new WP_Error( 'course_creation_failed', 'Falha ao criar o curso: ' . $course_id->get_error_message() );
	}

	// Set course metadata
	$course_meta = array(
		'_tutor_course_level'              => sanitize_text_field( $course_info['course_level'] ?? 'beginner' ),
		'_tutor_course_benefits'           => wp_kses_post( $course_info['course_benefits'] ?? '' ),
		'_tutor_course_requirements'       => wp_kses_post( $course_info['course_requirements'] ?? '' ),
		'_tutor_course_target_audience'    => wp_kses_post( $course_info['course_target_audience'] ?? '' ),
		'_tutor_course_material_includes'  => wp_kses_post( $course_info['course_materials'] ?? '' ),
		'_tutor_course_price_type'         => sanitize_text_field( $course_info['course_price_type'] ?? 'free' ),
		'_tutor_course_price'              => floatval( $course_info['course_price'] ?? 0 ),
	);

	foreach ( $course_meta as $meta_key => $meta_value ) {
		update_post_meta( $course_id, $meta_key, $meta_value );
	}

	// Create topics and their content
	foreach ( $course_data['topics'] as $topic_title => $topic_data ) {
		$topic_result = panapana_create_topic_with_content( $course_id, $topic_title, $topic_data );
		if ( is_wp_error( $topic_result ) ) {
			return new WP_Error( 'topic_creation_failed',
				"Falha ao criar tópico '{$topic_title}': " . $topic_result->get_error_message() );
		}
	}

	return $course_id;
}

/**
 * Creates a topic and all its associated content (lessons and quizzes).
 *
 * @param int    $course_id   Course ID.
 * @param string $topic_title Topic title.
 * @param array  $topic_data  Topic data including lessons and quizzes.
 * @return int|WP_Error Topic ID on success, error object on failure.
 */
function panapana_create_topic_with_content( $course_id, $topic_title, $topic_data ) {
	// Create the topic post
	$topic_post_data = array(
		'post_title'  => sanitize_text_field( $topic_title ),
		'post_content' => wp_kses_post( $topic_data['topic_info']['topic_description'] ?? '' ),
		'post_status' => 'publish',
		'post_type'   => 'topics',
		'post_parent' => $course_id,
		'post_author' => get_current_user_id(),
	);

	$topic_id = wp_insert_post( $topic_post_data );
	if ( is_wp_error( $topic_id ) ) {
		return new WP_Error( 'topic_creation_failed', 'Falha ao criar tópico: ' . $topic_id->get_error_message() );
	}

	// Set topic metadata
	update_post_meta( $topic_id, '_tutor_topic_course_id', $course_id );

	// Create lessons
	foreach ( $topic_data['lessons'] as $lesson_data ) {
		$lesson_result = panapana_create_lesson_from_data( $topic_id, $lesson_data );
		if ( is_wp_error( $lesson_result ) ) {
			return new WP_Error( 'lesson_creation_failed',
				"Falha ao criar aula: " . $lesson_result->get_error_message() );
		}
	}

	// Create quizzes
	foreach ( $topic_data['quizzes'] as $quiz_title => $quiz_data ) {
		$quiz_result = panapana_create_quiz_from_data( $topic_id, $quiz_data );
		if ( is_wp_error( $quiz_result ) ) {
			return new WP_Error( 'quiz_creation_failed',
				"Falha ao criar quiz '{$quiz_title}': " . $quiz_result->get_error_message() );
		}
	}

	return $topic_id;
}

/**
 * Creates a lesson from CSV data.
 *
 * @param int   $topic_id    Topic ID.
 * @param array $lesson_data Lesson data from CSV.
 * @return int|WP_Error Lesson ID on success, error object on failure.
 */
function panapana_create_lesson_from_data( $topic_id, $lesson_data ) {
	$lesson_title = sanitize_text_field( $lesson_data['item_title'] );
	$lesson_type = sanitize_text_field( $lesson_data['lesson_type'] ?? 'video' );

	// Generate lesson content based on type
	$lesson_content = panapana_generate_lesson_content( $lesson_data, $lesson_type );
	if ( is_wp_error( $lesson_content ) ) {
		return $lesson_content;
	}

	// Create the lesson post
	$lesson_post_data = array(
		'post_title'   => $lesson_title,
		'post_content' => $lesson_content,
		'post_status'  => 'publish',
		'post_type'    => 'lesson',
		'post_parent'  => $topic_id,
		'post_author'  => get_current_user_id(),
	);

	$lesson_id = wp_insert_post( $lesson_post_data );
	if ( is_wp_error( $lesson_id ) ) {
		return new WP_Error( 'lesson_creation_failed', 'Falha ao criar aula: ' . $lesson_id->get_error_message() );
	}

	// Set lesson metadata
	$course_id = get_post_meta( $topic_id, '_tutor_topic_course_id', true );
	update_post_meta( $lesson_id, '_tutor_lesson_course_id', $course_id );
	update_post_meta( $lesson_id, '_tutor_lesson_topic_id', $topic_id );

	// Set lesson type specific metadata
	if ( $lesson_type === 'video' ) {
		update_post_meta( $lesson_id, '_video_source', 'youtube' );
		update_post_meta( $lesson_id, '_video_url', esc_url_raw( $lesson_data['video_url'] ?? '' ) );
	}

	return $lesson_id;
}

/**
 * Generates lesson content based on lesson type.
 *
 * @param array  $lesson_data Lesson data from CSV.
 * @param string $lesson_type Type of lesson (video, ebook, etc.).
 * @return string|WP_Error Generated content or error object.
 */
function panapana_generate_lesson_content( $lesson_data, $lesson_type ) {
	switch ( $lesson_type ) {
		case 'video':
			return panapana_generate_video_lesson_content( $lesson_data );

		case 'ebook':
		case 'pdf':
			return panapana_generate_ebook_lesson_content( $lesson_data );

		default:
			return new WP_Error( 'invalid_lesson_type', "Tipo de aula inválido: {$lesson_type}" );
	}
}

/**
 * Generates content for video lessons.
 *
 * @param array $lesson_data Lesson data from CSV.
 * @return string|WP_Error Video lesson content or error object.
 */
function panapana_generate_video_lesson_content( $lesson_data ) {
	$video_url = $lesson_data['video_url'] ?? '';
	$vtt_path = $lesson_data['vtt_path'] ?? '';

	if ( empty( $video_url ) ) {
		return new WP_Error( 'missing_video_url', 'URL do vídeo é obrigatória para aulas de vídeo.' );
	}

	// Generate video shortcode with VTT subtitles if available
	$shortcode = '[custom_video_with_vtt src="' . esc_attr( $video_url ) . '" width="640" height="360"';
	if ( ! empty( $vtt_path ) ) {
		$shortcode .= ' vtt="' . esc_attr( $vtt_path ) . '"';
	}
	$shortcode .= ']';

	$content = '<div class="tutor-lesson-video">' . "\n";
	$content .= $shortcode . "\n";
	$content .= '</div>' . "\n\n";

	// Add lesson description if available
	if ( ! empty( $lesson_data['lesson_description'] ) ) {
		$content .= '<div class="tutor-lesson-description">' . "\n";
		$content .= wp_kses_post( $lesson_data['lesson_description'] ) . "\n";
		$content .= '</div>';
	}

	return $content;
}

/**
 * Generates content for e-book/PDF lessons.
 *
 * @param array $lesson_data Lesson data from CSV.
 * @return string|WP_Error E-book lesson content or error object.
 */
function panapana_generate_ebook_lesson_content( $lesson_data ) {
	$pdf_url = $lesson_data['pdf_url'] ?? '';

	if ( empty( $pdf_url ) ) {
		return new WP_Error( 'missing_pdf_url', 'URL do PDF é obrigatória para aulas de e-book.' );
	}

	// Generate Google Docs viewer iframe
	$viewer_url = 'https://docs.google.com/viewer?url=' . urlencode( $pdf_url ) . '&embedded=true';

	$content = '<div class="tutor-lesson-ebook">' . "\n";
	$content .= '<iframe src="' . esc_url( $viewer_url ) . '" width="100%" height="600" frameborder="0"></iframe>' . "\n";
	$content .= '</div>' . "\n\n";

	// Add lesson description if available
	if ( ! empty( $lesson_data['lesson_description'] ) ) {
		$content .= '<div class="tutor-lesson-description">' . "\n";
		$content .= wp_kses_post( $lesson_data['lesson_description'] ) . "\n";
		$content .= '</div>';
	}

	return $content;
}

/**
 * Creates a simple "Hello World" course for testing purposes.
 *
 * @return int|WP_Error Course ID on success, error object on failure.
 */
function panapana_create_hello_world_course() {
	// Sample course data structure
	$sample_data = array(
		'course_info' => array(
			'course_title' => 'Hello World - Curso de Teste',
			'course_description' => 'Este é um curso de teste criado automaticamente para verificar o funcionamento do sistema.',
			'course_level' => 'beginner',
		),
		'topics' => array(
			'Módulo 1: Introdução' => array(
				'topic_info' => array(
					'topic_description' => 'Módulo introdutório do curso de teste.',
				),
				'lessons' => array(
					array(
						'item_title' => 'Aula 1: Vídeo de Introdução',
						'lesson_type' => 'video',
						'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
						'vtt_path' => 'hello-world-intro.vtt',
						'lesson_description' => 'Vídeo introdutório do curso.',
					),
					array(
						'item_title' => 'Aula 2: Material de Leitura',
						'lesson_type' => 'ebook',
						'pdf_url' => 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
						'lesson_description' => 'Material de leitura complementar.',
					),
				),
				'quizzes' => array(
					'Quiz de Conhecimento' => array(
						'quiz_info' => array(
							'item_title' => 'Quiz de Conhecimento',
							'quiz_settings' => 'time_limit:10,passing_score:70,max_attempts:3',
						),
						'questions' => array(
							array(
								'question_info' => array(
									'question_text' => 'Qual é a resposta para a pergunta fundamental?',
									'question_type' => 'single_choice',
								),
								'answers' => array(
									array( 'answer_text' => '42', 'is_correct' => '1' ),
									array( 'answer_text' => '24', 'is_correct' => '0' ),
									array( 'answer_text' => '84', 'is_correct' => '0' ),
								),
							),
						),
					),
				),
			),
		),
	);

	return panapana_create_course_from_data( $sample_data );
}
