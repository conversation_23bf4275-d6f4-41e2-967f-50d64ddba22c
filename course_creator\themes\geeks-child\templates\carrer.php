<?php
function render_user_carrer() {
    // Obtém o ID do usuário logado
    $user_id = get_current_user_id();
    
    // Se não houver um usuário logado, retorne "Nenhum"
    if (!$user_id) {
        $carreira = 'Nenhum';
    } else {
        // Obtém o valor do meta "caminho_selecionado"
        $caminho_selecionado = get_user_meta($user_id, 'caminho_selecionado', true);
        
        // Se "caminho_selecionado" estiver vazio, obtém o valor do meta "vocacao_inicial"
        if (empty($caminho_selecionado)) {
            $vocacao_inicial = get_user_meta($user_id, 'vocacao_inicial', true);

            // Se "vocacao_inicial" for "empate" ou não existir, define "Nenhum"
            if (empty($vocacao_inicial) || $vocacao_inicial === 'empate') {
                $carreira = 'Nenhum';
            } else {
                $carreira = esc_html($vocacao_inicial);
            }
        } else {
            $carreira = esc_html($caminho_selecionado);
        }
    }

    $carrer_dict = [
        "designer" => "Design Gráfico",
        "programador" => "Programação",
        "wordpress" => "Desenvolvimento Wordpress",
        "editor" => "Edição de Vídeo",
        "trafego" => "Gestão de Tráfego",
        "Nenhum" => "Nenhum"
        ];
    // Retorna o span com o texto formatado
    return '<span>Carreira Selecionada: <b>' . $carrer_dict[$carreira] . '</b></span>';
}

// Registra o shortcode [user_meta]
add_shortcode('user_carrer', 'render_user_carrer');