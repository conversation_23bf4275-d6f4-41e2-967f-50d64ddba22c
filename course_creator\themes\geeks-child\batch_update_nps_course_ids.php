<?php
/**
 * Batch update course_id for generic NPS entries based on user role.
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
    die;
}

/**
 * Handles the batch update of course_id in wp_nps_responses.
 */
function panapana_process_batch_update_nps_course_ids() {
    // 1. Security & Capability Checks
    if ( ! isset( $_POST['batch_update_nps_course_ids_nonce'] ) || ! wp_verify_nonce( sanitize_key( $_POST['batch_update_nps_course_ids_nonce'] ), 'batch_update_nps_course_ids_action' ) ) {
        wp_die( 'Security check failed. Invalid nonce.', 'Error', array('response' => 403) );
    }
    if ( ! current_user_can( 'manage_options' ) ) {
        wp_die( 'You do not have sufficient permissions.', 'Error', array('response' => 403) );
    }

    global $wpdb;
    $nps_table_name = $wpdb->prefix . 'nps_responses';
    $generic_nps_sugestao_identifier = 'accepted_by_batch_script_v3_generic'; // From previous script

    // 2. Define Role to Course ID Mapping
    $role_to_main_course_id_map = array(
        'aluno_wordpress'       => 6946,
        'aluno_design'          => 3145,    // Role for User 339
        'aluno_programacao'     => 2999,
        'aluno_edio_de_vdeo'    => 3962,    // Ensure this slug is exact
        'aluno_marketing'       => 5339,
        // Add any other 'aluno_...' roles and their primary course ID if needed
    );
    // Also include 'aluno_designer' if that's a distinct role from 'aluno_design'
    if (!isset($role_to_main_course_id_map['aluno_designer'])) { // Example if 'aluno_designer' is also used
        // $role_to_main_course_id_map['aluno_designer'] = 3145; // Or its specific course
    }


    $results = [
        'updated' => [],
        'no_target_role_found' => [],
        'no_matching_nps_entry' => [],
        'error_updating' => [],
        'user_does_not_exist' => [],
    ];

    // 3. Find all generic NPS entries that need updating
    $generic_nps_entries = $wpdb->get_results( $wpdb->prepare(
        "SELECT id, user_id FROM {$nps_table_name} WHERE course_id IS NULL AND sugestoes = %s",
        $generic_nps_sugestao_identifier
    ) );

    if ( empty( $generic_nps_entries ) ) {
        wp_die( 'No generic NPS entries (course_id IS NULL and sugestoes="' . esc_html($generic_nps_sugestao_identifier) . '") found to update.', 'No Entries Found' );
    }

    error_log('[PANA_NPS_COURSE_UPDATE] Found ' . count($generic_nps_entries) . ' generic NPS entries to potentially update.');

    foreach ( $generic_nps_entries as $entry ) {
        $nps_entry_id = $entry->id;
        $user_id      = $entry->user_id;

        $user_data = get_userdata( $user_id );
        if ( ! $user_data ) {
            $results['user_does_not_exist'][] = "NPS Entry ID {$nps_entry_id}: User ID {$user_id} does not exist. Skipped.";
            error_log("[PANA_NPS_COURSE_UPDATE] NPS Entry ID {$nps_entry_id}: User ID {$user_id} does not exist.");
            continue;
        }

        $target_course_id_for_user = 0;
        $user_roles = (array) $user_data->roles;

        foreach ( $user_roles as $role ) {
            if ( array_key_exists( $role, $role_to_main_course_id_map ) ) {
                $target_course_id_for_user = $role_to_main_course_id_map[$role];
                error_log("[PANA_NPS_COURSE_UPDATE] User ID {$user_id} has role '{$role}', target course ID: {$target_course_id_for_user} for NPS entry {$nps_entry_id}.");
                break; // Found the primary role and its course
            }
        }

        if ( $target_course_id_for_user > 0 ) {
            $updated_count = $wpdb->update(
                $nps_table_name,
                array( 'course_id' => $target_course_id_for_user ), // Data to update
                array( 'id' => $nps_entry_id ),                     // WHERE clause
                array( '%d' ),                                      // Format for data
                array( '%d' )                                       // Format for WHERE
            );

            if ( false === $updated_count ) {
                $results['error_updating'][] = "NPS Entry ID {$nps_entry_id} (User ID {$user_id}): DB error updating course_id to {$target_course_id_for_user}. Error: " . $wpdb->last_error;
                error_log("[PANA_NPS_COURSE_UPDATE] NPS Entry ID {$nps_entry_id} (User ID {$user_id}): FAILED to update course_id to {$target_course_id_for_user}. Error: " . $wpdb->last_error);
            } elseif ( $updated_count > 0 ) {
                $results['updated'][] = "NPS Entry ID {$nps_entry_id} (User ID {$user_id}, Role: " . implode(', ', $user_roles) . "): Successfully updated course_id to {$target_course_id_for_user}.";
                error_log("[PANA_NPS_COURSE_UPDATE] NPS Entry ID {$nps_entry_id} (User ID {$user_id}): Successfully updated course_id to {$target_course_id_for_user}.");
            } else {
                // $updated_count is 0, meaning no rows were changed (perhaps data was already correct or WHERE clause didn't match)
                 $results['no_matching_nps_entry'][] = "NPS Entry ID {$nps_entry_id} (User ID {$user_id}): No rows updated (data might have been same or entry not found with ID). Tried to set course ID {$target_course_id_for_user}.";
                 error_log("[PANA_NPS_COURSE_UPDATE] NPS Entry ID {$nps_entry_id} (User ID {$user_id}): No rows changed by update query for course_id {$target_course_id_for_user}.");
            }
        } else {
            $results['no_target_role_found'][] = "NPS Entry ID {$nps_entry_id} (User ID {$user_id}, Roles: " . implode(', ', $user_roles) . "): No matching 'aluno_...' role found in map to determine course_id. Skipped.";
            error_log("[PANA_NPS_COURSE_UPDATE] NPS Entry ID {$nps_entry_id} (User ID {$user_id}): No target role found in map.");
        }
    }

    // 4. Display Results
    ob_start();
    // ... (HTML for displaying results - same structure as previous batch scripts) ...
    // You can copy the results display HTML block from batch_survey_completer.php
    // and adjust the titles and $results keys.
    ?>
    <div class="wrap" style="padding: 20px; font-family: sans-serif;">
        <h1><span class="dashicons dashicons-database-view" style="color: #0073aa;"></span> Resultados da Atualização de Course IDs em Respostas NPS Genéricas</h1>
        <p><em>Processamento concluído. Para cada resposta NPS genérica encontrada (criada pelo script anterior), o sistema tentou identificar o curso principal do usuário baseado em seu papel e atualizar o 'course_id' na resposta NPS.</em></p>
        <hr>

        <?php foreach ( $results as $status => $messages ) : ?>
            <?php if ( ! empty( $messages ) ) : ?>
                <?php
                $status_title = ucwords( str_replace( '_', ' ', $status ) );
                $color_map = [
                    'updated' => '#4CAF50', // Green
                    'no_target_role_found' => '#ffc107', // Amber
                    'no_matching_nps_entry' => '#2196F3', // Blue (or could be warning if unexpected)
                    'error_updating' => '#f44336', // Red
                    'user_does_not_exist' => '#9e9e9e', // Grey
                ];
                $bg_color_map = [
                    'updated' => '#f0fff0',
                    'no_target_role_found' => '#fff8e1',
                    'no_matching_nps_entry' => '#e3f2fd',
                    'error_updating' => '#ffebee',
                    'user_does_not_exist' => '#f5f5f5',
                ];
                $current_color = $color_map[$status] ?? '#777';
                $current_bg_color = $bg_color_map[$status] ?? '#f9f9f9';
                ?>
                <div style="margin-bottom: 20px; padding: 10px; border-left: 4px solid <?php echo $current_color; ?>; background-color: <?php echo $current_bg_color; ?>;">
                    <h2 style="margin-top: 0; color: <?php echo $current_color; ?>;"><?php echo esc_html( $status_title ); ?> (<?php echo count( $messages ); ?>):</h2>
                    <ul style="list-style-type: disc; margin-left: 20px; max-height: 300px; overflow-y: auto;">
                        <?php foreach ( $messages as $message ) : ?>
                            <li><?php echo esc_html( $message ); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
        <?php endforeach; ?>
        
        <hr>
        <p><a href="<?php echo esc_url( admin_url('admin.php?page=respostas-nps') ); ?>" class="button button-secondary">Voltar para Respostas NPS</a></p> <?php // Adjust link as needed ?>
    </div>
    <?php
    echo ob_get_clean();
    exit;
}
add_action( 'admin_post_panapana_batch_update_nps_course_ids', 'panapana_process_batch_update_nps_course_ids' );


/**
 * Shortcode to display the button for batch updating NPS course_ids.
 * Usage: [batch_update_nps_course_ids_button]
 */
function panapana_batch_update_nps_course_ids_button_shortcode() {
    if ( ! current_user_can( 'manage_options' ) ) {
        return '<p>Você não tem permissão para acessar esta funcionalidade.</p>';
    }
    ob_start();
    ?>
    <div class="panapana-batch-update-nps-section" style="margin: 20px 0; padding: 20px; border: 1px solid #ccd0d4; background-color: #fff; border-left: 5px solid #0073aa; box-shadow: 0 1px 1px rgba(0,0,0,.04);">
        <h3 style="margin-top:0; color: #0073aa;"><span class="dashicons dashicons-update" style="color: #0073aa;"></span> Ação em Lote: Atualizar ID do Curso em Respostas NPS Genéricas</h3>
        <p style="font-weight: bold;">Esta ação irá procurar por respostas NPS que foram criadas genericamente (com `course_id` NULO e `sugestoes` = 'accepted_by_batch_script_v3_generic'). Para cada uma, tentará identificar o curso principal do usuário baseado no seu papel (ex: 'aluno_design' -> curso 'Fundamentos do Design') e atualizará o `course_id` nessa resposta NPS.</p>
        <p style="color: #d9534f;"><strong>AVISO:</strong> Faça um backup do banco de dados antes de executar esta ação, especialmente se for a primeira vez.</p>
        <form method="post" action="<?php echo esc_url( admin_url('admin-post.php') ); ?>" onsubmit="return confirm('TEM CERTEZA que deseja atualizar os IDs de curso nas respostas NPS genéricas? Verifique o mapeamento de papéis para cursos no código.');">
            <input type="hidden" name="action" value="panapana_batch_update_nps_course_ids">
            <?php wp_nonce_field( 'batch_update_nps_course_ids_action', 'batch_update_nps_course_ids_nonce' ); ?>
            <?php submit_button( 'Atualizar IDs de Curso em Respostas NPS AGORA', 'primary', 'panapana_submit_batch_update_nps_ids', true, array('style' => 'background-color: #0073aa; border-color: #006799;') ); ?>
        </form>
        <p class="description"><em>O mapeamento de papéis para IDs de curso está definido no arquivo <code>batch_update_nps_course_ids.php</code>.</em></p>
    </div>
    <?php
    return ob_get_clean();
}
add_shortcode( 'batch_update_nps_course_ids_button', 'panapana_batch_update_nps_course_ids_button_shortcode' );