<?php
/**
 * Functionality to export "candidato" role users who are not Tutor students
 * and include all their metadata in a CSV.
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
    die;
}

/**
 * Shortcode to display the "Export Candidate Non-Students" button.
 *
 * Usage: [export_candidate_non_students_button]
 */
function panapana_export_candidate_non_students_button_shortcode() {
    // Check if the current user has the capability to perform this export
    // Using 'manage_options' for now, adjust if you have a more specific capability.
    if ( ! current_user_can( 'manage_options' ) ) {
        return '<p>Você não tem permissão para ver este botão.</p>';
    }

    $export_url = wp_nonce_url(
        admin_url( 'admin-post.php?action=export_candidate_non_students_csv' ), // The action hook
        'export_candidate_non_students_nonce',      // Nonce action name
        'export_cns_nonce'                          // Nonce name field in the URL
    );

    ob_start();
    ?>
    <div class="panapana-export-section" style="margin: 20px 0; padding: 15px; border: 1px solid #ccd0d4; background-color: #f6f7f7; border-radius: 4px;">
        <h3>Exportar Candidatos Não Alunos</h3>
        <p>Clique no botão abaixo para exportar um CSV de todos os usuários com o papel "candidato" que não estão marcados como alunos no Tutor LMS. O arquivo incluirá todos os metadados do usuário.</p>
        <p>
            <a href="<?php echo esc_url( $export_url ); ?>" class="button button-primary">
                <span class="dashicons dashicons-download" style="vertical-align: middle; margin-right: 5px;"></span>
                Exportar Candidatos Não Alunos (CSV)
            </a>
        </p>
    </div>
    <?php
    return ob_get_clean();
}
add_shortcode( 'export_candidate_non_students_button', 'panapana_export_candidate_non_students_button_shortcode' );

/**
 * Handles the CSV export for candidate non-students.
 * Hooked to admin_post_export_candidate_non_students_csv.
 */
function panapana_handle_export_candidate_non_students_csv() {
    // 1. Verify Nonce
    if ( ! isset( $_GET['export_cns_nonce'] ) || ! wp_verify_nonce( sanitize_key( $_GET['export_cns_nonce'] ), 'export_candidate_non_students_nonce' ) ) {
        wp_die( 'Invalid security token.', 'Security Check Failed', array( 'response' => 403 ) );
    }

    // 2. Check User Capabilities
    if ( ! current_user_can( 'manage_options' ) ) { // Adjust capability if needed
        wp_die( 'Você não tem permissão para realizar esta ação.', 'Permission Denied', array( 'response' => 403 ) );
    }

    // 3. Fetch Users
    $args = array(
        'role'    => 'candidato',
        'meta_query' => array(
            'relation' => 'AND',
            array(
                'key'     => '_is_tutor_student',
                'compare' => 'NOT EXISTS', // Key does not exist
            ),
            // You could add another condition if some users have it set to '0' or empty instead of not existing
            // array(
            //     'key'     => '_is_tutor_student',
            //     'value'   => '', // or '0'
            //     'compare' => '=',
            // )
        ),
        'fields' => 'all_with_meta', // Get all user data and their meta
    );
    $users = get_users( $args );

    if ( empty( $users ) ) {
        wp_die( 'Nenhum usuário "candidato" encontrado que não seja aluno do Tutor LMS ou sem o metadado _is_tutor_student.', 'Nenhum Usuário Encontrado' );
    }

    // 4. Prepare CSV Output
    $filename = 'candidate_non_students_export_' . date( 'Y-m-d_H-i-s' ) . '.csv';
    header( 'Content-Type: text/csv; charset=utf-8' );
    header( 'Content-Disposition: attachment; filename="' . $filename . '"' );
    $output = fopen( 'php://output', 'w' );

    // Add UTF-8 BOM for Excel compatibility
    fprintf( $output, chr(0xEF) . chr(0xBB) . chr(0xBF) );

    // --- Determine all possible meta keys to use as headers ---
    $all_meta_keys = array();
    foreach ( $users as $user ) {
        $user_meta = get_user_meta( $user->ID );
        foreach ( $user_meta as $key => $value ) {
            // Exclude sensitive or very WordPress-internal keys if desired
            if ( substr( $key, 0, 1 ) !== '_' && $key !== 'session_tokens' && $key !== 'wp_capabilities' ) {
                if ( ! in_array( $key, $all_meta_keys, true ) ) {
                    $all_meta_keys[] = $key;
                }
            }
        }
    }
    sort( $all_meta_keys ); // Sort for consistent column order

    // Define base headers + dynamic meta keys
    $base_headers = array( 'User ID', 'User Login', 'User Email', 'Display Name', 'User Registered' );
    $csv_headers  = array_merge( $base_headers, $all_meta_keys );
    fputcsv( $output, $csv_headers );

    // 5. Loop through data and write to CSV
    foreach ( $users as $user ) {
        $user_meta_values = get_user_meta( $user->ID );
        $row = array(
            $user->ID,
            $user->user_login,
            $user->user_email,
            $user->display_name,
            $user->user_registered,
        );

        // Add values for each meta key in the header order
        foreach ( $all_meta_keys as $meta_key ) {
            if ( isset( $user_meta_values[$meta_key] ) ) {
                // If the meta value is an array, serialize it or take the first element.
                // For simplicity, taking the first element. Join if multiple values needed.
                $value = $user_meta_values[$meta_key];
                if ( is_array( $value ) ) {
                    $row[] = implode( '; ', $value ); // Join multiple values with a semicolon
                } else {
                    $row[] = $value;
                }
            } else {
                $row[] = ''; // Empty string if meta key doesn't exist for this user
            }
        }
        fputcsv( $output, $row );
    }

    // 6. Close stream and exit
    fclose( $output );
    exit();
}
// Hook the handler to the admin-post action
add_action( 'admin_post_nopriv_export_candidate_non_students_csv', 'panapana_handle_export_candidate_non_students_csv' ); // For non-logged-in users if the page is public
add_action( 'admin_post_export_candidate_non_students_csv', 'panapana_handle_export_candidate_non_students_csv' );     // For logged-in users