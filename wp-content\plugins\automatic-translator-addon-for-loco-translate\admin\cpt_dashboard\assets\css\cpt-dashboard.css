.cpt-review-notice{
  position: relative;
  margin: 10px 20px 10px 0 !important;
  padding: 5px;
  display: flex;
  align-items: center;
}

.cpt-review-notice .cpt-review-notice-icon{
  max-width: 80px;
  height: auto;
  margin-right: 10px;
}

.cpt-review-notice div p{
  margin: 0;
  margin-bottom: .5rem;
}

.cpt-review-notice .atlt-review-notice-dismiss {
  display: -webkit-box;
  display: -ms-flexbox;
  gap: 10px;
}

.cpt-review-notice .cpt-not-interested {
  position: absolute;
  top: 5px;
  right: 5px;
}

body.rtl .cpt-review-notice .cpt-not-interested {
  right: auto;
  left: 5px;
}

.cpt-review-notice .button-primary {
  margin-right: 10px !important;
}

.cpt-review-notice :where(.cpt-not-interested, .cpt-already-reviewed)::before {
  color: #cc0000;
  content: "\f153";
  font: normal 16px/20px dashicons;
  display: inline-block;
  vertical-align: middle;
  margin-right: 4px;
  height: 22px;
}

.cpt-review-notice .cpt-review-notice-content{
  max-width: calc(100% - 240px);
}

@media (max-width: 767px) and (min-width: 100px) {
  .cpt-review-notice {
    margin: 80px 20px 10px 0 !important;
  }
}

