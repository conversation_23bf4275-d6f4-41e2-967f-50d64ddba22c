# Copyright (C) 2025 all-in-one-wp-security-and-firewall
# This file is distributed under the same license as the all-in-one-wp-security-and-firewall package.
msgid ""
msgstr ""
"Project-Id-Version: all-in-one-wp-security-and-firewall\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language-Team: Team Updraft <<EMAIL>>\n"
"Last-Translator: Team Updraft <<EMAIL>>\n"
"POT-Creation-Date: 2025-05-22 14:04+0000\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/all-in-one-wp-security-and-firewall\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-KeywordsList: __;_e;_ex:1,2c;_n:1,2;_n_noop:1,2;_nx:1,2,4c;_nx_noop:1,2,3c;_x:1,2c;esc_attr__;esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"
"X-Poedit-SourceCharset: UTF-8\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: src/wp-security-core.php:346, src/admin/wp-security-database-menu.php:134, src/admin/wp-security-database-menu.php:137, src/admin/wp-security-database-menu.php:141
msgid "Error:"
msgstr ""

#: src/wp-security-core.php:346
msgid "template not found"
msgstr ""

#: src/wp-security.php:43, src/admin/wp-security-firewall-menu.php:122, src/admin/wp-security-firewall-setup-notice.php:389, src/admin/wp-security-firewall-setup-notice.php:521, src/templates/notices/firewall-installed-notice.php:2, src/templates/notices/firewall-setup-notice.php:9, src/templates/wp-admin/settings/general-settings.php:12
msgid "All-In-One Security"
msgstr ""

#: src/wp-security.php:44
msgid "All-In-One Security plugin has been deactivated."
msgstr ""

#: src/wp-security.php:45
msgid "This plugin requires PHP version %s."
msgstr ""

#: src/wp-security.php:46
msgid "Your current PHP version is %s."
msgstr ""

#: src/wp-security.php:47
msgid "You will need to ask your web hosting company to upgrade."
msgstr ""

#: src/wp-security.php:62, src/admin/wp-security-admin-init.php:86, src/admin/wp-security-admin-init.php:87, src/admin/wp-security-settings-menu.php:23, src/templates/wp-admin/brute-force/captcha-settings.php:25, src/templates/wp-admin/filesystem-security/file-protection.php:29
msgid "Settings"
msgstr ""

#: src/admin/wp-security-admin-init.php:78, src/admin/wp-security-admin-init.php:79, src/admin/wp-security-dashboard-menu.php:19, src/admin/wp-security-dashboard-menu.php:30
msgid "Dashboard"
msgstr ""

#: src/admin/wp-security-admin-init.php:94, src/admin/wp-security-admin-init.php:95, src/admin/wp-security-user-security-menu.php:19
msgid "User Security"
msgstr ""

#: src/admin/wp-security-admin-init.php:102, src/admin/wp-security-admin-init.php:103
msgid "Database Security"
msgstr ""

#: src/admin/wp-security-admin-init.php:111, src/admin/wp-security-admin-init.php:112
msgid "File Security"
msgstr ""

#: src/admin/wp-security-admin-init.php:119, src/admin/wp-security-admin-init.php:120, src/admin/wp-security-firewall-menu.php:22, src/templates/wp-admin/dashboard/may-also-like.php:144, src/templates/wp-admin/firewall/partials/firewall-setup.php:12
msgid "Firewall"
msgstr ""

#: src/admin/wp-security-admin-init.php:127, src/admin/wp-security-admin-init.php:128
msgid "Brute Force"
msgstr ""

#: src/admin/wp-security-admin-init.php:135, src/admin/wp-security-admin-init.php:136
msgid "Spam Prevention"
msgstr ""

#: src/admin/wp-security-admin-init.php:143, src/admin/wp-security-admin-init.php:144, src/admin/wp-security-filescan-menu.php:25
msgid "Scanner"
msgstr ""

#: src/admin/wp-security-admin-init.php:152, src/admin/wp-security-admin-init.php:153, src/admin/wp-security-tools-menu.php:20
msgid "Tools"
msgstr ""

#: src/admin/wp-security-admin-init.php:221
msgid "Id"
msgstr ""

#: src/admin/wp-security-admin-init.php:222
msgid "Event Type"
msgstr ""

#: src/admin/wp-security-admin-init.php:223
msgid "IP Address"
msgstr ""

#: src/admin/wp-security-admin-init.php:224, src/admin/wp-security-list-404.php:115
msgid "Attempted URL"
msgstr ""

#: src/admin/wp-security-admin-init.php:225, src/admin/wp-security-list-404.php:116
msgid "Referer"
msgstr ""

#: src/admin/wp-security-admin-init.php:226, src/admin/wp-security-list-404.php:117, src/admin/wp-security-list-audit.php:176, src/admin/wp-security-list-debug.php:53, src/classes/commands/wp-security-log-commands.php:104
msgid "Date and time"
msgstr ""

#: src/admin/wp-security-admin-init.php:227
msgid "Lock Status"
msgstr ""

#: src/admin/wp-security-admin-init.php:348, src/templates/wp-admin/settings/advanced-settings.php:81
msgid "Unexpected response:"
msgstr ""

#: src/admin/wp-security-admin-init.php:349
msgid "Copied"
msgstr ""

#: src/admin/wp-security-admin-init.php:350
msgid "You have not yet selected a file to import."
msgstr ""

#: src/admin/wp-security-admin-init.php:351
msgid "Processing..."
msgstr ""

#: src/admin/wp-security-admin-init.php:352
msgid "Please enter a valid IP address or domain name."
msgstr ""

#: src/admin/wp-security-admin-init.php:354
msgid "Saving..."
msgstr ""

#: src/admin/wp-security-admin-init.php:355
msgid "Deleting..."
msgstr ""

#: src/admin/wp-security-admin-init.php:356
msgid "Blocking..."
msgstr ""

#: src/admin/wp-security-admin-init.php:357
msgid "Unlocking..."
msgstr ""

#: src/admin/wp-security-admin-init.php:358
msgid "Clearing..."
msgstr ""

#: src/admin/wp-security-admin-init.php:359
msgid "Importing..."
msgstr ""

#: src/admin/wp-security-admin-init.php:360
msgid "Exporting..."
msgstr ""

#: src/admin/wp-security-admin-init.php:361
msgid "Refreshing..."
msgstr ""

#: src/admin/wp-security-admin-init.php:362
msgid "Scanning..."
msgstr ""

#: src/admin/wp-security-admin-init.php:363
msgid "Close"
msgstr ""

#: src/admin/wp-security-admin-init.php:364
msgid "Completed."
msgstr ""

#: src/admin/wp-security-admin-init.php:365
msgid "Refreshed."
msgstr ""

#: src/admin/wp-security-admin-init.php:366
msgid "Deleted."
msgstr ""

#: src/admin/wp-security-admin-init.php:367
msgid "show more"
msgstr ""

#: src/admin/wp-security-admin-init.php:368
msgid "hide"
msgstr ""

#: src/admin/wp-security-admin-init.php:369
msgid "But the following notices have been raised"
msgstr ""

#: src/admin/wp-security-admin-init.php:370
msgid "Disabling..."
msgstr ""

#: src/admin/wp-security-admin-init.php:371
msgid "Setting up firewall..."
msgstr ""

#: src/admin/wp-security-admin-init.php:372
msgid "Downgrading firewall..."
msgstr ""

#: src/admin/wp-security-admin-init.php:373, src/admin/wp-security-dashboard-menu.php:489
msgid "Maintenance mode is currently enabled."
msgstr ""

#: src/admin/wp-security-admin-init.php:373, src/admin/wp-security-dashboard-menu.php:489
msgid "Remember to disable it when you are done."
msgstr ""

#: src/admin/wp-security-admin-init.php:374, src/admin/wp-security-dashboard-menu.php:491
msgid "Maintenance mode is currently disabled."
msgstr ""

#: src/admin/wp-security-admin-init.php:381
msgid "year(s)"
msgstr ""

#: src/admin/wp-security-admin-init.php:382
msgid "month(s)"
msgstr ""

#: src/admin/wp-security-admin-init.php:383
msgid "day(s)"
msgstr ""

#: src/admin/wp-security-admin-init.php:384
msgid "hour(s)"
msgstr ""

#: src/admin/wp-security-admin-init.php:385
msgid "minute(s)"
msgstr ""

#: src/admin/wp-security-admin-init.php:386
msgid "second(s)"
msgstr ""

#: src/admin/wp-security-admin-init.php:387
msgid "less than one second"
msgstr ""

#: src/admin/wp-security-admin-init.php:423
msgid "Enjoyed %s? Please leave us a %s rating on %s or %s"
msgstr ""

#: src/admin/wp-security-admin-init.php:423
msgid "We really appreciate your support!"
msgstr ""

#: src/admin/wp-security-admin-init.php:499, src/admin/wp-security-admin-init.php:499, src/classes/wp-security-two-factor-login.php:121, src/classes/wp-security-two-factor-login.php:121
msgid "WP Security"
msgstr ""

#: src/admin/wp-security-admin-init.php:516
msgid "Premium Upgrade"
msgstr ""

#: src/admin/wp-security-admin-menu.php:117
msgid "Press to toggle"
msgstr ""

#: src/admin/wp-security-admin-menu.php:160, src/admin/wp-security-admin-menu.php:176, src/classes/wp-security-commands.php:262, src/classes/commands/wp-brute-force-commands.php:51, src/classes/commands/wp-brute-force-commands.php:123, src/classes/commands/wp-security-tools-commands.php:129, src/classes/commands/wp-security-user-security-commands.php:29, src/classes/commands/wp-security-user-security-commands.php:207, src/classes/commands/wp-security-user-security-commands.php:285, src/classes/commands/wp-security-user-security-commands.php:330, src/classes/commands/wp-security-user-security-commands.php:358, src/classes/commands/wp-security-user-security-commands.php:399, src/classes/commands/wp-security-user-security-commands.php:490
msgid "The settings have been successfully updated."
msgstr ""

#: src/admin/wp-security-admin-menu.php:190, src/classes/commands/wp-brute-force-commands.php:467
msgid "The selected record(s) has been deleted successfully."
msgstr ""

#: src/admin/wp-security-admin-menu.php:200, src/classes/commands/wp-brute-force-commands.php:465
msgid "The selected record(s) have failed to delete."
msgstr ""

#: src/admin/wp-security-brute-force-menu.php:24
msgid "Brute force"
msgstr ""

#: src/admin/wp-security-brute-force-menu.php:35, src/admin/wp-security-dashboard-menu.php:532, src/templates/wp-admin/brute-force/login-whitelist.php:16
msgid "Rename login page"
msgstr ""

#: src/admin/wp-security-brute-force-menu.php:39, src/templates/wp-admin/brute-force/rename-login.php:6
msgid "Cookie based brute force prevention"
msgstr ""

#: src/admin/wp-security-brute-force-menu.php:44
msgid "CAPTCHA settings"
msgstr ""

#: src/admin/wp-security-brute-force-menu.php:48, src/templates/wp-admin/brute-force/login-whitelist.php:2
msgid "Login whitelist"
msgstr ""

#: src/admin/wp-security-brute-force-menu.php:52
msgid "404 detection"
msgstr ""

#: src/admin/wp-security-brute-force-menu.php:57
msgid "Honeypot"
msgstr ""

#: src/admin/wp-security-brute-force-menu.php:116, src/classes/commands/wp-brute-force-commands.php:316
msgid "Your Cloudflare Turnstile configuration is invalid."
msgstr ""

#: src/admin/wp-security-brute-force-menu.php:116, src/classes/commands/wp-brute-force-commands.php:316
msgid "Please enter the correct Cloudflare Turnstile keys below to use the Turnstile feature."
msgstr ""

#. translators: %s: Admin Dashboard > WP Security > Brute Force > Login CAPTCHA Tab Link
#: src/admin/wp-security-brute-force-menu.php:120, src/classes/wp-security-general-init-tasks.php:760, src/classes/commands/wp-brute-force-commands.php:318
msgid "Your Google reCAPTCHA configuration is invalid."
msgstr ""

#: src/admin/wp-security-brute-force-menu.php:120, src/classes/commands/wp-brute-force-commands.php:318
msgid "Please enter the correct reCAPTCHA keys below to use the reCAPTCHA feature."
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:34, src/admin/wp-security-dashboard-menu.php:218, src/admin/wp-security-dashboard-menu.php:589, src/classes/wp-security-user-login.php:83
msgid "Locked IP addresses"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:38
msgid "Permanent block list"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:42, src/templates/wp-admin/dashboard/audit-logs.php:3
msgid "Audit logs"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:46
msgid "Debugging"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:50
msgid "Premium upgrade"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:205
msgid "Security strength meter"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:206
msgid "Security points breakdown"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:207
msgid "Spread the word"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:208
msgid "Get to know the developers"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:209
msgid "Critical feature status"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:210
msgid "Last 5 login summary"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:211
msgid "Maintenance mode status"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:215
msgid "Brute force prevention login page"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:217, src/admin/wp-security-dashboard-menu.php:549, src/admin/wp-security-user-security-menu.php:42
msgid "Logged in users"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:224, src/admin/wp-security-dashboard-menu.php:477
msgid "View all"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:278
msgid "Website strength:"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:283
msgid "Total Achievable Points:"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:284
msgid "Current Score of Your Site:"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:334
msgid "We are working hard to make your WordPress site more secure."
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:334
msgid "Please support us, here is how:"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:335
msgid "Follow us on"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:339
msgid "Post to X"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:343
msgid "Give us a good rating"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:350
msgid "Wanna know more about the developers behind this plugin?"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:365
msgid "Admin username"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:369, src/admin/wp-security-user-security-menu.php:34, src/classes/grade-system/wp-security-feature-item-manager.php:81, src/templates/wp-admin/dashboard/locked-ip.php:4
msgid "Login lockout"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:373
msgid "File permission"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:378
msgid "Basic firewall"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:383, src/admin/wp-security-database-menu.php:86, src/classes/grade-system/wp-security-feature-item-manager.php:148
msgid "Database prefix"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:388
msgid "PHP file editing"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:393
msgid "Renamed login page"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:397
msgid "Hidden WP meta info"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:405
msgid "Below is the current status of the critical features that you should activate on your site to achieve a minimum level of recommended security"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:450, src/admin/wp-security-dashboard-menu.php:469
msgid "Date"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:450
msgid "Logins"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:469
msgid "User"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:465
msgid "No data found."
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:498, src/templates/wp-admin/tools/visitor-lockout.php:14
msgid "Enable maintenance mode"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:507
msgid "Configure"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:513
msgid "Cookie-based brute force"
msgstr ""

#. translators: %s: Brute Force Login URL
#. translators: %s: Rename Login URL
#: src/admin/wp-security-dashboard-menu.php:517, src/admin/wp-security-dashboard-menu.php:535
msgid "The %s feature is currently active."
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:518, src/admin/wp-security-dashboard-menu.php:536
msgid "Your new WordPress login URL is now:"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:551
msgid "Number of users currently logged into your site (including you) is:"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:552
msgid "There are no other users currently logged in."
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:566
msgid "Number of users currently logged in site-wide (including you) is:"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:567
msgid "There are no other site-wide users currently logged in."
msgstr ""

#. translators: %s: Users Online URL
#. translators: %s: Number of locked out IPs
#: src/admin/wp-security-dashboard-menu.php:581, src/admin/wp-security-dashboard-menu.php:598
msgid "Go to the %s menu to see more details"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:596
msgid "Number of temporarily locked out IP addresses:"
msgstr ""

#: src/admin/wp-security-dashboard-menu.php:593
msgid "There are no IP addresses currently locked out."
msgstr ""

#: src/admin/wp-security-database-menu.php:19
msgid "Database security"
msgstr ""

#: src/admin/wp-security-database-menu.php:49
msgid "UpdraftPlus is installed but currently not active."
msgstr ""

#: src/admin/wp-security-database-menu.php:49
msgid "Follow this link to activate UpdraftPlus, to take a backup."
msgstr ""

#: src/admin/wp-security-database-menu.php:54
msgid "Follow this link to install UpdraftPlus, to take a database backup."
msgstr ""

#: src/admin/wp-security-database-menu.php:91
msgid "Database backup"
msgstr ""

#: src/admin/wp-security-database-menu.php:114
msgid "Nonce check failed for DB prefix change operation."
msgstr ""

#: src/admin/wp-security-database-menu.php:134
msgid "prefix contains HTML tags"
msgstr ""

#: src/admin/wp-security-database-menu.php:137
msgid "prefix contains invalid characters, the prefix should only contain alphanumeric and underscore characters."
msgstr ""

#: src/admin/wp-security-database-menu.php:129
msgid "Please enter a value for the DB prefix."
msgstr ""

#: src/admin/wp-security-database-menu.php:121
msgid "The plugin has detected that it cannot write to the wp-config.php file."
msgstr ""

#: src/admin/wp-security-database-menu.php:121
msgid "This feature can only be used if the plugin can successfully write to the wp-config.php file."
msgstr ""

#: src/admin/wp-security-database-menu.php:169
msgid "Take a database backup using UpdraftPlus"
msgstr ""

#: src/admin/wp-security-database-menu.php:204
msgid "Error - Could not get tables or no tables found!"
msgstr ""

#: src/admin/wp-security-database-menu.php:208
msgid "Starting DB prefix change operations....."
msgstr ""

#: src/admin/wp-security-database-menu.php:210
msgid "Your WordPress system has a total of %s tables and your new DB prefix will be: %s"
msgstr ""

#: src/admin/wp-security-database-menu.php:218
msgid "A backup copy of your wp-config.php file was created successfully!"
msgstr ""

#: src/admin/wp-security-database-menu.php:215, src/classes/wp-security-utility.php:316
msgid "Failed to make a backup of the wp-config.php file."
msgstr ""

#: src/admin/wp-security-database-menu.php:215, src/classes/wp-security-utility.php:316
msgid "This operation will not go ahead."
msgstr ""

#: src/admin/wp-security-database-menu.php:243
msgid "%s table name update failed"
msgstr ""

#: src/admin/wp-security-database-menu.php:255
msgid "%s tables had their prefix updated successfully!"
msgstr ""

#: src/admin/wp-security-database-menu.php:253
msgid "Please change the prefix manually for the above tables to: %s"
msgstr ""

#: src/admin/wp-security-database-menu.php:277
msgid "The \"wp-config.php\" file was not able to be modified."
msgstr ""

#: src/admin/wp-security-database-menu.php:277
msgid "Please modify this file manually using your favourite editor and search for variable \"$table_prefix\" and assign the following value to that variable: %s"
msgstr ""

#: src/admin/wp-security-database-menu.php:275
msgid "wp-config.php file was updated successfully!"
msgstr ""

#: src/admin/wp-security-database-menu.php:288
msgid "The options table records which had references to the old DB prefix were updated successfully!"
msgstr ""

#: src/admin/wp-security-database-menu.php:285, src/admin/wp-security-database-menu.php:301
msgid "Update of table %s failed: unable to change %s to %s"
msgstr ""

#: src/admin/wp-security-database-menu.php:304
msgid "The %s table records which had references to the old DB prefix were updated successfully!"
msgstr ""

#: src/admin/wp-security-database-menu.php:325
msgid "Error updating user_meta table where new meta_key = %s, old meta_key = %s and user_id = %s."
msgstr ""

#: src/admin/wp-security-database-menu.php:330
msgid "The usermeta table records which had references to the old DB prefix were updated successfully!"
msgstr ""

#: src/admin/wp-security-database-menu.php:332
msgid "The database prefix change tasks have been completed."
msgstr ""

#: src/admin/wp-security-database-menu.php:379
msgid "Checking for MySQL tables of type \"view\"....."
msgstr ""

#: src/admin/wp-security-database-menu.php:396
msgid "Update of the following MySQL view definition failed: %s"
msgstr ""

#: src/admin/wp-security-database-menu.php:403
msgid "%s view definitions were updated successfully."
msgstr ""

#: src/admin/wp-security-filescan-menu.php:36, src/classes/grade-system/wp-security-feature-item-manager.php:392
msgid "File change detection"
msgstr ""

#: src/admin/wp-security-filescan-menu.php:40
msgid "Malware scan"
msgstr ""

#: src/admin/wp-security-filescan-menu.php:64, src/classes/commands/wp-security-file-scan-commands.php:95
msgid "Nothing is currently scheduled"
msgstr ""

#: src/admin/wp-security-filesystem-menu.php:18
msgid "File security"
msgstr ""

#: src/admin/wp-security-filesystem-menu.php:30, src/classes/grade-system/wp-security-feature-item-manager.php:158
msgid "File permissions"
msgstr ""

#: src/admin/wp-security-filesystem-menu.php:35, src/templates/wp-admin/filesystem-security/file-protection.php:2
msgid "File protection"
msgstr ""

#: src/admin/wp-security-filesystem-menu.php:40
msgid "Host system logs"
msgstr ""

#: src/admin/wp-security-filesystem-menu.php:45
msgid "Copy protection"
msgstr ""

#: src/admin/wp-security-filesystem-menu.php:49
msgid "Frames"
msgstr ""

#: src/admin/wp-security-firewall-menu.php:33
msgid "PHP rules"
msgstr ""

#: src/admin/wp-security-firewall-menu.php:37
msgid ".htaccess rules"
msgstr ""

#: src/admin/wp-security-firewall-menu.php:42
msgid "6G firewall rules"
msgstr ""

#: src/admin/wp-security-firewall-menu.php:47
msgid "Internet bots"
msgstr ""

#: src/admin/wp-security-firewall-menu.php:52
msgid "Block & allow lists"
msgstr ""

#: src/admin/wp-security-firewall-menu.php:57, src/admin/wp-security-settings-menu.php:60, src/templates/wp-admin/firewall/advanced-settings.php:2
msgid "Advanced settings"
msgstr ""

#: src/admin/wp-security-firewall-menu.php:123
msgid "We were unable to access the firewall's configuration file:"
msgstr ""

#: src/admin/wp-security-firewall-menu.php:125
msgid "As a result, the firewall will be unavailable."
msgstr ""

#: src/admin/wp-security-firewall-menu.php:126
msgid "Please check your PHP error log for further information."
msgstr ""

#: src/admin/wp-security-firewall-menu.php:127
msgid "If you're unable to locate your PHP log file, please contact your web hosting company to ask them where it can be found on their setup."
msgstr ""

#: src/admin/wp-security-firewall-setup-notice.php:391
msgid "We were unable to create the file necessary to give you the highest level of protection."
msgstr ""

#: src/admin/wp-security-firewall-setup-notice.php:392
msgid "Your firewall will have reduced protection which means some of your firewall's functionality will be unavailable."
msgstr ""

#: src/admin/wp-security-firewall-setup-notice.php:393
msgid "If you would like to manually set up the necessary file, please follow these steps:"
msgstr ""

#. translators: %s Bootstrap file name.
#. translators: %s Firewall file name.
#: src/admin/wp-security-firewall-setup-notice.php:397, src/admin/wp-security-firewall-setup-notice.php:495
msgid "1. Create a file with the name %s in the same directory as your WordPress install is in, i.e.:"
msgstr ""

#: src/admin/wp-security-firewall-setup-notice.php:401
msgid "2. Paste in the following code:"
msgstr ""

#: src/admin/wp-security-firewall-setup-notice.php:403, src/admin/wp-security-firewall-setup-notice.php:504
msgid "3. Save the file and press the 'Try again' button below:"
msgstr ""

#: src/admin/wp-security-firewall-setup-notice.php:422
msgid "1. Open the following file:"
msgstr ""

#: src/admin/wp-security-firewall-setup-notice.php:432
msgid "2. Look for the following:"
msgstr ""

#: src/admin/wp-security-firewall-setup-notice.php:428
msgid "2. Look for the auto_prepend_file directive."
msgstr ""

#: src/admin/wp-security-firewall-setup-notice.php:438
msgid "3. Change it to the following:"
msgstr ""

#: src/admin/wp-security-firewall-setup-notice.php:442
msgid "4. Save the file  and press the 'Try again' button below:"
msgstr ""

#: src/admin/wp-security-firewall-setup-notice.php:442, src/admin/wp-security-firewall-setup-notice.php:483
msgid "You may have to wait up to 5 minutes before the settings take effect."
msgstr ""

#: src/admin/wp-security-firewall-setup-notice.php:500
msgid "2. Paste in the following directives:"
msgstr ""

#: src/admin/wp-security-firewall-setup-notice.php:476
msgid "1. Open your php.ini file."
msgstr ""

#: src/admin/wp-security-firewall-setup-notice.php:479
msgid "2. Set the auto_prepend_file directive like below:"
msgstr ""

#: src/admin/wp-security-firewall-setup-notice.php:483
msgid "3. Restart the webserver and refresh the page"
msgstr ""

#: src/admin/wp-security-firewall-setup-notice.php:524
msgid "We were unable to set up your firewall with the highest level of protection."
msgstr ""

#: src/admin/wp-security-firewall-setup-notice.php:525
msgid "Your firewall will have reduced functionality."
msgstr ""

#: src/admin/wp-security-firewall-setup-notice.php:529
msgid "To give your site the highest level of protection, please follow these steps:"
msgstr ""

#: src/admin/wp-security-firewall-setup-notice.php:542
msgid "Note: if you're unable to perform any of the aforementioned steps, please ask your web hosting provider for further assistance."
msgstr ""

#: src/admin/wp-security-firewall-setup-notice.php:559
msgid "Try again"
msgstr ""

#: src/admin/wp-security-firewall-setup-notice.php:581
msgid "We have detected that your AIOS firewall is not fully installed, and therefore does not have the highest level of protection."
msgstr ""

#: src/admin/wp-security-firewall-setup-notice.php:582
msgid "Your firewall will have reduced functionality until it has been upgraded."
msgstr ""

#: src/admin/wp-security-firewall-setup-notice.php:584
msgid "Upgrade your protection now"
msgstr ""

#: src/admin/wp-security-list-404.php:48, src/admin/wp-security-list-audit.php:62, src/admin/wp-security-list-locked-ip.php:65
msgid "Are you sure you want to delete this item?"
msgstr ""

#: src/admin/wp-security-list-404.php:48, src/admin/wp-security-list-404.php:148, src/admin/wp-security-list-audit.php:62, src/admin/wp-security-list-locked-ip.php:65, src/admin/wp-security-list-locked-ip.php:160, src/admin/wp-security-list-registered-users.php:37, src/admin/wp-security-list-registered-users.php:104, src/templates/wp-admin/filesystem-security/partials/wp-file-access.php:24
msgid "Delete"
msgstr ""

#: src/admin/wp-security-list-404.php:60, src/admin/wp-security-list-registered-users.php:38
msgid "Are you sure you want to block this IP address?"
msgstr ""

#: src/admin/wp-security-list-404.php:60
msgid "Temporarily block"
msgstr ""

#: src/admin/wp-security-list-404.php:61, src/admin/wp-security-list-comment-spammer-ip.php:34
msgid "Are you sure you want to permanently block this IP address?"
msgstr ""

#: src/admin/wp-security-list-404.php:61, src/admin/wp-security-list-404.php:147, src/admin/wp-security-list-audit.php:98
msgid "Blacklist IP"
msgstr ""

#: src/admin/wp-security-list-404.php:56, src/admin/wp-security-list-404.php:52
msgid "Are you sure you want to unblock this item?"
msgstr ""

#: src/admin/wp-security-list-404.php:56, src/admin/wp-security-list-404.php:52, src/admin/wp-security-list-permanent-blocked-ip.php:91
msgid "Unblock"
msgstr ""

#: src/admin/wp-security-list-404.php:113
msgid "Event type"
msgstr ""

#: src/admin/wp-security-list-404.php:114, src/admin/wp-security-list-logged-in-users.php:55, src/admin/wp-security-list-registered-users.php:85
msgid "IP address"
msgstr ""

#: src/admin/wp-security-list-404.php:118
msgid "Lock status"
msgstr ""

#: src/admin/wp-security-list-404.php:146
msgid "Temporarily block IP"
msgstr ""

#: src/admin/wp-security-list-404.php:167, src/admin/wp-security-list-404.php:175, src/admin/wp-security-list-404.php:182, src/admin/wp-security-list-audit.php:239, src/admin/wp-security-list-comment-spammer-ip.php:102, src/admin/wp-security-list-locked-ip.php:176, src/admin/wp-security-list-locked-ip.php:184, src/admin/wp-security-list-permanent-blocked-ip.php:103, src/admin/wp-security-list-registered-users.php:122, src/admin/wp-security-list-registered-users.php:130, src/admin/wp-security-list-registered-users.php:138
msgid "Please select some records using the checkboxes"
msgstr ""

#: src/admin/wp-security-list-404.php:207, src/admin/wp-security-list-404.php:241
msgid "Could not process the request because the IP addresses for the selected entries could not be found."
msgstr ""

#: src/admin/wp-security-list-404.php:216
msgid "The selected IP addresses are now temporarily blocked."
msgstr ""

#: src/admin/wp-security-list-404.php:260, src/classes/commands/wp-brute-force-commands.php:510
msgid "The selected IP addresses have been added to the blacklist and will be permanently blocked."
msgstr ""

#: src/admin/wp-security-list-audit.php:78
msgid "Are you sure you want to unblacklist this IP address?"
msgstr ""

#: src/admin/wp-security-list-audit.php:79
msgid "Are you sure you want to unlock this IP address?"
msgstr ""

#: src/admin/wp-security-list-audit.php:80
msgid "Are you sure you want to temporarily lock this IP address?"
msgstr ""

#: src/admin/wp-security-list-audit.php:81
msgid "Are you sure you want to blacklist this IP address?"
msgstr ""

#: src/admin/wp-security-list-audit.php:94
msgid "Lock IP"
msgstr ""

#: src/admin/wp-security-list-audit.php:90, src/admin/wp-security-list-locked-ip.php:64, src/admin/wp-security-list-locked-ip.php:159
msgid "Unlock"
msgstr ""

#: src/admin/wp-security-list-audit.php:86
msgid "Unblacklist"
msgstr ""

#: src/admin/wp-security-list-audit.php:113
msgid "No event type available."
msgstr ""

#: src/admin/wp-security-list-audit.php:149
msgid "No stack trace available."
msgstr ""

#: src/admin/wp-security-list-audit.php:162, src/admin/wp-security-list-audit.php:184, src/classes/commands/wp-security-log-commands.php:112
msgid "Stack trace"
msgstr ""

#: src/admin/wp-security-list-audit.php:162
msgid "Show trace"
msgstr ""

#: src/admin/wp-security-list-audit.php:177, src/admin/wp-security-list-debug.php:54, src/classes/commands/wp-security-log-commands.php:105
msgid "Level"
msgstr ""

#: src/admin/wp-security-list-audit.php:178, src/admin/wp-security-list-debug.php:55, src/classes/commands/wp-security-log-commands.php:106
msgid "Network ID"
msgstr ""

#: src/admin/wp-security-list-audit.php:179, src/admin/wp-security-list-debug.php:56, src/admin/wp-security-list-logged-in-users.php:56, src/classes/commands/wp-security-log-commands.php:107
msgid "Site ID"
msgstr ""

#: src/admin/wp-security-list-audit.php:180, src/admin/wp-security-list-locked-ip.php:128, src/classes/commands/wp-security-log-commands.php:108
msgid "Username"
msgstr ""

#: src/admin/wp-security-list-audit.php:181, src/classes/commands/wp-security-log-commands.php:109
msgid "IP"
msgstr ""

#: src/admin/wp-security-list-audit.php:182, src/classes/commands/wp-security-log-commands.php:110
msgid "Event"
msgstr ""

#: src/admin/wp-security-list-audit.php:183, src/classes/commands/wp-security-log-commands.php:111
msgid "Details"
msgstr ""

#: src/admin/wp-security-list-audit.php:218
msgid "Delete all"
msgstr ""

#: src/admin/wp-security-list-audit.php:219
msgid "Delete selected"
msgstr ""

#: src/admin/wp-security-list-audit.php:220
msgid "Delete filtered"
msgstr ""

#: src/admin/wp-security-list-audit.php:252
msgid "Please select the level or the event type filter or filter by a search term"
msgstr ""

#: src/admin/wp-security-list-audit.php:274
msgid "All levels"
msgstr ""

#: src/admin/wp-security-list-audit.php:286
msgid "All events"
msgstr ""

#: src/admin/wp-security-list-audit.php:295
msgid "Filter"
msgstr ""

#: src/admin/wp-security-list-audit.php:300, src/templates/wp-admin/brute-force/404-detection.php:99, src/templates/wp-admin/brute-force/404-detection.php:108
msgid "Export to CSV"
msgstr ""

#: src/admin/wp-security-list-comment-spammer-ip.php:34, src/admin/wp-security-list-comment-spammer-ip.php:79
msgid "Block"
msgstr ""

#: src/admin/wp-security-list-comment-spammer-ip.php:57
msgid "Spammer IP"
msgstr ""

#: src/admin/wp-security-list-comment-spammer-ip.php:58
msgid "Number of spam comments from this IP"
msgstr ""

#: src/admin/wp-security-list-comment-spammer-ip.php:59
msgid "Status"
msgstr ""

#: src/admin/wp-security-list-comment-spammer-ip.php:132
msgid "The selected IP addresses are now permanently blocked."
msgstr ""

#: src/admin/wp-security-list-debug.php:57
msgid "Message"
msgstr ""

#: src/admin/wp-security-list-debug.php:58
msgid "Type"
msgstr ""

#: src/admin/wp-security-list-locked-ip.php:64
msgid "Are you sure you want to unlock this address range?"
msgstr ""

#: src/admin/wp-security-list-locked-ip.php:99, src/admin/wp-security-list-locked-ip.php:104
msgid "There is no IP lookup result available."
msgstr ""

#: src/admin/wp-security-list-locked-ip.php:107, src/classes/wp-security-user-login.php:414, src/classes/wp-security-user-login.php:415
msgid "Not Found"
msgstr ""

#: src/admin/wp-security-list-locked-ip.php:112, src/admin/wp-security-list-locked-ip.php:132
msgid "IP lookup result"
msgstr ""

#: src/admin/wp-security-list-locked-ip.php:112
msgid "Show result"
msgstr ""

#: src/admin/wp-security-list-locked-ip.php:126
msgid "Locked IP/range"
msgstr ""

#: src/admin/wp-security-list-locked-ip.php:127, src/admin/wp-security-list-logged-in-users.php:53, src/admin/wp-security-list-registered-users.php:80
msgid "User ID"
msgstr ""

#: src/admin/wp-security-list-locked-ip.php:129, src/admin/wp-security-list-permanent-blocked-ip.php:73
msgid "Reason"
msgstr ""

#: src/admin/wp-security-list-locked-ip.php:130
msgid "Date locked"
msgstr ""

#: src/admin/wp-security-list-locked-ip.php:131
msgid "Release date"
msgstr ""

#: src/admin/wp-security-list-locked-ip.php:209
msgid "The selected IP entries were unlocked successfully."
msgstr ""

#: src/admin/wp-security-list-logged-in-users.php:35
msgid "Are you sure you want to force this user to be logged out of this session?"
msgstr ""

#: src/admin/wp-security-list-logged-in-users.php:35, src/admin/wp-security-user-security-menu.php:38, src/classes/grade-system/wp-security-feature-item-manager.php:89
msgid "Force logout"
msgstr ""

#: src/admin/wp-security-list-logged-in-users.php:54, src/admin/wp-security-list-registered-users.php:81
msgid "Login name"
msgstr ""

#: src/admin/wp-security-list-logged-in-users.php:98
msgid "Logout all"
msgstr ""

#: src/admin/wp-security-list-logged-in-users.php:99
msgid "Logout selected"
msgstr ""

#: src/admin/wp-security-list-logged-in-users.php:163
msgid "Some users were not logged out due to the ID being invalid, or them being a super admin or a member of a different subsite on a multisite"
msgstr ""

#: src/admin/wp-security-list-permanent-blocked-ip.php:45
msgid "Are you sure you want to unblock this IP address?"
msgstr ""

#: src/admin/wp-security-list-permanent-blocked-ip.php:72
msgid "Blocked IP"
msgstr ""

#: src/admin/wp-security-list-permanent-blocked-ip.php:74
msgid "Date and Time"
msgstr ""

#: src/admin/wp-security-list-permanent-blocked-ip.php:131, src/classes/commands/wp-security-ip-commands.php:75
msgid "Failed to unblock and delete the selected record(s)."
msgstr ""

#: src/admin/wp-security-list-permanent-blocked-ip.php:127, src/classes/commands/wp-security-ip-commands.php:77
msgid "Successfully unblocked and deleted the selected record(s)."
msgstr ""

#: src/admin/wp-security-list-registered-users.php:35
msgid "View"
msgstr ""

#: src/admin/wp-security-list-registered-users.php:36
msgid "Are you sure you want to approve this account?"
msgstr ""

#: src/admin/wp-security-list-registered-users.php:36, src/admin/wp-security-list-registered-users.php:103
msgid "Approve"
msgstr ""

#: src/admin/wp-security-list-registered-users.php:37
msgid "Are you sure you want to delete this account?"
msgstr ""

#: src/admin/wp-security-list-registered-users.php:38, src/admin/wp-security-list-registered-users.php:105
msgid "Block IP"
msgstr ""

#: src/admin/wp-security-list-registered-users.php:57
msgid "blocked"
msgstr ""

#: src/admin/wp-security-list-registered-users.php:82
msgid "Email"
msgstr ""

#: src/admin/wp-security-list-registered-users.php:83
msgid "Register date"
msgstr ""

#: src/admin/wp-security-list-registered-users.php:84
msgid "Account status"
msgstr ""

#: src/admin/wp-security-list-registered-users.php:178
msgid "The selected accounts were approved successfully."
msgstr ""

#: src/admin/wp-security-list-registered-users.php:183
msgid "The following accounts failed to update successfully:"
msgstr ""

#: src/admin/wp-security-list-registered-users.php:203
msgid "Your account is now active"
msgstr ""

#: src/admin/wp-security-list-registered-users.php:204
msgid "Your account with username: %s is now active"
msgstr ""

#: src/admin/wp-security-list-registered-users.php:233
msgid "The selected accounts were deleted successfully."
msgstr ""

#: src/admin/wp-security-list-registered-users.php:252
msgid "Only invalid IP addresses were provided: you can not block your own IP address"
msgstr ""

#: src/admin/wp-security-list-registered-users.php:265
msgid "The selected IP addresses were successfully added to the permanent block list."
msgstr ""

#: src/admin/wp-security-list-registered-users.php:266, src/classes/commands/wp-security-user-security-commands.php:583
msgid "View Blocked IPs"
msgstr ""

#: src/admin/wp-security-settings-menu.php:34
msgid "General settings"
msgstr ""

#: src/admin/wp-security-settings-menu.php:38, src/admin/wp-security-settings-menu.php:43, src/classes/wp-security-settings-tasks.php:58, src/classes/wp-security-settings-tasks.php:62, src/classes/wp-security-settings-tasks.php:86
msgid "file"
msgstr ""

#: src/admin/wp-security-settings-menu.php:48
msgid "Delete plugin settings"
msgstr ""

#: src/admin/wp-security-settings-menu.php:52
msgid "WP version info"
msgstr ""

#: src/admin/wp-security-settings-menu.php:56
msgid "Import/Export"
msgstr ""

#: src/admin/wp-security-settings-menu.php:152
msgid "Default - if correct, then this is the best option"
msgstr ""

#: src/admin/wp-security-settings-menu.php:153
msgid "Only use if you're using Cloudflare."
msgstr ""

#: src/admin/wp-security-settings-menu.php:164
msgid "no value (i.e. empty) on your server"
msgstr ""

#: src/admin/wp-security-settings-menu.php:161
msgid "(current value: %s)"
msgstr ""

#: src/admin/wp-security-spam-menu.php:19, src/templates/wp-admin/dashboard/may-also-like.php:174
msgid "Spam prevention"
msgstr ""

#: src/admin/wp-security-spam-menu.php:30
msgid "Comment spam"
msgstr ""

#: src/admin/wp-security-spam-menu.php:34
msgid "Comment spam IP monitoring"
msgstr ""

#: src/admin/wp-security-spam-menu.php:82, src/classes/commands/wp-security-comment-commands.php:212
msgid "Spammer IPs added to permanent block list today:"
msgstr ""

#: src/admin/wp-security-spam-menu.php:82, src/classes/commands/wp-security-comment-commands.php:213
msgid "All time total:"
msgstr ""

#: src/admin/wp-security-spam-menu.php:82, src/classes/commands/wp-security-comment-commands.php:214
msgid "View blocked IPs"
msgstr ""

#: src/admin/wp-security-spam-menu.php:70, src/classes/commands/wp-security-comment-commands.php:207
msgid "You currently have no IP addresses permanently blocked due to spam."
msgstr ""

#: src/admin/wp-security-tools-menu.php:32, src/templates/wp-admin/tools/password-tool.php:2, src/templates/wp-admin/user-security/http-authentication.php:81
msgid "Password tool"
msgstr ""

#: src/admin/wp-security-tools-menu.php:36
msgid "WHOIS lookup"
msgstr ""

#: src/admin/wp-security-tools-menu.php:40, src/templates/wp-admin/tools/custom-htaccess.php:28
msgid "Custom .htaccess rules"
msgstr ""

#: src/admin/wp-security-tools-menu.php:45
msgid "Visitor lockout"
msgstr ""

#: src/admin/wp-security-user-security-menu.php:30
msgid "User accounts"
msgstr ""

#: src/admin/wp-security-user-security-menu.php:46
msgid "Manual approval"
msgstr ""

#: src/admin/wp-security-user-security-menu.php:50
msgid "Salt"
msgstr ""

#: src/admin/wp-security-user-security-menu.php:55
msgid "HTTP authentication"
msgstr ""

#: src/admin/wp-security-user-security-menu.php:59, src/templates/wp-admin/user-security/additional.php:9
msgid "Additional settings"
msgstr ""

#: src/admin/wp-security-user-security-menu.php:108
msgid "Account login name"
msgstr ""

#: src/admin/wp-security-user-security-menu.php:117
msgid "Edit user"
msgstr ""

#: src/admin/wp-security-user-security-menu.php:221
msgid "Failed to save 'Enable for WordPress dashboard'."
msgstr ""

#: src/admin/wp-security-user-security-menu.php:221, src/admin/wp-security-user-security-menu.php:232, src/templates/wp-admin/user-security/http-authentication.php:9
msgid "Your site is currently not using https."
msgstr ""

#: src/admin/wp-security-user-security-menu.php:232
msgid "Failed to save 'Enable for frontend'."
msgstr ""

#: src/admin/wp-security-user-security-menu.php:240
msgid "Failed to save 'Username'."
msgstr ""

#: src/admin/wp-security-user-security-menu.php:240
msgid "Please enter a value for the HTTP authentication username."
msgstr ""

#: src/admin/wp-security-user-security-menu.php:247
msgid "Failed to save 'Password'."
msgstr ""

#: src/admin/wp-security-user-security-menu.php:247
msgid "Please enter a value for the HTTP authentication password."
msgstr ""

#: src/classes/wp-security-ajax.php:146
msgid "Options can only be saved by network admin"
msgstr ""

#: src/classes/wp-security-ajax.php:177
msgid "The command \"%s\" was not found"
msgstr ""

#: src/classes/wp-security-audit-events.php:102
msgid "Core updated"
msgstr ""

#: src/classes/wp-security-audit-events.php:103
msgid "Plugin installed"
msgstr ""

#: src/classes/wp-security-audit-events.php:104
msgid "Plugin activated"
msgstr ""

#: src/classes/wp-security-audit-events.php:105
msgid "Plugin updated"
msgstr ""

#: src/classes/wp-security-audit-events.php:106
msgid "Plugin deactivated"
msgstr ""

#: src/classes/wp-security-audit-events.php:107
msgid "Plugin deleted"
msgstr ""

#: src/classes/wp-security-audit-events.php:108
msgid "Theme installed"
msgstr ""

#: src/classes/wp-security-audit-events.php:109
msgid "Theme activated"
msgstr ""

#: src/classes/wp-security-audit-events.php:110
msgid "Theme updated"
msgstr ""

#: src/classes/wp-security-audit-events.php:111
msgid "Theme deleted"
msgstr ""

#: src/classes/wp-security-audit-events.php:112
msgid "Translation updated"
msgstr ""

#: src/classes/wp-security-audit-events.php:113
msgid "Entity changed"
msgstr ""

#: src/classes/wp-security-audit-events.php:114
msgid "Successful login"
msgstr ""

#: src/classes/wp-security-audit-events.php:115
msgid "Successful logout"
msgstr ""

#: src/classes/wp-security-audit-events.php:116
msgid "Failed login"
msgstr ""

#: src/classes/wp-security-audit-events.php:117
msgid "User registration"
msgstr ""

#: src/classes/wp-security-audit-events.php:118
msgid "User deleted"
msgstr ""

#: src/classes/wp-security-audit-events.php:119
msgid "User removed from blog"
msgstr ""

#: src/classes/wp-security-audit-events.php:120
msgid "Table migration"
msgstr ""

#: src/classes/wp-security-audit-events.php:121
msgid "Rule triggered"
msgstr ""

#: src/classes/wp-security-audit-events.php:122
msgid "Rule not triggered"
msgstr ""

#: src/classes/wp-security-audit-events.php:123
msgid "Rule active"
msgstr ""

#: src/classes/wp-security-audit-events.php:124
msgid "Rule not active"
msgstr ""

#: src/classes/wp-security-audit-events.php:125
msgid "Password reset"
msgstr ""

#: src/classes/wp-security-audit-events.php:635
msgid "(force logout)"
msgstr ""

#. translators: %s: User name
#: src/classes/wp-security-audit-text-handler.php:18
msgid "Successful login with username: %s"
msgstr ""

#: src/classes/wp-security-audit-text-handler.php:29
msgid "Successful logout with username:"
msgstr ""

#. translators: 1: Old version, 2: New version
#: src/classes/wp-security-audit-text-handler.php:40
msgid "WordPress updated from version %1$s to %2$s"
msgstr ""

#: src/classes/wp-security-audit-text-handler.php:50
msgid "Plugin"
msgstr ""

#: src/classes/wp-security-audit-text-handler.php:63, src/classes/wp-security-audit-text-handler.php:61, src/templates/wp-admin/brute-force/captcha-provider.php:57
msgid "Theme"
msgstr ""

#: src/classes/wp-security-audit-text-handler.php:78
msgid "An unknown entity has changed, please check the stacktrace for more details"
msgstr ""

#: src/classes/wp-security-audit-text-handler.php:76
msgid "Entity: \"%s\" has changed, please check the stacktrace for more details"
msgstr ""

#. translators: 1: Slug, 2: Language name, 3: Version
#: src/classes/wp-security-audit-text-handler.php:97
msgid "Theme \"%1$s\" %2$s translations updated to version %3$s"
msgstr ""

#: src/classes/wp-security-audit-text-handler.php:94
msgid "Plugin \"%1$s\" %2$s translations updated to version %3$s"
msgstr ""

#: src/classes/wp-security-audit-text-handler.php:91
msgid "Core %1$s translations updated to version %2$s"
msgstr ""

#. translators: %s: User name
#: src/classes/wp-security-audit-text-handler.php:115
msgid "Failed login attempt with a unknown username: %s"
msgstr ""

#: src/classes/wp-security-audit-text-handler.php:112
msgid "Failed login attempt with a known username: %s"
msgstr ""

#: src/classes/wp-security-audit-text-handler.php:109
msgid "Event imported from the failed logins table"
msgstr ""

#. translators: %s: Registered User name
#: src/classes/wp-security-audit-text-handler.php:134
msgid "User %s registered"
msgstr ""

#: src/classes/wp-security-audit-text-handler.php:131
msgid "User %s registered and set to pending"
msgstr ""

#: src/classes/wp-security-audit-text-handler.php:128
msgid "Admin %1$s registered new user: %2$s"
msgstr ""

#. translators: 1: From table, 2: To table
#: src/classes/wp-security-audit-text-handler.php:150
msgid "Failed to migrate the `%1$s` table data to the `%2$s` table"
msgstr ""

#: src/classes/wp-security-audit-text-handler.php:147
msgid "Successfully migrated the `%1$s` table data to the `%2$s` table"
msgstr ""

#. translators: 1: Rule name, 2: Rule family
#: src/classes/wp-security-audit-text-handler.php:165
msgid "\"%1$s [%2$s]\" rule has been triggered."
msgstr ""

#. translators: 1: Rule name, 2: Rule family
#: src/classes/wp-security-audit-text-handler.php:169
msgid "\"%1$s [%2$s]\" rule was not triggered."
msgstr ""

#. translators: 1: Rule name, 2: Rule family
#: src/classes/wp-security-audit-text-handler.php:173
msgid "\"%1$s [%2$s]\" rule is active."
msgstr ""

#. translators: 1: Rule name, 2: Rule family
#: src/classes/wp-security-audit-text-handler.php:177
msgid "\"%1$s [%2$s]\" rule is not active."
msgstr ""

#: src/classes/wp-security-audit-text-handler.php:190
msgid "Configure this rule"
msgstr ""

#. translators: %s: User login
#: src/classes/wp-security-audit-text-handler.php:204
msgid "Password for user account: `%s` successfully changed"
msgstr ""

#. translators: 1: User login, 2: User ID, 3: Reassign
#: src/classes/wp-security-audit-text-handler.php:219
msgid "User account: `%1$s` with ID: `%2$s` has been deleted and all content has been reassigned to user with ID: `%3$s`"
msgstr ""

#: src/classes/wp-security-audit-text-handler.php:216
msgid "User account: %1$s with ID: `%2$s` has been deleted"
msgstr ""

#. translators: 1: User login, 2: User ID, 3: Blog ID, 4: Reassign
#: src/classes/wp-security-audit-text-handler.php:235
msgid "User account: `%1$s` with ID: `%2$s` has been removed from the blog with ID: `%3$s` and all content has been reassigned to user with  ID: `%4$s`"
msgstr ""

#: src/classes/wp-security-audit-text-handler.php:232
msgid "User account: %1$s with ID: `%2$s` has been removed from the blog with ID: `%3$s`"
msgstr ""

#: src/classes/wp-security-captcha.php:100
msgid "Auto"
msgstr ""

#: src/classes/wp-security-captcha.php:101
msgid "Light"
msgstr ""

#: src/classes/wp-security-captcha.php:102
msgid "Dark"
msgstr ""

#: src/classes/wp-security-captcha.php:182, src/classes/wp-security-captcha.php:222, src/classes/wp-security-captcha.php:364
msgid "Please enter an answer in digits:"
msgstr ""

#: src/classes/wp-security-captcha.php:212
msgid "This content is password protected."
msgstr ""

#: src/classes/wp-security-captcha.php:212
msgid "To view it please enter your password below:"
msgstr ""

#: src/classes/wp-security-captcha.php:213, src/templates/wp-admin/user-security/http-authentication.php:74
msgid "Password:"
msgstr ""

#: src/classes/wp-security-captcha.php:231
msgid "Enter"
msgstr ""

#: src/classes/wp-security-captcha.php:246
msgid "Captcha verification failed."
msgstr ""

#: src/classes/wp-security-captcha.php:246
msgid "Please try again."
msgstr ""

#: src/classes/wp-security-captcha.php:459
msgid "one"
msgstr ""

#: src/classes/wp-security-captcha.php:460
msgid "two"
msgstr ""

#: src/classes/wp-security-captcha.php:461
msgid "three"
msgstr ""

#: src/classes/wp-security-captcha.php:462
msgid "four"
msgstr ""

#: src/classes/wp-security-captcha.php:463
msgid "five"
msgstr ""

#: src/classes/wp-security-captcha.php:464
msgid "six"
msgstr ""

#: src/classes/wp-security-captcha.php:465
msgid "seven"
msgstr ""

#: src/classes/wp-security-captcha.php:466
msgid "eight"
msgstr ""

#: src/classes/wp-security-captcha.php:467
msgid "nine"
msgstr ""

#: src/classes/wp-security-captcha.php:468
msgid "ten"
msgstr ""

#: src/classes/wp-security-captcha.php:469
msgid "eleven"
msgstr ""

#: src/classes/wp-security-captcha.php:470
msgid "twelve"
msgstr ""

#: src/classes/wp-security-captcha.php:471
msgid "thirteen"
msgstr ""

#: src/classes/wp-security-captcha.php:472
msgid "fourteen"
msgstr ""

#: src/classes/wp-security-captcha.php:473
msgid "fifteen"
msgstr ""

#: src/classes/wp-security-captcha.php:474
msgid "sixteen"
msgstr ""

#: src/classes/wp-security-captcha.php:475
msgid "seventeen"
msgstr ""

#: src/classes/wp-security-captcha.php:476
msgid "eighteen"
msgstr ""

#: src/classes/wp-security-captcha.php:477
msgid "nineteen"
msgstr ""

#: src/classes/wp-security-captcha.php:478
msgid "twenty"
msgstr ""

#: src/classes/wp-security-captcha.php:811, src/classes/wp-security-general-init-tasks.php:687
msgid "Your CAPTCHA answer was incorrect - please try again."
msgstr ""

#: src/classes/wp-security-captcha.php:895
msgid "%s captcha"
msgstr ""

#: src/classes/wp-security-captcha.php:908
msgid "Generate a form-tag to use %s CAPTCHA"
msgstr ""

#: src/classes/wp-security-captcha.php:914
msgid "Insert tag"
msgstr ""

#. translators: %s: Error notification with strong HTML tag.
#: src/classes/wp-security-captcha.php:931, src/classes/wp-security-general-init-tasks.php:729, src/classes/wp-security-user-login.php:151
msgid "%s: Your answer was incorrect - please try again."
msgstr ""

#: src/classes/wp-security-captcha.php:931, src/classes/wp-security-general-init-tasks.php:721, src/classes/wp-security-general-init-tasks.php:729, src/classes/wp-security-user-login.php:110, src/classes/wp-security-user-login.php:151
msgid "ERROR"
msgstr ""

#: src/classes/wp-security-commands.php:68
msgid "The feature item manager could not be initialized."
msgstr ""

#: src/classes/wp-security-commands.php:86
msgid "Invalid IP retrieve method."
msgstr ""

#: src/classes/wp-security-commands.php:262
msgid "The settings update was unsuccessful."
msgstr ""

#: src/classes/wp-security-commands.php:313
msgid "Invalid email address."
msgstr ""

#: src/classes/wp-security-commands.php:322
msgid "The diagnostic report has been sent successfully."
msgstr ""

#: src/classes/wp-security-commands.php:328
msgid "There was an error sending the diagnostic report."
msgstr ""

#: src/classes/wp-security-configure-settings.php:499
msgid "Basic firewall settings disabled"
msgstr ""

#. translators: %s: Dashboard link.
#: src/classes/wp-security-configure-settings.php:501
msgid "Our basic firewall rules have been upgraded and to prevent any unexpected site issues we have disabled the features."
msgstr ""

#: src/classes/wp-security-configure-settings.php:501
msgid "You can enable the features again by logging into your WordPress dashboard."
msgstr ""

#: src/classes/wp-security-configure-settings.php:501, src/classes/wp-security-configure-settings.php:519
msgid "Go to dashboard: %s"
msgstr ""

#: src/classes/wp-security-configure-settings.php:501
msgid "Once logged in you will see a notification where you can decide on which course of action you wish to take."
msgstr ""

#: src/classes/wp-security-configure-settings.php:517
msgid "Blacklist manager disabled notification"
msgstr ""

#. translators: %s: Dashboard link
#: src/classes/wp-security-configure-settings.php:519
msgid "The blacklist manager feature has been updated and to prevent any unexpected site lockouts we have disabled the feature."
msgstr ""

#: src/classes/wp-security-configure-settings.php:519
msgid "You can enable the feature again by logging into your WordPress dashboard."
msgstr ""

#: src/classes/wp-security-configure-settings.php:519
msgid "Once logged in before turning the blacklist manger on please double check your settings to ensure you have not entered your own details."
msgstr ""

#: src/classes/wp-security-cronjob-handler.php:35
msgid "Every 15 minutes"
msgstr ""

#: src/classes/wp-security-debug-logger.php:48
msgid "Unable to get the reason why"
msgstr ""

#: src/classes/wp-security-debug-logger.php:49
msgid "Unable to clear the logs"
msgstr ""

#: src/classes/wp-security-debug.php:516
msgid "Enter your email address"
msgstr ""

#: src/classes/wp-security-debug.php:518
msgid "Send report"
msgstr ""

#: src/classes/wp-security-file-scan.php:78
msgid "All In One WP Security - File change detected"
msgstr ""

#: src/classes/wp-security-file-scan.php:80
msgid "A file change was detected on your system for site URL"
msgstr ""

#: src/classes/wp-security-file-scan.php:80
msgid ". Scan was generated on"
msgstr ""

#: src/classes/wp-security-file-scan.php:81
msgid "A summary of the scan results is shown below:"
msgstr ""

#: src/classes/wp-security-file-scan.php:84
msgid "Login to your site to view the scan details."
msgstr ""

#: src/classes/wp-security-file-scan.php:320
msgid "The following files were added to your host"
msgstr ""

#: src/classes/wp-security-file-scan.php:322, src/classes/wp-security-file-scan.php:330, src/classes/wp-security-file-scan.php:339
msgid "modified on:"
msgstr ""

#: src/classes/wp-security-file-scan.php:328
msgid "The following files were removed from your host"
msgstr ""

#: src/classes/wp-security-file-scan.php:337
msgid "The following files were changed on your host"
msgstr ""

#: src/classes/wp-security-general-init-tasks.php:230, src/classes/wp-security-general-init-tasks.php:537
msgid "Application passwords have been disabled by All-In-One Security plugin."
msgstr ""

#: src/classes/wp-security-general-init-tasks.php:508, src/classes/wp-security-general-init-tasks.php:597, src/classes/wp-security-general-init-tasks.php:703, src/classes/wp-security-user-registration.php:86
msgid "<strong>ERROR</strong>: Your answer was incorrect - please try again."
msgstr ""

#: src/classes/wp-security-general-init-tasks.php:519
msgid "Enter something special:"
msgstr ""

#: src/classes/wp-security-general-init-tasks.php:532
msgid "Application passwords"
msgstr ""

#: src/classes/wp-security-general-init-tasks.php:536
msgid "Disabled"
msgstr ""

#: src/classes/wp-security-general-init-tasks.php:543
msgid "Site admin can only change this setting."
msgstr ""

#: src/classes/wp-security-general-init-tasks.php:541
msgid "Change setting"
msgstr ""

#: src/classes/wp-security-general-init-tasks.php:572
msgid "Error: You entered an incorrect CAPTCHA answer, please go back and try again."
msgstr ""

#: src/classes/wp-security-general-init-tasks.php:696
msgid "<strong>ERROR</strong>: Your IP address is currently locked please contact the administrator!"
msgstr ""

#. translators: %s: Error notification with strong HTML tag.
#: src/classes/wp-security-general-init-tasks.php:721
msgid "%s: Your IP address is currently locked."
msgstr ""

#: src/classes/wp-security-general-init-tasks.php:721, src/classes/wp-security-user-login.php:110
msgid "Please contact the administrator."
msgstr ""

#: src/classes/wp-security-general-init-tasks.php:760
msgid "Please enter the correct reCAPTCHA keys %s to use the Google reCAPTCHA feature."
msgstr ""

#: src/classes/wp-security-general-init-tasks.php:760, src/templates/wp-admin/general/moved.php:12, src/templates/wp-admin/scanner/malware-scan.php:7
msgid "here"
msgstr ""

#: src/classes/wp-security-general-init-tasks.php:780
msgid "Your registration is pending approval."
msgstr ""

#: src/classes/wp-security-general-init-tasks.php:817
msgid "You are not authorized to perform this action."
msgstr ""

#: src/classes/wp-security-general-init-tasks.php:852
msgid "Accessing user details is forbidden."
msgstr ""

#: src/classes/wp-security-general-init-tasks.php:847
msgid "Listing users is forbidden."
msgstr ""

#: src/classes/wp-security-general-init-tasks.php:948, src/classes/wp-security-process-renamed-login-page.php:145
msgid "You do not have permission to access this page."
msgstr ""

#: src/classes/wp-security-general-init-tasks.php:949, src/classes/wp-security-process-renamed-login-page.php:145
msgid "Please log in and try again."
msgstr ""

#: src/classes/wp-security-notices.php:30
msgid "An error occurred while rendering this notice, please enable and check your debug log."
msgstr ""

#. translators: 1. HTML text. 2. HTML text, 3. HTML text.
#: src/classes/wp-security-notices.php:45
msgid "Get %1$s with %2$s. %3$s, downtime, and response time issues."
msgstr ""

#: src/classes/wp-security-notices.php:45, src/classes/wp-security-notices.php:48
msgid "added protection"
msgstr ""

#: src/classes/wp-security-notices.php:45, src/classes/wp-security-notices.php:48, src/templates/wp-admin/dashboard/may-also-like.php:24
msgid "Premium"
msgstr ""

#: src/classes/wp-security-notices.php:45, src/classes/wp-security-notices.php:48
msgid "Scan your site for malware"
msgstr ""

#. translators: %s: HTML text.
#: src/classes/wp-security-notices.php:48
msgid "Block traffic by country of origin, get advanced two-factor authentication, %s, and more."
msgstr ""

#: src/classes/wp-security-notices.php:52
msgid "The All in One Security plugin has deactivated some of the firewall settings that you had activated."
msgstr ""

#: src/classes/wp-security-notices.php:55
msgid "We have upgraded the following settings so that they are now part of the PHP firewall instead of .htaccess directives:"
msgstr ""

#: src/classes/wp-security-notices.php:85
msgid "None of the settings that have been upgraded were active."
msgstr ""

#: src/classes/wp-security-notices.php:68
msgid "Completely block xmlrpc.php"
msgstr ""

#: src/classes/wp-security-notices.php:71, src/templates/wp-admin/firewall/partials/proxy-comment.php:14
msgid "Forbid proxy comment posting"
msgstr ""

#: src/classes/wp-security-notices.php:74, src/templates/wp-admin/firewall/partials/bad-query-strings.php:14
msgid "Deny bad query strings"
msgstr ""

#: src/classes/wp-security-notices.php:77
msgid "Advanced character filter"
msgstr ""

#: src/classes/wp-security-notices.php:89
msgid "What would you like to do?"
msgstr ""

#: src/classes/wp-security-notices.php:92
msgid "The All in One Security plugin has disabled the login whitelist setting that you have enabled in the past."
msgstr ""

#: src/classes/wp-security-notices.php:98
msgid "Your website is running on a non-Apache webserver, so the login whitelisting was not functional until the recent update of AIOS (because it relied upon Apache-specific features)."
msgstr ""

#: src/classes/wp-security-notices.php:96
msgid "Your website is running on an Apache webserver, the login whitelisting might not be functional until the recent update of AIOS (because it relied upon Apache-specific module features)."
msgstr ""

#: src/classes/wp-security-notices.php:100
msgid "It began working with AIOS version 5.0.8."
msgstr ""

#: src/classes/wp-security-notices.php:100
msgid "We have disabled it so that your login page will not be blocked unexpectedly."
msgstr ""

#: src/classes/wp-security-notices.php:106
msgid "Whitelisted login IP address(es):"
msgstr ""

#: src/classes/wp-security-notices.php:109
msgid "Would you like to re-enable login whitelisting?"
msgstr ""

#: src/classes/wp-security-notices.php:115
msgid "Removed database backup feature from the All-In-One Security plugin"
msgstr ""

#: src/classes/wp-security-notices.php:117
msgid "Beginning with version 5.0.0, AIOS has replaced the AIOS backup method with the superior UpdraftPlus method."
msgstr ""

#: src/classes/wp-security-notices.php:118
msgid "It remains free and is fully supported by the UpdraftPlus team."
msgstr ""

#: src/classes/wp-security-notices.php:121
msgid "You are seeing this notice because you have previously set up automated database backups in AIOS."
msgstr ""

#: src/classes/wp-security-notices.php:122
msgid "Would you like to set up scheduled backups with UpdraftPlus?"
msgstr ""

#: src/classes/wp-security-notices.php:128
msgid "Setup UpdraftPlus backup plugin"
msgstr ""

#: src/classes/wp-security-notices.php:134
msgid "Important: set up your IP address detection settings"
msgstr ""

#: src/classes/wp-security-notices.php:136
msgid "The All in One Security plugin couldn't be certain about the correct method to detect the IP address for your site visitors with your currently-configured IP address detection settings."
msgstr ""

#: src/classes/wp-security-notices.php:137
msgid "It is important for your security to set the IP address detection settings properly."
msgstr ""

#: src/classes/wp-security-notices.php:140
msgid "Please go to the settings and set them now."
msgstr ""

#: src/classes/wp-security-notices.php:146
msgid "Setup IP address detection settings"
msgstr ""

#: src/classes/wp-security-notices.php:154
msgid "Failed to load the firewall resources."
msgstr ""

#: src/classes/wp-security-notices.php:155
msgid "The firewall won't operate correctly."
msgstr ""

#: src/classes/wp-security-notices.php:162
msgid "Important: Disabled firewall settings"
msgstr ""

#: src/classes/wp-security-notices.php:168
msgid "Reactivate"
msgstr ""

#: src/classes/wp-security-notices.php:169
msgid "Configure manually"
msgstr ""

#: src/classes/wp-security-notices.php:172
msgid "Keep deactivated"
msgstr ""

#: src/classes/wp-security-notices.php:176
msgid "Important: Blacklist manager disabled"
msgstr ""

#: src/classes/wp-security-notices.php:178
msgid "The blacklist manager feature has been disabled to prevent any unexpected site lockouts."
msgstr ""

#: src/classes/wp-security-notices.php:181
msgid "This feature will block any IP address or range listed in its settings, please double check your own details are not included before turning it back on."
msgstr ""

#: src/classes/wp-security-notices.php:188, src/classes/wp-security-notices.php:202
msgid "Edit the settings"
msgstr ""

#: src/classes/wp-security-notices.php:195
msgid "Important: Disabled login whitelist setting"
msgstr ""

#: src/classes/wp-security-notices.php:201
msgid "Turn it back on"
msgstr ""

#: src/classes/wp-security-notices.php:205
msgid "Keep it off"
msgstr ""

#: src/classes/wp-security-notices.php:209
msgid "We noticed AIOS has kept your site safe for a while."
msgstr ""

#: src/classes/wp-security-notices.php:209
msgid "If you like us, please consider leaving a positive review."
msgstr ""

#: src/classes/wp-security-notices.php:209
msgid "If you have any issues or questions, please contact %s."
msgstr ""

#: src/classes/wp-security-notices.php:209
msgid "support"
msgstr ""

#: src/classes/wp-security-notices.php:209
msgid "Thank you so much!"
msgstr ""

#: src/classes/wp-security-notices.php:209
msgid "All-In-One Security (AIOS)"
msgstr ""

#: src/classes/wp-security-notices.php:219
msgid "Enhance your security even more by backing up your site"
msgstr ""

#: src/classes/wp-security-notices.php:220
msgid "UpdraftPlus is the world's most trusted backup plugin."
msgstr ""

#: src/classes/wp-security-notices.php:220
msgid "From the owners of All-In-One Security (AIOS)."
msgstr ""

#: src/classes/wp-security-notices.php:230
msgid "Speed up your site"
msgstr ""

#: src/classes/wp-security-notices.php:231
msgid "After you've secured your site, we recommend you install our WP-Optimize plugin to streamline it for better website performance."
msgstr ""

#: src/classes/wp-security-notices.php:243
msgid "20% off - Black Friday Sale"
msgstr ""

#: src/classes/wp-security-notices.php:245, src/classes/wp-security-notices.php:269, src/classes/wp-security-notices.php:293, src/classes/wp-security-notices.php:317
msgid "at checkout."
msgstr ""

#: src/classes/wp-security-notices.php:245
msgid "Hurry, offer ends 2 December."
msgstr ""

#: src/classes/wp-security-notices.php:247, src/classes/wp-security-notices.php:271, src/classes/wp-security-notices.php:295, src/classes/wp-security-notices.php:319
msgid "Save 20%% with code %s"
msgstr ""

#: src/classes/wp-security-notices.php:267
msgid "20% off - New Year Sale"
msgstr ""

#: src/classes/wp-security-notices.php:269
msgid "Hurry, offer ends 28 January."
msgstr ""

#: src/classes/wp-security-notices.php:291
msgid "20% off - Spring Sale"
msgstr ""

#: src/classes/wp-security-notices.php:293
msgid "Hurry, offer ends 31 May."
msgstr ""

#: src/classes/wp-security-notices.php:315
msgid "20% off - Summer Sale"
msgstr ""

#: src/classes/wp-security-notices.php:317
msgid "Hurry, offer ends 31 August."
msgstr ""

#: src/classes/wp-security-notices.php:339
msgid "The Updraft Plugin Collection Sale"
msgstr ""

#. translators: %s: Checkout code
#: src/classes/wp-security-notices.php:341
msgid "Visit any of our websites and use code %s at checkout to get 20%% off all our plugins."
msgstr ""

#: src/classes/wp-security-notices.php:341
msgid "Be quick, offer ends 30 September."
msgstr ""

#: src/classes/wp-security-process-renamed-login-page.php:159
msgid "Invalid key"
msgstr ""

#: src/classes/wp-security-process-renamed-login-page.php:167
msgid "User action confirmed."
msgstr ""

#: src/classes/wp-security-settings-tasks.php:31, src/classes/wp-security-settings-tasks.php:58, src/classes/wp-security-settings-tasks.php:62, src/classes/wp-security-settings-tasks.php:86, src/classes/commands/wp-security-settings-commands.php:307
msgid "Could not write to the %s file."
msgstr ""

#: src/classes/wp-security-settings-tasks.php:31, src/classes/commands/wp-security-settings-commands.php:307
msgid "Please check the file permissions."
msgstr ""

#: src/classes/wp-security-settings-tasks.php:29
msgid "Settings were successfully saved."
msgstr ""

#: src/classes/wp-security-settings-tasks.php:58, src/classes/wp-security-settings-tasks.php:62, src/classes/wp-security-settings-tasks.php:86
msgid "Please restore it manually using the restore functionality in the \"%s\" tab."
msgstr ""

#: src/classes/wp-security-settings-tasks.php:56
msgid "All the security features have been disabled successfully."
msgstr ""

#: src/classes/wp-security-settings-tasks.php:84
msgid "All firewall rules have been disabled successfully."
msgstr ""

#: src/classes/wp-security-settings-tasks.php:118
msgid "All settings have been successfully reset."
msgstr ""

#: src/classes/wp-security-settings-tasks.php:116
msgid "Deletion of .htaccess directives failed."
msgstr ""

#: src/classes/wp-security-settings-tasks.php:114
msgid "Reset of aio_wp_security_configs option failed."
msgstr ""

#: src/classes/wp-security-settings-tasks.php:112
msgid "Deletion of aio_wp_security_configs option and .htaccess directives failed."
msgstr ""

#: src/classes/wp-security-two-factor-login.php:66
msgid "Two factor authentication - Admin settings"
msgstr ""

#: src/classes/wp-security-two-factor-login.php:123, src/classes/wp-security-two-factor-login.php:123
msgid "Two Factor Auth"
msgstr ""

#: src/classes/wp-security-two-factor-login.php:146
msgid "Two factor authentication"
msgstr ""

#: src/classes/wp-security-two-factor-login.php:195
msgid "PHP OpenSSL or mcrypt module required"
msgstr ""

#: src/classes/wp-security-two-factor-login.php:195
msgid "The All-In-One Security plugin's Two Factor Authentication module requires either the PHP openssl (preferred) or mcrypt module to be installed."
msgstr ""

#: src/classes/wp-security-two-factor-login.php:195
msgid "Please ask your web hosting company to install one of them."
msgstr ""

#: src/classes/wp-security-user-login.php:80
msgid "You have disabled login lockout by defining the AIOS_DISABLE_LOGIN_LOCKOUT constant value as true, and the login lockout setting has enabled it."
msgstr ""

#. translators: 1: Locked IP Addresses admin page link
#: src/classes/wp-security-user-login.php:82
msgid "Delete your login lockout IP from %s and define the AIOS_DISABLE_LOGIN_LOCKOUT constant value as false."
msgstr ""

#. translators: %s: Error notification with strong HTML tag.
#: src/classes/wp-security-user-login.php:110
msgid "%s: Access from your IP address has been blocked for security reasons."
msgstr ""

#: src/classes/wp-security-user-login.php:119
msgid "Service temporarily unavailable"
msgstr ""

#. translators: %s: Notification with strong HTML tag.
#: src/classes/wp-security-user-login.php:178
msgid "%s: Your account is currently not active."
msgstr ""

#: src/classes/wp-security-user-login.php:178
msgid "ACCOUNT PENDING"
msgstr ""

#: src/classes/wp-security-user-login.php:178
msgid "An administrator needs to activate your account before you can login."
msgstr ""

#: src/classes/wp-security-user-login.php:256
msgid "<strong>ERROR</strong>: Invalid login credentials."
msgstr ""

#: src/classes/wp-security-user-login.php:398
msgid "Site Lockout Notification"
msgstr ""

#: src/classes/wp-security-user-login.php:399
msgid "User login lockout events had occurred due to too many failed login attempts or invalid username:"
msgstr ""

#. translators: %s: User name.
#: src/classes/wp-security-user-login.php:403
msgid "Username: %s"
msgstr ""

#. translators: %s: IP Address.
#: src/classes/wp-security-user-login.php:406
msgid "IP address: %s"
msgstr ""

#. translators: %s: IP Range.
#: src/classes/wp-security-user-login.php:409
msgid "IP range: %s"
msgstr ""

#. translators: %s: Org.
#: src/classes/wp-security-user-login.php:418
msgid "Org: %s"
msgstr ""

#. translators: %s: AS.
#: src/classes/wp-security-user-login.php:420
msgid "AS: %s"
msgstr ""

#: src/classes/wp-security-user-login.php:427
msgid "Log into your site WordPress administration panel to see the duration of the lockout or to unlock the user."
msgstr ""

#: src/classes/wp-security-user-login.php:544
msgid "Unlock request notification"
msgstr ""

#. translators: 1: Email 2: Link
#: src/classes/wp-security-user-login.php:546
msgid "You have requested for the account with email address %s to be unlocked."
msgstr ""

#: src/classes/wp-security-user-login.php:546
msgid "Please press the link below to unlock your account:"
msgstr ""

#: src/classes/wp-security-user-login.php:546
msgid "Unlock link: %s"
msgstr ""

#: src/classes/wp-security-user-login.php:546
msgid "After pressing the above link you will be able to login to the WordPress administration panel."
msgstr ""

#. translators: %s: Minute count
#: src/classes/wp-security-user-login.php:746
msgid "Your session has expired because it has been over %d minutes since your last login."
msgstr ""

#: src/classes/wp-security-user-login.php:747, src/classes/wp-security-user-login.php:751
msgid "Please log back in to continue."
msgstr ""

#: src/classes/wp-security-user-login.php:750
msgid "You were logged out because you just changed the \"admin\" username."
msgstr ""

#: src/classes/wp-security-user-login.php:781
msgid "Request unlock"
msgstr ""

#: src/classes/wp-security-user-registration.php:80
msgid "<strong>ERROR</strong>: You are not allowed to register because your IP address is currently locked!"
msgstr ""

#: src/classes/wp-security-utility-file.php:457, src/templates/wp-admin/filesystem-security/partials/file-permissions-table.php:5, src/templates/wp-admin/filesystem-security/partials/file-permissions-table.php:21
msgid "Name"
msgstr ""

#: src/classes/wp-security-utility-file.php:457, src/admin/general/wp-security-ajax-data-table.php:561, src/admin/general/wp-security-ajax-data-table.php:1376, src/admin/general/wp-security-list-table.php:545, src/admin/general/wp-security-list-table.php:1399
msgid "Show more details"
msgstr ""

#: src/classes/wp-security-utility-file.php:458, src/templates/wp-admin/filesystem-security/partials/file-permissions-table.php:6, src/templates/wp-admin/filesystem-security/partials/file-permissions-table.php:22
msgid "File/Folder"
msgstr ""

#: src/classes/wp-security-utility-file.php:459, src/templates/wp-admin/filesystem-security/partials/file-permissions-table.php:7, src/templates/wp-admin/filesystem-security/partials/file-permissions-table.php:23
msgid "Current permissions"
msgstr ""

#: src/classes/wp-security-utility-file.php:460, src/templates/wp-admin/filesystem-security/partials/file-permissions-table.php:8, src/templates/wp-admin/filesystem-security/partials/file-permissions-table.php:24
msgid "Recommended permissions"
msgstr ""

#: src/classes/wp-security-utility-file.php:466
msgid "No action required"
msgstr ""

#: src/classes/wp-security-utility-file.php:462, src/templates/wp-admin/filesystem-security/partials/file-permissions-table.php:9, src/templates/wp-admin/filesystem-security/partials/file-permissions-table.php:25
msgid "Recommended action"
msgstr ""

#: src/classes/wp-security-utility-file.php:463
msgid "Set recommended permissions"
msgstr ""

#. translators: %s: IP Address
#: src/classes/wp-security-utility-ip-address.php:153
msgid "%s is not a valid IP address format."
msgstr ""

#: src/classes/wp-security-utility-ip-address.php:159
msgid "You cannot ban your own IP address:"
msgstr ""

#: src/classes/wp-security-utility-permissions.php:58
msgid "Multisite Super Admin"
msgstr ""

#: src/classes/wp-security-utility.php:263, src/templates/wp-admin/spam-prevention/comment-spam-ip-monitoring.php:101
msgid "The plugin has detected that you are using a Multi-Site WordPress installation."
msgstr ""

#: src/classes/wp-security-utility.php:264
msgid "Some features on this page can only be configured by the \"superadmin\"."
msgstr ""

#. translators: %s: File name
#: src/classes/wp-security-utility.php:1253
msgid "The %s file has already been deleted."
msgstr ""

#: src/classes/wp-security-utility.php:1243
msgid "Failed to delete the %s file."
msgstr ""

#: src/classes/wp-security-utility.php:1243
msgid "Check the file/directory permissions at: %s"
msgstr ""

#: src/classes/wp-security-utility.php:1235
msgid "Successfully deleted the %s file."
msgstr ""

#: src/other-includes/wp-security-stop-users-enumeration.php:8
msgid "Accessing author info via link is forbidden"
msgstr ""

#: src/other-includes/wp-security-unlock-request.php:14
msgid "Powered by WordPress"
msgstr ""

#: src/other-includes/wp-security-unlock-request.php:31
msgid "ERROR: Unable to process your request!"
msgstr ""

#: src/other-includes/wp-security-unlock-request.php:44
msgid "Please enter a valid email address"
msgstr ""

#: src/other-includes/wp-security-unlock-request.php:75
msgid "An email has been sent to you with the unlock instructions."
msgstr ""

#: src/other-includes/wp-security-unlock-request.php:70
msgid "Error: No locked entry was found in the database with your IP address range."
msgstr ""

#: src/other-includes/wp-security-unlock-request.php:56
msgid "User account not found!"
msgstr ""

#: src/other-includes/wp-security-unlock-request.php:92
msgid "You are here because you have been locked out due to too many incorrect login attempts."
msgstr ""

#: src/other-includes/wp-security-unlock-request.php:93
msgid "Please enter your email address and you will receive an email with instructions on how to unlock yourself."
msgstr ""

#: src/other-includes/wp-security-unlock-request.php:103
msgid "Email Address"
msgstr ""

#: src/other-includes/wp-security-unlock-request.php:107
msgid "Send unlock request"
msgstr ""

#: src/admin/general/wp-security-ajax-data-table.php:194, src/admin/general/wp-security-list-table.php:176
msgid "List view"
msgstr ""

#: src/admin/general/wp-security-ajax-data-table.php:195, src/admin/general/wp-security-list-table.php:177
msgid "Excerpt view"
msgstr ""

#: src/admin/general/wp-security-ajax-data-table.php:362, src/admin/general/wp-security-list-table.php:342
msgid "No items found."
msgstr ""

#: src/admin/general/wp-security-ajax-data-table.php:491, src/admin/general/wp-security-list-table.php:473
msgid "Select bulk action"
msgstr ""

#: src/admin/general/wp-security-ajax-data-table.php:493, src/admin/general/wp-security-list-table.php:475
msgid "Bulk actions"
msgstr ""

#: src/admin/general/wp-security-ajax-data-table.php:506, src/admin/general/wp-security-list-table.php:488
msgid "Are you sure you want to perform this bulk action?"
msgstr ""

#: src/admin/general/wp-security-ajax-data-table.php:509, src/admin/general/wp-security-list-table.php:491
msgid "Apply"
msgstr ""

#: src/admin/general/wp-security-ajax-data-table.php:618, src/admin/general/wp-security-list-table.php:616
msgid "Filter by date"
msgstr ""

#: src/admin/general/wp-security-ajax-data-table.php:620, src/admin/general/wp-security-list-table.php:618
msgid "All dates"
msgstr ""

#. translators: 1: month name, 2: 4-digit year
#: src/admin/general/wp-security-ajax-data-table.php:634, src/admin/general/wp-security-list-table.php:633
msgid "%1$s %2$d"
msgstr ""

#: src/admin/general/wp-security-ajax-data-table.php:710, src/admin/general/wp-security-list-table.php:718
msgid "No approved comments"
msgstr ""

#: src/admin/general/wp-security-ajax-data-table.php:710, src/admin/general/wp-security-ajax-data-table.php:690, src/admin/general/wp-security-ajax-data-table.php:733, src/admin/general/wp-security-list-table.php:718, src/admin/general/wp-security-list-table.php:696, src/admin/general/wp-security-list-table.php:741
msgid "No comments"
msgstr ""

#: src/admin/general/wp-security-ajax-data-table.php:733, src/admin/general/wp-security-list-table.php:741
msgid "No pending comments"
msgstr ""

#: src/admin/general/wp-security-ajax-data-table.php:849, src/admin/general/wp-security-list-table.php:858
msgid "First page"
msgstr ""

#: src/admin/general/wp-security-ajax-data-table.php:860, src/admin/general/wp-security-list-table.php:869
msgid "Previous page"
msgstr ""

#: src/admin/general/wp-security-ajax-data-table.php:871, src/admin/general/wp-security-ajax-data-table.php:867, src/admin/general/wp-security-list-table.php:880, src/admin/general/wp-security-list-table.php:876
msgid "Current page"
msgstr ""

#: src/admin/general/wp-security-ajax-data-table.php:885, src/admin/general/wp-security-list-table.php:897
msgid "Next page"
msgstr ""

#: src/admin/general/wp-security-ajax-data-table.php:896, src/admin/general/wp-security-list-table.php:908
msgid "Last page"
msgstr ""

#: src/admin/general/wp-security-ajax-data-table.php:1118, src/admin/general/wp-security-list-table.php:1134
msgid "Select all"
msgstr ""

#. translators: %s: Approved comments.
#: src/admin/general/wp-security-list-table.php:686
msgid "%s comment"
msgid_plural "%s comments"
msgstr[0] ""
msgstr[1] ""

#. translators: %s: Approved comments.
#: src/admin/general/wp-security-list-table.php:688
msgid "%s approved comment"
msgid_plural "%s approved comments"
msgstr[0] ""
msgstr[1] ""

#. translators: %s: Pending comments.
#: src/admin/general/wp-security-list-table.php:690
msgid "%s pending comment"
msgid_plural "%s pending comments"
msgstr[0] ""
msgstr[1] ""

#. translators: %s: Item count.
#. translators: %s: Total items
#: src/admin/general/wp-security-list-table.php:819, src/admin/general/wp-security-list-table.php:1425
msgid "%s item"
msgid_plural "%s items"
msgstr[0] ""
msgstr[1] ""

#. translators: 1: Current page, 2: Total pages
#: src/admin/general/wp-security-list-table.php:889
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr ""

#: src/classes/commands/wp-brute-force-commands.php:32
msgid "You must use alphanumeric characters for your login page slug."
msgstr ""

#: src/classes/commands/wp-brute-force-commands.php:30
msgid "You cannot use the value \"wp-admin\" for your login page slug."
msgstr ""

#: src/classes/commands/wp-brute-force-commands.php:26
msgid "Please enter a value for your login page slug."
msgstr ""

#: src/classes/commands/wp-brute-force-commands.php:110
msgid "You have successfully saved cookie based brute force prevention feature settings."
msgstr ""

#: src/classes/commands/wp-brute-force-commands.php:84
msgid "Settings have not been saved - your secret word must consist only of alphanumeric characters i.e., letters and/or numbers only."
msgstr ""

#: src/classes/commands/wp-brute-force-commands.php:82
msgid "You entered an invalid value for the secret word."
msgstr ""

#: src/classes/commands/wp-brute-force-commands.php:82, src/classes/commands/wp-brute-force-commands.php:92, src/classes/commands/wp-brute-force-commands.php:382, src/classes/commands/wp-brute-force-commands.php:387
msgid "It has been set to the default value."
msgstr ""

#: src/classes/commands/wp-brute-force-commands.php:92
msgid "You entered an invalid value for the redirect url."
msgstr ""

#: src/classes/commands/wp-brute-force-commands.php:101
msgid "You have successfully enabled the cookie based brute force prevention feature"
msgstr ""

#: src/classes/commands/wp-brute-force-commands.php:102
msgid "From now on you will need to log into your WP Admin using the following URL:"
msgstr ""

#: src/classes/commands/wp-brute-force-commands.php:104
msgid "It is important that you save this URL value somewhere in case you forget it, OR,"
msgstr ""

#: src/classes/commands/wp-brute-force-commands.php:105
msgid "simply remember to add a \"?%s=1\" to your current site URL address."
msgstr ""

#: src/classes/commands/wp-brute-force-commands.php:166
msgid "The cookie test failed."
msgstr ""

#: src/classes/commands/wp-brute-force-commands.php:166, src/classes/commands/wp-brute-force-commands.php:167
msgid "Consequently, this feature cannot be used on this site."
msgstr ""

#: src/classes/commands/wp-brute-force-commands.php:167
msgid "The cookie test failed on this server."
msgstr ""

#: src/classes/commands/wp-brute-force-commands.php:162, src/classes/commands/wp-brute-force-commands.php:163
msgid "The cookie test was successful, you can now enable this feature."
msgstr ""

#: src/classes/commands/wp-brute-force-commands.php:382
msgid "You entered a non numeric or negative value for the lockout time length field."
msgstr ""

#: src/classes/commands/wp-brute-force-commands.php:387
msgid "You entered an incorrect format for the \"Redirect URL\" field."
msgstr ""

#: src/classes/commands/wp-brute-force-commands.php:429
msgid "All 404 event logs were deleted from the database successfully."
msgstr ""

#: src/classes/commands/wp-brute-force-commands.php:427
msgid "404 Detection Feature - The operation to delete all the 404 event logs failed"
msgstr ""

#: src/classes/commands/wp-brute-force-commands.php:446
msgid "Invalid action provided for 404 log item."
msgstr ""

#: src/classes/commands/wp-brute-force-commands.php:455
msgid "Invalid 404 event log ID provided."
msgstr ""

#: src/classes/commands/wp-brute-force-commands.php:472, src/classes/commands/wp-brute-force-commands.php:491, src/classes/commands/wp-security-ip-commands.php:23, src/classes/commands/wp-security-ip-commands.php:47, src/classes/commands/wp-security-ip-commands.php:96, src/classes/commands/wp-security-ip-commands.php:122
msgid "Invalid IP provided."
msgstr ""

#: src/classes/commands/wp-brute-force-commands.php:478, src/classes/commands/wp-security-comment-commands.php:154, src/classes/commands/wp-security-user-security-commands.php:575
msgid "You cannot block your own IP address:"
msgstr ""

#: src/classes/commands/wp-brute-force-commands.php:485
msgid "The selected entry is not a valid IP address."
msgstr ""

#: src/classes/commands/wp-brute-force-commands.php:483
msgid "The selected IP address is now temporarily blocked."
msgstr ""

#: src/classes/commands/wp-brute-force-commands.php:515, src/classes/commands/wp-brute-force-commands.php:526
msgid "Invalid log event ID provided."
msgstr ""

#: src/classes/commands/wp-brute-force-commands.php:534
msgid "The selected IP entry could not be unlocked"
msgstr ""

#: src/classes/commands/wp-brute-force-commands.php:532
msgid "Access from the selected IP address has been unblocked."
msgstr ""

#: src/classes/commands/wp-security-comment-commands.php:30
msgid "You entered a non-numeric value for the \"move spam comments to trash after number of days\" field; it has been set to the default value."
msgstr ""

#: src/classes/commands/wp-security-comment-commands.php:44, src/classes/commands/wp-security-comment-commands.php:87
msgid "The settings were successfully updated."
msgstr ""

#: src/classes/commands/wp-security-comment-commands.php:76
msgid "You must enter an integer greater than zero for the \"minimum number of spam comments\" field; it has been set to the default value."
msgstr ""

#: src/classes/commands/wp-security-comment-commands.php:73
msgid "You entered a non-numeric value for the \"minimum number of spam comments\" field; it has been set to the default value."
msgstr ""

#: src/classes/commands/wp-security-comment-commands.php:115
msgid "You must enter an integer greater than zero for the minimum spam comments per IP field; it has been set to the default value."
msgstr ""

#: src/classes/commands/wp-security-comment-commands.php:112
msgid "You entered a non-numeric value for the minimum spam comments per IP field; it has been set to the default value."
msgstr ""

#: src/classes/commands/wp-security-comment-commands.php:148
msgid "Invalid IP address provided."
msgstr ""

#: src/classes/commands/wp-security-comment-commands.php:164
msgid "The selected IP address could not be blocked due to one of the following reasons:"
msgstr ""

#: src/classes/commands/wp-security-comment-commands.php:165
msgid "either it has already been blocked, or your user account lacks sufficient permissions to perform IP blocking."
msgstr ""

#: src/classes/commands/wp-security-comment-commands.php:161
msgid "The selected IP address is now permanently blocked."
msgstr ""

#: src/classes/commands/wp-security-file-scan-commands.php:31
msgid "You entered a non numeric value for the \"backup time interval\" field, it has been set to the default value."
msgstr ""

#: src/classes/commands/wp-security-file-scan-commands.php:57
msgid "The following address was removed because it is not a valid email address:"
msgstr ""

#: src/classes/commands/wp-security-file-scan-commands.php:80
msgid "You have configured your file change detection scan to occur at least once daily."
msgstr ""

#: src/classes/commands/wp-security-file-scan-commands.php:81
msgid "For most websites we recommended that you choose a less frequent schedule such as once every few days, once a week or once a month."
msgstr ""

#: src/classes/commands/wp-security-file-scan-commands.php:82
msgid "Choosing a less frequent schedule will also help reduce your server load."
msgstr ""

#: src/classes/commands/wp-security-file-scan-commands.php:88
msgid "New scan completed: The plugin has detected that you have made changes to the \"File Types To Ignore\" or \"Files To Ignore\" fields."
msgstr ""

#: src/classes/commands/wp-security-file-scan-commands.php:88
msgid "In order to ensure that future scan results are accurate, the old scan data has been refreshed."
msgstr ""

#: src/classes/commands/wp-security-file-scan-commands.php:132
msgid "No previous scan data was found; either run a manual scan or schedule regular file scans"
msgstr ""

#: src/classes/commands/wp-security-file-scan-commands.php:156
msgid "There was an error during the file change detection scan."
msgstr ""

#: src/classes/commands/wp-security-file-scan-commands.php:156
msgid "Please check the plugin debug logs."
msgstr ""

#: src/classes/commands/wp-security-file-scan-commands.php:167, src/templates/wp-admin/scanner/file-change-detect.php:5
msgid "The scan has detected that there was a change in your website's files."
msgstr ""

#: src/classes/commands/wp-security-file-scan-commands.php:167, src/classes/commands/wp-security-file-scan-commands.php:162
msgid "View the file scan results"
msgstr ""

#: src/classes/commands/wp-security-file-scan-commands.php:165
msgid "The scan is complete - There were no file changes detected."
msgstr ""

#: src/classes/commands/wp-security-file-scan-commands.php:162
msgid "This is your first file change detection scan."
msgstr ""

#: src/classes/commands/wp-security-file-scan-commands.php:162
msgid "The details from this scan will be used for future scans."
msgstr ""

#: src/classes/commands/wp-security-file-scan-commands.php:163
msgid "View last file scan results"
msgstr ""

#: src/classes/commands/wp-security-files-commands.php:44
msgid "Unable to change permissions for %s : not in list of valid files"
msgstr ""

#: src/classes/commands/wp-security-files-commands.php:40
msgid "Unable to change permissions for %s"
msgstr ""

#: src/classes/commands/wp-security-files-commands.php:38
msgid "The permissions for %s were successfully changed to %s"
msgstr ""

#: src/classes/commands/wp-security-files-commands.php:86
msgid "Disable PHP file editing failed: unable to modify or make a backup of the wp-config.php file."
msgstr ""

#: src/classes/commands/wp-security-files-commands.php:102
msgid "Could not write to the .htaccess file."
msgstr ""

#: src/classes/commands/wp-security-files-commands.php:99
msgid "The settings have been successfully updated"
msgstr ""

#: src/classes/commands/wp-security-files-commands.php:126
msgid "The files have been deleted successfully."
msgstr ""

#: src/classes/commands/wp-security-files-commands.php:132
msgid "Failed to delete the %s file(s)."
msgstr ""

#: src/classes/commands/wp-security-files-commands.php:132
msgid "Please try to delete them manually."
msgstr ""

#: src/classes/commands/wp-security-files-commands.php:190
msgid "No system logs were found."
msgstr ""

#: src/classes/commands/wp-security-firewall-commands.php:124
msgid "Could not write to the .htaccess file"
msgstr ""

#: src/classes/commands/wp-security-firewall-commands.php:227
msgid "The attempt to save the 'Block fake Googlebots' settings failed, because it was not possible to validate the Googlebot IP addresses:"
msgstr ""

#: src/classes/commands/wp-security-firewall-commands.php:352
msgid "Could not write to the .htaccess file, please check the file permissions."
msgstr ""

#: src/classes/commands/wp-security-firewall-commands.php:424
msgid "Firewall has been setup successfully."
msgstr ""

#: src/classes/commands/wp-security-firewall-commands.php:443
msgid "Something went wrong please try again later."
msgstr ""

#: src/classes/commands/wp-security-firewall-commands.php:443
msgid "Firewall has been downgraded successfully."
msgstr ""

#: src/classes/commands/wp-security-ip-commands.php:19, src/classes/commands/wp-security-ip-commands.php:43, src/classes/commands/wp-security-ip-commands.php:92, src/classes/commands/wp-security-ip-commands.php:118
msgid "No IP provided."
msgstr ""

#: src/classes/commands/wp-security-ip-commands.php:29
msgid "The selected IP address was unlocked successfully."
msgstr ""

#: src/classes/commands/wp-security-ip-commands.php:27
msgid "Failed to unlock the selected IP address."
msgstr ""

#: src/classes/commands/wp-security-ip-commands.php:53
msgid "The selected IP address was unblacklisted successfully."
msgstr ""

#: src/classes/commands/wp-security-ip-commands.php:51
msgid "Failed to unblacklist the selected IP address."
msgstr ""

#: src/classes/commands/wp-security-ip-commands.php:67
msgid "Invalid blocked IP ID provided."
msgstr ""

#: src/classes/commands/wp-security-ip-commands.php:100
msgid "No lockout reason provided."
msgstr ""

#: src/classes/commands/wp-security-ip-commands.php:105
msgid "The selected IP address is now temporarily locked."
msgstr ""

#: src/classes/commands/wp-security-ip-commands.php:130
msgid "The selected IP address has been added to the blacklist."
msgstr ""

#: src/classes/commands/wp-security-log-commands.php:19
msgid "No audit log ID provided."
msgstr ""

#: src/classes/commands/wp-security-log-commands.php:38
msgid "No locked IP record ID provided."
msgstr ""

#: src/classes/commands/wp-security-log-commands.php:61
msgid "The debug logs have been cleared."
msgstr ""

#: src/classes/commands/wp-security-settings-commands.php:30
msgid "Some of the security features could not be disabled."
msgstr ""

#: src/classes/commands/wp-security-settings-commands.php:141
msgid "htaccess backup failed."
msgstr ""

#: src/classes/commands/wp-security-settings-commands.php:135
msgid "Your .htaccess file was successfully backed up."
msgstr ""

#: src/classes/commands/wp-security-settings-commands.php:188
msgid "The restoration .htaccess file has failed, please check the contents of the file you are trying to restore from."
msgstr ""

#: src/classes/commands/wp-security-settings-commands.php:183
msgid "Your .htaccess file has successfully been restored."
msgstr ""

#: src/classes/commands/wp-security-settings-commands.php:180
msgid "The restoration of the .htaccess file failed; please attempt to restore the .htaccess file manually using FTP."
msgstr ""

#: src/classes/commands/wp-security-settings-commands.php:170
msgid "Please choose a valid .htaccess to restore from."
msgstr ""

#: src/classes/commands/wp-security-settings-commands.php:234
msgid "The restoration of the wp-config.php file failed, please check the contents of the file you are trying to restore from."
msgstr ""

#: src/classes/commands/wp-security-settings-commands.php:230
msgid "Your wp-config.php file has successfully been restored."
msgstr ""

#: src/classes/commands/wp-security-settings-commands.php:227
msgid "The restoration of the wp-config.php file failed, please attempt to restore this file manually using FTP."
msgstr ""

#: src/classes/commands/wp-security-settings-commands.php:215
msgid "Please choose a wp-config.php file to restore from."
msgstr ""

#: src/classes/commands/wp-security-settings-commands.php:306
msgid "Your AIOS settings were successfully imported."
msgstr ""

#: src/classes/commands/wp-security-settings-commands.php:387
msgid "The contents of your settings file are invalid, please check the contents of the file you are trying to import settings from."
msgstr ""

#: src/classes/commands/wp-security-settings-commands.php:355
msgid "Import AIOS settings operation failed."
msgstr ""

#: src/classes/commands/wp-security-settings-commands.php:311
msgid "Please choose a file to import your settings from."
msgstr ""

#: src/classes/commands/wp-security-tools-commands.php:39, src/classes/commands/wp-security-tools-commands.php:33
msgid "Nothing to show."
msgstr ""

#: src/classes/commands/wp-security-tools-commands.php:32
msgid "Please enter a valid IP address or domain name to look up."
msgstr ""

#: src/classes/commands/wp-security-tools-commands.php:99
msgid "The plugin was unable to write to the .htaccess file, please edit file manually."
msgstr ""

#: src/classes/commands/wp-security-tools-commands.php:71
msgid "You must enter some .htaccess directives in the text box below"
msgstr ""

#: src/classes/commands/wp-security-tools-commands.php:169, src/classes/commands/wp-security-tools-commands.php:212
msgid "Querying %s: %s"
msgstr ""

#: src/classes/commands/wp-security-tools-commands.php:177, src/classes/commands/wp-security-tools-commands.php:186, src/classes/commands/wp-security-tools-commands.php:228
msgid "Redirected to %s"
msgstr ""

#: src/classes/commands/wp-security-user-security-commands.php:109
msgid "Please enter a value for your username."
msgstr ""

#: src/classes/commands/wp-security-user-security-commands.php:105
msgid "You entered an invalid username, please enter another value."
msgstr ""

#: src/classes/commands/wp-security-user-security-commands.php:74
msgid "The database update operation of the user account failed."
msgstr ""

#: src/classes/commands/wp-security-user-security-commands.php:59
msgid "Username: %s already exists, please enter another value."
msgstr ""

#: src/classes/commands/wp-security-user-security-commands.php:115
msgid "The username has been successfully changed."
msgstr ""

#: src/classes/commands/wp-security-user-security-commands.php:212
msgid "The following options had invalid values and have been set to the defaults: %s"
msgstr ""

#: src/classes/commands/wp-security-user-security-commands.php:319
msgid "You entered a non numeric or negative value for the logout time period field, it has been set to the default value."
msgstr ""

#: src/classes/commands/wp-security-user-security-commands.php:426
msgid "Invalid action provided for logged in user."
msgstr ""

#: src/classes/commands/wp-security-user-security-commands.php:434
msgid "No user ID was provided"
msgstr ""

#: src/classes/commands/wp-security-user-security-commands.php:447
msgid "You cannot log out a user from a different subsite."
msgstr ""

#: src/classes/commands/wp-security-user-security-commands.php:445
msgid "Super admins cannot be logged out."
msgstr ""

#: src/classes/commands/wp-security-user-security-commands.php:443
msgid "You cannot log yourself out"
msgstr ""

#: src/classes/commands/wp-security-user-security-commands.php:441
msgid "Invalid user ID provided."
msgstr ""

#: src/classes/commands/wp-security-user-security-commands.php:463
msgid "Failed to log out the selected user."
msgstr ""

#: src/classes/commands/wp-security-user-security-commands.php:461
msgid "The selected user has been logged out successfully."
msgstr ""

#: src/classes/commands/wp-security-user-security-commands.php:517
msgid "Invalid action provided for registered user."
msgstr ""

#: src/classes/commands/wp-security-user-security-commands.php:526, src/classes/commands/wp-security-user-security-commands.php:549
msgid "No valid user ID was provided"
msgstr ""

#: src/classes/commands/wp-security-user-security-commands.php:542
msgid "The selected account could not be approved."
msgstr ""

#: src/classes/commands/wp-security-user-security-commands.php:538
msgid "The selected account was approved successfully."
msgstr ""

#: src/classes/commands/wp-security-user-security-commands.php:561
msgid "The selected account could not be deleted."
msgstr ""

#: src/classes/commands/wp-security-user-security-commands.php:557
msgid "The selected account was deleted successfully."
msgstr ""

#: src/classes/commands/wp-security-user-security-commands.php:568
msgid "No valid IP address was provided"
msgstr ""

#: src/classes/commands/wp-security-user-security-commands.php:587
msgid "The selected IP could not be added to the permanent block list."
msgstr ""

#: src/classes/commands/wp-security-user-security-commands.php:582
msgid "The selected IP was successfully added to the permanent block list."
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:53
msgid "Remove WP generator meta tag"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:62, src/templates/wp-admin/user-security/wp-username.php:23
msgid "Change admin username"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:71
msgid "Change display name"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:97, src/templates/wp-admin/user-security/additional.php:19
msgid "Disable application password"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:105
msgid "Login Lockout IP whitelisting"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:114
msgid "Registration approval"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:122
msgid "Registration CAPTCHA"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:130
msgid "Enable registration honeypot"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:138
msgid "HTTP authentication for admin and frontend"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:167
msgid "File editing"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:175
msgid "WordPress files access"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:184
msgid "IP and user agent blacklisting"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:193
msgid "Enable basic firewall"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:202
msgid "Enable pingback vulnerability protection"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:210, src/templates/wp-admin/firewall/partials/block-debug-log.php:3
msgid "Block access to debug log file"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:219, src/templates/wp-admin/firewall/partials/listing-directory-contents.php:14
msgid "Disable index views"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:228, src/templates/wp-admin/firewall/partials/disable-trace.php:14
msgid "Disable trace and track"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:237
msgid "Forbid proxy comments"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:245
msgid "Deny bad queries"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:253, src/templates/wp-admin/firewall/partials/advanced-character-filter.php:3
msgid "Advanced character string filter"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:261
msgid "6G firewall"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:269, src/templates/wp-admin/firewall/partials/fake-googlebots.php:3, src/templates/wp-admin/firewall/partials/fake-googlebots.php:33
msgid "Block fake Googlebots"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:277, src/templates/wp-admin/filesystem-security/partials/prevent-hotlinks.php:20
msgid "Prevent image hotlinking"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:285
msgid "Enable IP blocking for 404 detection"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:293
msgid "Disable RSS and ATOM feeds"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:302
msgid "Enable rename login page"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:310, src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:56
msgid "Enable brute force attack prevention"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:318
msgid "Login CAPTCHA"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:326
msgid "Lost password CAPTCHA"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:334
msgid "Custom login CAPTCHA"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:342
msgid "Password-protected CAPTCHA"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:350
msgid "Login IP whitelisting"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:358
msgid "Enable login honeypot"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:367
msgid "Comment CAPTCHA"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:375
msgid "Detect spambots"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:383
msgid "Auto block spam ips"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:401
msgid "Enable Copy Protection"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:409, src/templates/wp-admin/filesystem-security/frames.php:21
msgid "Enable iFrame protection"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:417
msgid "Disable users enumeration"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:425, src/templates/wp-admin/firewall/partials/wp-rest-api.php:35
msgid "Disallow unauthorized REST requests"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:433, src/templates/wp-admin/user-security/salt.php:30
msgid "Enable salt postfix"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:442
msgid "BuddyPress registration CAPTCHA"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:451
msgid "bbPress new topic CAPTCHA"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:460
msgid "Woo login CAPTCHA"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:469
msgid "Woo lost password CAPTCHA"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:478
msgid "Woo register CAPTCHA"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:487
msgid "Woo Checkout CAPTCHA"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:497
msgid "Ban POST requests that have blank user-agent and referer headers"
msgstr ""

#. translators: %s: Plugin name
#: src/classes/grade-system/wp-security-feature-item-manager.php:506
msgid "%s CAPTCHA"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:578
msgid "Feature difficulty"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item-manager.php:580
msgid "Security points"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item.php:64
msgid "Advanced"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item.php:62
msgid "Intermediate"
msgstr ""

#: src/classes/grade-system/wp-security-feature-item.php:60
msgid "Basic"
msgstr ""

#: src/templates/admin/incompatible-plugin.php:6
msgid "Two Factor Authentication"
msgstr ""

#: src/templates/admin/incompatible-plugin.php:10
msgid "Two Factor Authentication currently disabled"
msgstr ""

#. translators: %s: Incompatible plugin name.
#: src/templates/admin/incompatible-plugin.php:13, src/templates/admin/incompatible-plugin.php:18
msgid "Two factor authentication in All In One WP Security is currently disabled because the incompatible plugin %s is active."
msgstr ""

#: src/templates/info/ip-address-ip-range-info.php:2, src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:60, src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:97, src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:120, src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:142, src/templates/wp-admin/firewall/6g.php:29, src/templates/wp-admin/firewall/6g.php:71, src/templates/wp-admin/scanner/file-change-detect.php:97, src/templates/wp-admin/scanner/file-change-detect.php:116, src/templates/wp-admin/settings/advanced-settings.php:64, src/templates/wp-admin/spam-prevention/comment-spam-ip-monitoring.php:46, src/templates/wp-admin/spam-prevention/comment-spam-ip-monitoring.php:81, src/templates/wp-admin/spam-prevention/comment-spam.php:25, src/templates/wp-admin/spam-prevention/comment-spam.php:42, src/templates/wp-admin/spam-prevention/comment-spam.php:86, src/templates/wp-admin/user-security/login-lockout.php:116, src/templates/wp-admin/user-security/salt.php:36, src/templates/wp-admin/brute-force/partials/other-plugins.php:58, src/templates/wp-admin/firewall/partials/advanced-character-filter.php:18, src/templates/wp-admin/firewall/partials/advanced-settings-6g.php:15, src/templates/wp-admin/firewall/partials/bad-query-strings.php:18, src/templates/wp-admin/firewall/partials/basic-firewall-settings.php:17, src/templates/wp-admin/firewall/partials/blank-ref-and-useragent.php:18, src/templates/wp-admin/firewall/partials/block-debug-log.php:18, src/templates/wp-admin/firewall/partials/disable-rss-atom.php:17, src/templates/wp-admin/firewall/partials/disable-trace.php:18, src/templates/wp-admin/firewall/partials/fake-googlebots.php:37, src/templates/wp-admin/firewall/partials/firewall-setup.php:17, src/templates/wp-admin/firewall/partials/listing-directory-contents.php:18, src/templates/wp-admin/firewall/partials/proxy-comment.php:18, src/templates/wp-admin/firewall/partials/xmlrpc-pingback-protection.php:19, src/templates/wp-admin/firewall/partials/xmlrpc-pingback-protection.php:40
msgid "More info"
msgstr ""

#: src/templates/info/ip-address-ip-range-info.php:5
msgid "Each IP address must be on a new line."
msgstr ""

#: src/templates/info/ip-address-ip-range-info.php:6
msgid "You can add comments to the IP entries by placing a '#' at the start of a line."
msgstr ""

#: src/templates/info/ip-address-ip-range-info.php:7
msgid "This can be useful for annotating each IP address with notes (e.g., identifying the individual or system associated with the IP)."
msgstr ""

#: src/templates/info/ip-address-ip-range-info.php:8
msgid "To specify an IPv4 range use a wildcard \"*\" character, acceptable ways to use wildcards is shown in the examples below:"
msgstr ""

#: src/templates/info/ip-address-ip-range-info.php:9
msgid "Example 1: 195.47.89.*"
msgstr ""

#: src/templates/info/ip-address-ip-range-info.php:10
msgid "Example 2: 195.47.*.*"
msgstr ""

#: src/templates/info/ip-address-ip-range-info.php:11
msgid "Example 3: 195.*.*.*"
msgstr ""

#: src/templates/info/ip-address-ip-range-info.php:12
msgid "To specify an IPv6 range use CIDR format as shown in the examples below:"
msgstr ""

#: src/templates/info/ip-address-ip-range-info.php:13
msgid "Example 4: 2401:4900:54c3:af15:2:2:5dc0:0/112"
msgstr ""

#: src/templates/info/ip-address-ip-range-info.php:14
msgid "Example 5: 2001:db8:1263::/48"
msgstr ""

#: src/templates/notices/cookie-based-brute-force-prevention-disabled.php:4
msgid "Cookie based brute force login prevention currently disabled"
msgstr ""

#: src/templates/notices/cookie-based-brute-force-prevention-disabled.php:6
msgid "Cookie based brute force login prevention is currently disabled via the AIOS_DISABLE_COOKIE_BRUTE_FORCE_PREVENTION constant (which is most likely to be defined in your %s)"
msgstr ""

#: src/templates/notices/custom-notice.php:12, src/templates/notices/custom-notice.php:10, src/templates/notices/firewall-setup-notice.php:29, src/templates/notices/horizontal-notice.php:54, src/templates/notices/horizontal-notice.php:52, src/templates/notices/htaccess-to-php-feature-notice.php:12, src/templates/notices/htaccess-to-php-feature-notice.php:10
msgid "Dismiss"
msgstr ""

#: src/templates/notices/custom-notice.php:27, src/templates/wp-admin/dashboard/may-also-like.php:83, src/templates/wp-admin/dashboard/may-also-like.php:94, src/templates/wp-admin/dashboard/may-also-like.php:105, src/templates/wp-admin/dashboard/may-also-like.php:116, src/templates/wp-admin/dashboard/may-also-like.php:127, src/templates/wp-admin/dashboard/may-also-like.php:194, src/templates/wp-admin/dashboard/may-also-like.php:205, src/templates/wp-admin/dashboard/may-also-like.php:216, src/templates/wp-admin/dashboard/may-also-like.php:227, src/templates/wp-admin/dashboard/may-also-like.php:238, src/templates/wp-admin/dashboard/may-also-like.php:259, src/templates/wp-admin/dashboard/may-also-like.php:271, src/templates/wp-admin/dashboard/may-also-like.php:283
msgid "No"
msgstr ""

#: src/templates/notices/disable-login-whitelist.php:4
msgid "Login whitelisting currently disabled"
msgstr ""

#: src/templates/notices/disable-login-whitelist.php:6
msgid "Login whitelisting is currently disabled via the AIOS_DISABLE_LOGIN_WHITELIST constant (which is mostly likely to be defined in your wp-config.php)"
msgstr ""

#: src/templates/notices/firewall-installed-notice.php:5
msgid "Your firewall has been installed with the highest level of protection."
msgstr ""

#: src/templates/notices/firewall-installed-notice.php:6
msgid "You may have to wait 5 minutes for the changes to take effect."
msgstr ""

#: src/templates/notices/firewall-setup-notice.php:12
msgid "Our PHP-based firewall has been created to give you even greater protection."
msgstr ""

#: src/templates/notices/firewall-setup-notice.php:13
msgid "To ensure the PHP-based firewall runs before any potentially vulnerable code in your WordPress site can be reached, it will need to be set up."
msgstr ""

#: src/templates/notices/firewall-setup-notice.php:17
msgid "If you already have our .htaccess-based firewall enabled, you will still need to set up the PHP-based firewall to benefit from its protection."
msgstr ""

#: src/templates/notices/firewall-setup-notice.php:20
msgid "To set up the PHP-based firewall, press the 'Set up now' button below:"
msgstr ""

#: src/templates/notices/firewall-setup-notice.php:23
msgid "Set up now"
msgstr ""

#: src/templates/notices/horizontal-notice.php:42, src/templates/notices/horizontal-notice.php:9
msgid "notice image"
msgstr ""

#: src/templates/notices/horizontal-notice.php:90
msgid "Read more"
msgstr ""

#: src/templates/notices/horizontal-notice.php:88
msgid "Learn more"
msgstr ""

#: src/templates/notices/horizontal-notice.php:86
msgid "Go there"
msgstr ""

#: src/templates/notices/horizontal-notice.php:84
msgid "Sign up"
msgstr ""

#: src/templates/notices/horizontal-notice.php:82
msgid "Get Premium."
msgstr ""

#: src/templates/notices/horizontal-notice.php:80
msgid "Get WP-Optimize"
msgstr ""

#: src/templates/notices/horizontal-notice.php:78
msgid "Get UpdraftPlus"
msgstr ""

#: src/templates/notices/horizontal-notice.php:76
msgid "Get UpdraftCentral"
msgstr ""

#: src/templates/notices/horizontal-notice.php:19
msgid "Review"
msgstr ""

#: src/templates/notices/horizontal-notice.php:23
msgid "Maybe later"
msgstr ""

#: src/templates/notices/horizontal-notice.php:27
msgid "Never"
msgstr ""

#: src/templates/notices/thanks-for-using-main-dash.php:4
msgid "Dismiss (for %s months)"
msgstr ""

#: src/templates/notices/thanks-for-using-main-dash.php:10
msgid "Thank you for using All-In-One Security!"
msgstr ""

#: src/templates/notices/thanks-for-using-main-dash.php:8
msgid "Thank you for using All-In-One Security Premium!"
msgstr ""

#: src/templates/notices/thanks-for-using-main-dash.php:20
msgid "Protect your investment with the ultimate in WordPress website security, AIOS Premium or get more rated plugins below:"
msgstr ""

#: src/templates/notices/thanks-for-using-main-dash.php:27
msgid "All-In-One Security:"
msgstr ""

#: src/templates/notices/thanks-for-using-main-dash.php:31
msgid "Get malware scanning, country blocking, premium support and %s."
msgstr ""

#: src/templates/notices/thanks-for-using-main-dash.php:32
msgid "more"
msgstr ""

#: src/templates/notices/thanks-for-using-main-dash.php:39
msgid "WP-Optimize Premium:"
msgstr ""

#: src/templates/notices/thanks-for-using-main-dash.php:42
msgid "Unlock new ways to speed up your WordPress website."
msgstr ""

#: src/templates/notices/thanks-for-using-main-dash.php:44
msgid "Optimize from the WP-CLI, cache multilingual and multicurrency websites, get premium support and more."
msgstr ""

#: src/templates/notices/thanks-for-using-main-dash.php:49
msgid "UpdraftPlus:"
msgstr ""

#: src/templates/notices/thanks-for-using-main-dash.php:52
msgid "Back up your website with the world’s leading backup and migration plugin."
msgstr ""

#: src/templates/notices/thanks-for-using-main-dash.php:54
msgid "Actively installed on more than 3 million WordPress websites!"
msgstr ""

#: src/templates/notices/thanks-for-using-main-dash.php:59
msgid "Internal Link Juicer:"
msgstr ""

#: src/templates/notices/thanks-for-using-main-dash.php:62
msgid "Automate the building of internal links on your WordPress website."
msgstr ""

#: src/templates/notices/thanks-for-using-main-dash.php:64
msgid "Save time and boost SEO! You don’t need to be an SEO expert to use this plugin."
msgstr ""

#: src/templates/notices/thanks-for-using-main-dash.php:69
msgid "WP Overnight:"
msgstr ""

#: src/templates/notices/thanks-for-using-main-dash.php:72
msgid "Quality add-ons for WooCommerce."
msgstr ""

#: src/templates/notices/thanks-for-using-main-dash.php:74
msgid "Designed to optimize your store, enhance user experience and increase revenue!"
msgstr ""

#: src/templates/notices/thanks-for-using-main-dash.php:79
msgid "More quality plugins"
msgstr ""

#: src/templates/notices/thanks-for-using-main-dash.php:79
msgid "Premium WooCommerce plugins"
msgstr ""

#: src/templates/partials/non-apache-feature-notice.php:9, src/templates/wp-admin/firewall/partials/xmlrpc-warning-notice.php:6
msgid "Attention:"
msgstr ""

#: src/templates/partials/non-apache-feature-notice.php:9
msgid "This feature works only on the Apache server."
msgstr ""

#. translators: %s: Server software
#: src/templates/partials/non-apache-feature-notice.php:11
msgid "You are using the non-apache server %s, so this feature won't work on your site."
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:2
msgid "404 detection configuration"
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:5
msgid "A 404 or Not Found error occurs when somebody tries to access a non-existent page on your website."
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:6
msgid "Typically, most 404 errors happen quite innocently when people have mis-typed a URL or used an old link to page which doesn't exist anymore."
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:7
msgid "However, in some cases you may find many repeated 404 errors which occur in a relatively short space of time and from the same IP address which are all attempting to access a variety of non-existent page URLs."
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:8
msgid "Such behaviour can mean that a hacker might be trying to find a particular page or URL for sinister reasons."
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:9
msgid "With this feature enabled, you can use the table below to manually temporarily block IP addresses."
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:9
msgid "The %1$s Smart 404 %2$s feature in Premium automatically detects and blocks these IP addresses."
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:17, src/templates/wp-admin/dashboard/may-also-like.php:23, src/templates/wp-admin/firewall/block-and-allow-lists.php:16
msgid "All-In-One Security Premium"
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:18, src/templates/wp-admin/firewall/block-and-allow-lists.php:17
msgid "You may also be interested in %s."
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:19, src/templates/wp-admin/firewall/block-and-allow-lists.php:18
msgid "This plugin adds a number of extra features including %s and %s."
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:19, src/templates/wp-admin/firewall/block-and-allow-lists.php:18
msgid "smart 404 blocking"
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:19, src/templates/wp-admin/firewall/block-and-allow-lists.php:18
msgid "country IP blocking"
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:29
msgid "404 detection options"
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:40
msgid "Enable 404 IP detection"
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:43
msgid "Enable this option to detect IP addresses that return 404 errors."
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:49
msgid "Enable 404 event logging"
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:52
msgid "Check this if you want to enable the logging of 404 events"
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:57
msgid "Time length of 404 lockout (minutes)"
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:59
msgid "Set the length of time for which a blocked IP address will be prevented from visiting your site"
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:63
msgid "404 lockout redirect URL"
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:65
msgid "A blocked visitor will be automatically redirected to this URL."
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:69, src/templates/wp-admin/brute-force/captcha-settings.php:59, src/templates/wp-admin/brute-force/honeypot.php:50, src/templates/wp-admin/brute-force/login-whitelist.php:61, src/templates/wp-admin/brute-force/rename-login.php:50, src/templates/wp-admin/filesystem-security/file-protection.php:59, src/templates/wp-admin/filesystem-security/frames.php:30, src/templates/wp-admin/firewall/block-and-allow-lists.php:82, src/templates/wp-admin/scanner/file-change-detect.php:142, src/templates/wp-admin/settings/advanced-settings.php:218, src/templates/wp-admin/settings/delete-plugin-settings.php:37, src/templates/wp-admin/settings/wp-version-info.php:35, src/templates/wp-admin/spam-prevention/comment-spam-ip-monitoring.php:62, src/templates/wp-admin/spam-prevention/comment-spam.php:97, src/templates/wp-admin/user-security/additional.php:28, src/templates/wp-admin/user-security/force-logout.php:36, src/templates/wp-admin/user-security/http-authentication.php:101, src/templates/wp-admin/user-security/login-lockout.php:139, src/templates/wp-admin/user-security/login-lockout.php:181, src/templates/wp-admin/user-security/manual-approval.php:30, src/templates/wp-admin/user-security/salt.php:49, src/templates/wp-admin/user-security/user-enumeration.php:29
msgid "Save settings"
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:75
msgid "404 event logs"
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:78
msgid "This list displays the 404 event logs when somebody tries to access a non-existent page on your website."
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:78
msgid "404 event logs that are older than %1$d days are purged automatically."
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:87, src/templates/wp-admin/brute-force/captcha-settings.php:28, src/templates/wp-admin/dashboard/audit-logs.php:13, src/templates/wp-admin/dashboard/permanent-block.php:18, src/templates/wp-admin/filesystem-security/file-protection.php:32, src/templates/wp-admin/firewall/htaccess-firewall-rules.php:28, src/templates/wp-admin/firewall/php-firewall-rules.php:43, src/templates/wp-admin/user-security/manual-approval.php:51
msgid "Search"
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:105
msgid "Press this button if you wish to download this log in CSV format."
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:113, src/templates/wp-admin/brute-force/404-detection.php:121
msgid "Delete all 404 event logs"
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:118
msgid "Press this button if you wish to purge all 404 event logs from the DB."
msgstr ""

#: src/templates/wp-admin/brute-force/404-detection.php:121
msgid "Are you sure you want to delete all records?"
msgstr ""

#: src/templates/wp-admin/brute-force/captcha-provider.php:3
msgid "CAPTCHA provider"
msgstr ""

#: src/templates/wp-admin/brute-force/captcha-provider.php:9
msgid "CAPTCHA will not work because you have disabled login lockout by activating the AIOS_DISABLE_LOGIN_LOCKOUT constant value in a configuration file."
msgstr ""

#: src/templates/wp-admin/brute-force/captcha-provider.php:10
msgid "To enable it, define AIOS_DISABLE_LOGIN_LOCKOUT constant value as false, or remove it."
msgstr ""

#: src/templates/wp-admin/brute-force/captcha-provider.php:18
msgid "This feature allows you to add a CAPTCHA form on various WordPress login pages and forms."
msgstr ""

#: src/templates/wp-admin/brute-force/captcha-provider.php:18
msgid "Adding a CAPTCHA form on a login page or form is another effective yet simple \"Brute Force\" prevention technique."
msgstr ""

#: src/templates/wp-admin/brute-force/captcha-provider.php:19
msgid "You have the option of using either %s, %s or a plain maths CAPTCHA form."
msgstr ""

#: src/templates/wp-admin/brute-force/captcha-provider.php:20
msgid "We recommend %s as a more privacy-respecting option than %s"
msgstr ""

#: src/templates/wp-admin/brute-force/captcha-provider.php:24
msgid "Default CAPTCHA"
msgstr ""

#: src/templates/wp-admin/brute-force/captcha-provider.php:43, src/templates/wp-admin/brute-force/captcha-provider.php:78
msgid "Site key"
msgstr ""

#: src/templates/wp-admin/brute-force/captcha-provider.php:49, src/templates/wp-admin/brute-force/captcha-provider.php:84
msgid "Secret key"
msgstr ""

#: src/templates/wp-admin/brute-force/captcha-settings.php:6
msgid "Wordpress forms"
msgstr ""

#: src/templates/wp-admin/brute-force/captcha-settings.php:9
msgid "Woocommerce forms"
msgstr ""

#: src/templates/wp-admin/brute-force/captcha-settings.php:13
msgid "Other forms"
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:2
msgid "Brute force prevention firewall settings"
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:6
msgid "A Brute Force Attack is when a hacker tries many combinations of usernames and passwords until they succeed in guessing the right combination."
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:6
msgid "Due to the fact that at any one time there may be many concurrent login attempts occurring on your site via malicious automated robots, this also has a negative impact on your server's memory and performance."
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:6
msgid "The features in this tab will stop the majority of brute force login attacks thus providing even better protection for your WP login page."
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:11
msgid "Read our tutorial on how to use the cookie-based brute force prevention feature"
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:12
msgid "%s."
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:24
msgid "Cookie based brute force login prevention"
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:35, src/templates/wp-admin/brute-force/rename-login.php:30, src/templates/wp-admin/firewall/block-and-allow-lists.php:39
msgid "This feature can lock you out of admin if it doesn't work correctly on your site."
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:35
msgid "Before activating this feature, please read the following %s."
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:35, src/templates/wp-admin/brute-force/rename-login.php:30
msgid "message"
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:59
msgid "Enable this if you want to protect your login page from a brute force attack."
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:64
msgid "This feature will deny access to your WordPress login page for all people except those who have a special cookie in their browser."
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:66
msgid "To use this feature do the following:"
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:68
msgid "1) Enable the checkbox."
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:70
msgid "2) Enter a secret word consisting of alphanumeric characters which will be difficult to guess."
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:70
msgid "This secret word will be useful whenever you need to know the special URL which you will use to access the login page (see point below)."
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:72
msgid "3) You will then be provided with a special login URL."
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:72
msgid "You will need to use this URL to login to your WordPress site instead of the usual login URL."
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:74
msgid "NOTE: The system will deposit a special cookie in your browser which will allow you access to the WordPress administration login page."
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:76
msgid "Any person trying to access your login page who does not have the special cookie in their browser will be automatically blocked."
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:84
msgid "Secret word"
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:86
msgid "Choose a secret word consisting of alphanumeric characters which you can use to access your special URL."
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:86
msgid "You are highly encouraged to choose a word which will be difficult to guess."
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:90
msgid "Re-direct URL"
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:94
msgid "Specify a URL to redirect a hacker to when they try to access your WordPress login page."
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:101
msgid "The URL specified here can be any site's URL and does not have to be your own."
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:103
msgid "This field will default to: http://127.0.0.1 if you do not enter a value."
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:105
msgid "Useful Tip:"
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:107
msgid "It's a good idea to not redirect attempted brute force login attempts to your site because it increases the load on your server."
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:109
msgid "Redirecting a hacker or malicious bot back to \"http://127.0.0.1\" is ideal because it deflects them back to their own local host and puts the load on their server instead of yours."
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:116
msgid "My site has posts or pages which are password protected"
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:119
msgid "Enable this if you are using the native WordPress password protection feature for some or all of your blog posts or pages."
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:124
msgid "In the cases where you are protecting some of your posts or pages using the in-built WordPress password protection feature, a few extra lines of directives and exceptions need to be added so that people trying to access pages are not automatically blocked."
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:126
msgid "By enabling this checkbox, the plugin will add the necessary rules and exceptions so that people trying to access these pages are not automatically blocked."
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:128
msgid "Helpful Tip:"
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:130
msgid "If you do not use the WordPress password protection feature for your posts or pages then it is highly recommended that you leave this checkbox disabled."
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:138
msgid "My site has a theme or plugins which use AJAX"
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:141
msgid "Enable this if your site uses AJAX functionality."
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:146
msgid "In the cases where your WordPress installation has a theme or plugin that uses AJAX, a few extra lines of directives and exceptions need to be added to prevent AJAX requests from being automatically blocked by the brute force prevention feature."
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:148
msgid "By enabling this checkbox, the plugin will add the necessary rules and exceptions so that AJAX operations will work as expected."
msgstr ""

#: src/templates/wp-admin/brute-force/cookie-based-brute-force-prevention.php:158
msgid "Save feature settings"
msgstr ""

#: src/templates/wp-admin/brute-force/honeypot.php:4
msgid "This feature allows you to add a special hidden \"honeypot\" field on WordPress login and registration pages."
msgstr ""

#: src/templates/wp-admin/brute-force/honeypot.php:4
msgid "This will only be visible to robots and not humans."
msgstr ""

#: src/templates/wp-admin/brute-force/honeypot.php:4
msgid "Since robots usually fill in every input field on a form, they will also submit a value for the special hidden honeypot field."
msgstr ""

#: src/templates/wp-admin/brute-force/honeypot.php:4
msgid "The way honeypots work is that a hidden field is placed somewhere inside a form which only robots will submit."
msgstr ""

#: src/templates/wp-admin/brute-force/honeypot.php:4
msgid "If that field contains a value when the form is submitted then a robot has most likely submitted the form and it is consequently dealt with."
msgstr ""

#: src/templates/wp-admin/brute-force/honeypot.php:4
msgid "Therefore, if the plugin detects that this field has a value when the form is submitted, then the robot which is attempting to submit the form on your site will be redirected to its localhost address - http://127.0.0.1."
msgstr ""

#: src/templates/wp-admin/brute-force/honeypot.php:9
msgid "Login form honeypot settings"
msgstr ""

#: src/templates/wp-admin/brute-force/honeypot.php:19
msgid "Enable honeypot on login page"
msgstr ""

#: src/templates/wp-admin/brute-force/honeypot.php:22
msgid "Enable this if you want the honeypot feature for the login page"
msgstr ""

#: src/templates/wp-admin/brute-force/honeypot.php:30
msgid "Registration form honeypot settings"
msgstr ""

#: src/templates/wp-admin/brute-force/honeypot.php:40
msgid "Enable honeypot on registration page"
msgstr ""

#: src/templates/wp-admin/brute-force/honeypot.php:43
msgid "Enable this if you want the honeypot feature for the registration page"
msgstr ""

#: src/templates/wp-admin/brute-force/login-whitelist.php:5
msgid "The All-In-One Security whitelist feature gives you the option of only allowing certain IP addresses or ranges to have access to your WordPress login page."
msgstr ""

#: src/templates/wp-admin/brute-force/login-whitelist.php:5
msgid "This feature will deny login access for all IP addresses which are not in your whitelist as configured in the settings below."
msgstr ""

#: src/templates/wp-admin/brute-force/login-whitelist.php:5
msgid "By allowing/blocking IP addresses, you are using the most secure first line of defence because login access will only be granted to whitelisted IP addresses and other addresses will be blocked as soon as they try to access your login page."
msgstr ""

#: src/templates/wp-admin/brute-force/login-whitelist.php:10
msgid "If you are locked out by the login whitelist feature and you do not have a static IP address, define the following constant %s in wp-config.php to disable the feature."
msgstr ""

#: src/templates/wp-admin/brute-force/login-whitelist.php:15
msgid "Cookie-Based brute force login prevention"
msgstr ""

#: src/templates/wp-admin/brute-force/login-whitelist.php:17
msgid "Attention: If in addition to enabling the white list feature, you also have one of the %s or %s features enabled, %s you will still need to use your secret word or special slug in the URL when trying to access your WordPress login page %s"
msgstr ""

#: src/templates/wp-admin/brute-force/login-whitelist.php:17
msgid "These features are NOT functionally related."
msgstr ""

#: src/templates/wp-admin/brute-force/login-whitelist.php:17
msgid "Having both of them enabled on your site means you are creating 2 layers of security."
msgstr ""

#: src/templates/wp-admin/brute-force/login-whitelist.php:26
msgid "Login IP whitelist settings"
msgstr ""

#: src/templates/wp-admin/brute-force/login-whitelist.php:37
msgid "Enable IP whitelisting"
msgstr ""

#: src/templates/wp-admin/brute-force/login-whitelist.php:40, src/templates/wp-admin/user-security/login-lockout.php:170
msgid "Enable this if you want the whitelisting of selected IP addresses specified in the settings below"
msgstr ""

#: src/templates/wp-admin/brute-force/login-whitelist.php:45
msgid "Your current IP address"
msgstr ""

#: src/templates/wp-admin/brute-force/login-whitelist.php:50, src/templates/wp-admin/settings/advanced-settings.php:82, src/templates/wp-admin/settings/advanced-settings.php:191
msgid "getting..."
msgstr ""

#: src/templates/wp-admin/brute-force/login-whitelist.php:52
msgid "You can copy and paste the above address(es) in the text box below if you want to include it in your login whitelist."
msgstr ""

#: src/templates/wp-admin/brute-force/login-whitelist.php:57, src/templates/wp-admin/user-security/login-lockout.php:176
msgid "Enter whitelisted IP addresses:"
msgstr ""

#: src/templates/wp-admin/brute-force/login-whitelist.php:57, src/templates/wp-admin/user-security/login-lockout.php:176
msgid "Enter one or more IP addresses or IP ranges you wish to include in your whitelist."
msgstr ""

#: src/templates/wp-admin/brute-force/login-whitelist.php:57
msgid "Only the addresses specified here will have access to the WordPress login page."
msgstr ""

#: src/templates/wp-admin/brute-force/rename-login.php:4
msgid "An effective Brute Force prevention technique is to change the default WordPress login page URL."
msgstr ""

#: src/templates/wp-admin/brute-force/rename-login.php:4
msgid "Normally if you wanted to login to WordPress you would type your site's home URL followed by wp-login.php."
msgstr ""

#: src/templates/wp-admin/brute-force/rename-login.php:4
msgid "This feature allows you to change the login URL by setting your own slug and renaming the last portion of the login URL which contains the <strong>wp-login.php</strong> to any string that you like."
msgstr ""

#: src/templates/wp-admin/brute-force/rename-login.php:4
msgid "By doing this, malicious bots and hackers will not be able to access your login page because they will not know the correct login page URL."
msgstr ""

#: src/templates/wp-admin/brute-force/rename-login.php:7
msgid "Login page white list"
msgstr ""

#: src/templates/wp-admin/brute-force/rename-login.php:9
msgid "You may also be interested in the following alternative brute force prevention features:"
msgstr ""

#: src/templates/wp-admin/brute-force/rename-login.php:19
msgid "Rename login page settings"
msgstr ""

#: src/templates/wp-admin/brute-force/rename-login.php:30
msgid "Before activating this feature, you must read the following %s."
msgstr ""

#: src/templates/wp-admin/brute-force/rename-login.php:31
msgid "NOTE: If you are hosting your site on WPEngine or a provider which performs server caching, you will need to ask the host support people to NOT cache your renamed login page."
msgstr ""

#: src/templates/wp-admin/brute-force/rename-login.php:36
msgid "Enable rename login page feature"
msgstr ""

#: src/templates/wp-admin/brute-force/rename-login.php:39
msgid "Enable this if you want the rename login page feature"
msgstr ""

#: src/templates/wp-admin/brute-force/rename-login.php:44
msgid "Login page URL"
msgstr ""

#: src/templates/wp-admin/brute-force/rename-login.php:46
msgid "Enter a string which will represent your secure login page slug."
msgstr ""

#: src/templates/wp-admin/brute-force/rename-login.php:46
msgid "You are encouraged to choose something which is hard to guess and only you will remember."
msgstr ""

#: src/templates/wp-admin/dashboard/debug-logs.php:4
msgid "Debug log options"
msgstr ""

#: src/templates/wp-admin/dashboard/debug-logs.php:7
msgid "Clear logs"
msgstr ""

#: src/templates/wp-admin/dashboard/debug-logs.php:7
msgid "Are you sure you want to clear all the debug logs?"
msgstr ""

#: src/templates/wp-admin/dashboard/debug-logs.php:13
msgid "Debug logs"
msgstr ""

#: src/templates/wp-admin/dashboard/debug-logs.php:24
msgid "This section displays information valuable for diagnosing conflicts, configuration discrepancies, or compatibility concerns with other plugins, themes, or the hosting environment."
msgstr ""

#: src/templates/wp-admin/dashboard/debug-logs.php:25
msgid "You can use this information to help troubleshoot issues you may be experiencing with your WordPress site or send a report to the AIOS team."
msgstr ""

#: src/templates/wp-admin/dashboard/debug-logs.php:36
msgid "Copy/send report"
msgstr ""

#: src/templates/wp-admin/dashboard/debug-logs.php:39
msgid "All-In-One Security diagnostics report"
msgstr ""

#: src/templates/wp-admin/dashboard/debug-logs.php:40
msgid "Copy to clipboard"
msgstr ""

#: src/templates/wp-admin/dashboard/locked-ip.php:5
msgid "This tab displays the list of all IP addresses which are currently temporarily locked out."
msgstr ""

#: src/templates/wp-admin/dashboard/locked-ip.php:9
msgid "Currently locked out IP addresses and ranges"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:5
msgid "All-In-One Security Free vs Premium Comparison Chart"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:7
msgid "FAQs"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:9
msgid "Ask a pre-sales question"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:18
msgid "All-In-One Security Free"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:19
msgid "Free"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:30, src/templates/wp-admin/dashboard/may-also-like.php:136, src/templates/wp-admin/dashboard/may-also-like.php:247, src/templates/wp-admin/dashboard/may-also-like.php:292
msgid "Installed"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:33, src/templates/wp-admin/dashboard/may-also-like.php:139, src/templates/wp-admin/dashboard/may-also-like.php:250, src/templates/wp-admin/dashboard/may-also-like.php:295
msgid "Upgrade"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:38
msgid "Login security feature suite"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:39
msgid "Upgrade your WordPress security and protect against brute-force attacks with login and user security features."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:39
msgid "Limit login attempts, rename the login page to hide it from bots, add CAPTCHA and more."
msgstr ""

#. translators: %s: Features URL
#: src/templates/wp-admin/dashboard/may-also-like.php:42, src/templates/wp-admin/dashboard/may-also-like.php:148, src/templates/wp-admin/dashboard/may-also-like.php:163
msgid "%s"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:42
msgid "See all login security features"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:45, src/templates/wp-admin/dashboard/may-also-like.php:48, src/templates/wp-admin/dashboard/may-also-like.php:61, src/templates/wp-admin/dashboard/may-also-like.php:64, src/templates/wp-admin/dashboard/may-also-like.php:72, src/templates/wp-admin/dashboard/may-also-like.php:75, src/templates/wp-admin/dashboard/may-also-like.php:86, src/templates/wp-admin/dashboard/may-also-like.php:97, src/templates/wp-admin/dashboard/may-also-like.php:108, src/templates/wp-admin/dashboard/may-also-like.php:119, src/templates/wp-admin/dashboard/may-also-like.php:130, src/templates/wp-admin/dashboard/may-also-like.php:151, src/templates/wp-admin/dashboard/may-also-like.php:154, src/templates/wp-admin/dashboard/may-also-like.php:166, src/templates/wp-admin/dashboard/may-also-like.php:169, src/templates/wp-admin/dashboard/may-also-like.php:178, src/templates/wp-admin/dashboard/may-also-like.php:181, src/templates/wp-admin/dashboard/may-also-like.php:197, src/templates/wp-admin/dashboard/may-also-like.php:208, src/templates/wp-admin/dashboard/may-also-like.php:219, src/templates/wp-admin/dashboard/may-also-like.php:230, src/templates/wp-admin/dashboard/may-also-like.php:241, src/templates/wp-admin/dashboard/may-also-like.php:262, src/templates/wp-admin/dashboard/may-also-like.php:274, src/templates/wp-admin/dashboard/may-also-like.php:286
msgid "Yes"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:53
msgid "Two-factor authentication (TFA)"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:58
msgid "Supports Google Authenticator, Microsoft Authenticator, Authy and more."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:69
msgid "Configure TFA by user role."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:80
msgid "Control how often TFA is required on trusted devices."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:91
msgid "Adjust the TFA design to match your brand."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:102
msgid "Generate one-time use emergency codes to regain access if you lose your TFA device."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:113
msgid "TFA works consistently in subsites of WordPress multisite networks."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:124
msgid "Integrate TFA with third party login forms without additional coding."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:145
msgid "Get PHP, .htaccess and 6G firewall rules."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:145
msgid "Spot and block fake Google Bots and more!"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:148
msgid "See all firewall features"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:159
msgid "File and database security"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:160
msgid "Block access to files like readme.html to hide key information from hackers."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:160
msgid "Hide your WordPress database, scan critical folders and files to spot and fix insecure file permissions and more."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:163
msgid "See all file and database security features"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:175
msgid "Prevent annoying spam comments and reduce unnecessary server load."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:175
msgid "Automatically and permanently block IP addresses that exceed a set number of spam comments."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:186
msgid "Site Scanner"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:191
msgid "Monitors and alerts you to file changes outside of normal operations."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:202
msgid "Monitors and alerts you to infection by malware"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:213
msgid "Monitors and alerts you to blacklisting by search engines."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:224
msgid "Monitors and alerts you to downtime."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:235
msgid "Monitors and alerts you to response time issues."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:255
msgid "Smart 404 blocking"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:256
msgid "Automatically block IP addresses based on how many 404 errors they generate."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:256
msgid "Handy charts show how many 404s have occurred and where they’re coming from."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:267
msgid "Country blocking"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:268
msgid "Most attacks come from a handful of countries."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:268
msgid "Prevent most of them by blocking traffic based on country of origin to 99.5% accuracy."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:279
msgid "Premium support"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:280
msgid "We can do more to support you via our own support channels than is allowed in the WordPress forums."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:280
msgid "90% of tickets are responded to within 24 hours."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:303
msgid "Our other plugins"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:323
msgid "UpdraftPlus – the ultimate protection for your site, hard work and business"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:324
msgid "Simplifies backups and restoration."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:324
msgid "It is the world's highest ranking and most popular scheduled backup plugin, with over three million currently-active installs."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:325
msgid "Learn more about UpdraftPlus"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:329
msgid "WP-Optimize – keep your database fast and efficient"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:330
msgid "Makes your site fast and efficient."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:330
msgid "It cleans the database, compresses images and caches pages for ultimate speed."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:331
msgid "Learn more about WP-Optimize"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:335
msgid "UpdraftCentral – save hours managing multiple WP sites from one place"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:336
msgid "Highly efficient way to manage, optimize, update and backup multiple websites from one place."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:337
msgid "Learn more about UpdraftCentral"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:341
msgid "Easy Updates Manager - keep your WordPress site up to date and bug free"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:344
msgid "A light yet powerful plugin that allows you to manage all kinds of updates."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:345
msgid "With a huge number of settings for endless customization."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:346
msgid "Easy Updates Manager is an obvious choice for anyone wanting to take control of their website updates."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:350, src/templates/wp-admin/dashboard/may-also-like.php:366, src/templates/wp-admin/dashboard/may-also-like.php:381, src/templates/wp-admin/dashboard/may-also-like.php:394
msgid "Try for free"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:355
msgid "Internal Link Juicer - a five-star rated internal linking plugin for WordPress"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:359
msgid "This five-star rated plugin automates internal linking."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:360
msgid "It strategically places relevant links within your content."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:364
msgid "Improve your SEO with just a few clicks."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:370
msgid "WP Overnight - quality plugins for your WooCommerce store. 5 star rated invoicing, order and product management tools"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:373
msgid "WP Overnight is an independent plugin shop with a range of WooCommerce plugins."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:374
msgid "Our range of plugins have over 7,500,000 downloads and thousands of loyal customers."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:378, src/templates/wp-admin/dashboard/may-also-like.php:379
msgid "Create PDF invoices, automations, barcodes, reports and so much more."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:385
msgid "WPGetAPI - connect WordPress to APIs without a developer"
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:389
msgid "The easiest way to connect your WordPress website to an external API."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:390
msgid "WPGetAPI is free, powerful and easy to use."
msgstr ""

#: src/templates/wp-admin/dashboard/may-also-like.php:391
msgid "Connect to virtually any REST API and retrieve data without writing a line of code."
msgstr ""

#: src/templates/wp-admin/dashboard/permanent-block.php:4
msgid "This tab displays the list of all permanently blocked IP addresses."
msgstr ""

#: src/templates/wp-admin/dashboard/permanent-block.php:4
msgid "NOTE: This feature does NOT use the .htaccess file to permanently block the IP addresses so it should be compatible with all web servers running WordPress."
msgstr ""

#: src/templates/wp-admin/dashboard/permanent-block.php:8
msgid "Permanently blocked IP addresses"
msgstr ""

#: src/templates/wp-admin/database-security/database-backup.php:3
msgid "Manual backup"
msgstr ""

#: src/templates/wp-admin/database-security/database-backup.php:10, src/templates/wp-admin/database-security/database-backup.php:10
msgid "UpdraftPlus Backup/Restore"
msgstr ""

#: src/templates/wp-admin/database-security/database-backup.php:10
msgid "Your backups are on the UpdraftPlus Backup/Restore admin page."
msgstr ""

#: src/templates/wp-admin/database-security/database-backup.php:12
msgid "Create database backup now"
msgstr ""

#: src/templates/wp-admin/database-security/database-backup.php:21
msgid "Automated scheduled backups"
msgstr ""

#: src/templates/wp-admin/database-security/database-backup.php:26
msgid "Automate backup in the UpdraftPlus plugin"
msgstr ""

#: src/templates/wp-admin/database-security/database-backup.php:29
msgid "The automated backup feature in All-In-One Security was removed as of version 5.0.0."
msgstr ""

#: src/templates/wp-admin/database-security/database-backup.php:29
msgid "For a reliable backup solution, we recommend"
msgstr ""

#: src/templates/wp-admin/database-security/database-prefix.php:2, src/templates/wp-admin/database-security/database-prefix.php:54
msgid "Change database prefix"
msgstr ""

#: src/templates/wp-admin/database-security/database-prefix.php:5
msgid "Your WordPress database is the most important asset of your website because it contains a lot of your site's precious information."
msgstr ""

#: src/templates/wp-admin/database-security/database-prefix.php:5
msgid "The database is also a target for hackers via methods such as SQL injections and malicious and automated code which targets certain tables."
msgstr ""

#: src/templates/wp-admin/database-security/database-prefix.php:5
msgid "One way to add a layer of protection for your DB is to change the default WordPress table prefix from \"wp_\" to something else which will be difficult for hackers to guess."
msgstr ""

#: src/templates/wp-admin/database-security/database-prefix.php:5
msgid "This feature allows you to easily change the prefix to a value of your choice or to a random value set by this plugin."
msgstr ""

#: src/templates/wp-admin/database-security/database-prefix.php:9
msgid "Database prefix options"
msgstr ""

#: src/templates/wp-admin/database-security/database-prefix.php:19
msgid "database backup"
msgstr ""

#: src/templates/wp-admin/database-security/database-prefix.php:20
msgid "It is recommended that you perform a %s before using this feature"
msgstr ""

#: src/templates/wp-admin/database-security/database-prefix.php:29
msgid "Current database table prefix"
msgstr ""

#: src/templates/wp-admin/database-security/database-prefix.php:35
msgid "Your site is currently using the default WordPress database prefix value of \"wp_\"."
msgstr ""

#: src/templates/wp-admin/database-security/database-prefix.php:35
msgid "To increase your site's security you should consider changing the database prefix value to another value."
msgstr ""

#: src/templates/wp-admin/database-security/database-prefix.php:42
msgid "Generate new database table prefix"
msgstr ""

#: src/templates/wp-admin/database-security/database-prefix.php:46
msgid "Enable this if you want the plugin to generate a random 6 character string for the table prefix"
msgstr ""

#: src/templates/wp-admin/database-security/database-prefix.php:47
msgid "OR"
msgstr ""

#: src/templates/wp-admin/database-security/database-prefix.php:49
msgid "Choose your own database prefix by specifying a string which contains letters and/or numbers and/or underscores, example: xyz_"
msgstr ""

#: src/templates/wp-admin/filesystem-security/copy-protection.php:3
msgid "Disable the ability to copy text"
msgstr ""

#: src/templates/wp-admin/filesystem-security/copy-protection.php:14
msgid "This feature allows you to disable the ability to select and copy text from your front end."
msgstr ""

#: src/templates/wp-admin/filesystem-security/copy-protection.php:15
msgid "When admin user is logged in, the feature is automatically disabled for his session."
msgstr ""

#: src/templates/wp-admin/filesystem-security/copy-protection.php:20
msgid "Enable copy protection"
msgstr ""

#: src/templates/wp-admin/filesystem-security/copy-protection.php:23
msgid "Enable this to disable the \"Right click\", \"Text selection\" and \"Copy\" options on the front end of your site."
msgstr ""

#: src/templates/wp-admin/filesystem-security/copy-protection.php:29
msgid "Save copy protection settings"
msgstr ""

#: src/templates/wp-admin/filesystem-security/file-permissions.php:2
msgid "File permissions scan"
msgstr ""

#: src/templates/wp-admin/filesystem-security/file-permissions.php:5
msgid "Your WordPress file and folder permission settings govern the accessibility and read/write privileges of the files and folders which make up your WP installation."
msgstr ""

#: src/templates/wp-admin/filesystem-security/file-permissions.php:5
msgid "Your WP installation already comes with reasonably secure file permission settings for the filesystem."
msgstr ""

#: src/templates/wp-admin/filesystem-security/file-permissions.php:5
msgid "However, sometimes people or other plugins modify the various permission settings of certain core WP folders or files such that they end up making their site less secure because they chose the wrong permission values."
msgstr ""

#: src/templates/wp-admin/filesystem-security/file-permissions.php:5
msgid "This feature will scan the critical WP core folders and files and will highlight any permission settings which are insecure."
msgstr ""

#: src/templates/wp-admin/filesystem-security/file-permissions.php:19
msgid "WP directory and file permissions scan results"
msgstr ""

#: src/templates/wp-admin/filesystem-security/file-permissions.php:12
msgid "This plugin has detected that your site is running on a Windows server."
msgstr ""

#: src/templates/wp-admin/filesystem-security/file-permissions.php:13
msgid "This feature is not applicable for Windows server installations."
msgstr ""

#: src/templates/wp-admin/filesystem-security/file-protection.php:5
msgid "These features allow you to protect your files and assets."
msgstr ""

#: src/templates/wp-admin/filesystem-security/file-protection.php:6
msgid "By protecting your files and assets, you can help prevent nefarious users gain key information and protect your server's resources."
msgstr ""

#: src/templates/wp-admin/filesystem-security/file-protection.php:14, src/templates/wp-admin/filesystem-security/partials/wp-file-access.php:3
msgid "Delete default WP files"
msgstr ""

#: src/templates/wp-admin/filesystem-security/file-protection.php:17, src/templates/wp-admin/filesystem-security/partials/prevent-hotlinks.php:3
msgid "Prevent hotlinking"
msgstr ""

#: src/templates/wp-admin/filesystem-security/file-protection.php:20, src/templates/wp-admin/filesystem-security/partials/php-file-editing.php:3
msgid "Disable PHP file editing"
msgstr ""

#: src/templates/wp-admin/filesystem-security/filesystem-log-result.php:18
msgid "The file %s could not be read"
msgstr ""

#: src/templates/wp-admin/filesystem-security/filesystem-log-result.php:6
msgid "Showing latest entries for file: %s"
msgstr ""

#: src/templates/wp-admin/filesystem-security/frames.php:4
msgid "Prevent your site from being displayed in a frame"
msgstr ""

#: src/templates/wp-admin/filesystem-security/frames.php:15
msgid "This feature allows you to prevent other sites from displaying any of your content via a frame or iframe."
msgstr ""

#: src/templates/wp-admin/filesystem-security/frames.php:16
msgid "When enabled, this feature will set the \"X-Frame-Options\" parameter to \"sameorigin\" in the HTTP header."
msgstr ""

#: src/templates/wp-admin/filesystem-security/frames.php:24
msgid "Enable this to stop other sites from displaying your content in a frame or iframe."
msgstr ""

#: src/templates/wp-admin/filesystem-security/host-system-logs.php:2
msgid "System logs"
msgstr ""

#: src/templates/wp-admin/filesystem-security/host-system-logs.php:5
msgid "Sometimes your hosting platform will produce error or warning logs in a file called \"error_log\"."
msgstr ""

#: src/templates/wp-admin/filesystem-security/host-system-logs.php:5
msgid "Depending on the nature and cause of the error or warning, your hosting server can create multiple instances of this file in numerous directory locations of your WordPress installation."
msgstr ""

#: src/templates/wp-admin/filesystem-security/host-system-logs.php:5
msgid "By occasionally viewing the contents of these logs files you can keep informed of any underlying problems on your system which you might need to address."
msgstr ""

#: src/templates/wp-admin/filesystem-security/host-system-logs.php:11
msgid "View system logs"
msgstr ""

#: src/templates/wp-admin/filesystem-security/host-system-logs.php:13
msgid "Please press the button below to view the latest system logs"
msgstr ""

#: src/templates/wp-admin/filesystem-security/host-system-logs.php:17
msgid "Enter System Log File Name"
msgstr ""

#: src/templates/wp-admin/filesystem-security/host-system-logs.php:19
msgid "Enter your system log file name. (Defaults to error_log)"
msgstr ""

#: src/templates/wp-admin/filesystem-security/host-system-logs.php:22
msgid "View latest system logs"
msgstr ""

#: src/templates/wp-admin/firewall/6g.php:2
msgid "Firewall settings"
msgstr ""

#: src/templates/wp-admin/firewall/6g.php:5
msgid "This feature allows you to activate the %s (or legacy %s) firewall security protection rules designed and produced by %s."
msgstr ""

#: src/templates/wp-admin/firewall/6g.php:6
msgid "The 6G firewall is an updated and improved version of the 5G firewall that is PHP-based and doesn't use a .htaccess file."
msgstr ""

#: src/templates/wp-admin/firewall/6g.php:6
msgid "If you have the 5G firewall active, you might consider activating the 6G firewall instead."
msgstr ""

#: src/templates/wp-admin/firewall/6g.php:7
msgid "The 6G firewall is a simple, flexible blacklist that helps reduce the number of malicious URL requests that hit your website."
msgstr ""

#: src/templates/wp-admin/firewall/6g.php:8
msgid "The added advantage of applying the 6G firewall to your site is that it has been tested and confirmed by the people at PerishablePress.com to be an optimal and least disruptive set of security rules for general WP sites."
msgstr ""

#: src/templates/wp-admin/firewall/6g.php:15
msgid "6G firewall settings"
msgstr ""

#: src/templates/wp-admin/firewall/6g.php:25
msgid "Enable 6G firewall protection"
msgstr ""

#: src/templates/wp-admin/firewall/6g.php:28
msgid "Enable this to apply the recommended 6G firewall protection."
msgstr ""

#: src/templates/wp-admin/firewall/6g.php:32
msgid "This setting will implement the 6G security firewall protection mechanisms on your site which include the following things:"
msgstr ""

#: src/templates/wp-admin/firewall/6g.php:33, src/templates/wp-admin/firewall/6g.php:75
msgid "1) Block forbidden characters commonly used in exploitative attacks."
msgstr ""

#: src/templates/wp-admin/firewall/6g.php:34, src/templates/wp-admin/firewall/6g.php:76
msgid "2) Block malicious encoded URL characters such as the \".css(\" string."
msgstr ""

#: src/templates/wp-admin/firewall/6g.php:35, src/templates/wp-admin/firewall/6g.php:77
msgid "3) Guard against the common patterns and specific exploits in the root portion of targeted URLs."
msgstr ""

#: src/templates/wp-admin/firewall/6g.php:36, src/templates/wp-admin/firewall/6g.php:78
msgid "4) Stop attackers from manipulating query strings by disallowing illicit characters."
msgstr ""

#: src/templates/wp-admin/firewall/6g.php:37, src/templates/wp-admin/firewall/6g.php:79
msgid "....and much more."
msgstr ""

#: src/templates/wp-admin/firewall/6g.php:48
msgid "Show advanced options"
msgstr ""

#: src/templates/wp-admin/firewall/6g.php:49
msgid "Hide advanced options"
msgstr ""

#: src/templates/wp-admin/firewall/6g.php:59
msgid "5G firewall settings"
msgstr ""

#: src/templates/wp-admin/firewall/6g.php:62
msgid "This feature is marked for deprecation and will be removed in a future version of the plugin."
msgstr ""

#: src/templates/wp-admin/firewall/6g.php:67
msgid "Enable legacy 5G firewall protection"
msgstr ""

#: src/templates/wp-admin/firewall/6g.php:70
msgid "Enable this to apply the 5G firewall protection from perishablepress.com to your site."
msgstr ""

#: src/templates/wp-admin/firewall/6g.php:74
msgid "This setting will implement the 5G security firewall protection mechanisms on your site which include the following things:"
msgstr ""

#: src/templates/wp-admin/firewall/6g.php:88
msgid "Save firewall settings"
msgstr ""

#: src/templates/wp-admin/firewall/block-and-allow-lists.php:2
msgid "Ban IPs or user agents"
msgstr ""

#: src/templates/wp-admin/firewall/block-and-allow-lists.php:5
msgid "The All-In-One Security blacklist feature gives you the option of banning certain host IP addresses or ranges and also user agents."
msgstr ""

#: src/templates/wp-admin/firewall/block-and-allow-lists.php:6
msgid "This feature will deny total site access for users which have IP addresses or user agents matching those which you have configured in the settings below."
msgstr ""

#: src/templates/wp-admin/firewall/block-and-allow-lists.php:7
msgid "Black-listed visitors will be blocked as soon as WordPress loads, preventing them from gaining any further access."
msgstr ""

#: src/templates/wp-admin/firewall/block-and-allow-lists.php:26
msgid "IP hosts and user agent blacklist settings"
msgstr ""

#: src/templates/wp-admin/firewall/block-and-allow-lists.php:38
msgid "please read the following message"
msgstr ""

#: src/templates/wp-admin/firewall/block-and-allow-lists.php:39
msgid "You %s before activating this feature."
msgstr ""

#: src/templates/wp-admin/firewall/block-and-allow-lists.php:45
msgid "Enable IP or user agent blacklisting"
msgstr ""

#: src/templates/wp-admin/firewall/block-and-allow-lists.php:48
msgid "Enable this if you want the banning (or blacklisting) of selected IP addresses and/or user agents specified in the settings below"
msgstr ""

#: src/templates/wp-admin/firewall/block-and-allow-lists.php:53, src/templates/wp-admin/firewall/partials/allowlist.php:13
msgid "Enter IP addresses:"
msgstr ""

#: src/templates/wp-admin/firewall/block-and-allow-lists.php:57, src/templates/wp-admin/firewall/partials/allowlist.php:13
msgid "Enter one or more IP addresses or IP ranges."
msgstr ""

#: src/templates/wp-admin/firewall/block-and-allow-lists.php:62
msgid "Enter user agents:"
msgstr ""

#: src/templates/wp-admin/firewall/block-and-allow-lists.php:67
msgid "Enter one or more user agent strings."
msgstr ""

#: src/templates/wp-admin/firewall/block-and-allow-lists.php:68
msgid "More Info"
msgstr ""

#: src/templates/wp-admin/firewall/block-and-allow-lists.php:71
msgid "The user agent string will be checked in a case-insensitive manner."
msgstr ""

#: src/templates/wp-admin/firewall/block-and-allow-lists.php:72
msgid "Each user agent string must be on a new line."
msgstr ""

#: src/templates/wp-admin/firewall/block-and-allow-lists.php:73
msgid "Example 1 - A single user agent string to block:"
msgstr ""

#: src/templates/wp-admin/firewall/block-and-allow-lists.php:75
msgid "Example 2 - A list of more than 1 user agent strings to block"
msgstr ""

#: src/templates/wp-admin/firewall/htaccess-firewall-rules.php:2
msgid ".htaccess firewall settings"
msgstr ""

#: src/templates/wp-admin/firewall/htaccess-firewall-rules.php:8, src/templates/wp-admin/firewall/partials/basic-firewall-settings.php:3
msgid "Basic firewall settings"
msgstr ""

#: src/templates/wp-admin/firewall/htaccess-firewall-rules.php:11
msgid "Block debug log"
msgstr ""

#: src/templates/wp-admin/firewall/htaccess-firewall-rules.php:14
msgid "Listing directory content"
msgstr ""

#: src/templates/wp-admin/firewall/htaccess-firewall-rules.php:25, src/templates/wp-admin/firewall/php-firewall-rules.php:40
msgid "Rules"
msgstr ""

#: src/templates/wp-admin/firewall/htaccess-firewall-rules.php:56
msgid "Save .htaccess firewall settings"
msgstr ""

#: src/templates/wp-admin/firewall/internet-bots.php:2
msgid "Internet bot settings"
msgstr ""

#: src/templates/wp-admin/firewall/internet-bots.php:6
msgid "What is an Internet Bot"
msgstr ""

#: src/templates/wp-admin/firewall/internet-bots.php:7
msgid "%s?"
msgstr ""

#: src/templates/wp-admin/firewall/internet-bots.php:9
msgid "A bot is a piece of software which runs on the Internet and performs automatic tasks."
msgstr ""

#: src/templates/wp-admin/firewall/internet-bots.php:9
msgid "For example when Google indexes your pages it uses bots to achieve this task."
msgstr ""

#: src/templates/wp-admin/firewall/internet-bots.php:10
msgid "A lot of bots are legitimate and non-malicious but not all bots are good and often you will find some which try to impersonate legitimate bots such as \"Googlebot\" but in reality they have nohing to do with Google at all."
msgstr ""

#: src/templates/wp-admin/firewall/internet-bots.php:11
msgid "Although most of the bots out there are relatively harmless sometimes website owners want to have more control over which bots they allow into their site."
msgstr ""

#: src/templates/wp-admin/firewall/internet-bots.php:19
msgid "Save internet bot settings"
msgstr ""

#: src/templates/wp-admin/firewall/php-firewall-rules.php:3
msgid "PHP firewall settings"
msgstr ""

#: src/templates/wp-admin/firewall/php-firewall-rules.php:9
msgid "Security enhancements"
msgstr ""

#: src/templates/wp-admin/firewall/php-firewall-rules.php:13
msgid "Feed control"
msgstr ""

#: src/templates/wp-admin/firewall/php-firewall-rules.php:17
msgid "Comment protection"
msgstr ""

#: src/templates/wp-admin/firewall/php-firewall-rules.php:21
msgid "URL security"
msgstr ""

#: src/templates/wp-admin/firewall/php-firewall-rules.php:25
msgid "String filtering"
msgstr ""

#: src/templates/wp-admin/firewall/php-firewall-rules.php:29
msgid "WP REST API"
msgstr ""

#: src/templates/wp-admin/firewall/php-firewall-rules.php:71
msgid "Save PHP firewall settings"
msgstr ""

#: src/templates/wp-admin/general/moved.php:14
msgid "The %s feature is now located %s."
msgstr ""

#: src/templates/wp-admin/general/moved.php:14
msgid "This page will be removed in a future release."
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:5
msgid "View the scan results and clear this message"
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:10
msgid "If given an opportunity hackers can insert their code or files into your system which they can then use to carry out malicious acts on your site."
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:10
msgid "Being informed of any changes in your files can be a good way to quickly prevent a hacker from causing damage to your website."
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:10
msgid "In general, WordPress core and plugin files and file types such as \".php\" or \".js\" should not change often and when they do, it is important that you are made aware when a change occurs and which file was affected."
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:10
msgid "The \"File Change Detection Feature\" will notify you of any file change which occurs on your system, including the addition and deletion of files by performing a regular automated or manual scan of your system's files."
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:10
msgid "This feature also allows you to exclude certain files or folders from the scan in cases where you know that they change often as part of their normal operation. (For example log files and certain caching plugin files may change often and hence you may choose to exclude such files from the file change detection scan)"
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:17
msgid "Next file scan"
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:28
msgid "Previous file scan results"
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:36
msgid "No previous scan results"
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:34
msgid "View the last file scan results"
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:47
msgid "Time now"
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:52
msgid "Scan now"
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:53
msgid "or schedule regular file scans below."
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:62
msgid "File change detection settings"
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:73
msgid "Enable automated file change detection scan"
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:76
msgid "Enable this if you want the system to automatically and periodically scan your files to check for file changes based on the settings below"
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:81
msgid "Scan time interval"
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:84
msgid "Hours"
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:85
msgid "Days"
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:86
msgid "Weeks"
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:88
msgid "Set the value for how often you would like a scan to occur"
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:92
msgid "File types to ignore"
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:96
msgid "Enter each file type or extension on a new line which you wish to exclude from the file change detection scan."
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:100
msgid "You can exclude file types from the scan which would not normally pose any security threat if they were changed."
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:100
msgid "These can include things such as image files."
msgstr ""

#. translators: 1. JPG, 2. PNG, 3. BMP.
#: src/templates/wp-admin/scanner/file-change-detect.php:102
msgid "Example: If you want the scanner to ignore files of type %1$s, %2$s, and %3$s, then you would enter the following:"
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:111
msgid "Files/Directories to ignore"
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:115
msgid "Enter each file or directory on a new line which you wish to exclude from the file change detection scan."
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:119
msgid "You can exclude specific files/directories from the scan which would not normally pose any security threat if they were changed."
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:119
msgid "These can include things such as log files."
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:120
msgid "Example: If you want the scanner to ignore certain files in different directories or whole directories, then you would enter the following:"
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:122
msgid "somedirectory"
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:129
msgid "Send email when change detected"
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:133
msgid "Enable this if you want the system to email you if a file change was detected"
msgstr ""

#: src/templates/wp-admin/scanner/file-change-detect.php:138
msgid "Enter one or more email addresses on a new line."
msgstr ""

#: src/templates/wp-admin/scanner/malware-scan.php:4
msgid "What is malware?"
msgstr ""

#: src/templates/wp-admin/scanner/malware-scan.php:5
msgid "The word malware stands for malicious software."
msgstr ""

#: src/templates/wp-admin/scanner/malware-scan.php:5
msgid "It can consist of things like trojan horses, adware, worms, spyware and any other undesirable code which a hacker will try to inject into your website."
msgstr ""

#: src/templates/wp-admin/scanner/malware-scan.php:5
msgid "Often when malware code has been inserted into your site you will normally not notice anything out of the ordinary based on appearances, but it can have a dramatic effect on your site's search ranking."
msgstr ""

#: src/templates/wp-admin/scanner/malware-scan.php:5
msgid "This is because the bots and spiders from search engines such as Google have the capability to detect malware when they are indexing the pages on your site, and consequently they can blacklist your website which will in turn affect your search rankings."
msgstr ""

#: src/templates/wp-admin/scanner/malware-scan.php:9
msgid "Scanning for malware"
msgstr ""

#: src/templates/wp-admin/scanner/malware-scan.php:10
msgid "Due to the constantly changing and complex nature of Malware, scanning for such things using a standalone plugin will not work reliably."
msgstr ""

#: src/templates/wp-admin/scanner/malware-scan.php:10
msgid "This is something best done via an external scan of your site regularly."
msgstr ""

#: src/templates/wp-admin/scanner/malware-scan.php:10
msgid "This is why we have created an easy-to-use scanning service which is hosted off our own server which will scan your site for malware weekly and notify you if it finds anything."
msgstr ""

#: src/templates/wp-admin/scanner/malware-scan.php:11
msgid "This service is included with the premium plugin and provides the following:"
msgstr ""

#: src/templates/wp-admin/scanner/malware-scan.php:13
msgid "Automatic weekly scans"
msgstr ""

#: src/templates/wp-admin/scanner/malware-scan.php:14
msgid "Automatic malware and blacklist monitoring"
msgstr ""

#: src/templates/wp-admin/scanner/malware-scan.php:15
msgid "Automatic email alerting"
msgstr ""

#: src/templates/wp-admin/scanner/malware-scan.php:16
msgid "Site uptime monitoring"
msgstr ""

#: src/templates/wp-admin/scanner/malware-scan.php:17
msgid "Site response time monitoring"
msgstr ""

#: src/templates/wp-admin/scanner/malware-scan.php:18
msgid "We provide advice for malware cleanup"
msgstr ""

#: src/templates/wp-admin/scanner/malware-scan.php:19
msgid "Blacklist removal"
msgstr ""

#: src/templates/wp-admin/scanner/malware-scan.php:20
msgid "No contract (cancel anytime)"
msgstr ""

#. translators: %s: Scanner URL.
#: src/templates/wp-admin/scanner/malware-scan.php:23
msgid "Learn more %s."
msgstr ""

#: src/templates/wp-admin/scanner/scan-result.php:3
msgid "Latest file change scan results"
msgstr ""

#: src/templates/wp-admin/scanner/scan-result.php:8
msgid "The following files were added to your website."
msgstr ""

#: src/templates/wp-admin/scanner/scan-result.php:9
msgid "The following files were removed from your website."
msgstr ""

#: src/templates/wp-admin/scanner/scan-result.php:10
msgid "The following files were changed on your website."
msgstr ""

#: src/templates/wp-admin/scanner/scan-result.php:20
msgid "File"
msgstr ""

#: src/templates/wp-admin/scanner/scan-result.php:21
msgid "File size"
msgstr ""

#: src/templates/wp-admin/scanner/scan-result.php:22
msgid "File modified"
msgstr ""

#: src/templates/wp-admin/settings/advanced-settings.php:4
msgid "IP address detection settings"
msgstr ""

#: src/templates/wp-admin/settings/advanced-settings.php:8
msgid "The IP address detection settings allow you to specify how visitors' IP addresses are made known to PHP (and hence to WordPress and its plugins)."
msgstr ""

#: src/templates/wp-admin/settings/advanced-settings.php:9
msgid "Usually, this is automatic and there is only one choice."
msgstr ""

#: src/templates/wp-admin/settings/advanced-settings.php:10
msgid "However in some setups, such as those using proxies (including load-balancers and security firewalls like Cloudflare), it may be necessary to set this manually."
msgstr ""

#: src/templates/wp-admin/settings/advanced-settings.php:11, src/templates/wp-admin/firewall/partials/fake-googlebots.php:17
msgid "Attention"
msgstr ""

#: src/templates/wp-admin/settings/advanced-settings.php:11
msgid "It is important to set this correctly - otherwise you may make it possible for a hacker to ban all your visitors (e.g. via banning Cloudflare from connecting to you) instead of the hacker being banned."
msgstr ""

#: src/templates/wp-admin/settings/advanced-settings.php:11
msgid "The default is to use the REMOTE_ADDR PHP server variable."
msgstr ""

#: src/templates/wp-admin/settings/advanced-settings.php:11
msgid "If this variable does not contain the visitor's IP address, then whilst you can make a different selection below, it is better to ask your web hosting company to have it correctly set."
msgstr ""

#: src/templates/wp-admin/settings/advanced-settings.php:12
msgid "This is the most secure setup, because when set correctly it is immune from being spoofed by an attacker."
msgstr ""

#: src/templates/wp-admin/settings/advanced-settings.php:18
msgid "You have no available IP address detection method(s); you must contact your web hosting company."
msgstr ""

#: src/templates/wp-admin/settings/advanced-settings.php:24, src/templates/wp-admin/settings/advanced-settings.php:29, src/templates/wp-admin/settings/advanced-settings.php:34
msgid "Your detected IP address according to %s:"
msgstr ""

#: src/templates/wp-admin/settings/advanced-settings.php:40
msgid "If your site is setup on localhost, you won't see your external IP address using your server's IP detection setting; but on a localhost-served site (not available to the outside world), the setting is irrelevant and can be ignored."
msgstr ""

#: src/templates/wp-admin/settings/advanced-settings.php:62
msgid "Choose a $_SERVER variable you would like to detect visitors' IP address using."
msgstr ""

#: src/templates/wp-admin/settings/advanced-settings.php:68
msgid "If your chosen server variable fails the plugin will automatically fall back to retrieving the IP address from $_SERVER[\"REMOTE_ADDR\"]"
msgstr ""

#: src/templates/wp-admin/settings/advanced-settings.php:74
msgid "Your IP address if using this setting:"
msgstr ""

#: src/templates/wp-admin/settings/advanced-settings.php:76
msgid "fetching..."
msgstr ""

#: src/templates/wp-admin/settings/advanced-settings.php:90
msgid "look-up possibly blocked by an ad-blocker or similar tool"
msgstr ""

#: src/templates/wp-admin/settings/delete-plugin-settings.php:3
msgid "Manage delete plugin tasks"
msgstr ""

#: src/templates/wp-admin/settings/delete-plugin-settings.php:8
msgid "NOTE: Even if these options are disabled, the plugin settings will still be inactive when the plugin is uninstalled, but they will be remembered for the next time the plugin is installed and activated."
msgstr ""

#: src/templates/wp-admin/settings/delete-plugin-settings.php:13
msgid "Delete database tables"
msgstr ""

#: src/templates/wp-admin/settings/delete-plugin-settings.php:16
msgid "Enable this to remove all database tables for this site when uninstalling the plugin."
msgstr ""

#: src/templates/wp-admin/settings/delete-plugin-settings.php:21
msgid "Delete settings"
msgstr ""

#: src/templates/wp-admin/settings/delete-plugin-settings.php:24
msgid "Enable this to remove all plugin settings for this site when uninstalling the plugin."
msgstr ""

#: src/templates/wp-admin/settings/delete-plugin-settings.php:27
msgid "It will also remove all firewall rules that were added by this plugin."
msgstr ""

#: src/templates/wp-admin/settings/general-settings.php:3
msgid "For information, updates and documentation, please visit"
msgstr ""

#: src/templates/wp-admin/settings/general-settings.php:3
msgid "Page"
msgstr ""

#: src/templates/wp-admin/settings/general-settings.php:16
msgid "Thank you for using the All-In-One Security plugin."
msgstr ""

#: src/templates/wp-admin/settings/general-settings.php:20
msgid "There are a lot of security features in this plugin."
msgstr ""

#: src/templates/wp-admin/settings/general-settings.php:25
msgid "To start, go through each security option and enable the \"basic\" options."
msgstr ""

#: src/templates/wp-admin/settings/general-settings.php:29
msgid "The more features you enable, the more security points you will achieve."
msgstr ""

#: src/templates/wp-admin/settings/general-settings.php:32
msgid "Before doing anything we advise taking a backup of your .htaccess file, database and wp-config.php."
msgstr ""

#: src/templates/wp-admin/settings/general-settings.php:35
msgid "Backup your database"
msgstr ""

#: src/templates/wp-admin/settings/general-settings.php:36, src/templates/wp-admin/settings/htaccess-file-operations.php:16
msgid "Backup .htaccess file"
msgstr ""

#: src/templates/wp-admin/settings/general-settings.php:37, src/templates/wp-admin/settings/wp-config-file-operations.php:16
msgid "Backup wp-config.php file"
msgstr ""

#: src/templates/wp-admin/settings/general-settings.php:43
msgid "Disable security features"
msgstr ""

#: src/templates/wp-admin/settings/general-settings.php:48
msgid "If you think that some plugin functionality on your site is broken due to a security feature you enabled in this plugin, then use the following option to turn off all the security features of this plugin."
msgstr ""

#: src/templates/wp-admin/settings/general-settings.php:52
msgid "Disable all security features"
msgstr ""

#: src/templates/wp-admin/settings/general-settings.php:58, src/templates/wp-admin/settings/general-settings.php:67
msgid "Disable all firewall rules"
msgstr ""

#: src/templates/wp-admin/settings/general-settings.php:63
msgid "This feature will disable all firewall rules which are currently active in this plugin and it will also delete these rules from your .htaccess file."
msgstr ""

#: src/templates/wp-admin/settings/general-settings.php:63
msgid "Use it if you think one of the firewall rules is causing an issue on your site."
msgstr ""

#: src/templates/wp-admin/settings/general-settings.php:73, src/templates/wp-admin/settings/general-settings.php:86
msgid "Reset settings"
msgstr ""

#: src/templates/wp-admin/settings/general-settings.php:78
msgid "This feature will delete all of your settings related to the All-In-One Security plugin."
msgstr ""

#: src/templates/wp-admin/settings/general-settings.php:79
msgid "This feature will reset/empty all the database tables of the security plugin also."
msgstr ""

#: src/templates/wp-admin/settings/general-settings.php:80
msgid "Use this feature if you were locked out by the All-In-One Security plugin and/or you are having issues logging in when that plugin is activated."
msgstr ""

#: src/templates/wp-admin/settings/general-settings.php:81
msgid "In addition to the settings it will also delete any directives which were added to the .htaccess file by the All-In-One Security Plugin."
msgstr ""

#: src/templates/wp-admin/settings/general-settings.php:82
msgid "%1$sNOTE: %2$sAfter deleting the settings you will need to re-configure the All-In-One Security plugin."
msgstr ""

#: src/templates/wp-admin/settings/general-settings.php:95
msgid "Debug settings"
msgstr ""

#: src/templates/wp-admin/settings/general-settings.php:100
msgid "This setting allows you to enable/disable debug for this plugin."
msgstr ""

#: src/templates/wp-admin/settings/general-settings.php:105
msgid "Enable debug"
msgstr ""

#: src/templates/wp-admin/settings/general-settings.php:108
msgid "Enable debug mode."
msgstr ""

#: src/templates/wp-admin/settings/general-settings.php:108
msgid "You should keep this option disabled after you have finished debugging the issue."
msgstr ""

#: src/templates/wp-admin/settings/htaccess-file-operations.php:2
msgid ".htaccess file operations"
msgstr ""

#: src/templates/wp-admin/settings/htaccess-file-operations.php:5
msgid "Your \".htaccess\" file is a key component of your website's security and it can be modified to implement various levels of protection mechanisms."
msgstr ""

#: src/templates/wp-admin/settings/htaccess-file-operations.php:6
msgid "This feature allows you to backup and save your currently active .htaccess file should you need to re-use the the backed up file in the future."
msgstr ""

#: src/templates/wp-admin/settings/htaccess-file-operations.php:7
msgid "You can also restore your site's .htaccess settings using a backed up .htaccess file."
msgstr ""

#: src/templates/wp-admin/settings/htaccess-file-operations.php:12
msgid "Save the current .htaccess file"
msgstr ""

#: src/templates/wp-admin/settings/htaccess-file-operations.php:15
msgid "Press the button below to backup and save the currently active .htaccess file."
msgstr ""

#: src/templates/wp-admin/settings/htaccess-file-operations.php:21
msgid "Restore from a backed up .htaccess file"
msgstr ""

#: src/templates/wp-admin/settings/htaccess-file-operations.php:26
msgid ".htaccess file to restore from"
msgstr ""

#: src/templates/wp-admin/settings/htaccess-file-operations.php:28
msgid "Restore your .htaccess file"
msgstr ""

#: src/templates/wp-admin/settings/settings-file-operations.php:2
msgid "Export or import your AIOS settings"
msgstr ""

#: src/templates/wp-admin/settings/settings-file-operations.php:5
msgid "This section allows you to export or import your All-In-One Security settings."
msgstr ""

#: src/templates/wp-admin/settings/settings-file-operations.php:6
msgid "This can be handy if you wanted to save time by applying the settings from one site to another site."
msgstr ""

#: src/templates/wp-admin/settings/settings-file-operations.php:7
msgid "NOTE: Before importing, it is your responsibility to know what settings you are trying to import."
msgstr ""

#: src/templates/wp-admin/settings/settings-file-operations.php:7
msgid "Importing settings blindly can cause you to be locked out of your site."
msgstr ""

#: src/templates/wp-admin/settings/settings-file-operations.php:8
msgid "For Example: If a settings item relies on the domain URL then it may not work correctly when imported into a site with a different domain."
msgstr ""

#: src/templates/wp-admin/settings/settings-file-operations.php:13, src/templates/wp-admin/settings/settings-file-operations.php:21
msgid "Export AIOS settings"
msgstr ""

#: src/templates/wp-admin/settings/settings-file-operations.php:18
msgid "To export your All-In-One Security settings press the button below."
msgstr ""

#: src/templates/wp-admin/settings/settings-file-operations.php:26
msgid "Import AIOS settings"
msgstr ""

#: src/templates/wp-admin/settings/settings-file-operations.php:31
msgid "Use this section to import your All-In-One Security settings from a file."
msgstr ""

#: src/templates/wp-admin/settings/settings-file-operations.php:33
msgid "Settings file to restore from"
msgstr ""

#: src/templates/wp-admin/settings/settings-file-operations.php:36
msgid "Select your import settings file"
msgstr ""

#: src/templates/wp-admin/settings/wp-config-file-operations.php:2
msgid "wp-config.php file operations"
msgstr ""

#: src/templates/wp-admin/settings/wp-config-file-operations.php:5
msgid "Your \"wp-config.php\" file is one of the most important files in your WordPress installation."
msgstr ""

#: src/templates/wp-admin/settings/wp-config-file-operations.php:5
msgid "It is a primary configuration file and contains crucial things such as details of your database and other critical components."
msgstr ""

#: src/templates/wp-admin/settings/wp-config-file-operations.php:6
msgid "This feature allows you to backup and save your currently active wp-config.php file should you need to re-use the the backed up file in the future."
msgstr ""

#: src/templates/wp-admin/settings/wp-config-file-operations.php:7
msgid "You can also restore your site's wp-config.php settings using a backed up wp-config.php file."
msgstr ""

#: src/templates/wp-admin/settings/wp-config-file-operations.php:12
msgid "Save the current wp-config.php file"
msgstr ""

#: src/templates/wp-admin/settings/wp-config-file-operations.php:15
msgid "Press the button below to backup and download the contents of the currently active wp-config.php file."
msgstr ""

#: src/templates/wp-admin/settings/wp-config-file-operations.php:21
msgid "Restore from a backed up wp-config file"
msgstr ""

#: src/templates/wp-admin/settings/wp-config-file-operations.php:26
msgid "wp-config file to restore from"
msgstr ""

#: src/templates/wp-admin/settings/wp-config-file-operations.php:28
msgid "Restore your wp-config file"
msgstr ""

#: src/templates/wp-admin/settings/wp-version-info.php:2
msgid "WP generator meta tag and version info"
msgstr ""

#: src/templates/wp-admin/settings/wp-version-info.php:5
msgid "WordPress generator automatically adds some meta information inside the \"head\" tags of every page on your site's front end, below is an example of this:"
msgstr ""

#: src/templates/wp-admin/settings/wp-version-info.php:7
msgid "The above meta information shows which version of WordPress your site is currently running and thus can help hackers or crawlers scan your site to see if you have an older version of WordPress or one with a known exploit."
msgstr ""

#: src/templates/wp-admin/settings/wp-version-info.php:8
msgid "There are also other ways Wordpress reveals version info such as during style and script loading, an example of this is:"
msgstr ""

#: src/templates/wp-admin/settings/wp-version-info.php:11
msgid "This feature will allow you to remove the WP generator meta info and other version info from your site's pages."
msgstr ""

#: src/templates/wp-admin/settings/wp-version-info.php:16
msgid "WP generator meta info"
msgstr ""

#: src/templates/wp-admin/settings/wp-version-info.php:27
msgid "Remove WP generator meta info"
msgstr ""

#: src/templates/wp-admin/settings/wp-version-info.php:30
msgid "Enable this if you want to remove the version and meta info produced by WP from all pages"
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam-ip-monitoring.php:3
msgid "Auto block spammer IPs"
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam-ip-monitoring.php:7
msgid "spam comment detection"
msgstr ""

#. translators: %s: Feature URL.
#: src/templates/wp-admin/spam-prevention/comment-spam-ip-monitoring.php:9
msgid "This feature has detected that %s is not active."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam-ip-monitoring.php:9
msgid "It is highly recommended that you activate to make the most of this feature."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam-ip-monitoring.php:22
msgid "This feature allows you to automatically and permanently block IP addresses which have exceeded a certain number of spam comments."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam-ip-monitoring.php:22
msgid "Comments are considered spam if the \"Spam comment detection\" feature is enabled or an administrator manually marks a comment as \"spam\" from the WordPress Comments menu."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam-ip-monitoring.php:35
msgid "Enable auto block of spam comment IPs"
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam-ip-monitoring.php:38
msgid "Enable this if you want this plugin to automatically block IP addresses which submit spam comments."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam-ip-monitoring.php:43
msgid "Minimum number of spam comments"
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam-ip-monitoring.php:45
msgid "Specify the minimum number of spam comments for an IP address before it is permanently blocked."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam-ip-monitoring.php:49
msgid "Example 1: Setting this value to \"1\" will block ALL IP addresses which were used to submit at least one spam comment."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam-ip-monitoring.php:50
msgid "Example 2: Setting this value to \"5\" will block only those IP addresses which were used to submit 5 spam comments or more on your site."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam-ip-monitoring.php:67
msgid "List spammer IP addresses"
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam-ip-monitoring.php:71
msgid "This section displays a list of the IP addresses of the people or bots who have left spam comments on your site."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam-ip-monitoring.php:71
msgid "This information can be handy for identifying the most persistent IP addresses or ranges used by spammers."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam-ip-monitoring.php:71
msgid "By inspecting the IP address data coming from spammers you will be in a better position to determine which addresses or address ranges you should block by adding them to the permanent block list."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam-ip-monitoring.php:71
msgid "To add one or more of the IP addresses displayed in the table below to your blacklist, simply press the \"Block\" link for the individual row or select more than one address using the checkboxes and then choose the \"block\" option from the Bulk Actions dropdown list and press the \"Apply\" button."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam-ip-monitoring.php:77
msgid "Minimum number of spam comments per IP"
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam-ip-monitoring.php:80
msgid "This field allows you to list only those IP addresses which have been used to post X or more spam comments."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam-ip-monitoring.php:84
msgid "Example 1: Setting this value to \"1\" will list ALL IP addresses which were used to submit at least 1 spam comment."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam-ip-monitoring.php:85
msgid "Example 2: Setting this value to \"5\" will list only those IP addresses which were used to submit 5 spam comments or more on your site."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam-ip-monitoring.php:91
msgid "Find IP addresses"
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam-ip-monitoring.php:96
msgid "Spammer IP address results"
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam-ip-monitoring.php:101
msgid "Only the \"superadmin\" can block IP addresses from the main site."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam-ip-monitoring.php:101
msgid "Take note of the IP addresses you want blocked and ask the superadmin to add these to the blacklist using the \"Blacklist Manager\" on the main site."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam.php:2
msgid "Comment spam settings"
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam.php:6
msgid "Spam comment detect"
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam.php:10
msgid "A large portion of WordPress blog comment spam is produced by automated bots rather than by humans."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam.php:10
msgid "This feature will reduce the useless and unnecessary traffic and load on your server resulting from spam comments."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam.php:10
msgid "In other words, if the comment was not submitted by a human, the request will be discarded or marked as spam."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam.php:10
msgid "This feature uses cookies and JavaScript."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam.php:10
msgid "If your visitors have either cookies or JavaScript disabled, their comments will automatically be discarded or marked as spam."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam.php:21
msgid "Detect spambots posting comments"
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam.php:24
msgid "Enable this if you want to detect comments originating from spambots."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam.php:28
msgid "This feature will detect comment attempts which originate from spambots."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam.php:29
msgid "A legitimate comment is one which is submitted by a human who physically fills out the comment form and presses the submit button."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam.php:30
msgid "A comment submitted by a spambot is done by directly calling the wp-comments-post.php file."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam.php:31
msgid "This feature will detect these comments and either discard them completely or mark them as spam."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam.php:38
msgid "Use cookies to detect comment spam"
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam.php:41
msgid "Using cookies may prevent caches from caching pages containing comment forms."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam.php:45
msgid "This feature uses cookies."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam.php:45
msgid "Unless your cache (e.g. Cloudflare) is configured to ignore these cookies, it may decide to not cache any of these pages."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam.php:46
msgid "Cloudflare detects that the set-cookie header is set and will not cache the page by default."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam.php:54
msgid "Comment processing"
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam.php:59
msgid "Spam comments detected should be"
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam.php:63
msgid "Discarded"
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam.php:64
msgid "Marked as spam"
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam.php:66
msgid "Select the value for how you would like a comment detected as spam to be processed"
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam.php:71
msgid "Trash spam comments"
msgstr ""

#. translators: %s: Spam comments day threshold.
#: src/templates/wp-admin/spam-prevention/comment-spam.php:81
msgid "Move spam comments to trash after %s days."
msgstr ""

#: src/templates/wp-admin/spam-prevention/comment-spam.php:89
msgid "Enable this feature in order to move the spam comments to trash after given number of days."
msgstr ""

#: src/templates/wp-admin/tools/custom-htaccess.php:2
msgid "Custom .htaccess rules settings"
msgstr ""

#: src/templates/wp-admin/tools/custom-htaccess.php:8
msgid "This feature can be used to apply your own custom .htaccess rules and directives."
msgstr ""

#: src/templates/wp-admin/tools/custom-htaccess.php:9
msgid "It is useful for when you want to tweak our existing firewall rules or when you want to add your own."
msgstr ""

#: src/templates/wp-admin/tools/custom-htaccess.php:10
msgid "NOTE: This feature can only be used if your site is hosted using the Apache webserver, or another that uses .htaccess files."
msgstr ""

#. translators: %s: Warning
#: src/templates/wp-admin/tools/custom-htaccess.php:18
msgid "%s: Only use this feature if you know what you are doing."
msgstr ""

#: src/templates/wp-admin/tools/custom-htaccess.php:18
msgid "Warning"
msgstr ""

#: src/templates/wp-admin/tools/custom-htaccess.php:19
msgid "Incorrect .htaccess rules or directives can break or prevent access to your site."
msgstr ""

#: src/templates/wp-admin/tools/custom-htaccess.php:20
msgid "It is your responsibility to ensure that you are entering the correct code!"
msgstr ""

#: src/templates/wp-admin/tools/custom-htaccess.php:21
msgid "If you break your site you will need to access your server via FTP or something similar and then edit your .htaccess file and delete the changes you made."
msgstr ""

#: src/templates/wp-admin/tools/custom-htaccess.php:32
msgid "Enable custom .htaccess rules"
msgstr ""

#: src/templates/wp-admin/tools/custom-htaccess.php:35
msgid "Enable this to activate the custom rules entered in the text box below"
msgstr ""

#: src/templates/wp-admin/tools/custom-htaccess.php:40
msgid "Place custom rules at the top"
msgstr ""

#: src/templates/wp-admin/tools/custom-htaccess.php:43
msgid "Enable this if you want to place your custom rules at the beginning of all the rules applied by this plugin"
msgstr ""

#: src/templates/wp-admin/tools/custom-htaccess.php:48
msgid "Enter custom .htaccess rules:"
msgstr ""

#: src/templates/wp-admin/tools/custom-htaccess.php:52
msgid "Enter your custom .htaccess rules/directives."
msgstr ""

#: src/templates/wp-admin/tools/custom-htaccess.php:57
msgid "Save custom rules"
msgstr ""

#: src/templates/wp-admin/tools/password-tool.php:5
msgid "Poor password selection is one of the most common weak points of many sites and is usually the first thing a hacker will try to exploit when attempting to break into your site."
msgstr ""

#: src/templates/wp-admin/tools/password-tool.php:6
msgid "Many people fall into the trap of using a simple word or series of numbers as their password."
msgstr ""

#: src/templates/wp-admin/tools/password-tool.php:6
msgid "Such a predictable and simple password would take a competent hacker merely minutes to guess your password by using a simple script which cycles through the easy and most common combinations."
msgstr ""

#: src/templates/wp-admin/tools/password-tool.php:7
msgid "The longer and more complex your password is the harder it is for hackers to \"crack\" because more complex passwords require much greater computing power and time."
msgstr ""

#: src/templates/wp-admin/tools/password-tool.php:8
msgid "This section contains a useful password strength tool which you can use to check whether your password is sufficiently strong enough."
msgstr ""

#: src/templates/wp-admin/tools/password-tool.php:12
msgid "Password strength tool"
msgstr ""

#: src/templates/wp-admin/tools/password-tool.php:14
msgid "This password tool uses an algorithm which calculates how long it would take for your password to be cracked using the computing power of an off-the-shelf current model desktop PC with high end processor, graphics card and appropriate password cracking software."
msgstr ""

#: src/templates/wp-admin/tools/password-tool.php:16
msgid "Start typing a password."
msgstr ""

#: src/templates/wp-admin/tools/password-tool.php:23
msgid "It would take a desktop PC approximately %s to crack your password!"
msgstr ""

#: src/templates/wp-admin/tools/visitor-lockout.php:3
msgid "General visitor lockout"
msgstr ""

#: src/templates/wp-admin/tools/visitor-lockout.php:8
msgid "This feature allows you to put your site into \"maintenance mode\" by locking down the front-end to all visitors except logged in users with super admin privileges."
msgstr ""

#: src/templates/wp-admin/tools/visitor-lockout.php:9
msgid "Locking your site down to general visitors can be useful if you are investigating some issues on your site or perhaps you might be doing some maintenance and wish to keep out all traffic for security reasons."
msgstr ""

#: src/templates/wp-admin/tools/visitor-lockout.php:17
msgid "Enable this if you want all visitors except those who are logged in as an administrator to be locked out of the front-end of your site."
msgstr ""

#: src/templates/wp-admin/tools/visitor-lockout.php:22
msgid "Enter a message:"
msgstr ""

#: src/templates/wp-admin/tools/visitor-lockout.php:34
msgid "Enter a message you wish to display to visitors when your site is in maintenance mode."
msgstr ""

#: src/templates/wp-admin/tools/visitor-lockout.php:39
msgid "Save site lockout settings"
msgstr ""

#: src/templates/wp-admin/tools/whois-lookup.php:3
msgid "The WHOIS lookup feature gives you a way to look up who owns an IP address or domain name."
msgstr ""

#: src/templates/wp-admin/tools/whois-lookup.php:3
msgid "You can use this to investigate users engaging in malicious activity on your site."
msgstr ""

#: src/templates/wp-admin/tools/whois-lookup.php:6
msgid "WHOIS lookup on IP or domain"
msgstr ""

#: src/templates/wp-admin/tools/whois-lookup.php:12
msgid "IP address or domain name:"
msgstr ""

#: src/templates/wp-admin/tools/whois-lookup.php:19
msgid "Look up IP or domain"
msgstr ""

#: src/templates/wp-admin/user-security/additional.php:4
msgid "WordPress 5.6 introduced a new feature called \"Application passwords\"."
msgstr ""

#: src/templates/wp-admin/user-security/additional.php:5
msgid "This allows you to create a token from the WordPress dashboard which then can be used in the authorization header."
msgstr ""

#: src/templates/wp-admin/user-security/additional.php:5
msgid "This feature allows you to disable application passwords as they can leave your site vulnerable to social engineering and phishing scams."
msgstr ""

#: src/templates/wp-admin/user-security/additional.php:22
msgid "Enable this if you want to disable the application password."
msgstr ""

#: src/templates/wp-admin/user-security/display-name.php:2
msgid "Display name security"
msgstr ""

#: src/templates/wp-admin/user-security/display-name.php:5
msgid "When you submit a post or answer a comment, WordPress will usually display your \"nickname\"."
msgstr ""

#: src/templates/wp-admin/user-security/display-name.php:6
msgid "By default the nickname is set to the login (or user) name of your account."
msgstr ""

#: src/templates/wp-admin/user-security/display-name.php:7
msgid "From a security perspective, leaving your nickname the same as your user name is bad practice because it gives a hacker at least half of your account's login credentials."
msgstr ""

#. translators: 1. Open strong tag, 2. Close strong tag.
#: src/templates/wp-admin/user-security/display-name.php:9
msgid "Therefore to further tighten your site's security you are advised to change your %1$snickname%2$s and  %1$sDisplay name%2$s to be different from your %1$sUsername%2$s."
msgstr ""

#: src/templates/wp-admin/user-security/display-name.php:14
msgid "Modify accounts with identical login name and display name"
msgstr ""

#: src/templates/wp-admin/user-security/display-name.php:37, src/templates/wp-admin/user-security/partials/wp-username-content.php:21
msgid "No action required."
msgstr ""

#: src/templates/wp-admin/user-security/display-name.php:37
msgid "Your site does not have a user account where the display name is identical to the username."
msgstr ""

#: src/templates/wp-admin/user-security/display-name.php:22
msgid "Your site currently has the following accounts with identical login and display names."
msgstr ""

#: src/templates/wp-admin/user-security/display-name.php:22
msgid "Follow the link to edit the user profile of that particular user account, change Nickname, choose a different Display name compared to Username, and press the \"Update Profile\" button."
msgstr ""

#: src/templates/wp-admin/user-security/force-logout.php:4
msgid "Setting an expiry period for your administration session is a simple way to protect against unauthorized access to your site from your computer."
msgstr ""

#: src/templates/wp-admin/user-security/force-logout.php:5
msgid "This feature allows you to specify a time period in minutes after which the admin session will expire and the user will be forced to log back in."
msgstr ""

#: src/templates/wp-admin/user-security/force-logout.php:10
msgid "Force user logout options"
msgstr ""

#: src/templates/wp-admin/user-security/force-logout.php:21
msgid "Enable force user logout"
msgstr ""

#: src/templates/wp-admin/user-security/force-logout.php:24
msgid "Enable this if you want to force a user to be logged out after a configured amount of time"
msgstr ""

#: src/templates/wp-admin/user-security/force-logout.php:29
msgid "Logout the user after X minutes"
msgstr ""

#: src/templates/wp-admin/user-security/force-logout.php:31
msgid "(Minutes) The user will be forced to log back in after this time period has elapased."
msgstr ""

#: src/templates/wp-admin/user-security/http-authentication.php:3
msgid "The HTTP authentication feature gives you a way to add a login username and password to your site through the use of the WWW-Authenticate header."
msgstr ""

#: src/templates/wp-admin/user-security/http-authentication.php:3
msgid "Only enable this feature for the frontend of your site if you don't want your site to be public."
msgstr ""

#: src/templates/wp-admin/user-security/http-authentication.php:5
msgid "The username and password will only be secure if you're enforcing the use of TLS(https) on your site."
msgstr ""

#: src/templates/wp-admin/user-security/http-authentication.php:7
msgid "Your site is currently using https."
msgstr ""

#: src/templates/wp-admin/user-security/http-authentication.php:22
msgid "If you are locked out by the HTTP authentication feature, define the following constant %s in wp-config.php to disable the feature."
msgstr ""

#: src/templates/wp-admin/user-security/http-authentication.php:16
msgid "HTTP authentication is currently disabled via the AIOS_DISABLE_HTTP_AUTHENTICATION constant (which is mostly likely to be defined in your wp-config.php)"
msgstr ""

#: src/templates/wp-admin/user-security/http-authentication.php:29
msgid "Your web browser is already sending a username/password."
msgstr ""

#: src/templates/wp-admin/user-security/http-authentication.php:29
msgid "If this is because you previously activated this feature then no action is required."
msgstr ""

#: src/templates/wp-admin/user-security/http-authentication.php:30
msgid "However, if this is because you have HTTP authentication set up elsewhere, such as another plugin or at the webserver level, then this feature either shouldn't be activated, or should only be activated with the same username/password."
msgstr ""

#: src/templates/wp-admin/user-security/http-authentication.php:37
msgid "HTTP authentication for WordPress dashboard and frontend"
msgstr ""

#: src/templates/wp-admin/user-security/http-authentication.php:46
msgid "Enable for WordPress dashboard:"
msgstr ""

#: src/templates/wp-admin/user-security/http-authentication.php:50
msgid "Check this if you want to protect the WordPress dashboard area of your site with HTTP authentication."
msgstr ""

#: src/templates/wp-admin/user-security/http-authentication.php:56
msgid "Enable for frontend:"
msgstr ""

#: src/templates/wp-admin/user-security/http-authentication.php:60
msgid "Check this if you want to protect the frontend of your site with HTTP authentication."
msgstr ""

#: src/templates/wp-admin/user-security/http-authentication.php:66
msgid "Username:"
msgstr ""

#: src/templates/wp-admin/user-security/http-authentication.php:82
msgid "%s to crack by a desktop PC according to the %s"
msgstr ""

#: src/templates/wp-admin/user-security/http-authentication.php:88
msgid "Failure message:"
msgstr ""

#: src/templates/wp-admin/user-security/logged-in-users.php:3
msgid "Refresh logged in user data"
msgstr ""

#: src/templates/wp-admin/user-security/logged-in-users.php:6, src/templates/wp-admin/user-security/manual-approval.php:38
msgid "Refresh data"
msgstr ""

#: src/templates/wp-admin/user-security/logged-in-users.php:12
msgid "This tab displays all users who are currently logged into your site."
msgstr ""

#: src/templates/wp-admin/user-security/logged-in-users.php:13
msgid "If you suspect there is a user or users who are logged in which should not be, you can block them by inspecting the IP addresses from the data below and adding them to your blacklist."
msgstr ""

#: src/templates/wp-admin/user-security/logged-in-users.php:14
msgid "You can also instantly log them out by pressing on the \"Force logout\" link when you hover over the row in the user id column."
msgstr ""

#: src/templates/wp-admin/user-security/logged-in-users.php:19
msgid "Currently logged in users"
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:2
msgid "Login lockout configuration"
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:5
msgid "Cookie-based brute force login prevention"
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:6
msgid "One of the ways hackers try to compromise sites is via a"
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:6
msgid "Brute force login attack"
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:6
msgid "This is where attackers use repeated login attempts until they guess the password."
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:7
msgid "Apart from choosing strong passwords, monitoring and blocking IP addresses which are involved in repeated login failures in a short period of time is a very effective way to stop these types of attacks."
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:8
msgid "You may also want to checkout our %s feature for another secure way to protect against these types of attacks."
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:12
msgid "Login lockout options"
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:23
msgid "Enable login lockout feature"
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:26
msgid "Enable this to turn on the login lockout feature"
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:31
msgid "Allow unlock requests"
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:34
msgid "Enable this if you want to allow users to generate an automated unlock request link which will unlock their account"
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:39
msgid "Max login attempts"
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:41
msgid "Set the value for the maximum login retries before IP address is locked out"
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:45
msgid "Login retry time period (min)"
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:47
msgid "If the maximum number of failed login attempts for a particular IP address occur within this time period the plugin will lock out that address"
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:52
msgid "Minimum lockout time length"
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:58
msgid "Set the minimum time period in minutes of lockout."
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:58
msgid "This failed login lockout time will be tripled on each failed login."
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:65
msgid "Maximum lockout time length"
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:70
msgid "Set the maximum time period in minutes of lockout."
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:70
msgid "No IP address will be blocked for more than this time period after making a failed login attempt."
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:75
msgid "Display generic error message"
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:78
msgid "Enable this if you want to show a generic error message when a login attempt fails"
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:83
msgid "Instantly lockout invalid usernames"
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:86
msgid "Enable this if you want to instantly lockout login attempts with usernames which do not exist on your system"
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:92
msgid "Instantly lockout specific usernames"
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:102
msgid "Insert one username per line."
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:102
msgid "Existing usernames are not blocked even if present in the list."
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:107
msgid "Notify by email"
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:111
msgid "Enable this if you want to receive an email when someone has been locked out due to maximum failed login attempts"
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:115
msgid "Fill in one email address per line."
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:119
msgid "Each email address must be on a new line."
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:120
msgid "If a valid email address has not been filled in, it will not be saved."
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:121
msgid "The valid email address <NAME_EMAIL>"
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:122
msgid "Example: %s"
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:129
msgid "Enable PHP backtrace in email"
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:133
msgid "Enable this if you want to include the PHP backtrace in notification emails."
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:145
msgid "Currently locked out IP address ranges"
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:150
msgid "To see a list of all locked IP addresses and ranges go to the %s tab in the dashboard menu."
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:156
msgid "Login lockout IP whitelist settings"
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:167
msgid "Enable login lockout IP whitelist"
msgstr ""

#: src/templates/wp-admin/user-security/login-lockout.php:176
msgid "The addresses specified here will never be blocked by the login lockout feature."
msgstr ""

#: src/templates/wp-admin/user-security/manual-approval.php:2
msgid "User registration settings"
msgstr ""

#: src/templates/wp-admin/user-security/manual-approval.php:4
msgid "Manually approve new registrations"
msgstr ""

#: src/templates/wp-admin/user-security/manual-approval.php:8
msgid "If your site allows people to create their own accounts via the WordPress registration form, then you can minimize spam or bogus registrations by manually approving each registration."
msgstr ""

#: src/templates/wp-admin/user-security/manual-approval.php:9
msgid "This feature will automatically set a newly registered account to \"pending\" until the administrator activates it."
msgstr ""

#: src/templates/wp-admin/user-security/manual-approval.php:9
msgid "Therefore undesirable registrants will be unable to log in without your express approval."
msgstr ""

#: src/templates/wp-admin/user-security/manual-approval.php:10
msgid "You can view all accounts which have been newly registered via the handy table below and you can also perform bulk activation/deactivation/deletion tasks on each account."
msgstr ""

#: src/templates/wp-admin/user-security/manual-approval.php:22
msgid "Enable manual approval of new registrations"
msgstr ""

#: src/templates/wp-admin/user-security/manual-approval.php:25
msgid "Enable this if you want to automatically disable all newly registered accounts so that you can approve them manually."
msgstr ""

#: src/templates/wp-admin/user-security/manual-approval.php:35
msgid "Refresh manual approval data"
msgstr ""

#: src/templates/wp-admin/user-security/manual-approval.php:43
msgid "Approve registered users"
msgstr ""

#: src/templates/wp-admin/user-security/salt.php:4
msgid "Add salt postfix"
msgstr ""

#: src/templates/wp-admin/user-security/salt.php:15
msgid "WordPress \"salts\" are secret phrases which are combined with user passwords when those passwords are stored, with the end result that they become much harder for an attacker to crack even if he managed to steal the database of your website."
msgstr ""

#: src/templates/wp-admin/user-security/salt.php:15
msgid "Learn more about WordPress Salts."
msgstr ""

#: src/templates/wp-admin/user-security/salt.php:21
msgid "When you enable this feature, you and all other logged-in users will be logged out so that AIOS can append the additional code (the salt) to all users’ login information."
msgstr ""

#: src/templates/wp-admin/user-security/salt.php:35
msgid "Enable this if you want to activate the salt postfix feature."
msgstr ""

#: src/templates/wp-admin/user-security/salt.php:39
msgid "This setting will suffix the salt with an additional 64 characters."
msgstr ""

#: src/templates/wp-admin/user-security/salt.php:39
msgid "It improves your WordPress site's cryptographic mechanism."
msgstr ""

#: src/templates/wp-admin/user-security/user-enumeration.php:3
msgid "Prevent user enumeration"
msgstr ""

#: src/templates/wp-admin/user-security/user-enumeration.php:14
msgid "This feature allows you to prevent external users/bots from fetching the user info with URLs like \"%s\", \"%s\", oEmbed request."
msgstr ""

#: src/templates/wp-admin/user-security/user-enumeration.php:15
msgid "When enabled, this feature will print a \"forbidden\" error rather than the user information."
msgstr ""

#: src/templates/wp-admin/user-security/user-enumeration.php:20
msgid "Disable user enumeration"
msgstr ""

#: src/templates/wp-admin/user-security/user-enumeration.php:23
msgid "Enable this if you want to stop user enumeration."
msgstr ""

#: src/templates/wp-admin/user-security/wp-username.php:2
msgid "Admin user security"
msgstr ""

#: src/templates/wp-admin/user-security/wp-username.php:5
msgid "Depending on  how you installed WordPress, you could have a default administrator with the username \"admin\"."
msgstr ""

#: src/templates/wp-admin/user-security/wp-username.php:6
msgid "Hackers can try to take advantage of this information by attempting \"Brute force login attacks\" where they repeatedly try to guess the password by using \"admin\" for username."
msgstr ""

#: src/templates/wp-admin/user-security/wp-username.php:7
msgid "From a security perspective, changing the username \"admin\" is one of the first and smartest things you should do on your site."
msgstr ""

#: src/templates/wp-admin/user-security/wp-username.php:8
msgid "This feature will allow you to change your \"admin\" username to a more secure name of your choosing."
msgstr ""

#: src/templates/wp-admin/user-security/wp-username.php:14
msgid "List of administrator accounts"
msgstr ""

#: src/templates/wp-admin/brute-force/partials/cookie-test-container.php:4
msgid "Before using this feature, you must perform a cookie test first."
msgstr ""

#: src/templates/wp-admin/brute-force/partials/cookie-test-container.php:6
msgid "This ensures that your browser cookie is working correctly and that you won't lock yourself out."
msgstr ""

#: src/templates/wp-admin/brute-force/partials/cookie-test-container.php:11
msgid "Perform cookie test"
msgstr ""

#: src/templates/wp-admin/brute-force/partials/other-plugins.php:3
msgid "Other forms CAPTCHA settings"
msgstr ""

#: src/templates/wp-admin/brute-force/partials/other-plugins.php:14
msgid "Enable CAPTCHA on BuddyPress registration form"
msgstr ""

#: src/templates/wp-admin/brute-force/partials/other-plugins.php:17
msgid "Enable this if you want to insert a CAPTCHA field on the %s registration forms."
msgstr ""

#: src/templates/wp-admin/brute-force/partials/other-plugins.php:34
msgid "Enable CAPTCHA on bbPress new topic form"
msgstr ""

#: src/templates/wp-admin/brute-force/partials/other-plugins.php:37
msgid "Enable this if you want to insert a CAPTCHA field on the %s new topic forms."
msgstr ""

#: src/templates/wp-admin/brute-force/partials/other-plugins.php:54
msgid "Enable CAPTCHA on %s"
msgstr ""

#: src/templates/wp-admin/brute-force/partials/other-plugins.php:57
msgid "Enable this if you want to insert a CAPTCHA field on %s forms."
msgstr ""

#: src/templates/wp-admin/brute-force/partials/other-plugins.php:61
msgid "%s will automatically try to insert a CAPTCHA field before the form's submit button"
msgstr ""

#: src/templates/wp-admin/brute-force/partials/other-plugins.php:62
msgid "For the exact placement of the CAPTCHA you can use the following shortcode in your %s template"
msgstr ""

#: src/templates/wp-admin/brute-force/partials/other-plugins.php:64
msgid "This feature requires %s version %s or greater"
msgstr ""

#: src/templates/wp-admin/brute-force/partials/other-plugins.php:65
msgid "The validation message will be displayed only when using %s version %s or greater"
msgstr ""

#: src/templates/wp-admin/brute-force/partials/rename-login-notice.php:5
msgid "Your WordPress login page URL has been renamed."
msgstr ""

#: src/templates/wp-admin/brute-force/partials/rename-login-notice.php:6
msgid "Your current login URL is:"
msgstr ""

#: src/templates/wp-admin/brute-force/partials/woo-captcha.php:3
msgid "WooCommerce forms CAPTCHA settings"
msgstr ""

#: src/templates/wp-admin/brute-force/partials/woo-captcha.php:14
msgid "Enable CAPTCHA on WooCommerce login form"
msgstr ""

#: src/templates/wp-admin/brute-force/partials/woo-captcha.php:17
msgid "Enable this if you want to insert CAPTCHA on a %s login form."
msgstr ""

#: src/templates/wp-admin/brute-force/partials/woo-captcha.php:30
msgid "Enable CAPTCHA on WooCommerce lost password form"
msgstr ""

#: src/templates/wp-admin/brute-force/partials/woo-captcha.php:33
msgid "Enable this if you want to insert CAPTCHA on a %s lost password form."
msgstr ""

#: src/templates/wp-admin/brute-force/partials/woo-captcha.php:46
msgid "Enable CAPTCHA on WooCommerce registration form"
msgstr ""

#: src/templates/wp-admin/brute-force/partials/woo-captcha.php:49
msgid "Enable this if you want to insert CAPTCHA on a %s registration form."
msgstr ""

#: src/templates/wp-admin/brute-force/partials/woo-captcha.php:69
msgid "Guest checkout allows a customer to place an order without an account or being logged in."
msgstr ""

#: src/templates/wp-admin/brute-force/partials/woo-captcha.php:66
msgid "Guest checkout is not enabled in your WooCommerce settings."
msgstr ""

#: src/templates/wp-admin/brute-force/partials/woo-captcha.php:66
msgid "Therefore, the setting below is not relevant."
msgstr ""

#: src/templates/wp-admin/brute-force/partials/woo-captcha.php:76
msgid "Enable CAPTCHA on the WooCommerce checkout page"
msgstr ""

#: src/templates/wp-admin/brute-force/partials/woo-captcha.php:79
msgid "Enable this if you want to insert a CAPTCHA on the %s checkout page when a guest places an order."
msgstr ""

#: src/templates/wp-admin/brute-force/partials/wordpress-forms.php:3
msgid "Wordpress forms CAPTCHA settings"
msgstr ""

#: src/templates/wp-admin/brute-force/partials/wordpress-forms.php:13
msgid "Enable CAPTCHA on login page"
msgstr ""

#: src/templates/wp-admin/brute-force/partials/wordpress-forms.php:16
msgid "Enable this if you want to insert a CAPTCHA form on the login page."
msgstr ""

#: src/templates/wp-admin/brute-force/partials/wordpress-forms.php:31
msgid "Enable CAPTCHA on registration page"
msgstr ""

#: src/templates/wp-admin/brute-force/partials/wordpress-forms.php:34
msgid "Enable this if you want to insert a CAPTCHA form on the WordPress user registration page (if you allow user registration)."
msgstr ""

#: src/templates/wp-admin/brute-force/partials/wordpress-forms.php:50
msgid "Enable CAPTCHA on lost password page"
msgstr ""

#: src/templates/wp-admin/brute-force/partials/wordpress-forms.php:53
msgid "Enable this if you want to insert a CAPTCHA form on the lost password page."
msgstr ""

#: src/templates/wp-admin/brute-force/partials/wordpress-forms.php:69
msgid "Enable CAPTCHA on custom login form"
msgstr ""

#: src/templates/wp-admin/brute-force/partials/wordpress-forms.php:72
msgid "Enable this if you want to insert CAPTCHA on a custom login form generated by the following WP function: %s"
msgstr ""

#: src/templates/wp-admin/brute-force/partials/wordpress-forms.php:87
msgid "Enable CAPTCHA on comment forms"
msgstr ""

#: src/templates/wp-admin/brute-force/partials/wordpress-forms.php:90
msgid "Enable this if you want to insert a CAPTCHA field on the comment forms."
msgstr ""

#: src/templates/wp-admin/brute-force/partials/wordpress-forms.php:105
msgid "Enable CAPTCHA on password protected pages/posts"
msgstr ""

#: src/templates/wp-admin/brute-force/partials/wordpress-forms.php:108
msgid "Enable this if you want to insert a CAPTCHA field on password-protected posts and pages."
msgstr ""

#: src/templates/wp-admin/filesystem-security/partials/php-file-editing.php:7
msgid "The WordPress Dashboard by default allows administrators to edit PHP files, such as plugin and theme files."
msgstr ""

#: src/templates/wp-admin/filesystem-security/partials/php-file-editing.php:7
msgid "This is often the first tool an attacker will use if able to login, since it allows code execution."
msgstr ""

#: src/templates/wp-admin/filesystem-security/partials/php-file-editing.php:7
msgid "This feature will disable the ability for people to edit PHP files via the dashboard."
msgstr ""

#: src/templates/wp-admin/filesystem-security/partials/php-file-editing.php:16
msgid "The DISALLOW_FILE_EDIT constant has already been defined, please remove it before enabling this feature."
msgstr ""

#: src/templates/wp-admin/filesystem-security/partials/php-file-editing.php:16
msgid "The constant is likely already defined in your wp-config.php file."
msgstr ""

#: src/templates/wp-admin/filesystem-security/partials/php-file-editing.php:22
msgid "Disable ability to edit PHP files"
msgstr ""

#: src/templates/wp-admin/filesystem-security/partials/php-file-editing.php:25
msgid "Enable this to remove the ability for people to edit PHP files via the WP dashboard"
msgstr ""

#: src/templates/wp-admin/filesystem-security/partials/prevent-hotlinks.php:7
msgid "A hotlink is where someone displays an image on their site which is actually located on your site by using a direct link to the source of the image on your server."
msgstr ""

#: src/templates/wp-admin/filesystem-security/partials/prevent-hotlinks.php:8
msgid "Due to the fact that the image being displayed on the other person's site is coming from your server, this can cause leaking of bandwidth and resources for you because your server has to present this image for the people viewing it on someone elses's site."
msgstr ""

#: src/templates/wp-admin/filesystem-security/partials/prevent-hotlinks.php:9
msgid "This feature will prevent people from directly hotlinking images from your site's pages by writing some directives in your .htaccess file."
msgstr ""

#: src/templates/wp-admin/filesystem-security/partials/prevent-hotlinks.php:23
msgid "Enable this to prevent hotlinking to images on your site."
msgstr ""

#: src/templates/wp-admin/filesystem-security/partials/wp-file-access.php:7
msgid "This feature allows you to auto delete files such as %s, %s and %s which are delivered with all WP installations."
msgstr ""

#: src/templates/wp-admin/filesystem-security/partials/wp-file-access.php:8
msgid "By deleting these files you are hiding some key pieces of information (such as WordPress version info) from potential hackers."
msgstr ""

#: src/templates/wp-admin/filesystem-security/partials/wp-file-access.php:20
msgid "Delete readme.html, license.txt and wp-config-sample.php:"
msgstr ""

#: src/templates/wp-admin/filesystem-security/partials/wp-file-access.php:25
msgid "Automatically delete the files after a WP core update."
msgstr ""

#: src/templates/wp-admin/firewall/partials/advanced-character-filter.php:14
msgid "Enable advanced character string filter"
msgstr ""

#: src/templates/wp-admin/firewall/partials/advanced-character-filter.php:17
msgid "This will block character sequences which resemble XSS attacks."
msgstr ""

#: src/templates/wp-admin/firewall/partials/advanced-character-filter.php:22
msgid "This is an advanced character string filter to prevent malicious string attacks on your site coming from Cross Site Scripting (XSS)."
msgstr ""

#: src/templates/wp-admin/firewall/partials/advanced-character-filter.php:23
msgid "This setting matches for common malicious string patterns and exploits and will produce a 403 error for the hacker attempting the query."
msgstr ""

#: src/templates/wp-admin/firewall/partials/advanced-character-filter.php:24
msgid "NOTE: Some strings for this setting might break some functionality."
msgstr ""

#: src/templates/wp-admin/firewall/partials/advanced-settings-6g.php:2
msgid "Block request methods"
msgstr ""

#: src/templates/wp-admin/firewall/partials/advanced-settings-6g.php:3
msgid "HTTP request methods are used by browsers and clients to communicate with servers to get responses."
msgstr ""

#: src/templates/wp-admin/firewall/partials/advanced-settings-6g.php:3
msgid "The below request methods are not necessary for every site to function and you may disable all HTTP request methods that are not essential for your site to function."
msgstr ""

#: src/templates/wp-admin/firewall/partials/advanced-settings-6g.php:10
msgid "Block %s method"
msgstr ""

#: src/templates/wp-admin/firewall/partials/advanced-settings-6g.php:13
msgid "Check this to block the %s request method"
msgstr ""

#: src/templates/wp-admin/firewall/partials/advanced-settings-6g.php:18
msgid "Some WooCommerce extensions use the PUT request method in addition to GET and POST."
msgstr ""

#: src/templates/wp-admin/firewall/partials/advanced-settings-6g.php:18
msgid "This means WooCommerce users shouldn't block the PUT request method."
msgstr ""

#: src/templates/wp-admin/firewall/partials/advanced-settings-6g.php:19
msgid "A few REST requests use the PUT request method."
msgstr ""

#: src/templates/wp-admin/firewall/partials/advanced-settings-6g.php:19
msgid "If your site is communicated by the WP REST API, you should not block the PUT request method."
msgstr ""

#: src/templates/wp-admin/firewall/partials/advanced-settings-6g.php:29
msgid "Other settings"
msgstr ""

#: src/templates/wp-admin/firewall/partials/advanced-settings-6g.php:30
msgid "The 6G firewall provides other settings for blocking malicious query strings, request strings, referers and user-agents; you can configure their settings below."
msgstr ""

#: src/templates/wp-admin/firewall/partials/advanced-settings-6g.php:36
msgid "Block query strings"
msgstr ""

#: src/templates/wp-admin/firewall/partials/advanced-settings-6g.php:39
msgid "Enable this to block all query strings recommended by 6G"
msgstr ""

#: src/templates/wp-admin/firewall/partials/advanced-settings-6g.php:44
msgid "Block request strings"
msgstr ""

#: src/templates/wp-admin/firewall/partials/advanced-settings-6g.php:47
msgid "Enable this to block all request strings recommended by 6G"
msgstr ""

#: src/templates/wp-admin/firewall/partials/advanced-settings-6g.php:52
msgid "Block referers"
msgstr ""

#: src/templates/wp-admin/firewall/partials/advanced-settings-6g.php:55
msgid "Enable this to block all referers recommended by 6G"
msgstr ""

#: src/templates/wp-admin/firewall/partials/advanced-settings-6g.php:60
msgid "Block user-agents"
msgstr ""

#: src/templates/wp-admin/firewall/partials/advanced-settings-6g.php:63
msgid "Enable this to block all user-agents recommended by 6G"
msgstr ""

#: src/templates/wp-admin/firewall/partials/allowlist.php:2
msgid "Allow list"
msgstr ""

#: src/templates/wp-admin/firewall/partials/allowlist.php:6
msgid "This option allows you to add IP addresses to your allow list."
msgstr ""

#: src/templates/wp-admin/firewall/partials/allowlist.php:7
msgid "All IPs in your allow list will no longer be affected by the firewall's rules."
msgstr ""

#: src/templates/wp-admin/firewall/partials/allowlist.php:16
msgid "Save allow list"
msgstr ""

#: src/templates/wp-admin/firewall/partials/bad-query-strings.php:3
msgid "Bad query strings"
msgstr ""

#: src/templates/wp-admin/firewall/partials/bad-query-strings.php:17
msgid "This will help protect you against malicious queries via XSS."
msgstr ""

#: src/templates/wp-admin/firewall/partials/bad-query-strings.php:22
msgid "This feature will prevent malicious string attacks on your site using XSS."
msgstr ""

#: src/templates/wp-admin/firewall/partials/bad-query-strings.php:23
msgid "NOTE: Some of these strings might be used for plugins or themes and hence this might break some functionality."
msgstr ""

#: src/templates/wp-admin/firewall/partials/basic-firewall-settings.php:13
msgid "Enable basic firewall protection"
msgstr ""

#: src/templates/wp-admin/firewall/partials/basic-firewall-settings.php:16
msgid "Enable this to apply basic firewall protection to your site."
msgstr ""

#: src/templates/wp-admin/firewall/partials/basic-firewall-settings.php:20
msgid "This setting will implement the following basic firewall protection mechanisms on your site:"
msgstr ""

#: src/templates/wp-admin/firewall/partials/basic-firewall-settings.php:21
msgid "1) Protect your htaccess file by denying access to it."
msgstr ""

#: src/templates/wp-admin/firewall/partials/basic-firewall-settings.php:22
msgid "2) Disable the server signature."
msgstr ""

#: src/templates/wp-admin/firewall/partials/basic-firewall-settings.php:23
msgid "3) Limit file upload size (%sMB)."
msgstr ""

#: src/templates/wp-admin/firewall/partials/basic-firewall-settings.php:24
msgid "4) Protect your wp-config.php file by denying access to it."
msgstr ""

#: src/templates/wp-admin/firewall/partials/basic-firewall-settings.php:25
msgid "The above firewall features will be applied via your .htaccess file and should not affect your site's overall functionality."
msgstr ""

#: src/templates/wp-admin/firewall/partials/basic-firewall-settings.php:26
msgid "You are still advised to take a backup of your active .htaccess file just in case."
msgstr ""

#: src/templates/wp-admin/firewall/partials/basic-firewall-settings.php:33
msgid "Max file upload size (MB)"
msgstr ""

#: src/templates/wp-admin/firewall/partials/basic-firewall-settings.php:35
msgid "The value for the maximum file upload size used in the .htaccess file. (Defaults to %sMB if left blank)"
msgstr ""

#: src/templates/wp-admin/firewall/partials/blank-ref-and-useragent.php:3
msgid "Blank HTTP headers"
msgstr ""

#: src/templates/wp-admin/firewall/partials/blank-ref-and-useragent.php:14
msgid "Ban POST requests that have a blank user-agent and referer"
msgstr ""

#: src/templates/wp-admin/firewall/partials/blank-ref-and-useragent.php:17
msgid "Enable this if you want to ban POST requests that have a blank user-agent and referer."
msgstr ""

#: src/templates/wp-admin/firewall/partials/blank-ref-and-useragent.php:21
msgid "This feature will check whether the user-agent and referer HTTP headers are blank."
msgstr ""

#: src/templates/wp-admin/firewall/partials/blank-ref-and-useragent.php:22
msgid "If they are both blank, the IP address associated with the request will be added to your permanent block list."
msgstr ""

#: src/templates/wp-admin/firewall/partials/block-debug-log.php:14
msgid "Block access to debug.log file"
msgstr ""

#: src/templates/wp-admin/firewall/partials/block-debug-log.php:17
msgid "Enable this if you want to block access to the debug.log file that WordPress creates when debug logging is enabled."
msgstr ""

#: src/templates/wp-admin/firewall/partials/block-debug-log.php:21
msgid "WordPress has an option to turn on the debug logging to a file located in wp-content/debug.log."
msgstr ""

#: src/templates/wp-admin/firewall/partials/block-debug-log.php:21
msgid "This file may contain sensitive information."
msgstr ""

#: src/templates/wp-admin/firewall/partials/block-debug-log.php:22
msgid "Using this option will block external access to this file."
msgstr ""

#: src/templates/wp-admin/firewall/partials/block-debug-log.php:22
msgid "You can still access this file by logging into your site via FTP."
msgstr ""

#: src/templates/wp-admin/firewall/partials/disable-rss-atom.php:3
msgid "Disable WordPress RSS and ATOM feeds"
msgstr ""

#: src/templates/wp-admin/firewall/partials/disable-rss-atom.php:13
msgid "Disable RSS and ATOM feeds:"
msgstr ""

#: src/templates/wp-admin/firewall/partials/disable-rss-atom.php:16
msgid "Enable this if you do not want users using feeds."
msgstr ""

#: src/templates/wp-admin/firewall/partials/disable-rss-atom.php:16
msgid "RSS and ATOM feeds are used to read content from your site."
msgstr ""

#: src/templates/wp-admin/firewall/partials/disable-rss-atom.php:20
msgid "Most users will want to share their site content widely, but some may prefer to prevent automated site scraping."
msgstr ""

#: src/templates/wp-admin/firewall/partials/disable-rss-atom.php:21
msgid "For more information, check the %s"
msgstr ""

#: src/templates/wp-admin/firewall/partials/disable-rss-atom.php:21
msgid "documentation"
msgstr ""

#: src/templates/wp-admin/firewall/partials/disable-trace.php:3
msgid "Trace and track"
msgstr ""

#: src/templates/wp-admin/firewall/partials/disable-trace.php:17
msgid "Enable this to disable trace and track."
msgstr ""

#: src/templates/wp-admin/firewall/partials/disable-trace.php:22
msgid "HTTP Trace attack (XST) can be used to return header requests and grab cookies and other information."
msgstr ""

#: src/templates/wp-admin/firewall/partials/disable-trace.php:24
msgid "This hacking technique is usually used together with cross site scripting attacks (XSS)."
msgstr ""

#: src/templates/wp-admin/firewall/partials/disable-trace.php:26
msgid "Disabling trace and track on your site will help prevent HTTP Trace attacks."
msgstr ""

#: src/templates/wp-admin/firewall/partials/fake-googlebots.php:9
msgid "This feature allows you to block bots which are impersonating as a Googlebot but actually aren't. (In other words they are fake Google bots)"
msgstr ""

#: src/templates/wp-admin/firewall/partials/fake-googlebots.php:10
msgid "Googlebots have a unique identity which cannot easily be forged and this feature will identify any fake Google bots and block them from reading your site's pages."
msgstr ""

#: src/templates/wp-admin/firewall/partials/fake-googlebots.php:17
msgid "Sometimes non-malicious Internet organizations might have bots which impersonate as a \"Googlebot\"."
msgstr ""

#: src/templates/wp-admin/firewall/partials/fake-googlebots.php:18
msgid "Just be aware that if you activate this feature the plugin will block all bots which use the \"Googlebot\" string in their User Agent information but are NOT officially from Google (irrespective whether they are malicious or not)."
msgstr ""

#: src/templates/wp-admin/firewall/partials/fake-googlebots.php:19
msgid "All other bots from other organizations such as \"Yahoo\", \"Bing\" etc will not be affected by this feature."
msgstr ""

#: src/templates/wp-admin/firewall/partials/fake-googlebots.php:36
msgid "Enable this if you want to block all fake Googlebots."
msgstr ""

#: src/templates/wp-admin/firewall/partials/fake-googlebots.php:40
msgid "This feature will check if the User Agent information of a bot contains the string \"Googlebot\"."
msgstr ""

#: src/templates/wp-admin/firewall/partials/fake-googlebots.php:41
msgid "It will then perform a few tests to verify if the bot is legitimately from Google and if so it will allow the bot to proceed."
msgstr ""

#: src/templates/wp-admin/firewall/partials/fake-googlebots.php:42
msgid "If the bot fails the checks then the plugin will mark it as being a fake Googlebot and it will block it"
msgstr ""

#: src/templates/wp-admin/firewall/partials/firewall-downgrade-button.php:4, src/templates/wp-admin/firewall/partials/firewall-setup.php:21
msgid "Downgrade firewall"
msgstr ""

#: src/templates/wp-admin/firewall/partials/firewall-set-up-button.php:4, src/templates/wp-admin/firewall/partials/firewall-setup.php:19
msgid "Set up firewall"
msgstr ""

#: src/templates/wp-admin/firewall/partials/firewall-setup.php:2
msgid "Firewall setup"
msgstr ""

#: src/templates/wp-admin/firewall/partials/firewall-setup.php:6
msgid "This option allows you to set up or downgrade the firewall."
msgstr ""

#: src/templates/wp-admin/firewall/partials/firewall-setup.php:7
msgid "We recommend you set up the firewall for greater protection, but if for whatever reason you wish to downgrade the firewall, then you can do so here."
msgstr ""

#: src/templates/wp-admin/firewall/partials/firewall-setup.php:19
msgid "This will attempt to set up the firewall in order to give you the highest level of protection it has to offer."
msgstr ""

#: src/templates/wp-admin/firewall/partials/firewall-setup.php:21
msgid "This will undo the changes performed by the set-up mechanism."
msgstr ""

#: src/templates/wp-admin/firewall/partials/firewall-setup.php:23
msgid "The firewall will still be active if it is downgraded or not set up, but you will have reduced protection."
msgstr ""

#: src/templates/wp-admin/firewall/partials/listing-directory-contents.php:3
msgid "Listing of directory contents"
msgstr ""

#: src/templates/wp-admin/firewall/partials/listing-directory-contents.php:17
msgid "Enable this if you want to disable directory and file listing."
msgstr ""

#: src/templates/wp-admin/firewall/partials/listing-directory-contents.php:22
msgid "By default, an Apache server will allow the listing of the contents of a directory if it doesn't contain an index.php file."
msgstr ""

#: src/templates/wp-admin/firewall/partials/listing-directory-contents.php:24
msgid "This feature will prevent the listing of contents for all directories."
msgstr ""

#: src/templates/wp-admin/firewall/partials/listing-directory-contents.php:26
msgid "NOTE: In order for this feature to work \"AllowOverride\" of the Indexes directive must be enabled in your httpd.conf file."
msgstr ""

#: src/templates/wp-admin/firewall/partials/listing-directory-contents.php:26
msgid "Ask your hosting provider to check this if you don't have access to httpd.conf"
msgstr ""

#: src/templates/wp-admin/firewall/partials/proxy-comment.php:3
msgid "Proxy comment posting"
msgstr ""

#: src/templates/wp-admin/firewall/partials/proxy-comment.php:17
msgid "Enable this if you want to forbid proxy comment posting."
msgstr ""

#: src/templates/wp-admin/firewall/partials/proxy-comment.php:22
msgid "This setting will deny any requests that use a proxy server when posting comments."
msgstr ""

#: src/templates/wp-admin/firewall/partials/proxy-comment.php:23
msgid "By forbidding proxy comments you are in effect eliminating some spam and other proxy requests."
msgstr ""

#: src/templates/wp-admin/firewall/partials/rest-route-whitelist.php:3
msgid "%s this REST route allows websites to display core content, such as posts, pages, and other WordPress data."
msgstr ""

#: src/templates/wp-admin/firewall/partials/rest-route-whitelist.php:3
msgid "This route is essential for the WordPress block editor and API integrations."
msgstr ""

#: src/templates/wp-admin/firewall/partials/rest-route-whitelist.php:3
msgid "Disabling it may break plugins and themes."
msgstr ""

#: src/templates/wp-admin/firewall/partials/rest-route-whitelist.php:4
msgid "%s this REST route enables embedding content from your site on external platforms (e.g., Twitter, Facebook, and WordPress embeds)."
msgstr ""

#: src/templates/wp-admin/firewall/partials/rest-route-whitelist.php:4
msgid "Disabling this may prevent your site's content from being embedded in social media and other platforms."
msgstr ""

#: src/templates/wp-admin/firewall/partials/rest-route-whitelist.php:10
msgid "Whitelist REST routes"
msgstr ""

#: src/templates/wp-admin/firewall/partials/wp-rest-api.php:7
msgid "WP REST API settings"
msgstr ""

#: src/templates/wp-admin/firewall/partials/wp-rest-api.php:18
msgid "This feature allows you to block WordPress REST API access for unauthorized requests."
msgstr ""

#: src/templates/wp-admin/firewall/partials/wp-rest-api.php:19
msgid "When enabled this feature will only allow REST requests to be processed if the user is logged in."
msgstr ""

#: src/templates/wp-admin/firewall/partials/wp-rest-api.php:20
msgid "Only REST requests made by logged-in users with a role permitted below will succeed, unless the REST API endpoint has been white-listed for others to also use."
msgstr ""

#: src/templates/wp-admin/firewall/partials/wp-rest-api.php:21
msgid "You can whitelist REST routes by selecting from the list of all registered rest routes for all users, including those who are not logged in."
msgstr ""

#: src/templates/wp-admin/firewall/partials/wp-rest-api.php:28
msgid "You do not have any registered REST API routes to block unauthorized access."
msgstr ""

#: src/templates/wp-admin/firewall/partials/wp-rest-api.php:38
msgid "Enable this to stop REST API access for non-logged in requests."
msgstr ""

#: src/templates/wp-admin/firewall/partials/wp-rest-api.php:43
msgid "User roles allowed access when logged in"
msgstr ""

#: src/templates/wp-admin/firewall/partials/xmlrpc-pingback-protection.php:3
msgid "WordPress XMLRPC and pingback vulnerability protection"
msgstr ""

#: src/templates/wp-admin/firewall/partials/xmlrpc-pingback-protection.php:15
msgid "Completely block access to XMLRPC"
msgstr ""

#: src/templates/wp-admin/firewall/partials/xmlrpc-pingback-protection.php:18
msgid "Enable this if you are not using the WP XML-RPC functionality and you want to completely block external access to XMLRPC."
msgstr ""

#: src/templates/wp-admin/firewall/partials/xmlrpc-pingback-protection.php:22
msgid "This setting will disable access to the WordPress xmlrpc.php file which is responsible for the XML-RPC functionality in WordPress."
msgstr ""

#: src/templates/wp-admin/firewall/partials/xmlrpc-pingback-protection.php:23
msgid "Hackers can exploit various vulnerabilities in the WordPress XML-RPC API in a number of ways such as:"
msgstr ""

#: src/templates/wp-admin/firewall/partials/xmlrpc-pingback-protection.php:24
msgid "1) Denial of Service (DoS) attacks"
msgstr ""

#: src/templates/wp-admin/firewall/partials/xmlrpc-pingback-protection.php:25
msgid "2) Hacking internal routers."
msgstr ""

#: src/templates/wp-admin/firewall/partials/xmlrpc-pingback-protection.php:26
msgid "3) Scanning ports in internal networks to get info from various hosts."
msgstr ""

#: src/templates/wp-admin/firewall/partials/xmlrpc-pingback-protection.php:27
msgid "Apart from the security protection benefit, this feature may also help reduce load on your server, particularly if your site currently has a lot of unwanted traffic hitting the XML-RPC API on your installation."
msgstr ""

#: src/templates/wp-admin/firewall/partials/xmlrpc-pingback-protection.php:28
msgid "NOTE: You should only enable this feature if you are not currently using the XML-RPC functionality on your WordPress installation."
msgstr ""

#: src/templates/wp-admin/firewall/partials/xmlrpc-pingback-protection.php:29
msgid "Leave this feature disabled and use the feature below if you want pingback protection but you still need XMLRPC."
msgstr ""

#: src/templates/wp-admin/firewall/partials/xmlrpc-pingback-protection.php:36
msgid "Disable pingback functionality from XMLRPC"
msgstr ""

#: src/templates/wp-admin/firewall/partials/xmlrpc-pingback-protection.php:39
msgid "If you use Jetpack or WP iOS or other apps which need WP XML-RPC functionality then check this."
msgstr ""

#: src/templates/wp-admin/firewall/partials/xmlrpc-pingback-protection.php:39
msgid "This will enable protection against WordPress pingback vulnerabilities."
msgstr ""

#: src/templates/wp-admin/firewall/partials/xmlrpc-pingback-protection.php:43
msgid "NOTE: If you use Jetpack or the Wordpress iOS or other apps then you should enable this feature but leave the \"Completely Block Access To XMLRPC\" checkbox unchecked."
msgstr ""

#: src/templates/wp-admin/firewall/partials/xmlrpc-pingback-protection.php:44
msgid "The feature will still allow XMLRPC functionality on your site but will disable the pingback methods."
msgstr ""

#: src/templates/wp-admin/firewall/partials/xmlrpc-pingback-protection.php:45
msgid "This feature will also remove the \"X-Pingback\" header if it is present."
msgstr ""

#: src/templates/wp-admin/firewall/partials/xmlrpc-warning-notice.php:6
msgid "You have enabled the \"Completely Block Access To XMLRPC\" checkbox which means all XMLRPC functionality will be blocked."
msgstr ""

#: src/templates/wp-admin/firewall/partials/xmlrpc-warning-notice.php:7
msgid "By leaving this feature enabled you will prevent Jetpack or Wordpress iOS or other apps which need XMLRPC from working correctly on your site."
msgstr ""

#: src/templates/wp-admin/firewall/partials/xmlrpc-warning-notice.php:8
msgid "If you still need XMLRPC then uncheck the \"Completely Block Access To XMLRPC\" checkbox and enable only the \"Disable Pingback Functionality From XMLRPC\" checkbox."
msgstr ""

#: src/templates/wp-admin/user-security/partials/wp-username-content.php:23
msgid "Your site does not have any account which uses the \"admin\" username."
msgstr ""

#: src/templates/wp-admin/user-security/partials/wp-username-content.php:24
msgid "This is good security practice."
msgstr ""

#: src/templates/wp-admin/user-security/partials/wp-username-content.php:3
msgid "Your site currently has an account which uses the \"admin\" username."
msgstr ""

#: src/templates/wp-admin/user-security/partials/wp-username-content.php:3
msgid "It is highly recommended that you change this name to something else."
msgstr ""

#: src/templates/wp-admin/user-security/partials/wp-username-content.php:3
msgid "Use the following field to change the admin username."
msgstr ""

#: src/templates/wp-admin/user-security/partials/wp-username-content.php:8
msgid "New admin username"
msgstr ""

#: src/templates/wp-admin/user-security/partials/wp-username-content.php:10
msgid "Choose a new username for admin."
msgstr ""

#: src/templates/wp-admin/user-security/partials/wp-username-content.php:14
msgid "Change username"
msgstr ""

#: src/templates/wp-admin/user-security/partials/wp-username-content.php:16
msgid "NOTE: If you are currently logged in as \"admin\" you will be automatically logged out after changing your username and will be required to log back in."
msgstr ""
