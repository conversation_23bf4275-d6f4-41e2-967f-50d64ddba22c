# Translation of Plugins - Elementor Website Builder &#8211; More Than Just a Page Builder - Stable (latest release) in Portuguese (Brazil)
# This file is distributed under the same license as the Plugins - Elementor Website Builder &#8211; More Than Just a Page Builder - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-06-27 03:46:37+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: pt_BR\n"
"Project-Id-Version: Plugins - Elementor Website Builder &#8211; More Than Just a Page Builder - Stable (latest release)\n"

#: modules/variables/module.php:26
msgid "Enable variables. (For this feature to work - Atomic Widgets must be active)"
msgstr "Ativar variáveis. (Para que este recurso funcione, os widgets atômicos precisam estar ativos)"

#: modules/variables/module.php:25
msgid "Variables"
msgstr "Variáveis"

#: modules/promotions/pointers/birthday.php:35
msgid "View Deals"
msgstr "Ver ofertas"

#: modules/promotions/pointers/birthday.php:31
msgid "Celebrate Elementor’s birthday with us—exclusive deals are available now."
msgstr "Comemore o aniversário do Elementor com a gente - temos ofertas exclusivas disponíveis agora."

#: modules/promotions/pointers/birthday.php:30
msgid "Elementor’s 9th Birthday sale!"
msgstr "Promoção do 9º aniversário do Elementor!"

#: modules/global-classes/global-classes-rest-api.php:128
msgid "Global classes limit exceeded. Maximum allowed: %d"
msgstr "Excedido o limite de classes globais. Máximo permitido: %d"

#: modules/cloud-library/module.php:124 assets/js/editor.js:11908
msgid "Then you can find all your templates in one convenient library."
msgstr "Assim, você pode encontrar todos os seus modelos em uma biblioteca conveniente."

#: modules/cloud-library/module.php:123 assets/js/editor.js:11907
msgid "Connect to your Elementor account"
msgstr "Conectar à sua conta do Elementor"

#: modules/cloud-library/module.php:70
msgid "Cloud Templates empowers you to save and manage design elements across all your projects. This feature is associated and connected to your Elementor Pro account and can be accessed from any website associated with your account."
msgstr "Os modelos na nuvem permitem que você salve e gerencie elementos de design em todos os seus projetos. Este recurso está associado e conectado à sua conta do Elementor Pro e pode ser acessado de qualquer site associado à sua conta."

#: modules/cloud-library/documents/cloud-template-preview.php:44
msgid "Cloud Template Previews"
msgstr "Pré-visualizações de modelos na nuvem"

#: modules/cloud-library/documents/cloud-template-preview.php:40
msgid "Cloud Template Preview"
msgstr "Pré-visualização do modelo na nuvem"

#: modules/cloud-library/connect/cloud-library.php:219
msgid "Failed to mark preview as failed."
msgstr "Falha ao marcar a pré-visualização como com falha."

#: modules/cloud-library/connect/cloud-library.php:197
msgid "Failed to save preview."
msgstr "Falha ao salvar a pré-visualização."

#: modules/atomic-widgets/opt-in.php:41
msgid "Enable Editor V4."
msgstr "Ativar o Editor V4."

#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:53
msgid "This is a title"
msgstr "Este é um título"

#: modules/atomic-opt-in/module.php:23
msgid "Enable the settings Opt In page"
msgstr "Ativar a página de aceitação das configurações"

#: modules/atomic-opt-in/module.php:22
msgid "Editor v4 (Opt In Page)"
msgstr "Editor v4 (página de aceitação)"

#: includes/widgets/progress.php:154
msgid "Display Title"
msgstr "Exibir título"

#: includes/template-library/sources/cloud.php:305
msgid "You do not have permission to create preview documents."
msgstr "Você não tem permissão para criar documentos de pré-visualização."

#: includes/managers/elements.php:280
msgid "Atomic Elements"
msgstr "Elementos atômicos"

#: includes/editor-templates/templates.php:553
#: includes/editor-templates/templates.php:569
#: includes/editor-templates/templates.php:583
msgid "Learn more about the"
msgstr "Saiba mais sobre"

#. translators: %s is the "Upgrade now" link
#: includes/editor-templates/templates.php:538
msgid "To get more space %s"
msgstr "Para ter mais espaço %s"

#: includes/editor-templates/templates.php:534
msgid "You’ve saved 100% of the templates in your plan."
msgstr "Você salvou 100% dos modelos no seu plano."

#: includes/editor-templates/templates.php:528
msgid "Site Templates"
msgstr "Modelos de sites"

#: includes/editor-templates/templates.php:500
#: modules/cloud-library/module.php:69
msgid "Cloud Templates"
msgstr "Modelos na nuvem"

#: includes/editor-templates/templates.php:493
msgid "Give your template a name"
msgstr "Dê um nome ao seu modelo"

#: includes/editor-templates/templates.php:348
#: modules/cloud-library/module.php:58
msgid "Folder"
msgstr "Pasta"

#: includes/editor-templates/templates.php:321
#: includes/editor-templates/templates.php:412
msgid "Copy to"
msgstr "Copiar para"

#: includes/editor-templates/templates.php:317
#: includes/editor-templates/templates.php:408
msgid "Move to"
msgstr "Mover para"

#: includes/editor-templates/templates.php:277
#: includes/editor-templates/templates.php:386
msgid "Upgrade to get more storage space or delete old templates to make room."
msgstr "Atualize para ter mais espaço de armazenamento, ou exclua modelos antigos para liberar espaço."

#: includes/editor-templates/templates.php:166 assets/js/editor.js:10658
#: assets/js/editor.js:10671
msgid "Move"
msgstr "Mover"

#: includes/editor-templates/templates.php:147
msgid "List view"
msgstr "Visualização em lista"

#: includes/editor-templates/templates.php:146
msgid "List View"
msgstr "Visualização em lista"

#: includes/editor-templates/templates.php:143
msgid "Grid view"
msgstr "Visualização em grade"

#: includes/editor-templates/templates.php:142
msgid "Grid View"
msgstr "Visualização em grade"

#: includes/editor-templates/templates.php:118
#: includes/editor-templates/templates.php:636
msgid "Cloud templates"
msgstr "Modelos na nuvem"

#: includes/editor-templates/templates.php:114
#: includes/editor-templates/templates.php:632
msgid "Site templates"
msgstr "Modelos de sites"

#: includes/controls/url.php:68
#: modules/atomic-widgets/controls/types/link-control.php:23
msgid "Type or paste your URL"
msgstr "Digite ou cole seu URL"

#: core/frontend/render-mode-manager.php:150
msgid "Not Authorized"
msgstr "Não autorizado"

#: core/admin/admin-notices.php:411
msgid "Create a more inclusive site experience for all your visitors. With Ally, it's easy to add your statement page in just a few clicks."
msgstr "Crie uma experiência de site mais inclusiva para todos os seus visitantes. Com o Ally, é fácil adicionar sua página de declaração com apenas alguns cliques."

#: core/admin/admin-notices.php:410
msgid "Make sure your site has an accessibility statement page"
msgstr "Certifique-se de que seu site tenha uma página de declaração de acessibilidade"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/landing-pages/module.php:224
msgid "Or view %1$sTrashed Items%2$s"
msgstr "Ou visualize os %1$sItens da lixeira%2$s"

#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:32
#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:61
msgid "Paragraph"
msgstr "Parágrafo"

#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:49
#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:62
msgid "Type your paragraph here"
msgstr "Digite seu parágrafo aqui"

#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:62
msgid "Type your button text here"
msgstr "Digite o texto do seu botão aqui"

#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:61
msgid "Button text"
msgstr "Texto do botão"

#: includes/template-library/sources/cloud.php:122
msgid "New Folder"
msgstr "Nova pasta"

#: includes/template-library/sources/cloud.php:23
msgid "Cloud-Library is not instantiated."
msgstr "A biblioteca na nuvem não foi instanciada."

#: includes/settings/tools.php:318
msgid "Clear outdated CSS files and cached data in the database (rendered HTML, JS/CSS assets, etc.). We'll regenerate those files the next time someone visits any page on your website."
msgstr "Limpe os arquivos CSS desatualizados e os dados armazenados em cache no banco de dados (HTML renderizado, ativos JS/CSS etc.). Iremos gerar esses arquivos novamente na próxima vez que alguém visitar qualquer página do seu site."

#: includes/settings/tools.php:317 modules/admin-bar/module.php:148
msgid "Clear Files & Data"
msgstr "Limpar arquivos e dados"

#: includes/settings/tools.php:314
msgid "Elementor Cache"
msgstr "Cache do Elementor"

#: includes/editor-templates/templates.php:297
msgid "Open"
msgstr "Abrir"

#: includes/editor-templates/templates.php:137
#: includes/editor-templates/templates.php:138
msgid "Create a New Folder"
msgstr "Criar uma nova pasta"

#: includes/template-library/sources/cloud.php:36
#: modules/cloud-library/connect/cloud-library.php:15
msgid "Cloud Library"
msgstr "Biblioteca na nuvem"

#: includes/controls/media.php:307
msgid "Image size settings don’t apply to Dynamic Images."
msgstr "As configurações de tamanho da imagem não se aplicam a imagens dinâmicas."

#: includes/controls/gallery.php:127 includes/controls/media.php:323
msgid "Connect Now"
msgstr "Conectar agora"

#: includes/controls/gallery.php:126 includes/controls/media.php:322
msgid "This image isn't optimized. You need to connect your Image Optimizer account first."
msgstr "Esta imagem não está otimizada. Você precisa conectar sua conta do Image Optimizer primeiro."

#: modules/nested-tabs/widgets/nested-tabs.php:182
msgid "Add Tab"
msgstr "Adicionar aba"

#: modules/element-cache/module.php:144
msgid "Element Cache"
msgstr "Cache de elementos"

#: modules/atomic-widgets/elements/div-block/div-block.php:35
#: modules/library/documents/div-block.php:52 assets/js/editor.js:10577
msgid "Div Block"
msgstr "Bloco div"

#: modules/ai/site-planner-connect/module.php:51
msgid "Approve & Connect"
msgstr "Aprovar e conectar"

#: modules/ai/site-planner-connect/module.php:50
msgid "To connect your site to Site Planner, you need to generate an app password."
msgstr "Para conectar seu site ao “Planejador de sites”, você precisa gerar uma senha de aplicativo."

#: modules/ai/site-planner-connect/module.php:49
msgid "Connect to Site Planner"
msgstr "Se conectar ao Planejador de sites"

#: modules/ai/module.php:225 modules/ai/module.php:260
msgid "Animate With AI"
msgstr "Animar com IA"

#: includes/widgets/video.php:416
msgid "Captions"
msgstr "Legendas"

#: includes/widgets/image-gallery.php:303
msgid "Custom Gap"
msgstr "Espaçamento personalizado"

#: includes/template-library/sources/local.php:1766
msgid "Sorry, you are not allowed to do that."
msgstr "Você não tem permissão para fazer isso."

#: includes/managers/elements.php:298
msgid "Hello+"
msgstr "Olá+"

#: includes/elements/container.php:1447 includes/widgets/common-base.php:323
msgid "Row Span"
msgstr "Extensão da linha"

#: includes/elements/container.php:1403 includes/widgets/common-base.php:279
msgid "Column Span"
msgstr "Extensão da coluna"

#: includes/elements/container.php:1395 includes/widgets/common-base.php:271
msgid "Grid Item"
msgstr "Item da grade"

#: core/experiments/manager.php:379
msgid "To improve page load performance and user privacy, replace Google Fonts CDN links with self-hosted font files. This approach downloads and serves font files directly from your server, eliminating external requests to Google's servers."
msgstr "Para melhorar o desempenho do carregamento da página e a privacidade do usuário, substitua os links do CDN do Google Fonts por arquivos de fonte auto-hospedados. Esta abordagem baixa e fornece arquivos de fontes diretamente do seu servidor, eliminando solicitações externas aos servidores do Google."

#: core/experiments/manager.php:378
msgid "Load Google Fonts locally"
msgstr "Carregar fontes do Google localmente"

#: modules/promotions/promotion-data.php:114
msgid "Customize layouts for visual appeal."
msgstr "Personalize os layouts para melhorar o apelo visual."

#: modules/promotions/promotion-data.php:113
msgid "Boost credibility with dynamic testimonials."
msgstr "Aumente a credibilidade com depoimentos dinâmicos."

#: modules/promotions/promotion-data.php:112
msgid "Display reviews in a rotating carousel."
msgstr "Exiba as avaliações em um carrossel rotativo."

#: modules/promotions/promotion-data.php:110
msgid "Upgrade Your Testimonials"
msgstr "Melhore seus depoimentos"

#: modules/promotions/promotion-data.php:97
msgid "Showcase multiple items with style."
msgstr "Exiba vários itens com estilo."

#: modules/promotions/promotion-data.php:96
msgid "Adjust transitions and animations."
msgstr "Ajuste as transições e animações."

#: modules/promotions/promotion-data.php:95
msgid "Create flexible custom carousels."
msgstr "Crie carrosséis personalizados flexíveis."

#: modules/promotions/promotion-data.php:93
msgid "Design Custom Carousels"
msgstr "Crie carrosséis personalizados"

#: modules/promotions/promotion-data.php:80
msgid "Create unique, interactive designs."
msgstr "Crie designs exclusivos e interativos."

#: modules/promotions/promotion-data.php:79
msgid "Add hover animations and CSS effects."
msgstr "Adicione animações ao passar o mouse e efeitos CSS."

#: modules/promotions/promotion-data.php:78
msgid "Combine text, buttons, and images."
msgstr "Combine texto, botões e imagens."

#: modules/promotions/promotion-data.php:76
msgid "Boost Conversions with CTAs"
msgstr "Aumente as conversões com CTAs"

#: modules/promotions/promotion-data.php:63
msgid "Seamlessly customize video appearance."
msgstr "Personalize a aparência do vídeo com perfeição."

#: modules/promotions/promotion-data.php:62
msgid "Adjust layout and playback settings."
msgstr "Ajuste as configurações de layout e reprodução."

#: modules/promotions/promotion-data.php:61
msgid "Embed videos with full control."
msgstr "Incorpore vídeos com controle total."

#: modules/promotions/promotion-data.php:59
msgid "Showcase Video Playlists"
msgstr "Exiba listas de reprodução de vídeos"

#: modules/promotions/promotion-data.php:46
msgid "Fully customize your headlines."
msgstr "Personalize completamente seus títulos principais."

#: modules/promotions/promotion-data.php:45
msgid "Apply rotating effects to text."
msgstr "Aplique efeitos de rotação ao texto."

#: modules/promotions/promotion-data.php:44
msgid "Highlight key messages dynamically."
msgstr "Destaque as principais mensagens dinamicamente."

#: modules/promotions/promotion-data.php:42
msgid "Bring Headlines to Life"
msgstr "Dê vida aos títulos principais"

#: modules/global-classes/module.php:50
msgid "Enable global CSS classes."
msgstr "Ativar classes CSS globais."

#: modules/global-classes/module.php:49
msgid "Global Classes"
msgstr "Classes globais"

#: modules/ai/module.php:417
msgid "Image added successfully"
msgstr "Imagem adicionada"

#: modules/ai/module.php:321 assets/js/ai-unify-product-images.js:18369
msgid "Unify with Elementor AI"
msgstr "Unificar com a IA do Elementor"

#: modules/ai/feature-intro/product-image-unification-intro.php:35
msgid "Now you can process images in bulk and standardized the background and ratio - no manual editing required!"
msgstr "Agora você pode processar imagens em massa e padronizar o plano de fundo e a proporção - sem necessidade de edição manual!"

#: modules/ai/feature-intro/product-image-unification-intro.php:34
msgid "New! Unify pack-shots with Elementor AI"
msgstr "Novidade! Unifique imagens de produtos com a IA do Elementor"

#: includes/widgets/video.php:612
msgid "Video Playlist widget"
msgstr "Widget de lista de reprodução de vídeos"

#: includes/widgets/testimonial.php:249
msgid "Loop Carousel widget"
msgstr "Widget de carrossel em loop"

#: includes/widgets/image-carousel.php:653
msgid "Space Between Dots"
msgstr "Espaço entre pontos"

#: includes/widgets/image-carousel.php:405
msgid "Carousel PRO widget"
msgstr "Widget de carrossel PRO"

#: includes/widgets/image-carousel.php:132
msgid "Carousel Name"
msgstr "Nome do carrossel"

#: includes/widgets/heading.php:232
msgid "Animated Headline widget"
msgstr "Widget de título principal animado"

#: includes/widgets/button.php:103
msgid "Call to Action widget"
msgstr "Widget de chamada para ação"

#: core/admin/admin-notices.php:485
msgid "Use Elementor's Site Mailer to ensure your store emails like purchase confirmations, shipping updates and more are reliably delivered."
msgstr "Use o Site Mailer do Elementor para garantir que os e-mails da sua loja, como confirmações de compra, atualizações de envio e outros, sejam entregues de forma confiável."

#: core/admin/admin-notices.php:484
msgid "Improve Transactional Email Deliverability"
msgstr "Melhore a eficiência da entrega de e-mails transacionais"

#: core/admin/admin-notices.php:464
msgid "Use Site Mailer for improved email deliverability, detailed email logs, and an easy setup."
msgstr "Use o Site Mailer para melhorar a eficiência na entrega de e-mails, ter registros detalhados de e-mails e uma configuração fácil."

#: core/admin/admin-notices.php:463
msgid "Ensure your form emails avoid the spam folder!"
msgstr "Garanta que os e-mails do seu formulário não caiam na pasta de spam!"

#: modules/checklist/steps/setup-header.php:70
msgid "Add a header"
msgstr "Adicionar um cabeçalho"

#: modules/checklist/steps/setup-header.php:66
msgid "This element applies across different pages, so visitors can easily navigate around your site."
msgstr "Este elemento se aplica a diferentes páginas, para que os visitantes possam navegar facilmente pelo seu site."

#: modules/checklist/steps/setup-header.php:62
msgid "Set up a header"
msgstr "Configurar um cabeçalho"

#: modules/checklist/steps/set-fonts-and-colors.php:31
msgid "Global colors and fonts ensure a cohesive look across your site. Start by defining one color and one font."
msgstr "As cores e fontes globais garantem uma aparência coesa em todo o site. Comece definindo uma cor e uma fonte."

#: modules/checklist/steps/set-fonts-and-colors.php:27
msgid "Set up your Global Fonts & Colors"
msgstr "Configure suas fontes e cores globais"

#: modules/checklist/steps/assign-homepage.php:31
msgid "Assign homepage"
msgstr "Atribuir página inicial"

#: modules/checklist/steps/assign-homepage.php:27
msgid "Before your launch, make sure to assign a homepage so visitors have a clear entry point into your site."
msgstr "Antes do lançamento, certifique-se de atribuir uma página inicial para que os visitantes tenham um ponto de entrada claro no seu site."

#: modules/checklist/steps/assign-homepage.php:23
msgid "Assign a homepage"
msgstr "Atribua uma página inicial"

#: modules/checklist/steps/add-logo.php:33
#: modules/checklist/steps/set-fonts-and-colors.php:35
msgid "Go to Site Identity"
msgstr "Acesse a identidade do site"

#: modules/checklist/steps/add-logo.php:29
msgid "Let's start by adding your logo and filling in the site identity settings. This will establish your initial presence and also improve SEO."
msgstr "Vamos começar adicionando seu logo e preenchendo as configurações de identidade do site. Isso irá estabelecer sua presença inicial e também melhorar o SEO."

#: modules/checklist/steps/add-logo.php:25
msgid "Add your logo"
msgstr "Adicionar seu logo"

#: core/settings/editor-preferences/model.php:187
msgid "Show a checklist to guide you through your first steps of website creation."
msgstr "Mostre uma lista de verificação para orientar você nas primeiras etapas da criação do site."

#: core/experiments/manager.php:344
msgid "Create advanced layouts and responsive designs with %1$sFlexbox%2$s and %3$sGrid%4$s container elements. Give it a try using the %5$sContainer playground%6$s."
msgstr "Crie layouts avançados e designs responsivos com os elementos de contêineres %1$sFlexbox%2$s e %3$sGrade%4$s. Experimente usar o %5$sPlayground de contêineres%6$s."

#: modules/floating-buttons/widgets/floating-bars-var-1.php:25
msgid "Floating Bar CTA"
msgstr "Barra flutuante de CTA"

#: modules/floating-buttons/module.php:46 assets/js/editor.js:53414
msgid "Floating Bars"
msgstr "Barras flutuantes"

#: modules/floating-buttons/documents/floating-buttons.php:203
#: modules/floating-buttons/module.php:373
msgid "Floating Element"
msgstr "Elemento flutuante"

#. translators: 1: Platform name.
#: modules/floating-buttons/classes/render/contact-buttons-render-base.php:270
msgid "Open %1$s"
msgstr "Abrir %1$s"

#. translators: 1: Accessible name.
#: modules/floating-buttons/classes/render/contact-buttons-render-base.php:112
#: modules/floating-buttons/classes/render/floating-bars-core-render.php:112
msgid "Close %1$s"
msgstr "Fechar %1$s"

#. translators: 1: Accessible name.
#: modules/floating-buttons/classes/render/contact-buttons-render-base.php:87
msgid "Toggle %1$s"
msgstr "Alternar %1$s"

#: modules/floating-buttons/base/widget-floating-bars-base.php:1259
msgid "Headline"
msgstr "Manchete"

#: modules/floating-buttons/base/widget-floating-bars-base.php:1213
msgid "Element spacing"
msgstr "Espaçamento entre elementos"

#: modules/floating-buttons/base/widget-floating-bars-base.php:1183
msgid "Align Elements"
msgstr "Alinhar elementos"

#: modules/floating-buttons/base/widget-floating-bars-base.php:1049
msgid "Horizontal position"
msgstr "Posição horizontal"

#: modules/floating-buttons/base/widget-floating-bars-base.php:339
msgid "Enter your text"
msgstr "Digite seu texto"

#: modules/floating-buttons/base/widget-floating-bars-base.php:315
msgid "Headlines"
msgstr "Manchetes"

#: modules/floating-buttons/base/widget-floating-bars-base.php:240
msgid "Pause Icon"
msgstr "Ícone de pausa"

#: modules/floating-buttons/base/widget-floating-bars-base.php:228
#: modules/floating-buttons/base/widget-floating-bars-base.php:1130
msgid "Pause and Play"
msgstr "Pausa e reprodução"

#: modules/floating-buttons/base/widget-floating-bars-base.php:219
#: modules/floating-buttons/base/widget-floating-bars-base.php:1173
msgid "Floating Bar"
msgstr "Barra flutuante"

#: modules/floating-buttons/base/widget-floating-bars-base.php:199
msgid "Accessible Name"
msgstr "Nome acessível"

#: modules/floating-buttons/base/widget-floating-bars-base.php:157
msgid "Shop now"
msgstr "Comprar agora"

#: modules/floating-buttons/base/widget-floating-bars-base.php:156
#: modules/floating-buttons/base/widget-floating-bars-base.php:204
msgid "Enter text"
msgstr "Digitar o texto"

#: modules/floating-buttons/base/widget-floating-bars-base.php:143
#: modules/floating-buttons/base/widget-floating-bars-base.php:555
msgid "CTA Button"
msgstr "Botão de CTA"

#: modules/floating-buttons/base/widget-floating-bars-base.php:131
msgid "Enter your text here"
msgstr "Digite seu texto aqui"

#: modules/floating-buttons/base/widget-floating-bars-base.php:105
#: modules/floating-buttons/base/widget-floating-bars-base.php:391
msgid "Announcement"
msgstr "Anúncio"

#: modules/floating-buttons/base/widget-floating-bars-base.php:69
msgid "Banner"
msgstr "Banner"

#: modules/floating-buttons/base/widget-floating-bars-base.php:64
msgid "Just in! Cool summer tees"
msgstr "Acabou de chegar! Camisetas bacanas de verão"

#: modules/floating-buttons/base/widget-contact-button-base.php:764
msgid "Add up to <b>%d</b> contact buttons"
msgstr "Adicione até <b>%d</b> botões de contato"

#: modules/floating-buttons/base/widget-contact-button-base.php:751
msgid "Add between <b>%1$d</b> to <b>%2$d</b> contact buttons"
msgstr "Adicione entre <b>%1$d</b> a <b>%2$d</b> botões de contato"

#: modules/floating-buttons/base/widget-contact-button-base.php:466
#: modules/floating-buttons/base/widget-contact-button-base.php:722
msgid "Add accessible name"
msgstr "Adicionar nome acessível"

#: modules/floating-buttons/base/widget-contact-button-base.php:463
#: modules/floating-buttons/base/widget-contact-button-base.php:719
msgid "Accessible name"
msgstr "Nome acessível"

#: modules/floating-buttons/admin-menu-items/floating-buttons-menu-item.php:22
#: modules/floating-buttons/admin-menu-items/floating-buttons-menu-item.php:26
#: modules/floating-buttons/documents/floating-buttons.php:207
msgid "Floating Elements"
msgstr "Elementos flutuantes"

#: modules/checklist/steps/create-pages.php:36
msgid "Create a new page"
msgstr "Criar uma nova página"

#: modules/checklist/steps/create-pages.php:32
msgid "Jumpstart your creation with professional designs from the Template Library or start from scratch."
msgstr "Acelere sua criação com designs profissionais da biblioteca de modelos, ou comece do zero."

#: modules/checklist/steps/create-pages.php:28
msgid "Create your first 3 pages"
msgstr "Crie suas 3 primeiras páginas"

#: modules/checklist/module.php:225
msgid "Launchpad Checklist feature to boost productivity and deliver your site faster"
msgstr "Recurso de lista de verificação do Launchpad para aumentar a produtividade e entregar seu site mais rapidamente"

#: core/settings/editor-preferences/model.php:182
#: modules/checklist/module.php:224
msgid "Launchpad Checklist"
msgstr "Lista de verificação do Launchpad"

#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:68
msgid "Tag"
msgstr "Tag"

#: modules/atomic-widgets/module.php:121
msgid "Enable atomic widgets."
msgstr "Ativar widgets atômicos."

#: modules/atomic-widgets/module.php:120
msgid "Atomic Widgets"
msgstr "Widgets atômicos"

#: modules/ai/preferences.php:73
msgid "Enable Elementor AI functionality"
msgstr "Ativar funcionalidade de IA do Elementor"

#: modules/ai/preferences.php:61
msgid "Elementor - AI"
msgstr "Elementor - IA"

#: includes/settings/settings.php:466
msgid "Improve initial page load performance by lazy loading all background images except the first one."
msgstr "Melhore o desempenho do carregamento inicial da página, carregando preguiçosamente todas as imagens de fundo, exceto a primeira."

#: includes/settings/settings.php:336
msgid "Personalize the way Elementor works on your website by choosing the advanced features and how they operate."
msgstr "Personalize a maneira como o Elementor funciona no seu site, escolhendo os recursos avançados e como eles funcionam."

#: includes/settings/settings.php:259
msgid "Tailor how Elementor enhances your site, from post types to other functions."
msgstr "Personalize a forma como o Elementor melhora seu site, desde tipos de post até outras funções."

#: includes/admin-templates/new-floating-elements.php:50
msgid "Create Floating Element"
msgstr "Criar elemento flutuante"

#: includes/admin-templates/new-floating-elements.php:31
msgid "Choose Floating Element"
msgstr "Escolher elemento flutuante"

#: includes/admin-templates/new-floating-elements.php:21
msgid "Use floating elements to engage your visitors and increase conversions."
msgstr "Use elementos flutuantes para envolver seus visitantes e aumentar as conversões."

#. translators: %1$s Span open tag, %2$s: Span close tag.
#: includes/admin-templates/new-floating-elements.php:16
msgid "Floating Elements Help You %1$sWork Efficiently%2$s"
msgstr "Os elementos flutuantes ajudam você a %1$strabalhar de forma eficiente%2$s"

#: core/debug/classes/shop-page-edit.php:23
msgid "Sorry, The content area was not been found on your page"
msgstr "A área de conteúdo não foi encontrada na sua página"

#: core/debug/classes/shop-page-edit.php:15
msgid "You are trying to edit the Shop Page although it is a Product Archive. Use the Theme Builder to create your Shop Archive template instead"
msgstr "Você está tentando editar a página da loja, embora ela seja um arquivo de produto. Use o construtor de temas para criar seu modelo de arquivo da loja"

#: modules/link-in-bio/widgets/link-in-bio.php:28
msgid "Minimalist"
msgstr "Minimalista"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1542
msgid "Dimensions"
msgstr "Dimensões"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:332
msgid "CTA link"
msgstr "Link do CTA"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:284
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.strings.js:48
msgid "Add item"
msgstr "Adicionar item"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:242
msgid "Images Per Row"
msgstr "Imagens por linha"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:227
msgid "Add up to <b>%d</b> Images"
msgstr "Adicione até <b>%d</b> imagens"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:215
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1023
msgid "Image Links"
msgstr "Links de imagens"

#: modules/floating-buttons/widgets/contact-buttons.php:26
msgid "Single Chat"
msgstr "Bate-papo individual"

#: modules/floating-buttons/module.php:375
msgid "Add a Floating element so your users can easily get in touch!"
msgstr "Adicione um elemento flutuante para que seus usuários possam contatar você facilmente!"

#: modules/floating-buttons/module.php:308
msgid "Entire Site"
msgstr "Todo o site"

#: modules/floating-buttons/module.php:222
msgid "Instances"
msgstr "Instâncias"

#: modules/floating-buttons/module.php:219
msgid "Click Tracking"
msgstr "Rastreamento de cliques"

#: modules/floating-buttons/documents/floating-buttons.php:195
msgid "Set as Entire Site"
msgstr "Definir como todo o site"

#: modules/floating-buttons/documents/floating-buttons.php:188
msgid "Remove From Entire Site"
msgstr "Remover de todo o site"

#: modules/floating-buttons/documents/floating-buttons.php:32
msgid "After publishing this widget, you will be able to set it as visible on the entire site in the Admin Table."
msgstr "Após publicar este widget, você poderá defini-lo como visível em todo o site na tabela administrativa."

#: modules/floating-buttons/classes/render/contact-buttons-core-render.php:58
msgid "Links window"
msgstr "Janela de links"

#: modules/floating-buttons/base/widget-contact-button-base.php:3080
#: modules/floating-buttons/base/widget-floating-bars-base.php:1488
msgid "CSS"
msgstr "CSS"

#: modules/floating-buttons/base/widget-contact-button-base.php:3067
#: modules/floating-buttons/base/widget-floating-bars-base.php:1475
msgid "Responsive visibility will take effect only on preview mode or live page, and not while editing in Elementor."
msgstr "A visibilidade responsiva só terá efeito no modo de pré-visualização ou na página ativa, e não durante a edição no Elementor."

#: modules/floating-buttons/base/widget-contact-button-base.php:3043
msgid "Full Width on Mobile"
msgstr "Largura total em dispositivos móveis"

#: modules/floating-buttons/base/widget-contact-button-base.php:2479
#: modules/floating-buttons/base/widget-contact-button-base.php:2499
msgid "Text and Icon Color"
msgstr "Cor do texto e do ícone"

#: modules/floating-buttons/base/widget-contact-button-base.php:2438
msgid "Link Spacing"
msgstr "Espaçamento entre links"

#: modules/floating-buttons/base/widget-contact-button-base.php:2388
msgid "Info Links"
msgstr "Links de informações"

#: modules/floating-buttons/base/widget-contact-button-base.php:2254
msgid "Resource Links"
msgstr "Links de recursos"

#: modules/floating-buttons/base/widget-contact-button-base.php:2224
msgid "Adjust transition duration to change the speed of the <b>hover animation on desktop</b> and the <b>click animation on touchscreen</b>."
msgstr "Ajuste a duração da transição para alterar a velocidade da <b>animação ao passar o mouse no desktop</b> e da <b>animação ao clicar na tela sensível ao toque</b>."

#: modules/floating-buttons/base/widget-contact-button-base.php:2150
msgid "Button Bar"
msgstr "Barra de botões"

#: modules/floating-buttons/base/widget-contact-button-base.php:2105
msgid "Tooltips"
msgstr "Caixas de informação (tooltips)"

#: modules/floating-buttons/base/widget-contact-button-base.php:2054
msgid "Buttons Spacing"
msgstr "Espaçamento dos botões"

#: modules/floating-buttons/base/widget-contact-button-base.php:1828
msgid "Bubble Background Color"
msgstr "Cor de fundo do balão"

#: modules/floating-buttons/base/widget-contact-button-base.php:1485
msgid "Hover animation is <b>desktop only</b>"
msgstr "A animação ao passar o mouse é <b>apenas para desktop</b>"

#: modules/floating-buttons/base/widget-contact-button-base.php:826
msgid "Enter description"
msgstr "Digite a descrição"

#: modules/floating-buttons/base/widget-contact-button-base.php:811
msgid "Enter title"
msgstr "Digite o título"

#: modules/floating-buttons/base/widget-contact-button-base.php:736
msgid "Start conversation:"
msgstr "Iniciar conversa:"

#: modules/floating-buttons/base/widget-contact-button-base.php:691
msgid "Typing Animation"
msgstr "Animação da digitação"

#: modules/floating-buttons/base/widget-contact-button-base.php:626
msgid "Active Dot"
msgstr "Ponto ativo"

#: modules/floating-buttons/base/widget-contact-button-base.php:552
msgid "Enter the text"
msgstr "Digite o texto"

#: modules/floating-buttons/base/widget-contact-button-base.php:546
#: modules/floating-buttons/base/widget-contact-button-base.php:734
#: modules/floating-buttons/base/widget-contact-button-base.php:1874
msgid "Call to Action Text"
msgstr "Texto da chamada para ação"

#: modules/floating-buttons/base/widget-contact-button-base.php:537
msgid "Call to Action"
msgstr "Chamada para ação"

#: modules/floating-buttons/base/widget-contact-button-base.php:536
msgid "Contact Details"
msgstr "Detalhes do contato"

#: modules/floating-buttons/base/widget-contact-button-base.php:532
msgid "Display Text"
msgstr "Exibir texto"

#: modules/floating-buttons/base/widget-contact-button-base.php:516
msgid "Notification Dot"
msgstr "Ponto de notificação"

#: modules/floating-buttons/base/widget-contact-button-base.php:405
#: modules/floating-buttons/base/widget-contact-button-base.php:965
#: modules/link-in-bio/base/widget-link-in-bio-base.php:514
#: modules/link-in-bio/base/widget-link-in-bio-base.php:759
msgid "Paste Waze link"
msgstr "Colar o link do Waze"

#: modules/floating-buttons/base/widget-contact-button-base.php:146
msgid "Tooltip"
msgstr "Caixa de informação (tooltip)"

#: modules/floating-buttons/base/widget-contact-button-base.php:86
msgid "Call now"
msgstr "Ligar agora"

#: modules/floating-buttons/module.php:45 assets/js/editor.js:53416
#: assets/js/editor.js:53418
msgid "Floating Buttons"
msgstr "Botões flutuantes"

#: modules/editor-events/module.php:48
msgid "Editor events processing"
msgstr "Processamento de eventos do editor"

#: modules/editor-events/module.php:47
msgid "Elementor Editor Events"
msgstr "Eventos do editor do Elementor"

#: modules/announcements/module.php:111
msgid "<p>With AI for text, code, image generation and editing, you can bring your vision to life faster than ever. Start your free trial now - <b>no credit card required!</b></p>"
msgstr "<p>Com a IA para a geração e edição de texto, código e imagens, você pode dar vida às suas ideias mais rápido do que nunca. Comece sua avaliação gratuita agora - <b>não é necessário cartão de crédito!</b></p>"

#: modules/announcements/module.php:110
msgid "Discover your new superpowers "
msgstr "Descubra seus novos superpoderes "

#. translators: 1: `<a>` opening tag, 2: `</a>` closing tag.
#: includes/widgets/video.php:342
msgid "Note: Autoplay is affected by %1$s Google’s Autoplay policy %2$s on Chrome browsers."
msgstr "Observação: A reprodução automática é afetada pela %1$s política de reprodução automática do Google %2$s nos navegadores Chrome."

#: includes/widgets/image.php:386
msgid "Scale Down"
msgstr "Reduzir escala"

#. translators: %s: <head> tag.
#: includes/settings/settings.php:423
msgid "Internal Embedding places all CSS in the %s which works great for troubleshooting, while External File uses external CSS file for better performance (recommended)."
msgstr "A incorporação interna coloca todo o CSS no %s, o que funciona muito bem para a solução de problemas, enquanto o arquivo externo usa um arquivo CSS externo para melhorar o desempenho (recomendado)."

#: includes/editor-templates/panel.php:142
msgid "Copy and Share Link"
msgstr "Copiar e compartilhar o link"

#: includes/controls/repeater.php:194
msgid "In a Repeater control, if you specify a minimum number of items, you must also specify a default value that contains at least that number of items."
msgstr "Em um controle repetidor, se você especificar um número mínimo de itens, também deve especificar um valor padrão que contenha pelo menos esse número de itens."

#: core/kits/documents/tabs/settings-background.php:75
msgid "Overscroll Behavior"
msgstr "Comportamento da rolagem excessiva"

#: core/document-types/page-base.php:189
msgid "No %s found in Trash."
msgstr "Nenhum(a) %s encontrado(a) na lixeira."

#: core/document-types/page-base.php:188
msgid "No %s found."
msgstr "Nenhum(a) %s encontrado(a)."

#: core/document-types/page-base.php:187
msgid "Search %s"
msgstr "Pesquisar %s"

#: core/document-types/page-base.php:185
msgid "New %s"
msgstr "Novo(a) %s"

#: core/document-types/page-base.php:181
msgid "All %s"
msgstr "Todos(as) %s"

#. translators: %s: br
#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:25
msgid "Create Forms and Collect Leads %s with Elementor Pro"
msgstr "Crie formulários e colete leads %s com o Elementor Pro"

#. translators: %s: br
#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:25
msgid "Enjoy creative freedom %s with Custom Icons"
msgstr "Desfrute da liberdade criativa %s com ícones personalizados"

#: modules/element-cache/module.php:161
msgid "Specify the duration for which data is stored in the cache. Elements caching speeds up loading by serving pre-rendered copies of elements, rather than rendering them fresh each time. This control ensures efficient performance and up-to-date content."
msgstr "Especifique a duração pela qual os dados serão armazenados no cache. O cache de elementos acelera o carregamento ao fornecer cópias pré-renderizadas dos elementos, em vez de renderizá-las novamente a cada vez. Este controle garante um desempenho eficiente e conteúdo atualizado."

#: modules/element-cache/module.php:159
msgid "1 Year"
msgstr "1 ano"

#: modules/element-cache/module.php:158
msgid "1 Month"
msgstr "1 mês"

#: modules/element-cache/module.php:157
msgid "2 Weeks"
msgstr "2 semanas"

#: modules/element-cache/module.php:156
msgid "1 Week"
msgstr "1 semana"

#: modules/element-cache/module.php:155
msgid "3 Days"
msgstr "3 dias"

#: modules/element-cache/module.php:154
msgid "1 Day"
msgstr "1 dia"

#: modules/element-cache/module.php:153
msgid "12 Hours"
msgstr "12 horas"

#: modules/element-cache/module.php:152
msgid "6 Hours"
msgstr "6 horas"

#: modules/element-cache/module.php:151
msgid "1 Hour"
msgstr "1 hora"

#: modules/element-cache/module.php:123
msgid "Cache Settings"
msgstr "Configurações do cache"

#: modules/element-cache/module.php:48
msgid "Elements caching reduces loading times by serving up a copy of an element instead of rendering it fresh every time the page is loaded. When active, Elementor will determine which elements can benefit from static loading - but you can override this."
msgstr "O armazenamento em cache de elementos, reduz o tempo de carregamento, fornecendo uma cópia de um elemento em vez de renderizá-lo novamente toda vez que a página for carregada. Quando ativo, o Elementor irá determinar quais elementos podem se beneficiar do carregamento estático, mas você pode substituir isso."

#: modules/element-cache/module.php:46
msgid "Element Caching"
msgstr "Cache de elementos"

#: includes/managers/elements.php:306
msgid "Link In Bio"
msgstr "Link na biografia"

#: modules/floating-buttons/base/widget-contact-button-base.php:141
msgid "Contact Buttons"
msgstr "Botões de contato"

#: core/base/traits/shared-widget-controls-trait.php:107
msgid "Icons Per Row"
msgstr "Ícones por linha"

#: modules/floating-buttons/classes/render/contact-buttons-render-base.php:222
msgid "Powered by Elementor"
msgstr "Desenvolvido pelo Elementor"

#: core/base/providers/social-network-provider.php:240
msgid "Skype"
msgstr "Skype"

#: core/base/providers/social-network-provider.php:234
msgid "Viber"
msgstr "Viber"

#: core/base/providers/social-network-provider.php:228
msgid "SMS"
msgstr "SMS"

#: core/base/providers/social-network-provider.php:222
msgid "File Download"
msgstr "Baixar arquivo"

#: core/base/providers/social-network-provider.php:204
msgid "Telephone"
msgstr "Telefone"

#: core/base/providers/social-network-provider.php:198
msgid "Messenger"
msgstr "Messenger"

#: core/base/providers/social-network-provider.php:192
msgid "Waze"
msgstr "Waze"

#: core/base/providers/social-network-provider.php:180
msgid "Dribbble"
msgstr "Dribbble"

#: core/base/providers/social-network-provider.php:174
msgid "Behance"
msgstr "Behance"

#: core/base/providers/social-network-provider.php:162
msgid "Spotify"
msgstr "Spotify"

#: core/base/providers/social-network-provider.php:156
msgid "Apple Music"
msgstr "Apple Music"

#: core/base/providers/social-network-provider.php:150
msgid "WhatsApp"
msgstr "WhatsApp"

#: core/base/providers/social-network-provider.php:144
msgid "TikTok"
msgstr "TikTok"

#: core/base/providers/social-network-provider.php:132
msgid "Pinterest"
msgstr "Pinterest"

#: core/base/providers/social-network-provider.php:126
msgid "LinkedIn"
msgstr "LinkedIn"

#: core/base/providers/social-network-provider.php:120
msgid "Instagram"
msgstr "Instagram"

#: core/base/providers/social-network-provider.php:114
msgid "X (Twitter)"
msgstr "X (Twitter)"

#: core/base/providers/social-network-provider.php:108
msgid "Facebook"
msgstr "Facebook"

#: core/base/providers/social-network-provider.php:102
msgid "Save contact (vCard)"
msgstr "Salvar contato (vCard)"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1735
msgid "Bottom Border"
msgstr "Borda inferior"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1031
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1707
msgid "Image Height"
msgstr "Altura da imagem"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1671
msgid "Image Shape"
msgstr "Formato da imagem"

#: modules/floating-buttons/base/widget-contact-button-base.php:2523
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1196
msgid "Dividers"
msgstr "Divisores"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:974
msgid "Profile"
msgstr "Perfil"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:970
msgid "Image style"
msgstr "Estilo da imagem"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:911
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1244
msgid "Identity"
msgstr "Identidade"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:883
msgid "About Me"
msgstr "Sobre mim"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:882
msgid "About"
msgstr "Sobre"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:877
msgid "About Heading"
msgstr "Título \"Sobre\""

#: modules/link-in-bio/base/widget-link-in-bio-base.php:861
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1320
msgid "Title or Tagline"
msgstr "Título ou frase de efeito (slogan)"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:852
msgid "Sara Parker"
msgstr "Sara Parker"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:838
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1284
msgid "Bio"
msgstr "Biografia"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1626
msgid "Apply Full Screen Height on"
msgstr "Aplicar altura total da tela em"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1609
msgid "Full Screen Height"
msgstr "Altura total da tela"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1562
msgid "Layout Width"
msgstr "Largura do layout"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:797
msgid "Add Icon"
msgstr "Adicionar ícone"

#: modules/floating-buttons/base/widget-contact-button-base.php:147
#: modules/link-in-bio/base/widget-link-in-bio-base.php:587
msgid "Enter icon text"
msgstr "Digite o texto do ícone"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:569
msgid "Add up to <b>%d</b> icons"
msgstr "Adicione até <b>%d</b> ícones"

#: modules/floating-buttons/base/widget-contact-button-base.php:2262
#: modules/link-in-bio/base/widget-link-in-bio-base.php:557
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1381
msgid "Icons"
msgstr "Ícones"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:543
msgid "Add CTA Link"
msgstr "Adicionar link do CTA"

#: modules/floating-buttons/base/widget-contact-button-base.php:918
#: modules/link-in-bio/base/widget-link-in-bio-base.php:532
#: modules/link-in-bio/base/widget-link-in-bio-base.php:780
msgid "Enter your username"
msgstr "Digite seu nome de usuário"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:493
msgid "Enter your number"
msgstr "Digite seu número"

#: modules/floating-buttons/base/widget-contact-button-base.php:836
#: modules/link-in-bio/base/widget-link-in-bio-base.php:438
#: modules/link-in-bio/base/widget-link-in-bio-base.php:679
msgid "Enter your email"
msgstr "Digite seu e-mail"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:427
msgid "Mail"
msgstr "E-mail"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:417
#: modules/link-in-bio/base/widget-link-in-bio-base.php:647
msgid "Enter your link"
msgstr "Digite seu link"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:354
msgid "Link Type"
msgstr "Tipo de link"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:333
msgid "Enter link text"
msgstr "Digite o texto do link"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:314
msgid "Add up to <b>%d</b> CTA links"
msgstr "Adicione até <b>%d</b> links de CTA"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:302
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1072
msgid "CTA Link Buttons"
msgstr "Botões de link do CTA"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:97
msgid "Healthy Living Resources"
msgstr "Recursos para uma vida saudável"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:94
msgid "Meal Prep"
msgstr "Preparo da refeição"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:91
msgid "Top 10 Recipes"
msgstr "As 10 principais receitas"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:88
msgid "Get Healthy"
msgstr "Fique saudável"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:55
msgid "Join me on my journey to a healthier lifestyle"
msgstr "Junte-se a mim na minha jornada para um estilo de vida mais saudável"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:52
msgid "Kitchen Chronicles"
msgstr "Crônicas da cozinha"

#: modules/floating-buttons/base/widget-contact-button-base.php:2774
msgid "Close Animation"
msgstr "Animação de fechamento"

#: modules/floating-buttons/base/widget-contact-button-base.php:2764
msgid "Open Animation"
msgstr "Animação de abertura"

#: modules/floating-buttons/base/widget-contact-button-base.php:2085
#: modules/floating-buttons/base/widget-contact-button-base.php:2176
#: modules/floating-buttons/base/widget-contact-button-base.php:2869
#: modules/floating-buttons/base/widget-floating-bars-base.php:846
#: modules/link-in-bio/base/widget-link-in-bio-base.php:120
msgid "Sharp"
msgstr "Acentuado"

#: modules/floating-buttons/base/widget-contact-button-base.php:2079
#: modules/floating-buttons/base/widget-contact-button-base.php:2170
#: modules/floating-buttons/base/widget-contact-button-base.php:2863
#: modules/floating-buttons/base/widget-floating-bars-base.php:840
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1156
msgid "Corners"
msgstr "Cantos"

#: modules/floating-buttons/base/widget-contact-button-base.php:247
msgid "Chat Box"
msgstr "Caixa de bate-papo"

#: modules/floating-buttons/base/widget-contact-button-base.php:1797
msgid "Time"
msgstr "Tempo"

#: modules/floating-buttons/base/widget-contact-button-base.php:1655
msgid "Close Button Color"
msgstr "Cor do botão “Fechar\""

#: modules/floating-buttons/base/widget-contact-button-base.php:1641
#: modules/floating-buttons/base/widget-floating-bars-base.php:295
#: modules/floating-buttons/base/widget-floating-bars-base.php:1036
msgid "Close Button"
msgstr "Botão \"Fechar\""

#: modules/floating-buttons/base/widget-contact-button-base.php:737
#: modules/floating-buttons/base/widget-contact-button-base.php:1045
msgid "Type your text here"
msgstr "Digite seu texto aqui"

#: modules/floating-buttons/base/widget-contact-button-base.php:186
msgid "Click to start chat"
msgstr "Clique para iniciar o bate-papo"

#: modules/floating-buttons/base/widget-contact-button-base.php:183
msgid "Send Button"
msgstr "Botão “Enviar\""

#: modules/floating-buttons/base/widget-contact-button-base.php:682
msgid "14:20"
msgstr "14:20"

#: modules/floating-buttons/base/widget-contact-button-base.php:681
msgid "2:20 PM"
msgstr "2:20 PM"

#: modules/floating-buttons/base/widget-contact-button-base.php:677
msgid "Time format"
msgstr "Formato da hora"

#: modules/floating-buttons/base/widget-contact-button-base.php:669
msgid "Hey, how can I help you today?"
msgstr "Olá! Como posso te ajudar hoje?"

#: modules/floating-buttons/base/widget-contact-button-base.php:655
msgid "Rob"
msgstr "Rob"

#: modules/floating-buttons/base/widget-contact-button-base.php:645
#: modules/floating-buttons/base/widget-contact-button-base.php:1712
msgid "Message Bubble"
msgstr "Balão da mensagem"

#: modules/floating-buttons/base/widget-contact-button-base.php:613
#: modules/floating-buttons/base/widget-contact-button-base.php:1530
msgid "Profile Image"
msgstr "Imagem do perfil"

#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:66
#: modules/floating-buttons/base/widget-contact-button-base.php:131
msgid "Type your title here"
msgstr "Digite seu título aqui"

#: modules/floating-buttons/base/widget-contact-button-base.php:130
msgid "Store Manager"
msgstr "Gerente da loja"

#: modules/floating-buttons/base/widget-contact-button-base.php:123
#: modules/floating-buttons/base/widget-contact-button-base.php:656
msgid "Type your name here"
msgstr "Digite seu nome aqui"

#: modules/floating-buttons/base/widget-contact-button-base.php:122
msgid "Rob Jones"
msgstr "Rob Jones"

#: modules/floating-buttons/base/widget-contact-button-base.php:380
#: modules/floating-buttons/base/widget-contact-button-base.php:980
msgid "Action"
msgstr "Ação"

#: modules/floating-buttons/base/widget-contact-button-base.php:342
#: modules/floating-buttons/base/widget-contact-button-base.php:894
#: modules/link-in-bio/base/widget-link-in-bio-base.php:734
msgid "+"
msgstr "+"

#: modules/floating-buttons/base/widget-contact-button-base.php:321
#: modules/floating-buttons/base/widget-contact-button-base.php:663
#: modules/floating-buttons/base/widget-contact-button-base.php:670
#: modules/floating-buttons/base/widget-contact-button-base.php:870
#: modules/floating-buttons/base/widget-contact-button-base.php:872
#: modules/floating-buttons/base/widget-contact-button-base.php:1766
#: modules/link-in-bio/base/widget-link-in-bio-base.php:463
#: modules/link-in-bio/base/widget-link-in-bio-base.php:474
#: modules/link-in-bio/base/widget-link-in-bio-base.php:713
#: modules/link-in-bio/base/widget-link-in-bio-base.php:715
msgid "Message"
msgstr "Mensagem"

#: modules/floating-buttons/base/widget-contact-button-base.php:305
#: modules/floating-buttons/base/widget-contact-button-base.php:855
#: modules/floating-buttons/base/widget-contact-button-base.php:857
#: modules/link-in-bio/base/widget-link-in-bio-base.php:445
#: modules/link-in-bio/base/widget-link-in-bio-base.php:456
#: modules/link-in-bio/base/widget-link-in-bio-base.php:698
#: modules/link-in-bio/base/widget-link-in-bio-base.php:700
msgid "Subject"
msgstr "Assunto"

#: modules/floating-buttons/base/widget-contact-button-base.php:294
msgid "@"
msgstr "@"

#: core/base/providers/social-network-provider.php:210
#: modules/floating-buttons/base/widget-contact-button-base.php:285
#: modules/floating-buttons/base/widget-contact-button-base.php:834
#: modules/link-in-bio/base/widget-link-in-bio-base.php:677
msgid "Email"
msgstr "E-mail"

#: modules/floating-buttons/base/widget-contact-button-base.php:479
#: modules/floating-buttons/base/widget-contact-button-base.php:777
#: modules/link-in-bio/base/widget-link-in-bio-base.php:595
msgid "Platform"
msgstr "Plataforma"

#: modules/floating-buttons/base/widget-contact-button-base.php:65
msgid "Chat Button"
msgstr "Botão de bate-papo"

#: modules/apps/admin-apps-page.php:126
msgid "Cannot Install"
msgstr "Não foi possível instalar"

#: modules/apps/admin-apps-page.php:119 modules/apps/admin-apps-page.php:150
msgid "Cannot Activate"
msgstr "Não foi possível ativar"

#: includes/widgets/traits/button-trait.php:299
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:163
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:209
msgid "Space between"
msgstr "Espaço entre"

#: includes/settings/settings.php:454
msgid "Reduce unnecessary render-blocking loads by dequeuing unused Gutenberg block editor scripts and styles."
msgstr "Reduza as cargas de bloqueio de renderização desnecessárias, removendo da fila os scripts e estilos não usados do editor de blocos do Gutenberg."

#. translators: 1: fetchpriority attribute, 2: lazy loading attribute.
#: includes/settings/settings.php:439
msgid "Improve performance by applying %1$s on LCP image and %2$s on images below the fold."
msgstr "Melhore o desempenho, aplicando %1$s na imagem LCP e %2$s nas imagens abaixo da dobra."

#: includes/settings/settings.php:407
msgid "Improve loading times on your site by selecting the optimization tools that best fit your requirements."
msgstr "Melhore o tempo de carregamento do seu site, selecionando as ferramentas de otimização que melhor atendam aos seus requisitos."

#: core/settings/editor-preferences/model.php:233
msgid "Decide where you want to go when leaving the editor."
msgstr "Decida para onde você quer ir quando sair do editor."

#: core/settings/editor-preferences/model.php:209
#: modules/styleguide/module.php:129
msgid "Temporarily overlay the canvas with the style guide to preview your changes to global colors and fonts."
msgstr "Sobreponha temporariamente a tela com o guia de estilo para pré-visualizar suas alterações nas cores e fontes globais."

#: core/settings/editor-preferences/model.php:204
#: modules/styleguide/module.php:127
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:442
msgid "Show global settings"
msgstr "Mostrar configurações globais"

#: core/settings/editor-preferences/model.php:165
msgid "This refers to elements you’ve hidden in the Responsive Visibility settings."
msgstr "Isso se refere aos elementos que você ocultou nas configurações de visibilidade responsiva."

#: core/settings/editor-preferences/model.php:160
msgid "Show hidden elements"
msgstr "Mostrar elementos ocultos"

#: core/settings/editor-preferences/model.php:153
msgid "This only applies while you’re working in the editor. The front end won’t be affected."
msgstr "Isso se aplica apenas enquanto você estiver trabalhando no editor. A interface não será afetada."

#: core/settings/editor-preferences/model.php:148
msgid "Expand images in lightbox"
msgstr "Expandir imagens na lightbox"

#: core/settings/editor-preferences/model.php:141
msgid "Show additional actions while hovering over the handle of an element."
msgstr "Mostrar ações adicionais ao passar o mouse sobre a alça de um elemento."

#: core/settings/editor-preferences/model.php:137
msgid "Show quick edit options"
msgstr "Mostrar opções de edição rápida"

#: core/settings/editor-preferences/model.php:128
msgid "Choose which device to display when clicking the Responsive Mode icon."
msgstr "Escolha o dispositivo a ser exibido ao clicar no ícone do “Modo responsivo”."

#: core/settings/editor-preferences/model.php:84
msgid "Set light or dark mode, or auto-detect to sync with your operating system settings."
msgstr "Defina o modo claro ou escuro, ou a detecção automática para sincronizar com as configurações do sistema operacional."

#: core/settings/editor-preferences/model.php:75
msgid "Dark mode"
msgstr "Modo escuro"

#: core/settings/editor-preferences/model.php:71
msgid "Light mode"
msgstr "Modo claro"

#: core/settings/editor-preferences/model.php:67
msgid "Display mode"
msgstr "Modo de exibição"

#: core/settings/editor-preferences/model.php:59
msgid "Panel"
msgstr "Painel"

#: core/experiments/manager.php:371
msgid "Reduce the DOM size by eliminating HTML tags in various elements and widgets. This experiment includes markup changes so it might require updating custom CSS/JS code and cause compatibility issues with third party plugins."
msgstr "Reduza o tamanho do DOM eliminando as tags HTML em vários elementos e widgets. Este experimento inclui alterações de marcação, portanto, pode exigir a atualização do código CSS/JS personalizado e causar problemas de compatibilidade com plugins de terceiros."

#: core/experiments/manager.php:369
msgid "Optimized Markup"
msgstr "Marcação otimizada"

#: core/admin/admin.php:1022 includes/controls/gallery.php:122
#: includes/controls/media.php:318
msgid "Optimize your images to enhance site performance by using Image Optimizer."
msgstr "Otimize suas imagens para melhorar o desempenho do site usando o Image Optimizer."

#: modules/promotions/admin-menu-items/go-pro-promotion-item.php:33
msgid "Upgrade Sale Now"
msgstr "Promoção para atualizar agora"

#: core/admin/admin.php:349
msgid "Discounted Upgrades Now!"
msgstr "Agora com desconto nas atualizações!"

#: core/admin/admin-notices.php:571
msgid "Automatically compress and optimize images, resize larger files, or convert to WebP. Optimize images individually, in bulk, or on upload."
msgstr "Compacte e otimize imagens automaticamente, redimensione arquivos maiores ou converta-os em WebP. Otimize imagens individualmente, em massa ou no envio."

#: core/admin/admin-notices.php:570
msgid "Speed up your website with Image Optimizer by Elementor"
msgstr "Use o Image Optimizer by Elementor para acelerar seu site"

#: modules/home/<USER>
msgid "Default Elementor menu page."
msgstr "Página de menu padrão do Elementor."

#: modules/home/<USER>
msgid "Elementor Home Screen"
msgstr "Tela inicial do Elementor"

#: includes/widgets/counter.php:452
msgid "Number Gap"
msgstr "Espaço entre números"

#: includes/widgets/counter.php:424
msgid "Number Alignment"
msgstr "Alinhamento de números"

#: includes/widgets/counter.php:388
msgid "Number Position"
msgstr "Posição do número"

#: includes/widgets/counter.php:372
msgid "Title Gap"
msgstr "Espaço do título"

#: includes/widgets/counter.php:343
msgid "Title Vertical Alignment"
msgstr "Alinhamento vertical do título"

#: includes/widgets/counter.php:314
msgid "Title Horizontal Alignment"
msgstr "Alinhamento horizontal do título"

#: includes/widgets/counter.php:275
msgid "Title Position"
msgstr "Posição do título"

#: includes/settings/settings.php:215
msgid "Home"
msgstr "Página inicial"

#: elementor.php:96
msgid "Elementor isn’t running because WordPress is outdated."
msgstr "O Elementor não está funcionando porque o WordPress está desatualizado."

#. translators: %s: PHP version.
#. translators: %s: WordPress version.
#: elementor.php:75 elementor.php:99
msgid "Update to version %s and get back to creating!"
msgstr "Atualize para a versão %s e volte a criar!"

#: elementor.php:72
msgid "Elementor isn’t running because PHP is outdated."
msgstr "O Elementor não está funcionando porque o PHP está desatualizado."

#: core/files/uploads-manager.php:589
msgid "You do not have permission to upload JSON files."
msgstr "Você não tem permissão para enviar arquivos JSON."

#: core/admin/admin.php:1024
msgid "Image Optimizer"
msgstr "Otimizador de imagens"

#: core/admin/admin.php:1024 modules/apps/admin-apps-page.php:123
msgid "Install"
msgstr "Instalar"

#: modules/site-navigation/rest-fields/page-user-can.php:28
msgid "Whether the current user can edit or delete this post"
msgstr "Se o usuário atual pode editar ou excluir este post"

#: modules/shapes/widgets/text-path.php:149
msgid "Want to create custom text paths with SVG?"
msgstr "Quer criar caminhos de texto personalizados com SVG?"

#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:36
msgid "Add any icon, anywhere on your website"
msgstr "Adicione qualquer ícone, em qualquer lugar do seu site"

#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:33
msgid "Expand your icon library beyond FontAwesome and add icon %s libraries of your choice"
msgstr "Expanda sua biblioteca de ícones para além do FontAwesome e adicione bibliotecas de ícones %s de sua escolha"

#. translators: %s: br
#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:31
msgid "Remain GDPR compliant with Custom Fonts that let you disable %s Google Fonts from your website"
msgstr "Mantenha-se em conformidade com o GDPR com fontes personalizadas que permitem desativar %s fontes do Google no seu site"

#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:28
msgid "Upload any font to keep your website true to your brand"
msgstr "Envie qualquer fonte para manter seu site fiel à sua marca"

#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:23
msgid "Stay on brand with a Custom Font"
msgstr "Mantenha a identidade da marca com uma fonte personalizada"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:30
msgid "Leverage Elementor AI to instantly generate Custom Code for Elementor"
msgstr "Aproveite a IA do Elementor para gerar instantaneamente códigos personalizados para o Elementor"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:29
msgid "Use Custom Code to create sophisticated custom interactions to engage visitors"
msgstr "Use código personalizado para criar interações personalizadas sofisticadas para envolver os visitantes"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:28
msgid "Add Custom Code snippets anywhere on your website, including the header or footer to measure your page’s performance*"
msgstr "Adicione fragmento de código (snippet) personalizado em qualquer lugar do seu site, inclusive no cabeçalho ou no rodapé, para medir o desempenho da sua página*"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:23
msgid "Enjoy Creative Freedom with Custom Code"
msgstr "Desfrute da liberdade criativa com código personalizado"

#: modules/apps/module.php:70
msgid "For Elementor"
msgstr "Para o Elementor"

#: modules/apps/admin-pointer.php:35
msgid "Explore Add-ons"
msgstr "Explorar complementos"

#: modules/apps/admin-pointer.php:29
msgid "New! Popular Add-ons"
msgstr "Novo! Complementos populares"

#: modules/apps/admin-menu-apps.php:22 modules/apps/admin-menu-apps.php:26
#: modules/apps/module.php:37 assets/js/admin-top-bar.js:185
#: assets/js/editor.js:40505
msgid "Add-ons"
msgstr "Complementos"

#: modules/apps/admin-apps-page.php:35
msgid "Please note that certain tools and services on this page are developed by third-party companies and are not part of Elementor's suite of products or support. Before using them, we recommend independently evaluating them. Additionally, when clicking on their action buttons, you may be redirected to an external website."
msgstr "Observe que determinadas ferramentas e serviços nesta página são desenvolvidos por empresas terceirizadas e não fazem parte do conjunto de produtos ou suporte do Elementor. Antes de usá-los, recomendamos que você os avalie de forma independente. Além disso, ao clicar em seus botões de ação, você poderá ser redirecionado para um site externo."

#: includes/widgets/text-editor.php:191
msgid "10"
msgstr "10"

#: includes/widgets/text-editor.php:190
msgid "9"
msgstr "9"

#: includes/widgets/text-editor.php:189
msgid "8"
msgstr "8"

#: includes/widgets/text-editor.php:188
msgid "7"
msgstr "7"

#: includes/widgets/text-editor.php:187
msgid "6"
msgstr "6"

#: includes/widgets/text-editor.php:186
msgid "5"
msgstr "5"

#: includes/widgets/text-editor.php:185
msgid "4"
msgstr "4"

#: includes/widgets/text-editor.php:184
msgid "3"
msgstr "3"

#: includes/widgets/text-editor.php:183
msgid "2"
msgstr "2"

#: includes/widgets/text-editor.php:182
msgid "1"
msgstr "1"

#: includes/widgets/icon-box.php:352 includes/widgets/image-box.php:324
msgid "Content Spacing"
msgstr "Espaçamento do conteúdo"

#: includes/widgets/common-base.php:1028
msgid "Explore additional Premium Shape packs and use them in your site."
msgstr "Explore pacotes adicionais de formas premium e use-os no seu site."

#. translators: 1: Link open tag, 2: Link close tag.
#: includes/settings/tools.php:344
msgid "It is strongly recommended to %1$sbackup the database%2$s before using replacing URLs."
msgstr "É altamente recomendável %1$sfazer backup do banco de dados%2$s antes de substituir URLs."

#: includes/controls/base-units.php:138
msgid "Custom unit"
msgstr "Unidade personalizada"

#: core/role-manager/role-manager.php:215
msgid "Giving broad access to edit the HTML widget can pose a security risk to your website because it enables users to run malicious scripts, etc."
msgstr "Conceder amplo acesso para editar o widget HTML pode representar um risco de segurança para o seu site, pois permite que os usuários executem scripts maliciosos etc."

#: core/role-manager/role-manager.php:213
msgid "Enable the option to use the HTML widget"
msgstr "Ative a opção para usar o widget HTML"

#: includes/editor-templates/panel-elements.php:33
msgid "Access all Pro widgets."
msgstr "Acesse todos os widgets do Pro."

#: includes/editor-templates/navigator.php:19
msgid "Access all Pro widgets"
msgstr "Acesse todos os widgets Pro"

#: core/utils/hints.php:156 includes/controls/notice.php:83
msgid "Don’t show again."
msgstr "Não mostrar novamente."

#: includes/managers/controls.php:1319
#: modules/floating-buttons/base/widget-floating-bars-base.php:1453
#: assets/js/editor.js:55434
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:146
msgid "Sticky"
msgstr "Fixo"

#: includes/managers/controls.php:1310 assets/js/editor.js:55419
msgid "Mouse Effects"
msgstr "Efeitos do mouse"

#: includes/managers/controls.php:1301 assets/js/editor.js:55404
msgid "Scrolling Effects"
msgstr "Efeitos de rolagem"

#: core/admin/admin-notices.php:523 includes/controls/gallery.php:123
#: includes/controls/media.php:319
msgid "Install Plugin"
msgstr "Instalar plugin"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:35
#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:48
msgid "* Requires an Advanced subscription or higher"
msgstr "* Requer uma assinatura \"Advanced\" ou superior"

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:35
msgid "Collect lead submissions directly within your WordPress Admin to manage, analyze and perform bulk actions on the submitted lead*"
msgstr "Colete envios de leads diretamente dentro do seu painel administrativo do WordPress para gerenciar, analisar e executar ações em massa nos leads enviados*"

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:34
msgid "Integrate your favorite marketing software*"
msgstr "Integre seu programa de marketing favorito*"

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:33
msgid "Use any field to collect the information you need"
msgstr "Use qualquer campo para coletar as informações de que você precisa"

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:32
msgid "Create single or multi-step forms to engage and convert visitors"
msgstr "Crie formulários de uma ou várias etapas para envolver e converter visitantes"

#: includes/widgets/testimonial.php:176
msgid "Designer"
msgstr "Designer"

#: includes/widgets/testimonial.php:161
msgid "John Doe"
msgstr "Fulano de Tal"

#: includes/template-library/sources/local.php:826
msgid "You do not have permission to export this template."
msgstr "Você não tem permissão para exportar este modelo."

#: includes/template-library/manager.php:409
#: includes/template-library/manager.php:552
#: includes/template-library/sources/local.php:822
msgid "You do not have permission to access this template."
msgstr "Você não tem permissão para acessar este modelo."

#: includes/template-library/sources/local.php:817
msgid "Invalid template type or template does not exist."
msgstr "Tipo de modelo inválido ou modelo não existe."

#: includes/managers/controls.php:1270 assets/js/editor.js:55389
msgid "Display Conditions"
msgstr "Condições de exibição"

#: includes/editor-templates/templates.php:457
msgid "Generate Variations"
msgstr "Gerar variações"

#: includes/controls/groups/background.php:162
msgid "Set locations and angle for each breakpoint to ensure the gradient adapts to different screen sizes."
msgstr "Defina as localizações e o ângulo de cada ponto de interrupção para garantir que o gradiente se adapte a diferentes tamanhos de tela."

#: core/role-manager/role-manager.php:197
msgid "Giving broad access to upload JSON files can pose a security risk to your website because such files may contain malicious scripts, etc."
msgstr "Conceder amplo acesso para enviar arquivos JSON pode representar um risco de segurança para o seu site, pois esses arquivos podem conter scripts maliciosos etc."

#: core/role-manager/role-manager.php:197
#: core/role-manager/role-manager.php:215
msgid "Heads up"
msgstr "Atenção"

#: core/role-manager/role-manager.php:195
msgid "Enable the option to upload JSON files"
msgstr "Ativar a opção para enviar arquivos JSON"

#: core/files/uploads-manager.php:290
msgid "Invalid file name."
msgstr "Nome de arquivo inválido."

#: includes/editor-templates/templates.php:176
#: assets/js/element-manager-admin.js:2290
#: assets/js/element-manager-admin.js:2359
msgid "Usage"
msgstr "Uso"

#: modules/promotions/widgets/pro-widget-promotion.php:56
msgid "This result includes the Elementor Pro %s widget. Upgrade now to unlock it and grow your web creation toolkit."
msgstr "Este resultado inclui o widget Elementor Pro %s. Atualize agora para desbloqueá-lo e aumentar seu kit de ferramentas de criação web."

#: modules/element-manager/ajax.php:154
msgid "Unexpected elements data."
msgstr "Dados de elementos inesperados."

#: modules/element-manager/ajax.php:148
msgid "No elements to save."
msgstr "Nenhum elemento para salvar."

#: modules/element-manager/ajax.php:127
msgid "WordPress Widgets"
msgstr "Widgets do WordPress"

#: modules/element-manager/ajax.php:117
msgid "Invalid nonce."
msgstr "Nonce inválido."

#: modules/element-manager/ajax.php:112
msgid "You do not have permission to edit these settings."
msgstr "Você não tem permissão para editar estas configurações."

#: includes/widgets/image-gallery.php:108
msgid "Use interesting masonry layouts and other overlay features with Elementor's Pro Gallery widget."
msgstr "Use layouts interessantes de alvenaria e outros recursos de sobreposição com o widget de Galeria Pro do Elementor."

#: includes/template-library/sources/local.php:238
msgid "Parent Template:"
msgstr "Modelo principal (ascendente):"

#: includes/template-library/sources/local.php:237
msgid "No Templates found in Trash"
msgstr "Nenhum modelo encontrado na lixeira"

#: includes/template-library/sources/local.php:236
msgid "No Templates found"
msgstr "Nenhum modelo encontrado"

#: includes/template-library/sources/local.php:235
msgid "Search Template"
msgstr "Pesquisar modelo"

#: includes/template-library/sources/local.php:234
msgid "View Template"
msgstr "Ver modelo"

#: includes/template-library/sources/local.php:233
msgid "All Templates"
msgstr "Todos os modelos"

#: includes/template-library/sources/local.php:229
#: includes/template-library/sources/local.php:230
msgid "Add New Template"
msgstr "Adicionar novo modelo"

#: includes/controls/groups/image-size.php:296 includes/controls/media.php:297
#: includes/widgets/testimonial.php:317
msgid "Image Resolution"
msgstr "Resolução da imagem"

#: includes/controls/groups/flex-container.php:187
msgid "No Wrap"
msgstr "Sem envolver"

#: includes/controls/groups/flex-container.php:183
#: includes/controls/groups/flex-container.php:191
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:156
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:158
msgid "Wrap"
msgstr "Envolver"

#: core/experiments/manager.php:361
msgid "Container-based content will be hidden from your site and may not be recoverable in all cases."
msgstr "O conteúdo baseado em contêiner ficará oculto em seu site e poderá não ser recuperável em todos os casos."

#: core/common/modules/finder/categories/settings.php:79
#: modules/element-manager/admin-menu-app.php:22
#: modules/element-manager/admin-menu-app.php:26
#: modules/element-manager/admin-menu-app.php:35
msgid "Element Manager"
msgstr "Gerenciador de elementos"

#: includes/editor-templates/templates.php:539 assets/js/ai-admin.js:1034
#: assets/js/ai-admin.js:1081 assets/js/ai-admin.js:2494
#: assets/js/ai-admin.js:3225 assets/js/ai-gutenberg.js:2802
#: assets/js/ai-gutenberg.js:2849 assets/js/ai-gutenberg.js:4262
#: assets/js/ai-gutenberg.js:4993 assets/js/ai-layout.js:732
#: assets/js/ai-layout.js:779 assets/js/ai-media-library.js:2663
#: assets/js/ai-media-library.js:2710 assets/js/ai-media-library.js:4123
#: assets/js/ai-media-library.js:4854 assets/js/ai-unify-product-images.js:2663
#: assets/js/ai-unify-product-images.js:2710
#: assets/js/ai-unify-product-images.js:4123
#: assets/js/ai-unify-product-images.js:4854 assets/js/ai.js:3448
#: assets/js/ai.js:3495 assets/js/ai.js:4908 assets/js/ai.js:5639
#: assets/js/editor.js:10336 assets/js/editor.js:10338
#: assets/js/editor.js:11915 assets/js/editor.js:11916
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:516
msgid "Upgrade now"
msgstr "Atualizar agora"

#. translators: %s: Recommended PHP version.
#: modules/system-info/reporters/server.php:131
msgid "We recommend using PHP version %s or higher."
msgstr "Recomendamos usar a versão PHP %s ou superior."

#: modules/page-templates/module.php:158
msgid "Elementor Full Width"
msgstr "Elementor largura total"

#: modules/page-templates/module.php:157
msgid "Elementor Canvas"
msgstr "Tela do Elementor"

#: modules/nested-accordion/widgets/nested-accordion.php:320
msgid "Let Google know that this section contains an FAQ. Make sure to only use it only once per page"
msgstr "Informe ao Google que esta seção contém perguntas frequentes. Certifique-se de usá-la apenas uma vez por página"

#: modules/image-loading-optimization/module.php:240
msgid "An image should not be lazy-loaded and marked as high priority at the same time."
msgstr "Uma imagem não deve ser carregada lentamente e marcada como de alta prioridade ao mesmo tempo."

#: includes/settings/settings.php:429
msgid "Optimized Image Loading"
msgstr "Carregamento de imagem otimizado"

#: includes/settings/settings.php:446
msgid "Optimized Gutenberg Loading"
msgstr "Carregamento otimizado do Gutenberg"

#: includes/widgets/video.php:291
msgid "VideoPress URL"
msgstr "URL do VideoPress"

#: includes/widgets/video.php:140
msgid "VideoPress"
msgstr "VideoPress"

#: includes/widgets/star-rating.php:129
msgid "You are currently editing a Star Rating widget in its old version. Drag a new Rating widget onto your page to use a newer version, providing better capabilities."
msgstr "No momento, você está editando um widget de classificação por estrelas em sua versão antiga. Arraste um novo widget de classificação para sua página para usar uma versão mais recente, que oferece recursos melhores."

#: includes/widgets/rating.php:308
msgid "Rated %1$s out of %2$s"
msgstr "Classificado como %1$s de %2$s"

#: includes/settings/tools.php:165
msgid "An error occurred, the selected version is invalid. Try selecting different version."
msgstr "Ocorreu um erro, a versão selecionada é inválida. Tente selecionar uma versão diferente."

#: includes/controls/groups/typography.php:220
msgid "Letter Spacing"
msgstr "Espaçamento entre letras"

#: includes/controls/groups/typography.php:198
msgid "Line Height"
msgstr "Altura da linha"

#: includes/controls/groups/text-shadow.php:61
#: includes/controls/groups/text-shadow.php:85
msgid "Text Shadow"
msgstr "Sombra do texto"

#: includes/controls/groups/image-size.php:380
#: modules/atomic-widgets/image/image-sizes.php:38
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:81
msgid "Full"
msgstr "Completo"

#: includes/controls/groups/image-size.php:301
msgid "Image Dimension"
msgstr "Dimensão da imagem"

#: includes/controls/groups/grid-container.php:131
msgid "Justify Items"
msgstr "Justificar itens"

#: includes/controls/groups/flex-item.php:173
msgid "Flex Shrink"
msgstr "Encolhimento flexível"

#: includes/controls/groups/flex-item.php:159
msgid "Flex Grow"
msgstr "Crescimento flexível"

#: includes/controls/groups/flex-item.php:138
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:169
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:173
msgid "Shrink"
msgstr "Encolher"

#: includes/controls/groups/flex-item.php:134
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:168
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:172
msgid "Grow"
msgstr "Crescer"

#: includes/controls/groups/flex-item.php:113
msgid "Custom Order"
msgstr "Ordem personalizada"

#: includes/controls/groups/flex-item.php:76
#: includes/controls/groups/flex-item.php:109
msgid "This control will affect contained elements only."
msgstr "Este controle afetará apenas os elementos contidos."

#: includes/controls/groups/flex-item.php:51
msgid "Align Self"
msgstr "Alinhar-se"

#: includes/controls/groups/flex-item.php:20
msgid "Flex Basis"
msgstr "Base flexível"

#: includes/controls/groups/flex-container.php:204
#: includes/controls/groups/grid-container.php:226
msgid "Align Content"
msgstr "Alinhar conteúdo"

#: includes/controls/groups/flex-container.php:195
msgid "Items within the container can stay in a single line (No wrap), or break into multiple lines (Wrap)."
msgstr "Os itens dentro do contêiner podem permanecer em uma única linha (sem envolver) ou quebrar em várias linhas (envolver)."

#: includes/controls/groups/flex-container.php:128
#: includes/controls/groups/grid-container.php:159
msgid "Align Items"
msgstr "Alinhar itens"

#: includes/controls/groups/flex-container.php:91
#: includes/controls/groups/grid-container.php:186
msgid "Justify Content"
msgstr "Justificar conteúdo"

#: includes/controls/groups/flex-container.php:45
msgid "Column - reversed"
msgstr "Coluna - invertida"

#: includes/controls/groups/flex-container.php:41
msgid "Row - reversed"
msgstr "Linha - invertida"

#: includes/controls/groups/flex-container.php:37
msgid "Column - vertical"
msgstr "Coluna - vertical"

#: includes/controls/groups/flex-container.php:33
msgid "Row - horizontal"
msgstr "Linha - horizontal"

#: includes/controls/groups/css-filter.php:162
msgid "CSS Filters"
msgstr "Filtros CSS"

#: includes/controls/groups/box-shadow.php:61
#: includes/controls/groups/box-shadow.php:96
msgid "Box Shadow"
msgstr "Sombra da caixa"

#: includes/controls/groups/border.php:69
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:226
msgid "Groove"
msgstr "Sulco"

#: includes/controls/groups/border.php:60
msgid "Border Type"
msgstr "Tipo de borda"

#: includes/controls/groups/background.php:607
msgid "Background Fallback"
msgstr "Plano de fundo alternativo"

#: includes/controls/groups/background.php:478
msgid "Display Size"
msgstr "Tamanho de exibição"

#: includes/controls/groups/background.php:301
msgid "Background Image"
msgstr "Imagem de fundo"

#: includes/controls/groups/background.php:245
msgid "Angle"
msgstr "Ângulo"

#: includes/controls/groups/background.php:234
msgid "Radial"
msgstr "Radial"

#: includes/controls/groups/background.php:233
msgid "Linear"
msgstr "Linear"

#: includes/controls/groups/background.php:201
msgid "Second Color"
msgstr "Segunda cor"

#: includes/controls/groups/background.php:154
msgid "Background Type"
msgstr "Tipo de plano de fundo"

#: includes/controls/groups/background.php:95
msgid "Classic"
msgstr "Clássico"

#: app/modules/onboarding/module.php:140
msgid "You do not have permission to perform this action."
msgstr "Você não tem permissões para executar esta ação."

#: modules/nested-tabs/widgets/nested-tabs.php:1170
#: modules/nested-tabs/widgets/nested-tabs.php:1235
msgid "Tabs. Open items with Enter or Space, close with Escape and navigate using the Arrow keys."
msgstr "Abas. Abra itens com \"Enter\" ou \"Espaço\", feche com \"Escape\" e navegue usando as teclas de \"Seta\"."

#. translators: %s: Post type (e.g. Page, Post, etc.)
#: core/document-types/page-base.php:186
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:17
msgid "View %s"
msgstr "Ver %s"

#: modules/site-navigation/module.php:74
msgid "Pages Panel"
msgstr "Painel de páginas"

#: modules/nested-tabs/widgets/nested-tabs.php:199
#: modules/nested-tabs/widgets/nested-tabs.php:869
msgid "Below"
msgstr "Abaixo"

#: modules/nested-tabs/widgets/nested-tabs.php:195
#: modules/nested-tabs/widgets/nested-tabs.php:861
msgid "Above"
msgstr "Acima"

#: includes/editor-templates/templates.php:334
#: includes/editor-templates/templates.php:367
#: includes/editor-templates/templates.php:424 assets/js/editor.js:10847
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:13
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:70
#: assets/js/packages/editor-global-classes/editor-global-classes.js:6
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:4
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:19
msgid "Rename"
msgstr "Renomear"

#: modules/nested-accordion/widgets/nested-accordion.php:401
msgid "Space between Items"
msgstr "Espaço entre os itens"

#: modules/nested-accordion/widgets/nested-accordion.php:357
msgid "Multiple"
msgstr "Múltiplo"

#: modules/nested-accordion/widgets/nested-accordion.php:356
msgid "One"
msgstr "Um"

#: modules/nested-accordion/widgets/nested-accordion.php:353
msgid "Max Items Expanded"
msgstr "Máximo de itens expandidos"

#: modules/nested-accordion/widgets/nested-accordion.php:343
msgid "All collapsed"
msgstr "Todos recolhidos"

#: modules/nested-accordion/widgets/nested-accordion.php:342
msgid "First expanded"
msgstr "Primeiro expandido"

#: modules/nested-accordion/widgets/nested-accordion.php:339
msgid "Default State"
msgstr "Estado padrão"

#: modules/nested-accordion/widgets/nested-accordion.php:332
msgid "Interactions"
msgstr "Interações"

#: modules/nested-accordion/widgets/nested-accordion.php:252
msgid "Collapse"
msgstr "Recolher"

#: modules/nested-accordion/widgets/nested-accordion.php:238
#: assets/js/ai-admin.js:9402 assets/js/ai-gutenberg.js:11250
#: assets/js/ai-layout.js:4883 assets/js/ai-media-library.js:11031
#: assets/js/ai-unify-product-images.js:11031 assets/js/ai.js:12496
msgid "Expand"
msgstr "Expandir"

#: modules/nested-accordion/widgets/nested-accordion.php:168
msgid "Item Position"
msgstr "Posição do item"

#: modules/apps/admin-pointer.php:30
msgid "Discover our collection of plugins and add-ons carefully selected to enhance your Elementor website and unleash your creativity."
msgstr "Descubra nossa coleção de plugins e complementos cuidadosamente selecionados para aprimorar seu site Elementor e liberar sua criatividade."

#: modules/apps/admin-apps-page.php:27
msgid "Learn more about this page."
msgstr "Saiba mais sobre esta página."

#: modules/apps/admin-apps-page.php:26
msgid "Boost your web-creation process with add-ons, plugins, and more tools specially selected to unleash your creativity, increase productivity, and enhance your Elementor-powered website."
msgstr "Potencialize seu processo de criação na web com complementos, plugins e mais ferramentas especialmente selecionadas para liberar sua criatividade, aumentar a produtividade e melhorar seu site desenvolvido com o Elementor."

#: modules/apps/admin-apps-page.php:25
msgid "Popular Add-ons, New Possibilities."
msgstr "Complementos populares, novas possibilidades."

#: includes/widgets/video.php:929
msgid "Note: These controls have been deprecated and are only visible if they were previously in use. The video’s width and position are now set based on its aspect ratio."
msgstr "Observação: Esses controles foram descontinuados e só estão visíveis se estavam em uso anteriormente. A largura e a posição do vídeo agora são definidas com base em sua proporção."

#: includes/widgets/video.php:234
msgid "Choose Video File"
msgstr "Escolher arquivo de vídeo"

#. translators: 1: Slide count, 2: Total slides count.
#: includes/widgets/image-carousel.php:969
msgid "%1$s of %2$s"
msgstr "%1$s de %2$s"

#: includes/widgets/icon-box.php:229 includes/widgets/image-box.php:204
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1453
msgid "Box"
msgstr "Caixa"

#. translators: 1: Link open tag, 2: Link open tag, 3: Link close tag.
#: core/kits/documents/tabs/settings-site-identity.php:60
msgid "Changes will be reflected only after %1$s saving %3$s and %2$s reloading %3$s preview."
msgstr "As alterações serão refletidas apenas após %1$s salvar %3$s e %2$s recarregar %3$s a pré-visualização."

#: core/admin/admin.php:628
msgid "Build Smart with AI"
msgstr "Construa de forma inteligente com IA"

#: includes/controls/groups/background.php:99 assets/js/ai-admin.js:11334
#: assets/js/ai-gutenberg.js:13182 assets/js/ai-media-library.js:12963
#: assets/js/ai-unify-product-images.js:12963 assets/js/ai.js:14428
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:85
msgid "Gradient"
msgstr "Gradiente"

#: modules/nested-tabs/widgets/nested-tabs.php:379
msgid "Note: Scroll tabs if they don’t fit into their parent container."
msgstr "Observação: Role as abas se elas não couberem no contêiner principal."

#: modules/nested-tabs/widgets/nested-tabs.php:377
msgid "Horizontal Scroll"
msgstr "Rolagem horizontal"

#: modules/floating-buttons/base/widget-floating-bars-base.php:376
#: modules/nested-accordion/widgets/nested-accordion.php:157
msgid "Item #3"
msgstr "Item Nº 3"

#: modules/floating-buttons/base/widget-floating-bars-base.php:373
#: modules/nested-accordion/widgets/nested-accordion.php:154
msgid "Item #2"
msgstr "Item Nº 2"

#: modules/floating-buttons/base/widget-floating-bars-base.php:370
#: modules/nested-accordion/widgets/nested-accordion.php:151
msgid "Item #1"
msgstr "Item Nº 1"

#: modules/floating-buttons/base/widget-floating-bars-base.php:340
#: modules/nested-accordion/widgets/nested-accordion.php:117
#: modules/nested-accordion/widgets/nested-accordion.php:118
msgid "Item Title"
msgstr "Título do item"

#: modules/nested-accordion/widgets/nested-accordion.php:66
msgid "item #%s"
msgstr "item Nº%s"

#: includes/widgets/toggle.php:157
msgid "You are currently editing a Toggle widget in its old version. Drag a new Accordion widget onto your page to use a newer version, providing nested capabilities."
msgstr "No momento, você está editando um widget de \"Alternância\" na sua versão antiga. Arraste um novo widget \"Sanfona\" para sua página para usar uma versão mais recente, que oferece recursos aninhados."

#: includes/widgets/image.php:398
msgid "Object Position"
msgstr "Posição do objeto"

#: includes/widgets/icon.php:326
msgid "Fit to Size"
msgstr "Ajustar ao tamanho"

#: includes/widgets/accordion.php:154
msgid "You are currently editing an Accordion Widget in its old version. Any new Accordion widget dragged into the canvas will be the new Accordion widget, with the improved Nested capabilities."
msgstr "No momento, você está editando um widget de \"Sanfona\" em sua versão antiga. Qualquer novo widget de \"Sanfona\" arrastado para a tela será o novo widget de \"Sanfona\", com recursos aninhados melhorados."

#: includes/frontend.php:1422
msgid "Go to slide"
msgstr "Ir para o slide"

#: includes/frontend.php:1421
msgid "This is the last slide"
msgstr "Este é o último slide"

#: includes/frontend.php:1420
msgid "This is the first slide"
msgstr "Este é o primeiro slide"

#: includes/frontend.php:1419
msgid "Next slide"
msgstr "Próximo slide"

#: includes/frontend.php:1418
msgid "Previous slide"
msgstr "Slide anterior"

#: includes/editor-templates/navigator.php:88
msgid "Show/hide Element"
msgstr "Mostrar/ocultar elemento"

#: includes/editor-templates/navigator.php:75
msgid "Show/hide inner elements"
msgstr "Mostrar/ocultar elementos internos"

#: includes/editor-templates/navigator.php:64
msgid "Resize navigator"
msgstr "Redimensionar navegador"

#: includes/editor-templates/navigator.php:63
msgid "Resize structure"
msgstr "Redimensionar estrutura"

#: includes/editor-templates/hotkeys.php:94
msgid "Panels"
msgstr "Painéis"

#: includes/controls/gallery.php:84 assets/js/editor.js:16773
msgid "Clear gallery"
msgstr "Limpar galeria"

#: core/document-types/page-base.php:257
msgid "Allow Comments"
msgstr "Permitir comentários"

#: core/document-types/page-base.php:245
#: includes/controls/groups/flex-item.php:80
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:178
msgid "Order"
msgstr "Ordem"

#: includes/template-library/sources/local.php:515
msgid "Invalid template type."
msgstr "Tipo de modelo inválido."

#: core/admin/admin.php:347
msgid "Get Elementor Pro"
msgstr "Obter o Elementor Pro"

#: modules/ai/connect/ai.php:27 assets/js/ai-admin.js:656
#: assets/js/ai-admin.js:7783 assets/js/ai-gutenberg.js:2424
#: assets/js/ai-gutenberg.js:9631 assets/js/ai-layout.js:488
#: assets/js/ai-layout.js:3264 assets/js/ai-media-library.js:2285
#: assets/js/ai-media-library.js:9412 assets/js/ai-unify-product-images.js:2285
#: assets/js/ai-unify-product-images.js:9412 assets/js/ai.js:3070
#: assets/js/ai.js:10877
msgid "AI"
msgstr "IA"

#. translators: 1: Link open tag, 2: Link close tag.
#: includes/widgets/image-carousel.php:370
#: includes/widgets/image-gallery.php:204 includes/widgets/image.php:229
msgid "Manage your site’s lightbox settings in the %1$sLightbox panel%2$s."
msgstr "Gerencie as configurações do lightbox do seu site no painel %1$sLightbox%2$s."

#: includes/elements/container.php:360
msgid "Container Layout"
msgstr "Layout do contêiner"

#: includes/editor-templates/panel-elements.php:15
msgid "Globals"
msgstr "Globais"

#: includes/editor-templates/navigator.php:39
msgid "Close navigator"
msgstr "Fechar o navegador"

#: includes/editor-templates/navigator.php:39
msgid "Close structure"
msgstr "Fechar a estrutura"

#: includes/editor-templates/navigator.php:35 assets/js/editor.js:37629
msgid "Expand all elements"
msgstr "Expandir todos os elementos"

#: includes/editor-templates/global.php:68 includes/elements/container.php:96
#: includes/elements/container.php:104 includes/elements/container.php:366
#: assets/js/editor.js:35729 assets/js/editor.js:44570
msgid "Grid"
msgstr "Grade"

#: includes/editor-templates/global.php:60 includes/elements/container.php:365
#: modules/atomic-widgets/elements/flexbox/flexbox.php:23
#: modules/library/documents/flexbox.php:52 assets/js/editor.js:10578
msgid "Flexbox"
msgstr "Flexbox"

#: includes/editor-templates/global.php:52
msgid "Which layout would you like to use?"
msgstr "Qual layout você gostaria de usar?"

#: includes/editor-templates/global.php:9
msgid "Select your structure"
msgstr "Selecione sua estrutura"

#: includes/controls/groups/grid-container.php:115
msgid "Auto Flow"
msgstr "Fluxo automático"

#: core/kits/documents/tabs/settings-layout.php:100
#: includes/controls/groups/flex-container.php:156
#: includes/controls/groups/grid-container.php:96
#: includes/elements/container.php:497
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:167
msgid "Gaps"
msgstr "Espaçamentos"

#: includes/controls/groups/grid-container.php:71
msgid "Rows"
msgstr "Linhas"

#: includes/controls/groups/grid-container.php:31
msgid "Grid Outline"
msgstr "Contorno da grade"

#: includes/controls/gaps.php:58
#: includes/controls/groups/grid-container.php:118
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:35
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:180
msgid "Row"
msgstr "Linha"

#: includes/controls/base-units.php:130
msgid "Switch units"
msgstr "Alterar unidades"

#: modules/floating-buttons/base/widget-contact-button-base.php:116
msgid "Top Bar"
msgstr "Barra superior"

#. translators: %s: Document title.
#: core/editor/loader/v1/templates/editor-body-v1-view.php:27
#: core/editor/loader/v2/templates/editor-body-v2-view.php:27
#: includes/editor-templates/editor-wrapper.php:30
#: assets/js/packages/editor-documents/editor-documents.js:2
#: assets/js/packages/editor-documents/editor-documents.strings.js:2
msgid "Edit \"%s\" with Elementor"
msgstr "Editar \"%s\" com o Elementor"

#. translators: 1: Link open tag, 2: Link close tag.
#: core/document-types/page-base.php:100
msgid "Set a different selector for the title in the %1$sLayout panel%2$s."
msgstr "Defina um seletor diferente para o título no painel %1$sLayout%2$s."

#: includes/widgets/icon-list.php:599
msgid "Adjust Vertical Position"
msgstr "Ajustar a posição vertical"

#: includes/elements/column.php:211 includes/widgets/icon-list.php:542
msgid "Horizontal Alignment"
msgstr "Alinhamento horizontal"

#: includes/widgets/alert.php:506 includes/widgets/alert.php:561
msgid "Dismiss this alert."
msgstr "Dispensar este alerta."

#: modules/editor-app-bar/module.php:46
msgid "Get a sneak peek of the new Editor powered by React. The beautiful design and experimental layout of the Top bar are just some of the exciting tools on their way."
msgstr "Dê uma espiada no novo editor desenvolvido com React. O lindo design e o layout experimental da barra superior são apenas algumas das ferramentas incríveis que estão a caminho."

#: modules/editor-app-bar/module.php:43
msgid "Editor Top Bar"
msgstr "Barra superior do editor"

#: core/breakpoints/manager.php:329
msgid "Tablet Landscape"
msgstr "Tablet no modo paisagem"

#: core/breakpoints/manager.php:324
msgid "Tablet Portrait"
msgstr "Tablet no modo retrato"

#: core/admin/admin-notices.php:624 core/admin/admin.php:1036
msgid "Dismiss this notice."
msgstr "Dispensar esta notificação."

#: modules/generator-tag/module.php:84
msgid "A generator tag is a meta element that indicates the attributes used to create a webpage. It is used for analytical purposes."
msgstr "Uma tag de gerador é um metaelemento que indica os atributos usados para criar uma página da web. É usada para fins analíticos."

#: modules/generator-tag/module.php:76
msgid "Generator Tag"
msgstr "Tag do gerador"

#. translators: %d: Number of rows.
#: includes/utils.php:254
msgid "%d database row affected."
msgid_plural "%d database rows affected."
msgstr[0] "%d linha do banco de dados foi afetada."
msgstr[1] "%d linhas do banco de dados foram afetadas."

#: core/kits/documents/tabs/theme-style-form-fields.php:188
msgid "Accent Color"
msgstr "Cor de realce"

#: core/experiments/manager.php:546
msgid "Deactivate All"
msgstr "Desativar tudo"

#: core/experiments/manager.php:545
msgid "Activate All"
msgstr "Ativar tudo"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: core/experiments/manager.php:528
msgid "Personalize your Elementor experience by controlling which features and experiments are active on your site. Help make Elementor better by %1$ssharing your experience and feedback with us%2$s."
msgstr "Personalize sua experiência com o Elementor, controlando quais recursos e experimentos estão ativos em seu site. Ajude a tornar o Elementor melhor %1$scompartilhando sua experiência e feedback conosco%2$s."

#: core/experiments/manager.php:522
msgid "Experiments and Features"
msgstr "Experimentos e recursos"

#: modules/atomic-opt-in/opt-in-page.php:88
#: modules/atomic-widgets/opt-in.php:40 assets/js/editor-v4-opt-in.js:346
#: assets/js/editor-v4-opt-in.js:505
msgid "Editor V4"
msgstr "Editor V4"

#: modules/nested-tabs/widgets/nested-tabs.php:713
msgid "Titles"
msgstr "Títulos"

#: includes/widgets/video.php:853
msgid "Shadow"
msgstr "Sombra"

#: modules/nested-accordion/widgets/nested-accordion.php:427
#: modules/nested-tabs/widgets/nested-tabs.php:469
msgid "Distance from content"
msgstr "Distância do conteúdo"

#: modules/nested-tabs/widgets/nested-tabs.php:449
msgid "Gap between tabs"
msgstr "Espaço entre as abas"

#: modules/nested-tabs/widgets/nested-tabs.php:434
msgid "Note: Choose at which breakpoint tabs will automatically switch to a vertical (“accordion”) layout."
msgstr "Observação: Escolha em qual ponto de interrupção as abas mudarão automaticamente para um layout vertical (\"sanfona\")."

#. translators: 1: Breakpoint label, 2: `>` character, 3: Breakpoint value.
#: modules/nested-tabs/widgets/nested-tabs.php:422
msgid "%1$s (%2$s %3$dpx)"
msgstr "%1$s (%2$s %3$dpx)"

#: modules/nested-tabs/widgets/nested-tabs.php:342
msgid "Align Title"
msgstr "Alinhar título"

#: modules/nested-tabs/widgets/nested-tabs.php:178
msgid "Tab #3"
msgstr "Aba Nº 3"

#: modules/nested-tabs/widgets/nested-tabs.php:80
msgid "Tab #%d"
msgstr "Aba Nº %d"

#: modules/nested-tabs/widgets/nested-tabs.php:61
msgid "Tab #%s"
msgstr "Aba Nº %s"

#: modules/nested-elements/module.php:20
msgid "Create a rich user experience by layering widgets together inside \"Nested\" Tabs, etc. When turned on, we’ll automatically enable new nested features. Your old widgets won’t be affected."
msgstr "Crie uma experiência de usuário enriquecedora colocando widgets em camadas dentro de abas \"aninhadas\", etc. Quando ativado, ativaremos automaticamente os novos recursos aninhados. Os seus widgets antigos não serão afetados."

#: modules/nested-elements/module.php:17
msgid "Nested Elements"
msgstr "Elementos aninhados"

#: includes/widgets/video.php:583
msgid "Preload attribute lets you specify how the video should be loaded when the page loads."
msgstr "O atributo de pré-carregamento permite especificar como o vídeo deve ser carregado quando a página for carregada."

#: includes/widgets/video.php:577
msgid "Metadata"
msgstr "Metadados"

#: includes/widgets/video.php:574
msgid "Preload"
msgstr "Pré-carregamento"

#: includes/widgets/tabs.php:153
msgid "You are currently editing a Tabs Widget in its old version. Any new tabs widget dragged into the canvas will be the new Tab widget, with the improved Nested capabilities."
msgstr "No momento, você está editando um widget de \"Abas\" em sua versão antiga. Qualquer novo widget de abas arrastado para a tela será o novo widget de abas, com recursos aninhados melhorados."

#: includes/settings/settings.php:374
msgid "Disable this option if you want to prevent Google Fonts from being loaded. This setting is recommended when loading fonts from a different source (plugin, theme or %1$scustom fonts%2$s)."
msgstr "Desative esta opção se você quiser evitar que as fontes do Google sejam carregadas. Esta configuração é recomendada ao carregar fontes de uma origem diferente (plugin, tema ou %1$sfontes personalizadas%2$s)."

#: includes/settings/settings.php:365
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:245
msgid "Google Fonts"
msgstr "Fontes do Google"

#: includes/settings/settings.php:458
msgid "Lazy Load Background Images"
msgstr "Imagens de fundo com carregamento lento"

#: includes/elements/container.php:1933
msgid "Note: Avoid applying transform properties on sticky containers. Doing so might cause unexpected results."
msgstr "Observação: Evite aplicar propriedades de transformação em contêineres fixos. Isso pode resultar em comportamentos inesperados."

#: core/admin/admin.php:1024 modules/apps/admin-apps-page.php:116
#: modules/apps/admin-apps-page.php:147
#: modules/home/<USER>/filter-plugins.php:82 assets/js/admin.js:795
#: assets/js/editor-v4-opt-in.js:357
msgid "Activate"
msgstr "Ativar"

#: includes/elements/container.php:566
msgid "(link)"
msgstr "(link)"

#: includes/controls/groups/typography.php:150
msgctxt "Typography Control"
msgid "(Black)"
msgstr "(Preto)"

#: includes/controls/groups/typography.php:149
msgctxt "Typography Control"
msgid "(Extra Bold)"
msgstr "(Extra negrito)"

#: includes/controls/groups/typography.php:148
msgctxt "Typography Control"
msgid "(Bold)"
msgstr "(Negrito)"

#: includes/controls/groups/typography.php:147
msgctxt "Typography Control"
msgid "(Semi Bold)"
msgstr "(Semi negrito)"

#: includes/controls/groups/typography.php:146
msgctxt "Typography Control"
msgid "(Medium)"
msgstr "(Médio)"

#: includes/controls/groups/typography.php:145
msgctxt "Typography Control"
msgid "(Normal)"
msgstr "(Normal)"

#: includes/controls/groups/typography.php:144
msgctxt "Typography Control"
msgid "(Light)"
msgstr "(Leve)"

#: includes/controls/groups/typography.php:143
msgctxt "Typography Control"
msgid "(Extra Light)"
msgstr "(Extra leve)"

#: includes/controls/groups/typography.php:142
msgctxt "Typography Control"
msgid "(Thin)"
msgstr "(Fino)"

#: core/experiments/manager.php:618
msgid "Requires"
msgstr "Requer"

#: app/modules/import-export/module.php:235
msgid "Remove Kit"
msgstr "Remover kit"

#: app/modules/import-export/module.php:228
msgid "Remove the most recent Kit"
msgstr "Remover o kit mais recente"

#: app/modules/import-export/module.php:190
msgid "Remove all the content and site settings that came with \"%1$s\" on %2$s.%3$s Your original site settings will be restored."
msgstr "Remova todo o conteúdo e as configurações do site que vieram com \"%1$s\" em %2$s.%3$s Suas configurações originais do site serão restauradas."

#: app/modules/import-export/module.php:182
#: app/modules/import-export/module.php:185
#: app/modules/import-export/module.php:191
msgid "imported kit"
msgstr "kit importado"

#: app/modules/import-export/module.php:181
msgid "Remove all the content and site settings that came with \"%1$s\" on %2$s %3$s and revert to the site setting that came with \"%4$s\" on %5$s."
msgstr "Remova todo o conteúdo e as configurações do site que vieram com \"%1$s\" em %2$s %3$s e reverta para as configurações do site que foram fornecidas com \"%4$s\" em %5$s."

#. Translators: %s is the current item index.
#: modules/nested-accordion/widgets/nested-accordion.php:85
#: modules/nested-elements/base/widget-nested-base.php:45
#: assets/js/editor.js:27362
msgid "Item #%d"
msgstr "Item Nº %d"

#: includes/widgets/image-carousel.php:282
msgid "Next Arrow Icon"
msgstr "Ícone de seta próximo"

#: includes/widgets/image-carousel.php:227
msgid "Previous Arrow Icon"
msgstr "Ícone de seta anterior"

#: includes/widgets/alert.php:394
#: modules/floating-buttons/base/widget-contact-button-base.php:2934
msgid "Horizontal Position"
msgstr "Posição horizontal"

#: includes/widgets/alert.php:376
#: modules/floating-buttons/base/widget-contact-button-base.php:2988
#: modules/floating-buttons/base/widget-floating-bars-base.php:1433
msgid "Vertical Position"
msgstr "Posição vertical"

#: includes/widgets/alert.php:161 includes/widgets/alert.php:342
msgid "Dismiss Icon"
msgstr "Ícone de dispensar"

#: includes/elements/container.php:587
msgid "Don’t add links to elements nested in this container - it will break the layout."
msgstr "Não adicione links aos elementos aninhados neste contêiner, pois isso quebrará o layout."

#: includes/editor-templates/panel.php:60
msgid "Any time you can change the settings in %1$sUser Preferences%2$s"
msgstr "A qualquer momento, você pode alterar as configurações em %1$sPreferências do usuário%2$s"

#: includes/editor-templates/panel.php:56
msgid "Now you can choose where you want to go on the site from the following options"
msgstr "Agora você pode escolher para onde deseja ir no site a partir das seguintes opções"

#: core/settings/editor-preferences/model.php:231
msgid "WP Dashboard"
msgstr "Painel do WP"

#: core/settings/editor-preferences/model.php:230
msgid "All Posts"
msgstr "Todos os posts"

#: core/settings/editor-preferences/model.php:229
msgid "This Post"
msgstr "Este post"

#: core/settings/editor-preferences/model.php:225
msgid "Exit to"
msgstr "Sair para"

#: core/kits/documents/tabs/settings-layout.php:91
msgid "Sets the default space inside the container (Default is 10px)"
msgstr "Define o espaço padrão dentro do contêiner (o padrão é 10px)"

#: core/kits/documents/tabs/settings-layout.php:88
msgid "Container Padding"
msgstr "Preenchimento do contêiner"

#: core/common/modules/finder/categories/settings.php:64
#: core/experiments/manager.php:313 core/experiments/manager.php:370
#: core/experiments/manager.php:380 includes/settings/settings.php:400
#: includes/settings/settings.php:403 modules/element-cache/module.php:47
msgid "Performance"
msgstr "Desempenho"

#: app/modules/onboarding/module.php:158
msgid "There was a problem setting your site name."
msgstr "Houve um problema ao definir o nome do seu site."

#: core/admin/admin-notices.php:364
msgid "Try it out"
msgstr "Experimente"

#: core/admin/admin-notices.php:361
msgid "With our experimental speed boosting features you can go faster than ever before. Look for the Performance label on our Experiments page and activate those experiments to improve your site loading speed."
msgstr "Com nossos recursos experimentais de aumento de velocidade, você pode ir mais rápido do que nunca. Procure pelo rótulo \"Desempenho\" em nossa página \"Experimentos\" e ative esses experimentos para melhorar a velocidade de carregamento do seu site."

#: core/admin/admin-notices.php:360
msgid "Improve your site’s performance score."
msgstr "Melhore a pontuação de desempenho do seu site."

#: core/editor/notice-bar.php:45 assets/js/element-manager-admin.js:2352
msgid "Unleash the full power of Elementor's features and web creation tools."
msgstr "Libere todo o poder dos recursos e ferramentas de criação de sites do Elementor."

#: includes/editor-templates/hotkeys.php:191 assets/js/notes.js:136
#: assets/js/notes.js:140 assets/js/notes.js:226
msgid "Notes"
msgstr "Anotações"

#: core/editor/notice-bar.php:41 core/editor/promotion.php:34
#: includes/editor-templates/navigator.php:20
#: includes/editor-templates/panel-elements.php:29
#: includes/editor-templates/panel-elements.php:34
#: includes/editor-templates/panel-elements.php:101
#: includes/managers/controls.php:1161 includes/widgets/image-gallery.php:110
#: modules/admin-top-bar/module.php:79
#: modules/checklist/steps/setup-header.php:93
#: modules/element-manager/ajax.php:73 modules/element-manager/ajax.php:80
#: modules/element-manager/ajax.php:88
#: modules/promotions/admin-menu-items/base-promotion-item.php:32
#: modules/promotions/admin-menu-items/base-promotion-template.php:37
#: modules/promotions/admin-menu-items/popups-promotion-item.php:24
#: modules/promotions/promotion-data.php:48
#: modules/promotions/promotion-data.php:65
#: modules/promotions/promotion-data.php:82
#: modules/promotions/promotion-data.php:99
#: modules/promotions/promotion-data.php:116 assets/js/app-packages.js:2413
#: assets/js/app-packages.js:5590 assets/js/app-packages.js:5856
#: assets/js/app.js:2727 assets/js/checklist.js:298 assets/js/editor.js:8274
#: assets/js/editor.js:12969 assets/js/editor.js:55394
#: assets/js/editor.js:55409 assets/js/editor.js:55424
#: assets/js/editor.js:55439
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:516
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:1464
msgid "Upgrade Now"
msgstr "Atualizar agora"

#: core/admin/admin.php:632 core/role-manager/role-manager.php:241
#: includes/editor-templates/panel-elements.php:40
#: includes/editor-templates/panel-elements.php:46
#: includes/editor-templates/panel-elements.php:65
#: includes/editor-templates/panel.php:328
#: includes/editor-templates/templates.php:513
#: includes/managers/controls.php:1152 includes/widgets/image-gallery.php:107
#: modules/promotions/admin-menu-items/go-pro-promotion-item.php:30
#: modules/promotions/promotion-data.php:41
#: modules/promotions/promotion-data.php:58
#: modules/promotions/promotion-data.php:75
#: modules/promotions/promotion-data.php:92
#: modules/promotions/promotion-data.php:109 assets/js/ai-admin.js:1024
#: assets/js/ai-admin.js:2966 assets/js/ai-admin.js:3095
#: assets/js/ai-gutenberg.js:2792 assets/js/ai-gutenberg.js:4734
#: assets/js/ai-gutenberg.js:4863 assets/js/ai-layout.js:722
#: assets/js/ai-layout.js:1002 assets/js/ai-media-library.js:2653
#: assets/js/ai-media-library.js:4595 assets/js/ai-media-library.js:4724
#: assets/js/ai-unify-product-images.js:2653
#: assets/js/ai-unify-product-images.js:4595
#: assets/js/ai-unify-product-images.js:4724 assets/js/ai.js:3438
#: assets/js/ai.js:5380 assets/js/ai.js:5509 assets/js/app-packages.js:5821
#: assets/js/e-react-promotions.js:196 assets/js/editor.js:6791
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:1320
#: assets/js/notes.js:149
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:770
#: assets/js/styleguide.js:196
msgid "Upgrade"
msgstr "Atualizar"

#: modules/announcements/module.php:118
msgid "Let's do it"
msgstr "Vamos lá"

#: modules/container-converter/module.php:88
#: modules/container-converter/module.php:121
msgid "Copies all of the selected sections and columns and pastes them in a container beneath the original."
msgstr "Copie todas as seções e colunas selecionadas e as cole em um contêiner abaixo do original."

#: modules/container-converter/module.php:86
#: modules/container-converter/module.php:119
msgid "Convert"
msgstr "Converter"

#: modules/container-converter/module.php:85
#: modules/container-converter/module.php:118
msgid "Convert to container"
msgstr "Converter em contêiner"

#: includes/widgets/video.php:989
msgid "Play Video about"
msgstr "Reproduzir vídeo sobre"

#: includes/widgets/image-carousel.php:624
msgid "Pagination"
msgstr "Paginação"

#: includes/elements/container.php:481
msgid "To achieve full height Container use %s."
msgstr "Para obter um contêiner com altura total, use %s."

#: core/experiments/manager.php:342 includes/elements/container.php:72
#: includes/elements/container.php:344
#: modules/library/documents/container.php:52 assets/js/editor.js:10576
#: assets/js/editor.js:35729 assets/js/editor.js:41372
msgid "Container"
msgstr "Contêiner"

#: includes/elements/column.php:461 includes/elements/section.php:730
#: includes/widgets/heading.php:323
msgid "Hue"
msgstr "Matiz"

#: includes/elements/column.php:460 includes/elements/section.php:729
#: includes/widgets/heading.php:322
msgid "Exclusion"
msgstr "Exclusão"

#: includes/elements/column.php:459 includes/elements/section.php:728
#: includes/widgets/heading.php:321
msgid "Difference"
msgstr "Diferença"

#: includes/editor-templates/global.php:33 assets/js/editor.js:35588
msgid "Add New Container"
msgstr "Adicionar novo contêiner"

#: includes/controls/groups/typography.php:153
msgid "Bold"
msgstr "Negrito"

#: includes/controls/groups/background.php:727
#: includes/widgets/image-carousel.php:423
msgid "Lazyload"
msgstr "Carregamento lento"

#: core/kits/views/panel.php:36
msgid "Reorder"
msgstr "Reordenar"

#: core/kits/documents/tabs/global-typography.php:160
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:227
msgid "System Fonts"
msgstr "Fontes do sistema"

#: core/kits/documents/tabs/global-colors.php:101
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:178
msgid "System Colors"
msgstr "Cores do sistema"

#: core/common/modules/finder/categories/tools.php:68
msgid "Import Export"
msgstr "Importar/exportar"

#: core/common/modules/event-tracker/personal-data.php:24
msgid "Elementor Event Tracker"
msgstr "Rastreador de eventos do Elementor"

#: app/modules/onboarding/module.php:265 app/modules/onboarding/module.php:350
msgid "There was a problem uploading your file."
msgstr "Houve um problema ao enviar o seu arquivo."

#: app/modules/onboarding/module.php:215
msgid "There was a problem setting your site logo."
msgstr "Houve um problema ao definir o logo do seu site."

#: elementor.php:78 elementor.php:102 assets/js/ai-admin.js:1063
#: assets/js/ai-gutenberg.js:2831 assets/js/ai-layout.js:761
#: assets/js/ai-media-library.js:2692 assets/js/ai-unify-product-images.js:2692
#: assets/js/ai.js:3477
msgid "Show me how"
msgstr "Me mostre como"

#: modules/library/documents/page.php:65
msgid "Add New Page Template"
msgstr "Adicionar novo modelo de página"

#: core/editor/promotion.php:31 assets/js/e-react-promotions.js:196
#: assets/js/editor.js:6791 assets/js/notes.js:149 assets/js/styleguide.js:196
msgid "Connect & Activate"
msgstr "Conectar e ativar"

#: modules/usage/usage-reporter.php:22
msgid "Elements Usage"
msgstr "Uso dos elementos"

#. translators: 1: Integration settings link open tag, 2: Create API key link
#. open tag, 3: Link close tag.
#: includes/widgets/google-maps.php:140
msgid "Set your Google Maps API Key in Elementor's %1$sIntegrations Settings%3$s page. Create your key %2$shere.%3$s"
msgstr "Defina sua chave da API do Google Maps na página %1$sConfigurações de integrações%3$s do Elementor. Crie sua chave %2$saqui.%3$s"

#: includes/base/element-base.php:1346
msgid "Y Anchor Point"
msgstr "Ponto de ancoragem Y"

#: includes/base/element-base.php:1318
msgid "X Anchor Point"
msgstr "Ponto de ancoragem X"

#: includes/base/element-base.php:1244 includes/base/element-base.php:1248
msgid "Flip Vertical"
msgstr "Girar verticalmente"

#: includes/base/element-base.php:1225 includes/base/element-base.php:1229
msgid "Flip Horizontal"
msgstr "Girar horizontalmente"

#: includes/base/element-base.php:1201
msgid "Skew Y"
msgstr "Inclinar em Y"

#: includes/base/element-base.php:1179
msgid "Skew X"
msgstr "Inclinar em X"

#: includes/base/element-base.php:1167
msgid "Skew"
msgstr "Inclinar"

#: includes/base/element-base.php:1143
msgid "Scale Y"
msgstr "Escala em Y"

#: includes/base/element-base.php:1121
msgid "Scale X"
msgstr "Escala em X"

#: includes/base/element-base.php:1088
msgid "Keep Proportions"
msgstr "Manter proporções"

#: includes/base/element-base.php:1048
msgid "Offset Y"
msgstr "Deslocar em Y"

#: includes/base/element-base.php:1022
msgid "Offset X"
msgstr "Deslocar em X"

#: includes/base/element-base.php:987
msgid "Perspective"
msgstr "Perspectiva"

#: includes/base/element-base.php:964
msgid "Rotate Y"
msgstr "Rotacionar em Y"

#: includes/base/element-base.php:941
msgid "Rotate X"
msgstr "Rotacionar em X"

#: includes/base/element-base.php:924
msgid "3D Rotate"
msgstr "Rotação 3D"

#: includes/base/element-base.php:861
msgid "Transform"
msgstr "Transformar"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:15
#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:19
msgid "Custom Code"
msgstr "Código personalizado"

#: includes/controls/groups/text-stroke.php:85
msgid "Stroke Color"
msgstr "Cor do traço"

#: includes/controls/groups/text-stroke.php:60
#: includes/controls/groups/text-stroke.php:111
msgid "Text Stroke"
msgstr "Traço do texto"

#: includes/admin-templates/beta-tester.php:53 assets/js/ai-admin.js:6389
#: assets/js/ai-admin.js:15922 assets/js/ai-gutenberg.js:8237
#: assets/js/ai-gutenberg.js:17770 assets/js/ai-layout.js:2491
#: assets/js/ai-layout.js:5208 assets/js/ai-media-library.js:8018
#: assets/js/ai-media-library.js:17551
#: assets/js/ai-unify-product-images.js:8018
#: assets/js/ai-unify-product-images.js:17551 assets/js/ai.js:9380
#: assets/js/ai.js:9483 assets/js/ai.js:19016
msgid "Privacy Policy"
msgstr "Política de privacidade"

#: includes/admin-templates/beta-tester.php:48 assets/js/ai-admin.js:6385
#: assets/js/ai-admin.js:15918 assets/js/ai-gutenberg.js:8233
#: assets/js/ai-gutenberg.js:17766 assets/js/ai-layout.js:2487
#: assets/js/ai-layout.js:5204 assets/js/ai-media-library.js:8014
#: assets/js/ai-media-library.js:17547
#: assets/js/ai-unify-product-images.js:8014
#: assets/js/ai-unify-product-images.js:17547 assets/js/ai.js:9376
#: assets/js/ai.js:9479 assets/js/ai.js:19012
msgid "Terms of Service"
msgstr "Termos de serviço"

#. translators: 1. "Terms of service" link, 2. "Privacy policy" link
#: includes/admin-templates/beta-tester.php:44
msgid "By clicking Sign Up, you agree to Elementor's %1$s and %2$s"
msgstr "Ao clicar em \"Cadastrar\", você concorda com os %1$s e %2$s do Elementor"

#: core/logger/log-reporter.php:25
msgid "Log"
msgstr "Registro"

#: core/experiments/manager.php:551
msgid "Ongoing Experiments"
msgstr "Experimentos em andamento"

#: core/experiments/manager.php:508
msgid "Stable Features"
msgstr "Recursos estáveis"

#: core/experiments/manager.php:316
msgid "The “Inline Font Icons” will render the icons as inline SVG without loading the Font-Awesome and the eicons libraries and its related CSS files and fonts."
msgstr "Os \"Ícones de fontes em linha\" irão renderizar os ícones como SVG em linha sem carregar as bibliotecas do Font-Awesome e Eicons, assim como seus arquivos CSS e fontes relacionadas."

#: core/experiments/manager.php:312
msgid "Inline Font Icons"
msgstr "Ícones de fontes em linha"

#: core/experiments/experiments-reporter.php:21
msgid "Elementor Experiments"
msgstr "Experimentos do Elementor"

#: core/editor/data/globals/endpoints/base.php:46
msgid "Invalid title"
msgstr "Título inválido"

#: includes/settings/tools.php:158
msgid "Not allowed to rollback versions"
msgstr "Não é permitido reverter versões"

#: modules/page-templates/module.php:316
msgid "The default page template as defined in Elementor Panel → Hamburger Menu → Site Settings."
msgstr "O modelo de página padrão, conforme definido no Painel do Elementor → Menu de hambúrguer → Configurações do site."

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/safe-mode/module.php:290
msgid "Please note! We couldn't deactivate all of your plugins on Safe Mode. Please %1$sread more%2$s about this issue"
msgstr "Observação! Não foi possível desativar todos os seus plugins no \"Modo seguro\". %1$sLeia mais%2$s sobre este problema"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/safe-mode/module.php:261 modules/safe-mode/module.php:274
msgid "%1$sClick here%2$s to troubleshoot"
msgstr "%1$sClique aqui%2$s para solucionar problemas"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/floating-buttons/module.php:383
msgid "Or view %1$sTrashed Items%1$s"
msgstr "Ou visualize %1$sItens excluídos%1$s"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/history/views/revisions-panel-template.php:31
msgid "Learn more about %1$sWordPress revisions%2$s"
msgstr "Saiba mais sobre %1$sRevisões do WordPress%2$s"

#. translators: 1: Function argument, 2: Elementor version number.
#: modules/dev-tools/deprecation.php:292
msgid "The %1$s argument is deprecated since version %2$s!"
msgstr "O argumento %1$s está obsoleto desde a versão %2$s!"

#. translators: 1: Function argument, 2: Elementor version number, 3:
#. Replacement argument name.
#: modules/dev-tools/deprecation.php:288
msgid "The %1$s argument is deprecated since version %2$s! Use %3$s instead."
msgstr "O argumento %1$s está obsoleto desde a versão %2$s! Em vez disso, use %3$s."

#. translators: 1: Link open tag, 2: Link close tag.
#: includes/settings/tools.php:403
msgid "%1$sClick here%2$s %3$sto join our first-to-know email updates.%4$s"
msgstr "%1$sClique aqui%2$s para receber nossas primeiras atualizações por e-mail."

#: includes/managers/controls.php:1138
msgid "Meet Page Transitions"
msgstr "Conheça as transições de página"

#: includes/managers/controls.php:1126
msgid "Page Transitions let you style entrance and exit animations between pages as well as display loader until your page assets load."
msgstr "As transições de página permitem estilizar animações de entrada e saída entre páginas, bem como exibir um carregador até que os recursos da sua página sejam carregados."

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: includes/maintenance-mode.php:375
msgid "Select one or go ahead and %1$screate one%2$s now."
msgstr "Selecione um ou vá em frente e %1$scrie um%2$s agora."

#: includes/elements/column.php:462 includes/elements/container.php:856
#: includes/elements/section.php:727 includes/widgets/heading.php:324
msgid "Luminosity"
msgstr "Luminosidade"

#: includes/elements/column.php:457 includes/elements/container.php:854
#: includes/elements/section.php:725 includes/widgets/heading.php:319
msgid "Saturation"
msgstr "Saturação"

#: includes/elements/column.php:456 includes/elements/container.php:853
#: includes/elements/section.php:724 includes/widgets/heading.php:318
msgid "Color Dodge"
msgstr "Subexposição de cores"

#: includes/elements/column.php:455 includes/elements/container.php:852
#: includes/elements/section.php:723 includes/widgets/heading.php:317
msgid "Lighten"
msgstr "Clarear"

#: includes/elements/column.php:454 includes/elements/container.php:851
#: includes/elements/section.php:722 includes/widgets/heading.php:316
msgid "Darken"
msgstr "Escurecer"

#: includes/elements/column.php:453 includes/elements/container.php:850
#: includes/elements/section.php:721 includes/widgets/heading.php:315
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:82
msgid "Overlay"
msgstr "Sobreposição"

#: includes/elements/column.php:452 includes/elements/container.php:849
#: includes/elements/section.php:720 includes/widgets/heading.php:314
msgid "Screen"
msgstr "Tela"

#: includes/elements/column.php:451 includes/elements/container.php:848
#: includes/elements/section.php:719 includes/widgets/heading.php:313
msgid "Multiply"
msgstr "Multiplicar"

#. translators: %d: Breakpoint screen size.
#: includes/editor-templates/responsive-bar.php:26
msgid "Widescreen <br> Settings added for the Widescreen device will apply to screen sizes %dpx and up"
msgstr "Tela ampla <br> As configurações adicionadas para o dispositivo de tela ampla (widescreen) serão aplicadas aos tamanhos de tela %dpx e superiores"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: includes/editor-templates/panel.php:213
msgid "You can enable it from the %1$sElementor settings page%2$s."
msgstr "Você pode ativá-lo na %1$spágina de configurações do Elementor%2$s."

#. translators: %s: Device name.
#: includes/base/element-base.php:1393
msgid "Hide On %s"
msgstr "Ocultar em %s"

#. translators: %1$s Span open tag, %2$s: Span close tag.
#: includes/admin-templates/new-template.php:52
msgid "Templates Help You %1$sWork Efficiently%2$s"
msgstr "Os modelos ajudam você a %1$strabalhar de forma eficiente%2$s"

#. translators: 1: Link open tag, 2: Link close tag.
#: core/kits/documents/tabs/tab-base.php:80
msgid "In order for Theme Style to affect all relevant Elementor elements, please disable Default Colors and Fonts from the %1$sSettings Page%2$s."
msgstr "Para que o estilo do tema tenha efeito em todos os elementos relevantes do Elementor, desative as cores e fontes padrão na %1$spágina de configurações%2$s."

#: core/kits/documents/tabs/settings-page-transitions.php:19
#: includes/managers/controls.php:1120
msgid "Page Transitions"
msgstr "Transições de página"

#: core/kits/documents/tabs/settings-layout.php:371
msgid "Widescreen breakpoint settings will apply from the selected value and up."
msgstr "As configurações de ponto de interrupção de tela ampla (widescreen) serão aplicadas a partir do valor selecionado e acima."

#: core/experiments/manager.php:332
msgid "Get pixel-perfect design for every screen size. You can now add up to 6 customizable breakpoints beyond the default desktop setting: mobile, mobile extra, tablet, tablet extra, laptop, and widescreen."
msgstr "Tenha um design perfeito em pixels para todos os tamanhos de tela. Agora você pode adicionar até 6 pontos de interrupção personalizáveis além da configuração padrão para desktop: dispositivo móvel, dispositivo móvel extra, tablet, tablet extra, notebook e tela ampla (widescreen)."

#: core/experiments/manager.php:329
msgid "Additional Custom Breakpoints"
msgstr "Pontos de interrupção personalizados adicionais"

#: includes/managers/elements.php:328
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3662
msgid "Favorites"
msgstr "Favoritos"

#: core/common/modules/finder/categories/settings.php:74
#: core/experiments/manager.php:485
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3286
msgid "Features"
msgstr "Recursos"

#: includes/template-library/sources/admin-menu-items/templates-categories-menu-item.php:23
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3277
msgid "Categories"
msgstr "Categorias"

#: modules/nested-accordion/widgets/nested-accordion.php:554
#: assets/js/app-packages.js:5346
msgid "Header"
msgstr "Cabeçalho"

#: modules/library/documents/section.php:47
msgid "Sections"
msgstr "Seções"

#: includes/settings/tools.php:435
msgid "It seems like your site doesn't have any active Kit. The active Kit includes all of your Site Settings. By recreating your Kit you will able to start edit your Site Settings again."
msgstr "Parece que o seu site não tem nenhum kit ativo. O kit ativo inclui todas as configurações do seu site. Ao recriar o seu kit, você poderá começar a editar as configurações do seu site novamente."

#: includes/settings/tools.php:431 includes/settings/tools.php:434
#: assets/js/editor.js:30987
msgid "Recreate Kit"
msgstr "Recriar kit"

#: includes/settings/tools.php:112
msgid "New kit have been created successfully"
msgstr "O novo kit foi criado"

#: includes/settings/tools.php:107
msgid "An error occurred while trying to create a kit."
msgstr "Ocorreu um erro ao tentar criar um kit."

#: includes/settings/tools.php:101
msgid "There's already an active kit."
msgstr "Já existe um kit ativo."

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:15
#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:19
msgid "Submissions"
msgstr "Envios"

#: includes/editor-templates/panel.php:302 assets/js/editor.js:15912
msgid "Color Sampler"
msgstr "Amostragem de cores"

#: core/settings/editor-preferences/model.php:119
msgid "Default device view"
msgstr "Visualização padrão do dispositivo"

#: app/modules/kit-library/data/repository.php:147
#: app/modules/kit-library/data/repository.php:167
msgid "Kit not found"
msgstr "Kit não encontrado"

#: app/modules/kit-library/data/kits/controller.php:29
msgid "Kit not exists."
msgstr "O kit não existe."

#: app/modules/kit-library/connect/kit-library.php:16
#: app/modules/kit-library/kit-library-menu-item.php:22
#: app/modules/kit-library/module.php:34 app/modules/kit-library/module.php:35
#: core/common/modules/finder/categories/general.php:78
#: assets/js/import-export-admin.js:300
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:1538
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3715
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:4153
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:4453
msgid "Kit Library"
msgstr "Biblioteca de kits"

#: modules/global-classes/global-classes-rest-api.php:168
#: assets/js/packages/editor-global-classes/editor-global-classes.js:6
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:26
msgid "Something went wrong"
msgstr "Algo deu errado"

#: includes/settings/tools.php:341 assets/js/app.js:7614 assets/js/app.js:8335
msgid "Important:"
msgstr "Importante:"

#: includes/settings/settings.php:318
msgid "API Key"
msgstr "Chave de API"

#. translators: 1: Link open tag, 2: Link close tag
#: includes/settings/settings.php:311
msgid "Google Maps Embed API is a free service by Google that allows embedding Google Maps in your site. For more details, visit Google Maps' %1$sUsing API Keys%2$s page."
msgstr "A API de incorporação do Google Maps é um serviço gratuito do Google que permite incorporar mapas do Google em seu site. Para mais detalhes, visite a página %1$sUsando chaves de API%2$s do Google Maps."

#: includes/settings/settings.php:307
msgid "Google Maps Embed API"
msgstr "API de incorporação do Google Maps"

#: modules/compatibility-tag/compatibility-tag-report.php:172
msgid "Compatibility unknown"
msgstr "Compatibilidade desconhecida"

#: modules/compatibility-tag/compatibility-tag-report.php:171
msgid "Compatibility not specified"
msgstr "Compatibilidade não especificada"

#: modules/compatibility-tag/compatibility-tag-report.php:170
msgid "Incompatible"
msgstr "Incompatível"

#: modules/compatibility-tag/compatibility-tag-report.php:169
msgid "Compatible"
msgstr "Compatível"

#: includes/widgets/common-base.php:1195
#: modules/floating-buttons/base/widget-contact-button-base.php:2083
#: modules/floating-buttons/base/widget-contact-button-base.php:2174
#: modules/floating-buttons/base/widget-contact-button-base.php:2867
#: modules/floating-buttons/base/widget-floating-bars-base.php:844
#: modules/link-in-bio/base/widget-link-in-bio-base.php:118
msgid "Round"
msgstr "Redondo"

#: includes/controls/groups/background.php:465
#: includes/widgets/common-base.php:1193
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:96
msgid "Repeat-x"
msgstr "Repetir X (horizontal)"

#: includes/controls/groups/background.php:466
#: includes/widgets/common-base.php:1194
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:97
msgid "Repeat-y"
msgstr "Repetir Y (vertical)"

#: includes/controls/groups/background.php:463
#: includes/widgets/common-base.php:1191
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:98
msgid "No-repeat"
msgstr "Não repetir"

#: includes/controls/groups/background.php:464
#: includes/widgets/common-base.php:1188 includes/widgets/common-base.php:1192
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:95
#: assets/js/packages/editor-controls/editor-controls.strings.js:99
msgid "Repeat"
msgstr "Repetir"

#: includes/controls/groups/background.php:385
#: includes/widgets/common-base.php:1152
msgid "Y Position"
msgstr "Posição Y"

#: includes/controls/groups/background.php:342
#: includes/widgets/common-base.php:1116
msgid "X Position"
msgstr "Posição X"

#: includes/controls/groups/background.php:276
#: includes/controls/groups/background.php:328
#: includes/controls/groups/background.php:716
#: includes/widgets/common-base.php:1102 includes/widgets/image.php:409
#: modules/link-in-bio/base/widget-link-in-bio-base.php:188
msgid "Bottom Right"
msgstr "Inferior à direita"

#: includes/controls/groups/background.php:275
#: includes/controls/groups/background.php:327
#: includes/controls/groups/background.php:715
#: includes/widgets/common-base.php:1101 includes/widgets/image.php:408
#: modules/link-in-bio/base/widget-link-in-bio-base.php:187
msgid "Bottom Left"
msgstr "Inferior à esquerda"

#: includes/controls/groups/background.php:274
#: includes/controls/groups/background.php:326
#: includes/controls/groups/background.php:714
#: includes/widgets/common-base.php:1100 includes/widgets/image.php:407
#: modules/link-in-bio/base/widget-link-in-bio-base.php:186
msgid "Bottom Center"
msgstr "Inferior ao centro"

#: includes/controls/groups/background.php:273
#: includes/controls/groups/background.php:325
#: includes/controls/groups/background.php:713
#: includes/widgets/common-base.php:1099 includes/widgets/image.php:406
#: modules/link-in-bio/base/widget-link-in-bio-base.php:185
msgid "Top Right"
msgstr "Superior à direita"

#: includes/controls/groups/background.php:272
#: includes/controls/groups/background.php:324
#: includes/controls/groups/background.php:712
#: includes/widgets/common-base.php:1098 includes/widgets/image.php:405
#: modules/link-in-bio/base/widget-link-in-bio-base.php:184
msgid "Top Left"
msgstr "Superior à esquerda"

#: includes/controls/groups/background.php:271
#: includes/controls/groups/background.php:323
#: includes/controls/groups/background.php:711
#: includes/widgets/common-base.php:1097 includes/widgets/image.php:404
#: modules/link-in-bio/base/widget-link-in-bio-base.php:183
msgid "Top Center"
msgstr "Superior ao centro"

#: includes/controls/groups/background.php:270
#: includes/controls/groups/background.php:322
#: includes/controls/groups/background.php:710
#: includes/widgets/common-base.php:1096 includes/widgets/image.php:403
#: modules/link-in-bio/base/widget-link-in-bio-base.php:182
msgid "Center Right"
msgstr "Centro à direita"

#: includes/controls/groups/background.php:269
#: includes/controls/groups/background.php:321
#: includes/controls/groups/background.php:709
#: includes/widgets/common-base.php:1095 includes/widgets/image.php:402
#: modules/link-in-bio/base/widget-link-in-bio-base.php:181
msgid "Center Left"
msgstr "Centro à esquerda"

#: includes/controls/groups/background.php:268
#: includes/controls/groups/background.php:320
#: includes/controls/groups/background.php:708
#: includes/widgets/common-base.php:1094 includes/widgets/image.php:401
#: modules/link-in-bio/base/widget-link-in-bio-base.php:180
msgid "Center Center"
msgstr "Centro ao centro"

#: includes/base/element-base.php:1076 includes/base/element-base.php:1099
#: includes/widgets/common-base.php:1059
msgid "Scale"
msgstr "Escala"

#: includes/widgets/common-base.php:1044
msgid "Fit"
msgstr "Ajustar"

#: includes/widgets/common-base.php:1024
msgid "Need More Shapes?"
msgstr "Precisa de mais formas?"

#: includes/widgets/common-base.php:971 includes/widgets/common-base.php:979
msgid "Mask"
msgstr "Máscara"

#: includes/widgets/common-base.php:140
msgid "Hexagon"
msgstr "Hexágono"

#: includes/widgets/common-base.php:139
msgid "Blob"
msgstr "Mancha"

#: includes/widgets/common-base.php:138
msgid "Triangle"
msgstr "Triângulo"

#: includes/widgets/common-base.php:137
msgid "Sketch"
msgstr "Esboço"

#: includes/widgets/common-base.php:136
msgid "Flower"
msgstr "Flor"

#: includes/widgets/accordion.php:262 includes/widgets/toggle.php:265
#: modules/nested-accordion/widgets/nested-accordion.php:307
msgid "FAQ Schema"
msgstr "Dado estruturado (schema) de perguntas frequentes"

#: includes/settings/settings.php:392
msgid "Set the way Google Fonts are being loaded by selecting the font-display property (Recommended: Swap)."
msgstr "Defina a forma como as fontes do Google estão sendo carregadas, selecionando a propriedade “font-display” (Recomendado: Trocar)."

#: includes/settings/settings.php:392
msgid "Font-display property defines how font files are loaded and displayed by the browser."
msgstr "A propriedade \"font-display\" define como os arquivos de fonte são carregados e exibidos pelo navegador."

#: includes/settings/settings.php:390
msgid "Optional"
msgstr "Opcional"

#: includes/settings/settings.php:388
msgid "Swap"
msgstr "Trocar"

#: includes/settings/settings.php:387
msgid "Blocking"
msgstr "Bloquear"

#: includes/settings/settings.php:381
msgid "Google Fonts Load"
msgstr "Carregamento de fontes do Google"

#: includes/editor-templates/responsive-bar.php:62
msgid "Manage Breakpoints"
msgstr "Gerenciar pontos de interrupção"

#: includes/editor-templates/responsive-bar.php:22
msgid "Desktop <br> Settings added for the base device will apply to all breakpoints unless edited"
msgstr "Desktop <br> As configurações adicionadas ao dispositivo base serão aplicadas a todos os pontos de interrupção, a menos que sejam editadas"

#. translators: %1$s: Device name, %2$s: Breakpoint screen size.
#: includes/editor-templates/responsive-bar.php:32
msgid "%1$s <br> Settings added for the %1$s device will apply to %2$spx screens and down"
msgstr "%1$s <br> As configurações adicionadas ao dispositivo %1$s serão aplicadas às telas %2$spx e inferiores"

#: core/breakpoints/manager.php:339
msgid "Widescreen"
msgstr "Tela ampla (widescreen)"

#: core/base/db-upgrades-manager.php:114
msgid "Database update process is running in the background. Taking a while?"
msgstr "O processo de atualização do banco de dados está sendo executado em segundo plano. Está demorando?"

#: core/admin/admin-notices.php:324
msgid "With Elementor Pro, you can control user access and make sure no one messes up your design."
msgstr "Com o Elementor Pro, você pode controlar o acesso do usuário e garantir que ninguém bagunce o seu design."

#: core/admin/admin-notices.php:323
msgid "Managing a multi-user site?"
msgstr "Gerenciando um site com vários usuários?"

#: core/admin/admin-notices.php:232
msgid "Love using Elementor?"
msgstr "Gostando de usar o Elementor?"

#: app/modules/import-export/module.php:119
msgid "Template Kits"
msgstr "Kits de modelos"

#: app/modules/import-export/module.php:116
msgid "Import / Export Kit"
msgstr "Importação/exportação de kits"

#: app/modules/import-export/module.php:161
msgid "Apply the design and settings of another site to this one."
msgstr "Aplique o design e as configurações de outro site a este."

#: app/modules/import-export/module.php:159
msgid "Start Import"
msgstr "Iniciar importação"

#: app/modules/import-export/module.php:156
msgid "Import a Template Kit"
msgstr "Importar um kit de modelo"

#: app/modules/import-export/module.php:149
msgid "Bundle your whole site - or just some of its elements - to be used for another website."
msgstr "Empacote todo o seu site - ou apenas alguns de seus elementos - para serem usados em outro site."

#: app/modules/import-export/module.php:147
msgid "Start Export"
msgstr "Iniciar exportação"

#: app/modules/import-export/module.php:144
msgid "Export a Template Kit"
msgstr "Exportar um kit de modelo"

#. translators: 1: New line break, 2: Learn more link.
#: app/modules/import-export/module.php:137
msgid "Design sites faster with a template kit that contains some or all components of a complete site, like templates, content & site settings.%1$sYou can import a kit and apply it to your site, or export the elements from this site to be used anywhere else. %2$s"
msgstr "Crie sites mais rapidamente com um kit de modelos que contém alguns ou todos os componentes de um site completo, como modelos, conteúdo e configurações do site. %1$sVocê pode importar um kit e aplicá-lo ao seu site, ou exportar os elementos deste site para serem usados em qualquer outro lugar. %2$s"

#: app/modules/import-export/module.php:133 core/admin/admin-notices.php:369
#: core/admin/admin-notices.php:425 core/admin/admin-notices.php:473
#: core/experiments/manager.php:317 core/experiments/manager.php:333
#: core/experiments/manager.php:362 core/experiments/manager.php:539
#: includes/controls/url.php:78 includes/elements/section.php:473
#: includes/settings/settings-page.php:404
#: includes/widgets/common-base.php:1029 includes/widgets/video.php:584
#: modules/ai/feature-intro/product-image-unification-intro.php:40
#: modules/checklist/steps/step-base.php:102
#: modules/editor-app-bar/module.php:47 modules/nested-elements/module.php:21
#: modules/shapes/widgets/text-path.php:150 assets/js/app.js:7604
#: assets/js/editor-v4-opt-in-alphachip.js:187
#: assets/js/editor-v4-opt-in.js:500 assets/js/editor-v4-welcome-opt-in.js:86
#: assets/js/editor.js:30089
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3497
msgid "Learn more"
msgstr "Saiba mais"

#: core/utils/import-export/wp-import.php:1143
msgid "The uploaded file could not be moved"
msgstr "O arquivo enviado não pôde ser movido"

#: core/utils/import-export/wp-import.php:1127
msgid "Sorry, this file type is not permitted for security reasons."
msgstr "Este tipo de arquivo não é permitido por motivos de segurança."

#. translators: %s: Max file size.
#: core/utils/import-export/wp-import.php:1095
msgid "Remote file is too large, limit is %s"
msgstr "O arquivo remoto é muito grande, o limite é %s"

#: core/utils/import-export/wp-import.php:1087
msgid "Downloaded file has incorrect size"
msgstr "O arquivo baixado tem tamanho incorreto"

#: core/utils/import-export/wp-import.php:1081
msgid "Zero size file downloaded"
msgstr "O arquivo baixado está vazio"

#: core/utils/import-export/wp-import.php:1073
msgid "Remote server did not respond"
msgstr "O servidor remoto não respondeu"

#. translators: 1: HTTP error message, 2: HTTP error code.
#: core/utils/import-export/wp-import.php:1064
msgid "Remote server returned the following unexpected result: %1$s (%2$s)"
msgstr "O servidor remoto retornou o seguinte resultado inesperado: %1$s (%2$s)"

#. translators: 1: WordPress error message, 2: WordPress error code.
#: core/utils/import-export/wp-import.php:1055
msgid "Request failed due to an error: %1$s (%2$s)"
msgstr "A solicitação falhou devido a um erro: %1$s (%2$s)"

#: core/utils/import-export/wp-import.php:1039
msgid "Could not create temporary file."
msgstr "Não foi possível criar o arquivo temporário."

#: core/utils/import-export/wp-import.php:995
msgid "Invalid file type"
msgstr "Tipo de arquivo inválido"

#: core/utils/import-export/wp-import.php:978
msgid "Fetching attachments is not enabled"
msgstr "A coleta de anexos não está ativada"

#. translators: %s: Menu slug.
#: core/utils/import-export/wp-import.php:891
msgid "Menu item skipped due to invalid menu slug: %s"
msgstr "Item de menu ignorado devido a um slug de menu inválido: %s"

#: core/utils/import-export/wp-import.php:878
msgid "Menu item skipped due to missing menu slug"
msgstr "Item de menu ignorado devido à falta de um slug de menu"

#. translators: 1: Post title, 2: Post type.
#: core/utils/import-export/wp-import.php:584
msgid "Failed to import %1$s: Invalid post type %2$s"
msgstr "Falha ao importar %1$s: Tipo de post inválido %2$s"

#. translators: 1: Term taxonomy, 2: Term name.
#. translators: 1: Post type singular label, 2: Post title.
#. translators: 1: Taxonomy name, 2: Term name.
#: core/utils/import-export/wp-import.php:485
#: core/utils/import-export/wp-import.php:676
#: core/utils/import-export/wp-import.php:726
msgid "Failed to import %1$s %2$s"
msgstr "Falha ao importar%1$s %2$s"

#. translators: %s: Author display name.
#: core/utils/import-export/wp-import.php:385
msgid "Failed to create new user for %s. Their posts will be attributed to the current user."
msgstr "Falha ao criar novo usuário para %s. Seus posts serão atribuídos ao usuário atual."

#. translators: %s: Post author.
#: core/utils/import-export/wp-import.php:320
msgid "Failed to import author %s. Their posts will be attributed to the current user."
msgstr "Falha ao importar o autor %s. Seus posts serão atribuídos ao usuário atual."

#: core/utils/import-export/wp-import.php:253
msgid "The file does not exist, please try again."
msgstr "O arquivo não existe, tente novamente."

#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:57
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:65
msgid "There was an error when reading this WXR file"
msgstr "Ocorreu um erro ao ler este arquivo WXR"

#: core/utils/import-export/parsers/wxr-parser-regex.php:146
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:70
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:76
#: core/utils/import-export/parsers/wxr-parser-xml.php:190
msgid "This does not appear to be a WXR file, missing/invalid WXR version number"
msgstr "Este não parece ser um arquivo WXR, número de versão WXR ausente/inválido"

#: core/kits/documents/tabs/settings-layout.php:217
msgid "Mobile and Tablet options cannot be deleted."
msgstr "As opções de dispositivos móveis e tablets não podem ser excluídas."

#: core/kits/documents/tabs/settings-layout.php:215
msgid "Active Breakpoints"
msgstr "Pontos de interrupção ativos"

#: modules/shapes/widgets/text-path.php:520
#: modules/shapes/widgets/text-path.php:591
msgid "Stroke"
msgstr "Traço"

#: modules/shapes/widgets/text-path.php:485
msgid "Path"
msgstr "Caminho"

#: modules/shapes/widgets/text-path.php:382
msgid "Starting Point"
msgstr "Ponto inicial"

#: includes/controls/groups/typography.php:245
#: modules/shapes/widgets/text-path.php:347
msgid "Word Spacing"
msgstr "Espaçamento entre palavras"

#: modules/shapes/widgets/text-path.php:217
msgid "Show Path"
msgstr "Mostrar caminho"

#: modules/shapes/widgets/text-path.php:205
msgid "LTR"
msgstr "LTR"

#: modules/shapes/widgets/text-path.php:204
msgid "RTL"
msgstr "RTL"

#: modules/shapes/widgets/text-path.php:199
msgid "Text Direction"
msgstr "Direção do texto"

#: modules/atomic-widgets/elements/atomic-svg/atomic-svg.php:33
#: modules/shapes/widgets/text-path.php:136
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:1
msgid "SVG"
msgstr "SVG"

#: modules/shapes/widgets/text-path.php:126
msgid "Path Type"
msgstr "Tipo de caminho"

#: modules/shapes/widgets/text-path.php:114
msgid "Add Your Curvy Text Here"
msgstr "Adicione seu texto curvado aqui"

#: modules/shapes/widgets/text-path.php:51
#: modules/shapes/widgets/text-path.php:103
#: modules/shapes/widgets/text-path.php:243
msgid "Text Path"
msgstr "Caminho do texto"

#: modules/shapes/module.php:48
msgid "Spiral"
msgstr "Espiral"

#: modules/shapes/module.php:47
msgid "Oval"
msgstr "Oval"

#: modules/shapes/module.php:44
msgid "Arc"
msgstr "Arco"

#: modules/shapes/module.php:43
msgid "Wave"
msgstr "Onda"

#: core/experiments/manager.php:684
msgid "Inactive by default"
msgstr "Inativo por padrão"

#: core/experiments/manager.php:683
msgid "Active by default"
msgstr "Ativo por padrão"

#. translators: %s Release status.
#: core/experiments/manager.php:570
msgid "Status: %s"
msgstr "Status: %s"

#: core/experiments/manager.php:538
msgid "To use an experiment or feature on your site, simply click on the dropdown next to it and switch to Active. You can always deactivate them at any time."
msgstr "Para usar um experimento ou recurso no seu site, basta clicar no menu suspenso ao lado dele e mudar para \"Ativo\". Você sempre pode desativá-los a qualquer momento."

#: core/common/modules/finder/categories/settings.php:69
msgid "Experiments"
msgstr "Experimentos"

#: core/experiments/manager.php:473
msgid "The current version of Elementor doesn't have any experimental features . if you're feeling curious make sure to come back in future versions."
msgstr "A versão atual do Elementor não tem nenhum recurso experimental. Se você estiver curioso, certifique-se de voltar em versões futuras."

#: core/experiments/manager.php:470
msgid "No available experiments"
msgstr "Nenhum experimento disponível"

#: core/experiments/manager.php:412
msgid "Stable"
msgstr "Estável"

#: core/experiments/manager.php:411 assets/js/ai-admin.js:657
#: assets/js/ai-admin.js:7784 assets/js/ai-gutenberg.js:2425
#: assets/js/ai-gutenberg.js:9632 assets/js/ai-layout.js:489
#: assets/js/ai-layout.js:3265 assets/js/ai-media-library.js:2286
#: assets/js/ai-media-library.js:9413 assets/js/ai-unify-product-images.js:2286
#: assets/js/ai-unify-product-images.js:9413 assets/js/ai.js:3071
#: assets/js/ai.js:10878
msgid "Beta"
msgstr "Beta"

#: core/experiments/manager.php:410 modules/atomic-widgets/module.php:240
#: assets/js/editor-v4-opt-in.js:347 assets/js/editor-v4-opt-in.js:495
msgid "Alpha"
msgstr "Alfa"

#: core/experiments/manager.php:409
msgid "Development"
msgstr "Desenvolvimento"

#: modules/landing-pages/module.php:292
msgid "No landing pages found in trash"
msgstr "Nenhuma página de destino encontrada na lixeira"

#: modules/landing-pages/module.php:291
msgid "No landing pages found"
msgstr "Nenhuma página de destino encontrada"

#: modules/landing-pages/module.php:290
msgid "Search Landing Pages"
msgstr "Pesquisar páginas de destino"

#: modules/landing-pages/module.php:289
msgid "View Landing Page"
msgstr "Ver página de destino"

#: modules/landing-pages/module.php:288
msgid "All Landing Pages"
msgstr "Todas as páginas de destino"

#: modules/landing-pages/module.php:287
msgid "New Landing Page"
msgstr "Nova página de destino"

#: modules/landing-pages/module.php:286
msgid "Edit Landing Page"
msgstr "Editar página de destino"

#: modules/landing-pages/module.php:285
msgid "Add New Landing Page"
msgstr "Adicionar nova página de destino"

#: modules/landing-pages/module.php:217
msgid "Build Effective Landing Pages for your business' marketing campaigns."
msgstr "Crie páginas de destino (landing pages) eficazes para as campanhas de marketing de sua empresa."

#: modules/landing-pages/module.php:46
msgid "Adds a new Elementor content type that allows creating beautiful landing pages instantly in a streamlined workflow."
msgstr "Adiciona um novo tipo de conteúdo do Elementor que permite criar páginas de destino (landing pages) bonitas instantaneamente, em um fluxo de trabalho otimizado."

#: modules/landing-pages/admin-menu-items/landing-pages-menu-item.php:22
#: modules/landing-pages/admin-menu-items/landing-pages-menu-item.php:26
#: modules/landing-pages/documents/landing-page.php:54
#: modules/landing-pages/module.php:45 modules/landing-pages/module.php:151
#: modules/landing-pages/module.php:282 modules/landing-pages/module.php:294
#: assets/js/app.js:10352 assets/js/editor.js:55153
msgid "Landing Pages"
msgstr "Páginas de destino"

#: modules/landing-pages/documents/landing-page.php:46
#: modules/landing-pages/module.php:217 modules/landing-pages/module.php:283
msgid "Landing Page"
msgstr "Página de destino"

#: modules/compatibility-tag/compatibility-tag-report.php:123
#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:57
#: modules/element-manager/ajax.php:139
msgid "Unknown"
msgstr "Desconhecido"

#. translators: %s: Elementor plugin name.
#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:45
msgid "Tested up to %s version"
msgstr "Testado até a versão %s"

#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:42
#: assets/js/element-manager-admin.js:2298
#: assets/js/element-manager-admin.js:2359
msgid "Plugin"
msgstr "Plugin"

#. translators: 1: Plugin name, 2: Plugin version.
#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:33
msgid "Some of the plugins you’re using have not been tested with the latest version of %1$s (%2$s). To avoid issues, make sure they are all up to date and compatible before updating %1$s."
msgstr "Alguns dos plugins que você está usando não foram testados com a última versão do %1$s (%2$s). Para evitar problemas, certifique-se de que todos estejam atualizados e sejam compatíveis antes de atualizar o %1$s."

#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:28
msgid "Compatibility Alert"
msgstr "Alerta de compatibilidade"

#: includes/elements/section.php:301
msgid "Custom Columns Gap"
msgstr "Espaço entre colunas personalizado"

#: core/kits/views/trash-kit-confirmation.php:33
msgid "Keep my settings"
msgstr "Manter minhas configurações"

#: core/kits/views/trash-kit-confirmation.php:21
msgid "By removing this template you will delete your entire Site Settings. If this template is deleted, all associated settings: Global Colors & Fonts, Theme Style, Layout, Background, and Lightbox settings will be removed from your existing site. This action can not be undone."
msgstr "Ao remover este modelo, você irá excluir todas as configurações do seu site. Se este modelo for excluído, todas as configurações associadas - cores e fontes globais, estilo do tema, layout, plano de fundo e configurações de lightbox - serão removidas do seu site existente. Esta ação não pode ser desfeita."

#: core/kits/views/trash-kit-confirmation.php:17
msgid "Are you sure you want to delete your Site Settings?"
msgstr "Tem certeza de que deseja excluir suas configurações do site?"

#: core/editor/data/globals/endpoints/base.php:34
msgid "The Global value you are trying to use is not available."
msgstr "O valor global que você está tentando usar não está disponível."

#: includes/controls/media.php:195
msgid "Choose SVG"
msgstr "Escolher SVG"

#. Description of the plugin
#: elementor.php
msgid "The Elementor Website Builder has it all: drag and drop page builder, pixel perfect design, mobile responsive editing, and more. Get started now!"
msgstr "O construtor de sites Elementor tem tudo: construtor de páginas do tipo arrastar e soltar, design perfeito em pixels, edição responsiva para dispositivos móveis e muito mais. Comece agora!"

#: core/kits/documents/tabs/global-colors.php:24
#: core/kits/documents/tabs/global-colors.php:43 assets/js/app.js:10360
#: assets/js/editor.js:49514
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:173
msgid "Global Colors"
msgstr "Cores globais"

#: core/kits/documents/tabs/settings-layout.php:350
#: modules/nested-tabs/widgets/nested-tabs.php:432
msgid "Breakpoint"
msgstr "Ponto de interrupção"

#: core/frontend/render-mode-manager.php:150
#: modules/compatibility-tag/compatibility-tag-report.php:173
msgid "Error"
msgstr "Erro"

#: includes/widgets/social-icons.php:485
msgid "Rows Gap"
msgstr "Espaço entre as linhas"

#: includes/widgets/icon-list.php:213
msgid "Apply Link On"
msgstr "Aplicar link em"

#: includes/controls/groups/flex-container.php:24
#: includes/controls/groups/grid-container.php:26
#: includes/widgets/icon-list.php:180
#: modules/nested-accordion/widgets/nested-accordion.php:146
msgid "Items"
msgstr "Itens"

#: includes/widgets/common-base.php:1045 includes/widgets/image.php:383
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:135
msgid "Fill"
msgstr "Preencher"

#: includes/widgets/image.php:376
msgid "Object Fit"
msgstr "Ajuste do objeto"

#: includes/frontend.php:1409
msgid "Download"
msgstr "Baixar"

#: core/admin/admin-notices.php:216 includes/settings/settings-page.php:403
msgid "Become a super contributor by opting in to share non-sensitive plugin data and to receive periodic email updates from us."
msgstr "Torne-se um super colaborador ao optar por compartilhar dados não confidenciais do plugin e receber as nossas atualizações periódicas por e-mail."

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: core/admin/admin.php:873
msgid "The latest update includes some substantial changes across different areas of the plugin. We highly recommend you %1$sbackup your site before upgrading%2$s, and make sure you first update in a staging environment"
msgstr "A atualização mais recente inclui algumas alterações substanciais em diferentes áreas do plugin. É altamente recomendável que você faça um %1$sbackup do seu site antes de atualizar%2$s e certifique-se de atualizar primeiro em um ambiente de teste"

#: core/admin/admin.php:867
msgid "Heads up, Please backup before upgrade!"
msgstr "Atenção, faça backup antes de atualizar!"

#: core/settings/editor-preferences/model.php:39
#: includes/editor-templates/hotkeys.php:153 assets/js/editor.js:40485
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:6
msgid "User Preferences"
msgstr "Preferências do usuário"

#: core/kits/manager.php:436 includes/editor-templates/hotkeys.php:115
#: assets/js/app.js:10358 assets/js/app.js:10838 assets/js/editor.js:49463
#: assets/js/editor.js:49467 assets/js/editor.js:49477
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:9
msgid "Site Settings"
msgstr "Configurações do site"

#: modules/nested-tabs/widgets/nested-tabs.php:371 assets/js/editor.js:49873
msgid "Additional Settings"
msgstr "Configurações adicionais"

#: core/settings/editor-preferences/model.php:195 assets/js/editor.js:49881
msgid "Design System"
msgstr "Sistema de design"

#: core/kits/documents/tabs/global-typography.php:197
msgid "Fallback Font Family"
msgstr "Família de fontes alternativas"

#: core/kits/documents/tabs/settings-background.php:67
msgid "The `theme-color` meta tag will only be available in supported browsers and devices."
msgstr "A meta tag `theme-color` estará disponível apenas em navegadores e dispositivos compatíveis."

#: core/kits/documents/tabs/settings-background.php:65
msgid "Mobile Browser Background"
msgstr "Fundo do navegador em dispositivos móveis"

#: core/kits/documents/tabs/settings-site-identity.php:125
msgid "Suggested favicon dimensions: 512 × 512 pixels."
msgstr "Dimensões sugeridas para o favicon: 512 × 512 pixels."

#: core/kits/documents/tabs/settings-site-identity.php:118
msgid "Site Favicon"
msgstr "Favicon do site"

#. translators: 1: Width number pixel, 2: Height number pixel.
#: core/kits/documents/tabs/settings-site-identity.php:102
msgid "Suggested image dimensions: %1$s × %2$s pixels."
msgstr "Dimensões sugeridas para a imagem: %1$s × %2$s pixels."

#: core/kits/documents/tabs/settings-site-identity.php:93
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:1051
msgid "Site Logo"
msgstr "Logo do site"

#: core/kits/documents/tabs/settings-site-identity.php:84
msgid "Choose description"
msgstr "Escolher descrição"

#: core/kits/documents/tabs/settings-site-identity.php:82
msgid "Site Description"
msgstr "Descrição do site"

#: core/kits/documents/tabs/settings-site-identity.php:73
msgid "Choose name"
msgstr "Escolher nome"

#: core/kits/documents/tabs/settings-site-identity.php:71
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:1047
msgid "Site Name"
msgstr "Nome do site"

#: core/kits/documents/tabs/settings-site-identity.php:20
msgid "Site Identity"
msgstr "Identidade do site"

#: core/kits/documents/tabs/settings-layout.php:194
msgid "Breakpoints"
msgstr "Pontos de interrupção"

#: core/kits/documents/tabs/settings-layout.php:183
#: modules/page-templates/module.php:159
msgid "Theme"
msgstr "Tema"

#: core/kits/documents/tabs/settings-layout.php:180
msgid "Default Page Layout"
msgstr "Layout de página padrão"

#: core/kits/documents/tabs/settings-layout.php:47 assets/js/app.js:10360
msgid "Layout Settings"
msgstr "Configurações de layout"

#: modules/page-templates/module.php:365
msgid "Changes will be reflected in the preview only after the page reloads."
msgstr "As alterações serão refletidas na pré-visualização apenas após o recarregamento da página."

#: core/common/modules/connect/apps/base-app.php:109
msgid "Reset Data"
msgstr "Redefinir dados"

#: includes/controls/media.php:270
msgid "Click the media icon to upload file"
msgstr "Clique no ícone de mídia para enviar o arquivo"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:74
msgid "Watch the Full Guide"
msgstr "Assista ao guia completo"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:62
msgid "Get introduced to Elementor by watching our \"Getting Started\" video series. It will guide you through the steps needed to create your website. Then click to create your first page."
msgstr "Conheça o Elementor assistindo à nossa série de vídeos \"Iniciando\". Ela o guiará pelos passos necessários para criar seu site. Em seguida, clique para criar sua primeira página."

#: includes/settings/settings.php:353 assets/js/admin.js:294
#: assets/js/admin.js:304 assets/js/ai-admin.js:64 assets/js/ai-admin.js:74
#: assets/js/ai-gutenberg.js:1693 assets/js/ai-gutenberg.js:1703
#: assets/js/ai-media-library.js:1693 assets/js/ai-media-library.js:1703
#: assets/js/ai-unify-product-images.js:1693
#: assets/js/ai-unify-product-images.js:1703 assets/js/ai.js:1693
#: assets/js/ai.js:1703 assets/js/common.js:2146 assets/js/common.js:2156
#: assets/js/editor.js:42299 assets/js/editor.js:42309
msgid "Enable Unfiltered File Uploads"
msgstr "Ativar envios de arquivos não filtrados"

#: modules/safe-mode/module.php:367
msgid "If you are experiencing a loading issue, contact your site administrator to troubleshoot the problem using Safe Mode."
msgstr "Se você estiver enfrentando um problema de carregamento, fale com o administrador do seu site para solucionar o problema usando o \"Modo seguro\"."

#: includes/frontend.php:1410
msgid "Download image"
msgstr "Baixar imagem"

#: includes/frontend.php:1408
msgid "Pin it"
msgstr "Fixar"

#: includes/frontend.php:1407
msgid "Share on Twitter"
msgstr "Compartilhar no Twitter"

#: includes/frontend.php:1406
msgid "Share on Facebook"
msgstr "Compartilhar no Facebook"

#: includes/controls/url.php:119
msgid "Custom Attributes"
msgstr "Atributos personalizados"

#: includes/controls/url.php:77
msgid "Set custom attributes for the link element. Separate attribute keys from values using the | (pipe) character. Separate key-value pairs with a comma."
msgstr "Defina atributos personalizados para o elemento de link. Separe as chaves de atributo dos valores usando o caractere | (barra vertical). Separe os pares de chave-valor com uma vírgula."

#: includes/editor-templates/panel.php:326
msgid "Get more dynamic capabilities by incorporating dozens of Elementor's native dynamic tags."
msgstr "Obtenha mais recursos dinâmicos incorporando dezenas de tags dinâmicas nativas do Elementor."

#: includes/editor-templates/panel.php:325
msgid "You’re missing out!"
msgstr "Você está perdendo!"

#: includes/editor-templates/panel.php:322
msgid "Elementor Dynamic Content"
msgstr "Conteúdo dinâmico do Elementor"

#: includes/editor-templates/panel.php:296
msgid "Dynamic Tags"
msgstr "Tags dinâmicas"

#: includes/managers/icons.php:491
msgid "We highly recommend backing up your database before performing this upgrade."
msgstr "É altamente recomendável fazer o backup de seu banco de dados antes de realizar esta atualização."

#: includes/managers/icons.php:490
msgid "The upgrade process includes a database update"
msgstr "O processo de atualização inclui uma atualização do banco de dados"

#: includes/managers/controls.php:1216
msgid "Attributes lets you add custom HTML attributes to any element."
msgstr "Os atributos permitem que você adicione atributos HTML personalizados a qualquer elemento."

#: includes/managers/controls.php:1214
msgid "Meet Our Attributes"
msgstr "Conheça nossos atributos"

#: includes/managers/controls.php:1204
msgid "Attributes"
msgstr "Atributos"

#: core/base/db-upgrades-manager.php:118
msgid "Click here to run it now"
msgstr "Clique aqui para executar agora"

#: core/kits/documents/tabs/settings-lightbox.php:187
msgid "Navigation Icons Size"
msgstr "Tamanho dos ícones de navegação"

#: core/kits/documents/tabs/settings-lightbox.php:174
msgid "Toolbar Icons Size"
msgstr "Tamanho dos ícones da barra de ferramentas"

#: core/kits/documents/tabs/settings-lightbox.php:102
#: core/kits/documents/tabs/settings-lightbox.php:119
msgid "Alt"
msgstr "Alt"

#: core/kits/documents/tabs/settings-lightbox.php:86 includes/frontend.php:1413
msgid "Share"
msgstr "Compartilhar"

#: core/kits/documents/tabs/settings-lightbox.php:66 includes/frontend.php:1411
msgid "Fullscreen"
msgstr "Tela cheia"

#. translators: %s: Widget title.
#: core/editor/promotion.php:56
msgid "Use %s widget and dozens more pro features to extend your toolbox and build sites faster and better."
msgstr "Use o widget %s e dezenas de outros recursos profissionais para ampliar sua caixa de ferramentas e criar sites de forma mais rápida e melhores."

#. translators: %s: Widget title.
#: core/editor/promotion.php:54
msgid "%s Widget"
msgstr "Widget %s"

#: core/experiments/manager.php:104 includes/editor-templates/global.php:27
#: includes/editor-templates/templates.php:232 assets/js/ai-admin.js:9578
#: assets/js/ai-gutenberg.js:11426 assets/js/ai-media-library.js:11207
#: assets/js/ai-unify-product-images.js:11207 assets/js/ai.js:12672
#: assets/js/app.js:7176 assets/js/editor.js:49769
msgid "Back"
msgstr "Voltar"

#: core/isolation/elementor-adapter.php:28 core/kits/manager.php:156
#: core/kits/manager.php:174
msgid "Default Kit"
msgstr "Kit padrão"

#: core/kits/documents/tabs/theme-style-form-fields.php:128
msgid "Focus"
msgstr "Foco"

#: core/kits/documents/tabs/theme-style-form-fields.php:99
msgid "Field"
msgstr "Campo"

#: core/kits/documents/tabs/theme-style-form-fields.php:71
msgid "Label"
msgstr "Rótulo"

#: core/kits/documents/tabs/theme-style-form-fields.php:21
#: core/kits/documents/tabs/theme-style-form-fields.php:60
msgid "Form Fields"
msgstr "Campos do formulário"

#: core/kits/documents/tabs/theme-style-typography.php:75
#: includes/widgets/text-editor.php:306
msgid "Paragraph Spacing"
msgstr "Espaçamento entre parágrafos"

#: core/kits/documents/tabs/theme-style-typography.php:48
msgid "Body"
msgstr "Corpo"

#: core/kits/documents/tabs/theme-style-buttons.php:23
#: core/kits/documents/tabs/theme-style-buttons.php:63
#: modules/floating-buttons/base/widget-contact-button-base.php:221
msgid "Buttons"
msgstr "Botões"

#: core/kits/documents/kit.php:154
msgid "Draft"
msgstr "Rascunho"

#: core/kits/documents/kit.php:43
msgid "Kit"
msgstr "Kit"

#: core/common/modules/connect/apps/base-app.php:161
msgid "Already connected."
msgstr "Já conectado."

#: includes/widgets/image-carousel.php:462
msgid "Pause on Interaction"
msgstr "Pausar na interação"

#. translators: %s: Video provider
#: includes/embed.php:185
msgid "%s Video Player"
msgstr "Reprodutor de vídeo %s"

#: includes/controls/groups/background.php:702
msgid "Background Position"
msgstr "Posição do fundo"

#: core/kits/documents/tabs/settings-background.php:81
#: includes/controls/groups/background.php:486
#: includes/controls/groups/background.php:691 includes/widgets/image.php:385
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:92
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:137
msgid "Contain"
msgstr "Conter"

#: includes/controls/groups/background.php:485
#: includes/controls/groups/background.php:690 includes/widgets/image.php:384
#: modules/link-in-bio/base/widget-link-in-bio-base.php:920
#: modules/link-in-bio/base/widget-link-in-bio-base.php:975
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:91
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:136
msgid "Cover"
msgstr "Cobertura"

#: core/kits/documents/tabs/settings-background.php:80
#: includes/controls/groups/background.php:484
#: includes/controls/groups/background.php:689
#: includes/elements/container.php:549 includes/widgets/social-icons.php:299
#: includes/widgets/video.php:578
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:46
#: assets/js/packages/editor-controls/editor-controls.strings.js:90
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:123
msgid "Auto"
msgstr "Automático"

#: includes/controls/groups/background.php:683
msgid "Background Size"
msgstr "Tamanho do fundo"

#: includes/settings/settings-page.php:396
msgid "Usage Data Sharing"
msgstr "Compartilhamento de dados de uso"

#: core/settings/editor-preferences/model.php:173 assets/js/ai-admin.js:15932
#: assets/js/ai-gutenberg.js:17780 assets/js/ai-layout.js:5218
#: assets/js/ai-media-library.js:17561
#: assets/js/ai-unify-product-images.js:17561 assets/js/ai.js:9390
#: assets/js/ai.js:19026 assets/js/editor.js:10093
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:658
msgid "Get Started"
msgstr "Iniciar"

#: core/common/modules/connect/apps/library.php:29
#: core/common/modules/connect/apps/library.php:56
msgid "Connecting to the Library failed. Please try reloading the page and try again"
msgstr "A conexão com a biblioteca falhou. Tente recarregar a página e tente novamente"

#. translators: %s: Remote user.
#: core/common/modules/connect/apps/base-app.php:84
msgid "Connected as %s"
msgstr "Conectado como %s"

#: core/settings/editor-preferences/model.php:79
msgid "Auto detect"
msgstr "Detecção automática"

#: core/settings/editor-preferences/model.php:52
msgid "Preferences"
msgstr "Preferências"

#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:15
#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:19
msgid "Custom Icons"
msgstr "Ícones personalizados"

#: includes/controls/groups/background.php:655
msgid "Transition"
msgstr "Transição"

#: includes/controls/groups/background.php:645
msgid "Duration"
msgstr "Duração"

#: core/logger/log-reporter.php:44
msgid "Clear Log"
msgstr "Limpar registro"

#: includes/frontend.php:1416 assets/js/app.js:7115 assets/js/app.js:8795
#: assets/js/app.js:9738
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:1686
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:1737
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:2017
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:2294
msgid "Next"
msgstr "Próximo"

#: includes/frontend.php:1415 assets/js/app.js:7988 assets/js/app.js:8782
#: assets/js/app.js:9731
msgid "Previous"
msgstr "Anterior"

#: includes/widgets/divider.php:667
msgid "Amount"
msgstr "Quantidade"

#: includes/widgets/divider.php:496
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:21
msgid "Add Element"
msgstr "Adicionar elemento"

#: includes/widgets/divider.php:340 modules/shapes/module.php:46
msgid "Line"
msgstr "Linha"

#: includes/widgets/divider.php:310
msgctxt "Shapes"
msgid "X"
msgstr "X"

#: includes/widgets/divider.php:301
msgctxt "Shapes"
msgid "Tribal"
msgstr "Tribal"

#: includes/widgets/divider.php:292
msgctxt "Shapes"
msgid "Trees"
msgstr "Árvores"

#: includes/widgets/divider.php:283
msgctxt "Shapes"
msgid "Squares"
msgstr "Quadrados"

#: includes/widgets/divider.php:274
msgctxt "Shapes"
msgid "Stripes"
msgstr "Listras"

#: includes/widgets/divider.php:265
msgctxt "Shapes"
msgid "Leaves"
msgstr "Folhas"

#: includes/widgets/divider.php:256
msgctxt "Shapes"
msgid "Half Rounds"
msgstr "Semicírculo"

#: includes/widgets/divider.php:247
msgctxt "Shapes"
msgid "Fir Tree"
msgstr "Pinheiro"

#: includes/widgets/divider.php:238
msgctxt "Shapes"
msgid "Dots"
msgstr "Pontos"

#: includes/widgets/divider.php:229
msgctxt "Shapes"
msgid "Rectangles"
msgstr "Retângulos"

#: includes/widgets/divider.php:221
msgctxt "Shapes"
msgid "Parallelogram"
msgstr "Paralelogramo"

#: includes/widgets/divider.php:213
msgctxt "Shapes"
msgid "Rhombus"
msgstr "Losango"

#: includes/widgets/divider.php:205
msgctxt "Shapes"
msgid "Pluses"
msgstr "Mais"

#: includes/widgets/divider.php:197
msgctxt "Shapes"
msgid "Arrows"
msgstr "Setas"

#: includes/widgets/divider.php:181
msgctxt "Shapes"
msgid "Wavy"
msgstr "Ondulado"

#: includes/widgets/divider.php:173
msgctxt "Shapes"
msgid "Squared"
msgstr "Ao quadrado"

#: includes/widgets/divider.php:164
msgctxt "Shapes"
msgid "Slashes"
msgstr "Barras"

#: includes/widgets/divider.php:156
msgctxt "Shapes"
msgid "Multiple"
msgstr "Múltiplo"

#: includes/widgets/divider.php:148
msgctxt "Shapes"
msgid "Curved"
msgstr "Curvado"

#: includes/widgets/divider.php:140
msgctxt "Shapes"
msgid "Curly"
msgstr "Cacheado"

#: includes/widgets/image-gallery.php:44 includes/widgets/image-gallery.php:130
msgid "Basic Gallery"
msgstr "Galeria básica"

#: includes/controls/groups/background.php:754
msgid "Out"
msgstr "Externo"

#: includes/controls/groups/background.php:753
msgid "In"
msgstr "Interno"

#: includes/controls/groups/background.php:738
msgid "Ken Burns Effect"
msgstr "Efeito Ken Burns"

#: includes/controls/groups/background.php:608
msgid "This cover image will replace the background video in case that the video could not be loaded."
msgstr "Esta imagem de capa substituirá o vídeo de fundo, caso o vídeo não possa ser carregado."

#: includes/controls/groups/background.php:583 includes/widgets/video.php:354
msgid "Play On Mobile"
msgstr "Reproduzir em dispositivos móveis"

#: includes/controls/groups/background.php:528
msgid "YouTube/Vimeo link, or link to video file (mp4 is recommended)."
msgstr "Link do YouTube/Vimeo ou link para o arquivo de vídeo (recomenda-se mp4)."

#: includes/settings/tools.php:386
msgid "Reinstall"
msgstr "Reinstalar"

#: core/document-types/post.php:51
msgid "Post"
msgstr "Post"

#. translators: %s: Path to .htaccess file.
#: core/debug/classes/htaccess.php:31
msgid "File Path: %s"
msgstr "Caminho do arquivo: %s"

#: core/kits/views/panel.php:12 includes/editor-templates/panel.php:30
#: includes/editor-templates/panel.php:183
msgid "Need Help"
msgstr "Preciso de ajuda"

#: includes/controls/groups/background.php:573
msgid "Play Once"
msgstr "Reproduzir uma vez"

#: includes/controls/icons.php:91 includes/controls/icons.php:112
#: includes/controls/icons.php:198
msgid "Upload SVG"
msgstr "Enviar arquivo SVG"

#: includes/controls/media.php:192
msgid "Choose Video"
msgstr "Escolher vídeo"

#: includes/admin-templates/beta-tester.php:32
msgid "Your Email"
msgstr "Seu e-mail"

#: includes/admin-templates/beta-tester.php:30
msgid "As a beta tester, you’ll receive an update that includes a testing version of Elementor and its content directly to your Email"
msgstr "Como testador beta, você receberá uma atualização que inclui uma versão de teste do Elementor e seu conteúdo diretamente no seu e-mail"

#: includes/admin-templates/beta-tester.php:29
msgid "Get Beta Updates"
msgstr "Receber atualizações da versão beta"

#: includes/settings/settings.php:361
msgid "We recommend you only enable this feature if you understand the security risks involved."
msgstr "Recomendamos que você só ative esse recurso se compreender os riscos de segurança envolvidos."

#: includes/settings/settings.php:361
msgid "Elementor will try to sanitize the unfiltered files, removing potential malicious code and scripts."
msgstr "O Elementor tentará limpar os arquivos não filtrados, removendo possíveis códigos e scripts maliciosos."

#: includes/settings/settings.php:361
msgid "Please note! Allowing uploads of any files (SVG & JSON included) is a potential security risk."
msgstr "Observação! Permitir o envio de qualquer arquivo (incluindo SVG e JSON) é um possível risco à segurança."

#: includes/template-library/sources/local.php:618
msgid "Template not exist."
msgstr "O modelo não existe."

#. translators: 1: Link open tag, 2: Link close tag.
#: includes/elements/column.php:934 includes/elements/container.php:1887
#: includes/elements/section.php:1407 includes/widgets/common-base.php:1230
msgid "Responsive visibility will take effect only on %1$s preview mode %2$s or live page, and not while editing in Elementor."
msgstr "A visibilidade responsiva terá efeito apenas no modo de pré-visualização %1$s %2$s ou na página ao vivo, e não durante a edição no Elementor."

#: core/experiments/manager.php:664 includes/base/widget-base.php:1019
msgid "Deprecated"
msgstr "Obsoleto"

#: includes/managers/icons.php:551
msgid "Hurray! The upgrade process to Font Awesome 5 was completed successfully."
msgstr "Ótimo! O processo de atualização para o Font Awesome 5 foi concluído."

#: includes/managers/icons.php:505
msgid "Upgrade To Font Awesome 5"
msgstr "Atualizar para o Font Awesome 5"

#: includes/managers/icons.php:493
msgid "This action is not reversible and cannot be undone by rolling back to previous versions."
msgstr "Esta ação não é reversível e não pode ser desfeita revertendo para versões anteriores."

#: includes/managers/icons.php:488
msgid "Please note that the upgrade process may cause some of the previously used Font Awesome 4 icons to look a bit different due to minor design changes made by Font Awesome."
msgstr "Observe que o processo de atualização pode fazer com que alguns dos ícones do Font Awesome 4 usados anteriormente pareçam um pouco diferentes devido a pequenas alterações de design feitas pelo Font Awesome."

#: includes/managers/icons.php:486
msgid "By upgrading, whenever you edit a page containing a Font Awesome 4 icon, Elementor will convert it to the new Font Awesome 5 icon."
msgstr "Ao atualizar, sempre que você editar uma página contendo um ícone do Font Awesome 4, o Elementor o converterá para o novo ícone do Font Awesome 5."

#: includes/managers/icons.php:485
msgid "Access 1,500+ amazing Font Awesome 5 icons and enjoy faster performance and design flexibility."
msgstr "Acesse mais de 1.500 ícones incríveis do Font Awesome 5 e desfrute de um melhor desempenho e flexibilidade de design."

#: includes/managers/icons.php:479 includes/managers/icons.php:483
#: includes/managers/icons.php:498
msgid "Font Awesome Upgrade"
msgstr "Atualização do Font Awesome"

#: includes/managers/icons.php:472
msgid "Font Awesome 4 support script (shim.js) is a script that makes sure all previously selected Font Awesome 4 icons are displayed correctly while using Font Awesome 5 library."
msgstr "O script de suporte do Font Awesome 4 (shim.js) é um script que garante que todos os ícones do Font Awesome 4 selecionados anteriormente sejam exibidos corretamente ao usar a biblioteca do Font Awesome 5."

#: includes/managers/icons.php:464
msgid "Load Font Awesome 4 Support"
msgstr "Carregar suporte para o Font Awesome 4"

#: includes/managers/icons.php:248
msgid "All Icons"
msgstr "Todos os ícones"

#: includes/managers/icons.php:156
msgid "Font Awesome - Brands"
msgstr "Font Awesome - Marcas"

#: includes/managers/icons.php:144
msgid "Font Awesome - Solid"
msgstr "Font Awesome - Sólidos"

#: includes/managers/icons.php:132
msgid "Font Awesome - Regular"
msgstr "Font Awesome - Regular"

#: core/debug/classes/htaccess.php:12
msgid "Your site's .htaccess file appears to be missing."
msgstr "O arquivo .htaccess do seu site parece estar faltando."

#: core/debug/classes/theme-missing.php:22
msgid "Some of your theme files are missing."
msgstr "Alguns dos arquivos do seu tema estão faltando."

#: core/files/file-types/svg.php:73 core/files/uploads-manager.php:590
msgid "This file is not allowed for security reasons."
msgstr "Este arquivo não é permitido por motivos de segurança."

#: includes/admin-templates/beta-tester.php:37 assets/js/beta-tester.js:64
msgid "Sign Up"
msgstr "Cadastrar"

#: includes/controls/media.php:283 includes/controls/media.php:285
#: assets/js/editor.js:8112
#: assets/js/packages/editor-controls/editor-controls.js:12
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:4
#: assets/js/packages/editor-controls/editor-controls.strings.js:27
msgid "Upload"
msgstr "Enviar"

#: includes/controls/icons.php:90 includes/controls/icons.php:116
#: includes/controls/icons.php:202 assets/js/editor.js:8696
msgid "Icon Library"
msgstr "Biblioteca de ícones"

#: core/editor/editor.php:210
msgid "Document not found."
msgstr "Documento não encontrado."

#: includes/elements/container.php:1650 includes/widgets/common-base.php:565
msgid "Vertical Orientation"
msgstr "Orientação vertical"

#: includes/base/element-base.php:1010 includes/elements/container.php:1574
#: includes/elements/container.php:1612 includes/elements/container.php:1674
#: includes/elements/container.php:1711 includes/widgets/common-base.php:489
#: includes/widgets/common-base.php:527 includes/widgets/common-base.php:589
#: includes/widgets/common-base.php:626
#: modules/floating-buttons/base/widget-contact-button-base.php:2959
#: modules/floating-buttons/base/widget-contact-button-base.php:3013
msgid "Offset"
msgstr "Deslocamento"

#: includes/elements/container.php:1549 includes/widgets/common-base.php:464
msgid "Horizontal Orientation"
msgstr "Orientação horizontal"

#: includes/elements/container.php:1530 includes/widgets/common-base.php:450
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:111
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:145
msgid "Fixed"
msgstr "Fixo"

#: includes/elements/container.php:1529 includes/widgets/common-base.php:449
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:144
msgid "Absolute"
msgstr "Absoluto"

#: includes/elements/container.php:1512 includes/widgets/common-base.php:433
msgid "Custom positioning is not considered best practice for responsive web design and should not be used too frequently."
msgstr "O posicionamento personalizado não é considerado uma prática recomendada para o design responsivo da web e não deve ser usado com muita frequência."

#: includes/elements/container.php:1511 includes/widgets/common-base.php:432
msgid "Please note!"
msgstr "Observe!"

#: includes/controls/groups/flex-item.php:30
#: includes/widgets/common-base.php:249
msgid "Custom Width"
msgstr "Largura personalizada"

#: includes/settings/controls.php:236
msgid "Super Admin"
msgstr "Super administrador"

#: includes/settings/admin-menu-items/get-help-menu-item.php:23
msgid "Get Help"
msgstr "Obter ajuda"

#: includes/elements/container.php:548 includes/elements/section.php:453
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:122
msgid "Hidden"
msgstr "Oculto"

#: includes/elements/container.php:543 includes/elements/section.php:448
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:124
msgid "Overflow"
msgstr "Transbordar"

#: includes/elements/column.php:869 includes/elements/container.php:1807
#: includes/elements/section.php:1316 includes/widgets/common-base.php:720
msgid "Motion Effects"
msgstr "Efeitos de movimento"

#: includes/controls/groups/flex-container.php:117
#: includes/controls/groups/flex-container.php:230
#: includes/controls/groups/grid-container.php:212
#: includes/controls/groups/grid-container.php:252
#: includes/elements/column.php:193 includes/elements/column.php:221
#: includes/elements/section.php:430
msgid "Space Evenly"
msgstr "Espaço uniforme"

#: includes/controls/groups/flex-container.php:113
#: includes/controls/groups/flex-container.php:226
#: includes/controls/groups/grid-container.php:208
#: includes/controls/groups/grid-container.php:248
#: includes/elements/column.php:192 includes/elements/column.php:220
#: includes/elements/section.php:429
msgid "Space Around"
msgstr "Espaço ao redor"

#: includes/elements/section.php:420 includes/widgets/common-base.php:404
#: includes/widgets/image-carousel.php:732
msgid "Vertical Align"
msgstr "Alinhar verticalmente"

#: modules/safe-mode/module.php:358
msgid "Having problems loading Elementor? Please enable Safe Mode to troubleshoot."
msgstr "Tendo problemas para carregar o Elementor? Ative o \"Modo seguro\" para solucionar problemas."

#: modules/safe-mode/module.php:256
msgid "The issue was probably caused by one of your plugins or theme."
msgstr "O problema provavelmente foi causado por um dos seus plugins ou tema."

#. translators: %s: Accepted chars.
#: includes/widgets/menu-anchor.php:136
msgid "Note: The ID link ONLY accepts these chars: %s"
msgstr "Observação: O link de ID aceita APENAS estes caracteres: %s"

#: includes/controls/media.php:198
#: modules/link-in-bio/base/widget-link-in-bio-base.php:387
msgid "Choose File"
msgstr "Escolher arquivo"

#: includes/widgets/video.php:223
msgid "External URL"
msgstr "URL externo"

#: includes/widgets/image-gallery.php:263
msgid "Order By"
msgstr "Ordenar por"

#: includes/widgets/read-more.php:124
msgid "Read More Text"
msgstr "Texto \"Leia mais\""

#. translators: %s: The `the_content` function.
#: includes/widgets/read-more.php:115
msgid "Note: This widget only affects themes that use `%s` in archive pages."
msgstr "Observação: Este widget afeta apenas temas que usam `%s` em páginas de arquivo."

#: includes/widgets/read-more.php:95
msgid "Continue reading"
msgstr "Continuar lendo"

#: includes/widgets/read-more.php:40 includes/widgets/read-more.php:91
msgid "Read More"
msgstr "Leia mais"

#: includes/widgets/google-maps.php:154
#: modules/floating-buttons/base/widget-contact-button-base.php:396
#: modules/floating-buttons/base/widget-contact-button-base.php:956
#: modules/link-in-bio/base/widget-link-in-bio-base.php:500
#: modules/link-in-bio/base/widget-link-in-bio-base.php:750
msgid "Location"
msgstr "Localização"

#. translators: %s: Current post name.
#: includes/frontend.php:1551
msgid "Continue reading %s"
msgstr "Continuar lendo %s"

#: includes/frontend.php:1544
msgid "(more&hellip;)"
msgstr "(mais&hellip;)"

#: includes/template-library/sources/local.php:1455
msgctxt "Template Library"
msgid "Filter by category"
msgstr "Filtrar por categoria"

#: includes/template-library/sources/local.php:323
msgctxt "Template Library"
msgid "All Categories"
msgstr "Todas as categorias"

#: includes/template-library/sources/local.php:322
msgctxt "Template Library"
msgid "Category"
msgstr "Categoria"

#: includes/template-library/sources/local.php:321
msgctxt "Template Library"
msgid "Categories"
msgstr "Categorias"

#: core/admin/menu/main.php:34 core/admin/menu/main.php:35
#: includes/template-library/sources/local.php:239
msgctxt "Template Library"
msgid "Templates"
msgstr "Modelos"

#: modules/promotions/admin-menu-items/popups-promotion-item.php:19
msgid "The Popup Builder lets you take advantage of all the amazing features in Elementor, so you can build beautiful & highly converting popups. Get Elementor Pro and start designing your popups today."
msgstr "O construtor de pop-ups permite que você aproveite todos os recursos incríveis do Elementor, para que você possa criar pop-ups bonitos e de alta conversão. Adquira o Elementor Pro e comece a criar seus pop-ups hoje mesmo."

#: modules/promotions/admin-menu-items/popups-promotion-item.php:18
msgid "Get Popup Builder"
msgstr "Obter o construtor de pop-ups"

#: includes/template-library/sources/local.php:1733
#: modules/promotions/admin-menu-items/popups-promotion-item.php:41
#: modules/promotions/admin-menu-items/popups-promotion-item.php:45
#: assets/js/app.js:10343
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:2219
msgid "Popups"
msgstr "Pop-ups"

#: app/admin-menu-items/theme-builder-menu-item.php:22
#: app/modules/site-editor/module.php:31
#: core/common/modules/finder/categories/general.php:72
#: includes/template-library/sources/local.php:1732
#: assets/js/app-packages.js:5890 assets/js/editor.js:49488
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:7
msgid "Theme Builder"
msgstr "Construtor de temas"

#: modules/safe-mode/module.php:354
msgid "Enable Safe Mode"
msgstr "Ativar o \"Modo seguro\""

#: modules/safe-mode/module.php:352 modules/safe-mode/module.php:364
msgid "Can't Edit?"
msgstr "Não consegue editar?"

#: modules/safe-mode/module.php:269
msgid "Still experiencing issues?"
msgstr "Ainda está tendo problemas?"

#: modules/safe-mode/module.php:253
msgid "Editor successfully loaded?"
msgstr "O editor foi carregado?"

#: modules/safe-mode/module.php:246 modules/safe-mode/module.php:476
msgid "Disable Safe Mode"
msgstr "Desativar o \"Modo seguro\""

#: modules/safe-mode/module.php:244
msgid "Safe Mode ON"
msgstr "Modo seguro ATIVADO"

#: modules/safe-mode/module.php:102
msgid "Cannot enable Safe Mode"
msgstr "Não foi possível ativar o \"Modo seguro\""

#: modules/safe-mode/module.php:51
msgid "Safe Mode allows you to troubleshoot issues by only loading the editor, without loading the theme or any other plugin."
msgstr "O \"Modo seguro\" permite que você solucione problemas carregando apenas o editor, sem carregar o tema ou qualquer outro plugin."

#: modules/safe-mode/module.php:42
msgid "Safe Mode"
msgstr "Modo seguro"

#: core/upgrade/manager.php:51
msgid "Elementor Data Updater"
msgstr "Atualizador de dados do Elementor"

#. translators: %d: Interval in minutes.
#: core/base/background-process/wp-background-process.php:439
#: core/base/background-task.php:316
msgid "Every %d minutes"
msgstr "A cada %d minutos"

#: core/base/db-upgrades-manager.php:130
msgid "The database update process is now complete. Thank you for updating to the latest version!"
msgstr "O processo de atualização do banco de dados foi concluído. Agradecemos por atualizar para a versão mais recente!"

#: core/base/db-upgrades-manager.php:93
msgid "Your site database needs to be updated to the latest version."
msgstr "O banco de dados do seu site precisa ser atualizado para a versão mais recente."

#: modules/library/documents/not-supported.php:56
msgid "Not Supported"
msgstr "Não suportado"

#: core/common/modules/finder/categories/site.php:82
msgid "Users"
msgstr "Usuários"

#: core/common/modules/finder/categories/site.php:76 assets/js/app.js:10846
msgid "Plugins"
msgstr "Plugins"

#: includes/widgets/video.php:495
msgid "Any Video"
msgstr "Qualquer vídeo"

#: includes/widgets/video.php:494
msgid "Current Video Channel"
msgstr "Canal de vídeo atual"

#: includes/widgets/rating.php:122 includes/widgets/star-rating.php:390
msgid "Unmarked Color"
msgstr "Cor não marcada"

#: includes/widgets/star-rating.php:319
msgid "Stars"
msgstr "Estrelas"

#: includes/widgets/star-rating.php:192
msgid "Outline"
msgstr "Contorno"

#: includes/widgets/star-rating.php:184
msgid "Unmarked Style"
msgstr "Estilo não marcado"

#: includes/widgets/rating.php:147 includes/widgets/star-rating.php:140
msgid "Rating Scale"
msgstr "Escala de classificação"

#: includes/widgets/rating.php:22 includes/widgets/rating.php:140
#: includes/widgets/rating.php:164 includes/widgets/star-rating.php:153
msgid "Rating"
msgstr "Classificação"

#: includes/widgets/star-rating.php:45 includes/widgets/star-rating.php:122
msgid "Star Rating"
msgstr "Classificação por estrelas"

#: includes/widgets/image.php:163 includes/widgets/image.php:175
msgid "Custom Caption"
msgstr "Legenda personalizada"

#: includes/widgets/image-gallery.php:175 includes/widgets/image.php:162
msgid "Attachment Caption"
msgstr "Legenda do anexo"

#: includes/editor-templates/hotkeys.php:208
msgid "Quit"
msgstr "Sair"

#: includes/editor-templates/hotkeys.php:200 assets/js/editor.js:7421
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:16
msgid "Keyboard Shortcuts"
msgstr "Atalhos do teclado"

#: includes/editor-templates/hotkeys.php:107
msgid "Show / Hide Panel"
msgstr "Mostrar/ocultar painel"

#: includes/editor-templates/hotkeys.php:167
msgid "Go To"
msgstr "Acessar"

#: includes/editor-templates/hotkeys.php:32 assets/js/ai-admin.js:12614
#: assets/js/ai-admin.js:13703 assets/js/ai-gutenberg.js:14462
#: assets/js/ai-gutenberg.js:15551 assets/js/ai-media-library.js:14243
#: assets/js/ai-media-library.js:15332
#: assets/js/ai-unify-product-images.js:14243
#: assets/js/ai-unify-product-images.js:15332 assets/js/ai.js:15708
#: assets/js/ai.js:16797
msgid "Redo"
msgstr "Refazer"

#: includes/editor-templates/hotkeys.php:24 assets/js/ai-admin.js:12603
#: assets/js/ai-admin.js:13692 assets/js/ai-gutenberg.js:14451
#: assets/js/ai-gutenberg.js:15540 assets/js/ai-media-library.js:14232
#: assets/js/ai-media-library.js:15321
#: assets/js/ai-unify-product-images.js:14232
#: assets/js/ai-unify-product-images.js:15321 assets/js/ai.js:15697
#: assets/js/ai.js:16786 assets/js/editor.js:11564
#: assets/js/kit-elements-defaults-editor.js:232
msgid "Undo"
msgstr "Desfazer"

#: core/common/modules/finder/template.php:13
msgid "Type to find anything in Elementor"
msgstr "Digite para encontrar qualquer coisa no Elementor"

#: includes/editor-templates/hotkeys.php:99 assets/js/admin-top-bar.js:189
#: assets/js/common.js:4651 assets/js/editor.js:40497
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:18
msgid "Finder"
msgstr "Localizador"

#: core/common/modules/finder/categories/site.php:70
msgid "Customizer"
msgstr "Personalizador"

#: core/common/modules/finder/categories/site.php:58
msgid "Menus"
msgstr "Menus"

#: core/common/modules/finder/categories/site.php:52
msgid "Dashboard"
msgstr "Painel"

#: core/common/modules/finder/categories/site.php:46
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:12
msgid "Homepage"
msgstr "Pagina inicial"

#: core/common/modules/finder/categories/create.php:27
#: assets/js/editor.js:10966 assets/js/editor.js:48086
msgid "Create"
msgstr "Criar"

#: core/common/modules/connect/apps/base-app.php:87
msgid "Disconnect"
msgstr "Desconectar"

#: core/common/modules/connect/apps/base-app.php:232
msgid "Disconnected successfully."
msgstr "Desconectado."

#: core/common/modules/connect/apps/base-app.php:220 assets/js/editor.js:11960
#: assets/js/editor.js:12025 assets/js/editor.js:13007
msgid "Connected successfully."
msgstr "Conectado."

#: core/common/modules/connect/apps/connect.php:11
#: core/common/modules/connect/connect-menu-item.php:24
#: core/common/modules/connect/connect-menu-item.php:28
#: includes/editor-templates/templates.php:521
#: modules/cloud-library/module.php:125 assets/js/ai-admin.js:1072
#: assets/js/ai-admin.js:6399 assets/js/ai-gutenberg.js:2840
#: assets/js/ai-gutenberg.js:8247 assets/js/ai-layout.js:770
#: assets/js/ai-layout.js:2501 assets/js/ai-media-library.js:2701
#: assets/js/ai-media-library.js:8028 assets/js/ai-unify-product-images.js:2701
#: assets/js/ai-unify-product-images.js:8028 assets/js/ai.js:3486
#: assets/js/ai.js:9401 assets/js/ai.js:9493 assets/js/editor.js:11910
msgid "Connect"
msgstr "Conectar"

#: core/base/document.php:1994
msgid "Future"
msgstr "Futuro"

#: includes/widgets/video.php:597
msgid "Poster"
msgstr "Capa"

#: includes/widgets/video.php:457
msgid "Lazy Load"
msgstr "Carregamento lento"

#: includes/widgets/video.php:159 includes/widgets/video.php:184
#: includes/widgets/video.php:208 includes/widgets/video.php:268
msgid "Enter your URL"
msgstr "Digite seu URL"

#: includes/widgets/accordion.php:171 includes/widgets/accordion.php:175
#: includes/widgets/icon-box.php:186 includes/widgets/image-box.php:161
#: includes/widgets/tabs.php:170 includes/widgets/tabs.php:174
#: includes/widgets/testimonial.php:124 includes/widgets/text-editor.php:142
#: includes/widgets/toggle.php:174 includes/widgets/toggle.php:178
msgid "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo."
msgstr "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo."

#: includes/settings/admin-menu-items/getting-started-menu-item.php:61
msgid "Welcome to Elementor"
msgstr "Boas-vindas ao Elementor"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:39
msgid "Create Your First Post"
msgstr "Crie seu primeiro post"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:36
msgid "Create Your First Page"
msgstr "Crie sua primeira página"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:23
#: includes/settings/admin-menu-items/getting-started-menu-item.php:27
#: includes/settings/admin-menu-items/getting-started-menu-item.php:52
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:795
msgid "Getting Started"
msgstr "Iniciando"

#: includes/widgets/inner-section.php:35 assets/js/editor.js:30103
msgid "Inner Section"
msgstr "Seção interna"

#: includes/editor-templates/navigator.php:103
msgid "Once you fill your page with content, this window will give you an overview display of all the page elements. This way, you can easily move around any section, column, or widget."
msgstr "Depois de preencher sua página com conteúdo, esta janela lhe dará uma visão geral de todos os elementos da página. Desta forma, você pode mover facilmente qualquer seção, coluna ou widget."

#: includes/editor-templates/navigator.php:102
msgid "Easy Navigation is Here!"
msgstr "A navegação fácil está aqui!"

#: includes/editor-templates/navigator.php:97
msgid "Empty"
msgstr "Vazio"

#: includes/editor-templates/hotkeys.php:126
#: includes/editor-templates/navigator.php:38
#: includes/editor-templates/panel.php:87
#: includes/editor-templates/panel.php:91 assets/js/editor.js:34004
msgid "Navigator"
msgstr "Navegador"

#: includes/editor-templates/library-layout.php:13
#: includes/settings/admin-menu-items/getting-started-menu-item.php:55
#: includes/settings/admin-menu-items/getting-started-menu-item.php:56
#: modules/announcements/module.php:124 assets/js/app-packages.js:2837
#: assets/js/app.js:3271
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:1271
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:1490
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:1582
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:1834
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:2008
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:2329
msgid "Skip"
msgstr "Pular"

#: modules/floating-buttons/base/widget-contact-button-base.php:949
#: modules/floating-buttons/base/widget-floating-bars-base.php:166
#: modules/floating-buttons/base/widget-floating-bars-base.php:352
#: modules/link-in-bio/base/widget-link-in-bio-base.php:270
#: modules/shapes/widgets/text-path.php:164
msgid "Paste URL or type"
msgstr "Cole o URL ou digite"

#: includes/controls/groups/css-filter.php:129
msgctxt "Filter Control"
msgid "Hue"
msgstr "Matiz"

#: core/debug/inspector.php:49
msgid "Debug Bar"
msgstr "Barra de depuração"

#: core/admin/admin-notices.php:285
msgid "Hide Notification"
msgstr "Ocultar notificação"

#: core/admin/admin-notices.php:279
msgid "Happy To Help"
msgstr "Feliz em ajudar"

#: core/admin/admin-notices.php:276
msgid "You created over 10 pages with Elementor. Great job! If you can spare a minute, please help us by leaving a five star review on WordPress.org."
msgstr "Você criou mais de 10 páginas com o Elementor. Ótimo trabalho! Se você puder, dedique um minuto para nos ajudar deixando uma avaliação de cinco estrelas no WordPress.org."

#: core/admin/admin-notices.php:275
msgid "Congrats!"
msgstr "Parabéns!"

#: core/kits/documents/tabs/settings-lightbox.php:76 includes/frontend.php:1412
#: includes/widgets/google-maps.php:174 assets/js/ai-admin.js:10240
#: assets/js/ai-admin.js:10243 assets/js/ai-gutenberg.js:12088
#: assets/js/ai-gutenberg.js:12091 assets/js/ai-media-library.js:11869
#: assets/js/ai-media-library.js:11872
#: assets/js/ai-unify-product-images.js:11869
#: assets/js/ai-unify-product-images.js:11872 assets/js/ai.js:13334
#: assets/js/ai.js:13337
msgid "Zoom"
msgstr "Zoom"

#: core/debug/inspector.php:57
msgid "Debug Bar adds an admin bar menu that lists all the templates that are used on a page that is being displayed."
msgstr "A barra de depuração adiciona um menu na barra de administração que lista todos os modelos que estão sendo usados em uma página que está sendo exibida."

#: core/document-types/page-base.php:47
msgid "Single"
msgstr "Único"

#: includes/widgets/video.php:429
msgid "Logo"
msgstr "Logo"

#: includes/widgets/video.php:402
msgid "Video Info"
msgstr "Informações do vídeo"

#: core/base/providers/social-network-provider.php:216
#: includes/widgets/video.php:255 includes/widgets/video.php:279
msgid "URL"
msgstr "URL"

#: includes/widgets/video.php:141
msgid "Self Hosted"
msgstr "Auto-hospedado"

#: includes/widgets/video.php:139
msgid "Dailymotion"
msgstr "Dailymotion"

#: includes/widgets/video.php:133
msgid "Source"
msgstr "Fonte"

#: includes/widgets/traits/button-trait.php:216
msgid "Please make sure the ID is unique and not used elsewhere on the page this form is displayed. This field allows %1$sA-z 0-9%2$s & underscore chars without spaces."
msgstr "Certifique-se de que o ID seja único e não seja usado em nenhum outro lugar na página onde este formulário é exibido. Este campo permite os caracteres %1$sA-z 0-9%2$s e sublinhado, sem espaços."

#: includes/widgets/traits/button-trait.php:205
msgid "Button ID"
msgstr "ID do botão"

#: includes/widgets/audio.php:193
msgid "Artwork"
msgstr "Arte"

#: includes/managers/elements.php:317
msgid "WooCommerce"
msgstr "WooCommerce"

#: app/modules/site-editor/module.php:32
#: core/common/modules/finder/categories/site.php:26 core/kits/manager.php:437
#: includes/managers/elements.php:310 modules/admin-bar/module.php:149
msgid "Site"
msgstr "Site"

#: includes/editor-templates/templates.php:122
#: includes/editor-templates/templates.php:640
#: includes/managers/elements.php:292
#: modules/promotions/widgets/pro-widget-promotion.php:66
#: assets/js/ai-admin.js:8001 assets/js/ai-gutenberg.js:9849
#: assets/js/ai-layout.js:3482 assets/js/ai-media-library.js:9630
#: assets/js/ai-unify-product-images.js:9630 assets/js/ai.js:11095
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3180
msgid "Pro"
msgstr "Pro"

#: includes/elements/column.php:447 includes/elements/container.php:844
#: includes/elements/section.php:715 includes/widgets/heading.php:309
msgid "Blend Mode"
msgstr "Modo de mesclagem"

#: includes/editor-templates/hotkeys.php:41
#: includes/editor-templates/templates.php:165 assets/js/editor.js:10664
#: assets/js/editor.js:10678 assets/js/editor.js:32758
msgid "Copy"
msgstr "Copiar"

#: includes/editor-templates/global.php:49
#: assets/js/cf70912a0f34653ad242.bundle.js:130
msgid "Drag widget here"
msgstr "Arraste o widget aqui"

#: includes/controls/groups/css-filter.php:113
msgctxt "Filter Control"
msgid "Saturation"
msgstr "Saturação"

#: includes/controls/groups/css-filter.php:97
msgctxt "Filter Control"
msgid "Contrast"
msgstr "Contraste"

#: includes/controls/groups/css-filter.php:81
msgctxt "Filter Control"
msgid "Brightness"
msgstr "Brilho"

#: includes/controls/groups/css-filter.php:62
msgctxt "Filter Control"
msgid "Blur"
msgstr "Desfoque"

#: includes/controls/groups/background.php:563 includes/widgets/video.php:319
msgid "Specify an end time (in seconds)"
msgstr "Especificar um horário de término (em segundos)"

#: includes/controls/groups/background.php:561 includes/widgets/video.php:317
msgid "End Time"
msgstr "Horário de término"

#: includes/controls/groups/background.php:551 includes/widgets/video.php:308
msgid "Specify a start time (in seconds)"
msgstr "Especificar um horário de início (em segundos)"

#: includes/controls/groups/background.php:549 includes/widgets/video.php:306
msgid "Start Time"
msgstr "Horário de início"

#: core/admin/feedback.php:107
msgid "Wait! Don't deactivate Elementor. You have to activate both Elementor and Elementor Pro in order for the plugin to work."
msgstr "Espere! Não desative o Elementor. Você precisa ativar tanto o Elementor quanto o Elementor Pro para que o plugin funcione."

#: core/admin/feedback.php:105
msgid "I have Elementor Pro"
msgstr "Eu tenho o Elementor Pro"

#: core/debug/inspector.php:115
msgid "Elementor Debugger"
msgstr "Depurador do Elementor"

#: core/admin/admin.php:219 assets/js/admin.js:2058 assets/js/gutenberg.js:147
msgid "Back to WordPress Editor"
msgstr "Voltar ao editor do WordPress"

#. translators: %s: Document title.
#: core/documents-manager.php:388
msgid "Elementor %s"
msgstr "Elementor %s"

#. translators: %s: Document title.
#. translators: %s: Template type label.
#: core/base/document.php:269
#: core/common/modules/finder/categories/create.php:86
#: core/document-types/page-base.php:183
#: includes/template-library/sources/local.php:1416
msgid "Add New %s"
msgstr "Adicionar novo(a) %s"

#: core/kits/documents/tabs/theme-style-images.php:95
#: core/kits/documents/tabs/theme-style-images.php:166
#: includes/elements/column.php:416 includes/elements/column.php:490
#: includes/elements/container.php:798 includes/elements/container.php:912
#: includes/elements/section.php:669 includes/elements/section.php:773
#: includes/widgets/image-box.php:429 includes/widgets/image-box.php:464
#: includes/widgets/image.php:441 includes/widgets/image.php:475
#: modules/floating-buttons/base/widget-floating-bars-base.php:1010
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1499
msgid "Opacity"
msgstr "Opacidade"

#: includes/widgets/image.php:319
msgid "Max Width"
msgstr "Largura máxima"

#: includes/controls/groups/background.php:265
#: includes/controls/groups/background.php:313
#: includes/controls/groups/box-shadow.php:69
#: includes/elements/container.php:1524 includes/widgets/common-base.php:444
#: includes/widgets/common-base.php:1091 includes/widgets/divider.php:766
#: includes/widgets/divider.php:932 includes/widgets/image-carousel.php:572
#: includes/widgets/image-carousel.php:636 includes/widgets/tabs.php:184
#: includes/widgets/traits/button-trait.php:251
#: modules/floating-buttons/base/widget-floating-bars-base.php:448
#: modules/link-in-bio/base/widget-link-in-bio-base.php:940
#: modules/link-in-bio/base/widget-link-in-bio-base.php:995
#: modules/nested-accordion/widgets/nested-accordion.php:213
#: modules/nested-tabs/widgets/nested-tabs.php:857
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:39
#: assets/js/packages/editor-controls/editor-controls.strings.js:110
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:15
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:147
msgid "Position"
msgstr "Posição"

#: includes/template-library/sources/local.php:232
#: assets/js/new-template.js:147
msgid "New Template"
msgstr "Novo modelo"

#. translators: 1: Elementor, 2: Link to plugin review
#: core/admin/admin.php:408
msgid "Enjoyed %1$s? Please leave us a %2$s rating. We really appreciate your support!"
msgstr "Gostando do %1$s? Deixe-nos uma classificação de %2$s. Agradecemos muito pelo seu apoio!"

#: core/common/modules/finder/categories/general.php:67
msgid "Knowledge Base"
msgstr "Base de conhecimento"

#: modules/page-templates/module.php:352
msgid "This template includes the header, full-width content and footer"
msgstr "Este modelo inclui o cabeçalho, conteúdo com largura total e rodapé"

#: modules/page-templates/module.php:340
msgid "No header, no footer, just Elementor"
msgstr "Sem cabeçalho, sem rodapé, apenas o Elementor"

#: modules/page-templates/module.php:328
msgid "Default Page Template from your theme."
msgstr "Modelo de página padrão do seu tema."

#: modules/page-templates/module.php:297
msgid "Page Layout"
msgstr "Layout da página"

#: includes/frontend.php:1414 includes/widgets/video.php:987
msgid "Play Video"
msgstr "Reproduzir vídeo"

#: includes/widgets/common-base.php:233 includes/widgets/icon-list.php:126
#: includes/widgets/icon-list.php:217
msgid "Inline"
msgstr "Em linha"

#: includes/widgets/counter.php:210
msgid "Separator"
msgstr "Separador"

#: core/document-types/page-base.php:182
#: includes/template-library/sources/admin-menu-items/add-new-template-menu-item.php:23
#: modules/landing-pages/module.php:284 assets/js/app-packages.js:4592
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:8
msgid "Add New"
msgstr "Adicionar novo"

#: includes/template-library/sources/local.php:1376
msgid "Add templates and reuse them across your website. Easily export and import them to any other project, for an optimized workflow."
msgstr "Adicione modelos e reutilize-os em seu site. Exporte e importe facilmente para qualquer outro projeto, otimizando seu fluxo de trabalho."

#. translators: %s: Template type label.
#: includes/template-library/sources/local.php:1409
msgid "Create Your First %s"
msgstr "Crie seu(ua) primeiro(a) %s"

#: includes/template-library/sources/local.php:1281
msgid "All"
msgstr "Tudo"

#: includes/template-library/sources/local.php:227
msgctxt "Template Library"
msgid "My Templates"
msgstr "Meus modelos"

#: includes/admin-templates/new-template.php:115
msgid "Create Template"
msgstr "Criar modelo"

#: includes/admin-templates/new-floating-elements.php:47
#: includes/admin-templates/new-template.php:112
msgid "Enter template name (optional)"
msgstr "Digite o nome do modelo (opcional)"

#: includes/admin-templates/new-floating-elements.php:44
#: includes/admin-templates/new-template.php:109
msgid "Name your template"
msgstr "Dê um nome ao seu modelo"

#: includes/admin-templates/new-template.php:66
msgid "Select the type of template you want to work on"
msgstr "Selecione o tipo de modelo no qual você deseja trabalhar"

#: includes/admin-templates/new-template.php:64
msgid "Choose Template Type"
msgstr "Escolha o tipo de modelo"

#: includes/admin-templates/new-template.php:57
msgid "Use templates to create the different pieces of your site, and reuse them with one click whenever needed."
msgstr "Use modelos para criar as diferentes partes do seu site e reutilize-os com um clique sempre que necessário."

#: core/kits/documents/tabs/global-typography.php:182
#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:15
#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:19
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:244
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:235
msgid "Custom Fonts"
msgstr "Fontes personalizadas"

#: includes/editor-templates/templates.php:310
#: includes/editor-templates/templates.php:355
#: includes/editor-templates/templates.php:402
#: assets/js/packages/editor-global-classes/editor-global-classes.js:6
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:3
msgid "More actions"
msgstr "Mais ações"

#: includes/editor-templates/templates.php:152
msgid "Search Templates:"
msgstr "Pesquisar modelos:"

#: core/document-types/page.php:72 modules/library/documents/page.php:61
#: assets/js/app.js:11125 assets/js/editor.js:10055
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:2191
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:1
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:3
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:7
msgid "Pages"
msgstr "Páginas"

#: includes/editor-templates/global.php:120
msgid "This tag has no settings."
msgstr "Esta tag não tem configurações."

#. translators: %s: Document title.
#. translators: %s: Post type label.
#: core/base/document.php:1229 core/settings/page/model.php:127
#: includes/editor-templates/panel.php:80
#: assets/js/packages/editor-app-bar/editor-app-bar.js:2
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:32
msgid "%s Settings"
msgstr "Configurações do %s"

#: core/role-manager/role-manager.php:239
msgid "Want to give access only to content?"
msgstr "Quer dar acesso apenas ao conteúdo?"

#: core/role-manager/role-manager.php:157
msgid "No access to editor"
msgstr "Sem acesso ao editor"

#: core/role-manager/role-manager.php:150
msgid "Role Excluded"
msgstr "Função excluída"

#: core/role-manager/role-manager.php:114
msgid "Manage What Your Users Can Edit In Elementor"
msgstr "Gerencie o que seus usuários podem editar no Elementor"

#: core/common/modules/finder/categories/general.php:61
#: core/role-manager/role-manager-menu-item.php:28
#: core/role-manager/role-manager-menu-item.php:32
#: core/role-manager/role-manager.php:50
msgid "Role Manager"
msgstr "Gerenciador de funções"

#: core/dynamic-tags/tag.php:115 includes/settings/settings.php:389
msgid "Fallback"
msgstr "Alternativo"

#: core/document-types/page-base.php:230
msgid "Featured Image"
msgstr "Imagem destacada"

#: core/document-types/page-base.php:124
msgid "Body Style"
msgstr "Estilo do corpo"

#: core/base/document.php:259
msgid "Document"
msgstr "Documento"

#: core/common/modules/ajax/module.php:165
msgid "Action not found."
msgstr "Ação não encontrada."

#: core/common/modules/ajax/module.php:131
msgid "Token Expired."
msgstr "Token expirado."

#: includes/widgets/image-carousel.php:183
msgid "Set how many slides are scrolled per swipe."
msgstr "Defina quantos slides são rolados por deslizamento."

#: includes/controls/groups/background.php:448
msgid "Note: Attachment Fixed works only on desktop."
msgstr "Observação: A fixação de anexos funciona apenas no desktop."

#: core/admin/admin.php:477
msgid "Create New Post"
msgstr "Criar novo post"

#: includes/fonts.php:77
msgid "Google (Early Access)"
msgstr "Google (acesso antecipado)"

#: modules/history/revisions-manager.php:157
msgid "Current Version"
msgstr "Versão atual"

#: includes/widgets/video.php:446
msgid "When you turn on privacy mode, YouTube/Vimeo won't store information about visitors on your website unless they play the video."
msgstr "Quando você ativa o modo de privacidade, o YouTube/Vimeo não armazena informações sobre os visitantes do seu site, a menos que eles reproduzam o vídeo."

#: includes/controls/groups/background.php:597 includes/widgets/video.php:444
msgid "Privacy Mode"
msgstr "Modo de privacidade"

#: includes/widgets/shortcode.php:110
msgid "Enter your shortcode"
msgstr "Digite seu shortcode"

#: includes/widgets/image.php:178
msgid "Enter your image caption"
msgstr "Digite a legenda da sua imagem"

#: includes/widgets/html.php:107
msgid "Enter your code"
msgstr "Digite seu código"

#: includes/widgets/heading.php:170
msgid "Add Your Heading Text Here"
msgstr "Adicione o texto do seu título aqui"

#: includes/widgets/alert.php:150 includes/widgets/icon-box.php:187
#: includes/widgets/image-box.php:162
msgid "Enter your description"
msgstr "Digite sua descrição"

#: includes/widgets/alert.php:137
msgid "This is an Alert"
msgstr "Isso é um alerta"

#: includes/widgets/accordion.php:213 includes/widgets/toggle.php:216
#: modules/nested-tabs/widgets/nested-tabs.php:137
msgid "Active Icon"
msgstr "Ícone ativo"

#. translators: 1: Editing date, 2: Author display name.
#: core/base/document.php:1553
msgid "Last edited on %1$s by %2$s"
msgstr "Última edição em %1$s por %2$s"

#. translators: 1: Saving date, 2: Author display name.
#: core/base/document.php:1546
msgid "Draft saved on %1$s by %2$s"
msgstr "Rascunho salvo em %1$s por %2$s"

#: core/base/document.php:1540
msgctxt "revision date format"
msgid "M j, H:i"
msgstr "j \\d\\e M, H:i"

#. translators: %s: Document title.
#: core/base/document.php:200
msgid "Hurray! Your %s is live."
msgstr "Ótimo! Seu %s está ativo."

#: core/kits/documents/kit.php:155
#: modules/history/views/revisions-panel-template.php:64
msgid "Published"
msgstr "Publicado"

#: core/common/modules/finder/template.php:19 assets/js/editor.js:13071
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:1879
msgid "No Results Found"
msgstr "Nenhum resultado encontrado"

#: includes/editor-templates/templates.php:566 assets/js/app-packages.js:2528
#: assets/js/app.js:2962
msgid "Select File"
msgstr "Selecionar arquivo"

#: includes/editor-templates/templates.php:565
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:2180
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:2524
msgid "or"
msgstr "ou"

#: includes/editor-templates/templates.php:564
msgid "Drag & drop your .JSON or .zip template file"
msgstr "Arraste e solte seu arquivo de modelo .JSON ou .ZIP"

#: includes/editor-templates/templates.php:563
msgid "Import Template to Your Library"
msgstr "Importar modelo para sua biblioteca"

#: includes/widgets/traits/button-trait.php:57
#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:49
#: assets/js/app.js:6856 assets/js/app.js:7861
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:2536
msgid "Click here"
msgstr "Clique aqui"

#: includes/editor-templates/templates.php:256
msgid "Favorite"
msgstr "Favorito"

#: includes/editor-templates/templates.php:208
msgid "Creation Date"
msgstr "Data de criação"

#: includes/editor-templates/templates.php:204
msgid "Created By"
msgstr "Criado por"

#: includes/editor-templates/templates.php:153
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:55
msgid "Search"
msgstr "Pesquisar"

#: includes/editor-templates/templates.php:103
msgid "My Favorites"
msgstr "Meus favoritos"

#: includes/editor-templates/templates.php:81
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3811
msgid "Popular"
msgstr "Popular"

#: includes/editor-templates/templates.php:79
msgid "Trend"
msgstr "Tendência"

#: includes/editor-templates/templates.php:77
#: includes/editor-templates/templates.php:121
#: includes/editor-templates/templates.php:639
#: assets/js/atomic-widgets-editor.js:720 assets/js/editor.js:35576
#: assets/js/editor.js:36074 assets/js/editor.js:50372
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3807
msgid "New"
msgstr "Novo"

#: includes/editor-templates/templates.php:13
#: includes/editor-templates/templates.php:14
msgid "Import Template"
msgstr "Importar modelo"

#: core/kits/views/panel.php:40 includes/controls/icons.php:83
#: includes/controls/icons.php:85 includes/controls/media.php:215
#: includes/controls/media.php:217 includes/controls/media.php:279
#: includes/controls/media.php:281 includes/editor-templates/repeater.php:23
#: modules/promotions/widgets/pro-widget-promotion.php:74
#: assets/js/ai-admin.js:2311 assets/js/ai-admin.js:7489
#: assets/js/ai-gutenberg.js:4079 assets/js/ai-gutenberg.js:9337
#: assets/js/ai-layout.js:2970 assets/js/ai-media-library.js:3940
#: assets/js/ai-media-library.js:9118 assets/js/ai-unify-product-images.js:3940
#: assets/js/ai-unify-product-images.js:9118 assets/js/ai.js:4725
#: assets/js/ai.js:10583
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:919
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.strings.js:52
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:13
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:65
msgid "Remove"
msgstr "Remover"

#: includes/editor-templates/hotkeys.php:73
#: includes/editor-templates/repeater.php:18 assets/js/editor.js:32743
#: assets/js/editor.js:53857
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.strings.js:49
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:20
msgid "Duplicate"
msgstr "Duplicar"

#: includes/editor-templates/repeater.php:12
msgid "Drag & Drop"
msgstr "Arrastar e soltar"

#: includes/editor-templates/panel.php:151
#: includes/editor-templates/panel.php:153 assets/js/editor.js:38825
msgid "Hide Panel"
msgstr "Ocultar painel"

#: includes/editor-templates/panel.php:137
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:23
msgid "Save as Template"
msgstr "Salvar como modelo"

#: includes/editor-templates/panel.php:133
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:24
msgid "Save Draft"
msgstr "Salvar rascunho"

#: includes/editor-templates/panel.php:119
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:26
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:27
msgid "Save Options"
msgstr "Salvar opções"

#: core/base/document.php:173 includes/editor-templates/panel.php:114
#: assets/js/editor.js:27768
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:28
msgid "Publish"
msgstr "Publicar"

#: includes/editor-templates/panel.php:102
#: assets/js/packages/editor-app-bar/editor-app-bar.js:2
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:30
msgid "Preview Changes"
msgstr "Pré-visualizar alterações"

#: includes/editor-templates/panel-elements.php:74
msgid "Search Widget:"
msgstr "Pesquisar widget:"

#: core/experiments/manager.php:547 includes/controls/popover-toggle.php:71
#: includes/controls/popover-toggle.php:73
msgid "Back to default"
msgstr "Voltar ao padrão"

#: includes/controls/groups/typography.php:192
msgctxt "Typography Control"
msgid "Line Through"
msgstr "Linha através (tachado)"

#: includes/controls/groups/typography.php:191
msgctxt "Typography Control"
msgid "Overline"
msgstr "Sobrelinhado"

#: includes/controls/groups/typography.php:190
msgctxt "Typography Control"
msgid "Underline"
msgstr "Sublinhado"

#: includes/controls/groups/typography.php:185
msgctxt "Typography Control"
msgid "Decoration"
msgstr "Decoração"

#: includes/controls/dimensions.php:148
msgid "Unlinked values"
msgstr "Valores desvinculados"

#: core/admin/admin.php:617
msgid "Blog"
msgstr "Blog"

#: core/admin/admin.php:597
msgid "(opens in a new window)"
msgstr "(abre em uma nova janela)"

#: core/admin/admin.php:565
msgid "News & Updates"
msgstr "Notícias e atualizações"

#: core/admin/admin.php:532
msgctxt "Dashboard Overview Widget Recently Date"
msgid "M jS"
msgstr "j \\d\\e M"

#: core/admin/admin.php:524
msgid "Recently Edited"
msgstr "Editado recentemente"

#: core/admin/admin.php:474
msgid "Create New Page"
msgstr "Criar nova página"

#: core/admin/admin.php:428
msgid "Elementor Overview"
msgstr "Visão geral do Elementor"

#: core/document-types/page-base.php:215
msgid "Excerpt"
msgstr "Resumo"

#: includes/template-library/sources/local.php:496
#: includes/template-library/sources/local.php:612
#: includes/template-library/sources/local.php:762
#: includes/template-library/sources/local.php:771
msgid "Access denied."
msgstr "Acesso negado."

#: includes/settings/settings.php:288
msgid "Disable Default Fonts"
msgstr "Desativar fontes padrão"

#: includes/controls/groups/flex-container.php:105
#: includes/controls/groups/flex-container.php:141
#: includes/controls/groups/flex-container.php:218
#: includes/controls/groups/flex-item.php:63
#: includes/controls/groups/flex-item.php:89
#: includes/controls/groups/grid-container.php:143
#: includes/controls/groups/grid-container.php:171
#: includes/controls/groups/grid-container.php:200
#: includes/controls/groups/grid-container.php:240
#: includes/elements/column.php:218 includes/widgets/accordion.php:427
#: includes/widgets/common-base.php:416 includes/widgets/counter.php:292
#: includes/widgets/counter.php:326 includes/widgets/counter.php:400
#: includes/widgets/counter.php:436 includes/widgets/icon-list.php:585
#: includes/widgets/image-carousel.php:744 includes/widgets/rating.php:211
#: includes/widgets/tabs.php:217 includes/widgets/tabs.php:247
#: includes/widgets/toggle.php:451 includes/widgets/traits/button-trait.php:151
#: includes/widgets/traits/button-trait.php:295
#: modules/floating-buttons/base/widget-floating-bars-base.php:586
#: modules/floating-buttons/base/widget-floating-bars-base.php:1197
#: modules/floating-buttons/base/widget-floating-bars-base.php:1296
#: modules/nested-accordion/widgets/nested-accordion.php:181
#: modules/nested-accordion/widgets/nested-accordion.php:221
#: modules/nested-tabs/widgets/nested-tabs.php:242
#: modules/nested-tabs/widgets/nested-tabs.php:284
#: modules/nested-tabs/widgets/nested-tabs.php:354
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:90
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:162
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:198
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:203
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:208
msgid "End"
msgstr "Fim"

#: includes/controls/groups/flex-container.php:97
#: includes/controls/groups/flex-container.php:133
#: includes/controls/groups/flex-container.php:210
#: includes/controls/groups/flex-item.php:55
#: includes/controls/groups/flex-item.php:85
#: includes/controls/groups/grid-container.php:135
#: includes/controls/groups/grid-container.php:163
#: includes/controls/groups/grid-container.php:192
#: includes/controls/groups/grid-container.php:232
#: includes/elements/column.php:216 includes/widgets/accordion.php:423
#: includes/widgets/common-base.php:408 includes/widgets/counter.php:288
#: includes/widgets/counter.php:318 includes/widgets/counter.php:392
#: includes/widgets/counter.php:428 includes/widgets/icon-list.php:577
#: includes/widgets/image-carousel.php:736 includes/widgets/rating.php:203
#: includes/widgets/tabs.php:209 includes/widgets/tabs.php:239
#: includes/widgets/toggle.php:447 includes/widgets/traits/button-trait.php:147
#: includes/widgets/traits/button-trait.php:287
#: modules/floating-buttons/base/widget-floating-bars-base.php:582
#: modules/floating-buttons/base/widget-floating-bars-base.php:1189
#: modules/floating-buttons/base/widget-floating-bars-base.php:1292
#: modules/nested-accordion/widgets/nested-accordion.php:173
#: modules/nested-accordion/widgets/nested-accordion.php:217
#: modules/nested-tabs/widgets/nested-tabs.php:234
#: modules/nested-tabs/widgets/nested-tabs.php:276
#: modules/nested-tabs/widgets/nested-tabs.php:346
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:88
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:160
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:196
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:201
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:206
msgid "Start"
msgstr "Início"

#: core/debug/loading-inspection-manager.php:43
msgid "We’re sorry, but something went wrong. Click on 'Learn more' and follow each of the steps to quickly solve it."
msgstr "Algo deu errado. Clique em \"Saiba mais\" e siga cada uma das etapas para resolver o problema rapidamente."

#: core/debug/classes/inspection-base.php:25
#: core/debug/loading-inspection-manager.php:44
msgid "The preview could not be loaded"
msgstr "A pré-visualização não pôde ser carregada"

#: core/admin/admin-notices.php:145 core/admin/admin-notices.php:180
msgid "Update Notification"
msgstr "Notificação de atualização"

#. Author URI of the plugin
#: elementor.php
msgid "https://elementor.com/?utm_source=wp-plugins&utm_campaign=author-uri&utm_medium=wp-dash"
msgstr "https://elementor.com/?utm_source=wp-plugins&utm_campaign=author-uri&utm_medium=wp-dash"

#. Plugin URI of the plugin
#: elementor.php
msgid "https://elementor.com/?utm_source=wp-plugins&utm_campaign=plugin-uri&utm_medium=wp-dash"
msgstr "https://elementor.com/?utm_source=wp-plugins&utm_campaign=plugin-uri&utm_medium=wp-dash"

#: modules/history/views/history-panel-template.php:25
msgid "Once you start working, you'll be able to redo / undo any action you make in the editor."
msgstr "Quando começar a trabalhar, você poderá refazer/desfazer qualquer ação que fizer no editor."

#: modules/history/views/history-panel-template.php:24
msgid "No History Yet"
msgstr "Ainda não há histórico"

#: modules/history/views/history-panel-template.php:17
msgid "Switch to Revisions tab for older versions"
msgstr "Mude para a aba \"Revisões\" para versões anteriores"

#: modules/history/views/history-panel-template.php:10
#: assets/js/editor.js:53587
msgid "Revisions"
msgstr "Revisões"

#: includes/editor-templates/hotkeys.php:19
#: includes/editor-templates/templates.php:211
#: modules/history/views/history-panel-template.php:9 assets/js/editor.js:53584
msgid "Actions"
msgstr "Ações"

#: includes/editor-templates/hotkeys.php:144
#: includes/editor-templates/panel.php:96 assets/js/ai-admin.js:2060
#: assets/js/ai-gutenberg.js:3828 assets/js/ai-media-library.js:3689
#: assets/js/ai-unify-product-images.js:3689 assets/js/ai.js:4474
#: assets/js/editor.js:54179
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:17
msgid "History"
msgstr "Histórico"

#: core/kits/documents/tabs/settings-lightbox.php:152
#: includes/widgets/video.php:905
msgid "UI Hover Color"
msgstr "Cor da interface do usuário ao passar o mouse"

#: core/kits/documents/tabs/settings-lightbox.php:141
#: includes/widgets/video.php:893
msgid "UI Color"
msgstr "Cor da interface do usuário"

#: includes/widgets/video.php:366
msgid "Mute"
msgstr "Sem som"

#: includes/template-library/sources/local.php:1018
msgid "Choose an Elementor template JSON file or a .zip archive of Elementor templates, and add them to the list of templates available in your library."
msgstr "Escolha um arquivo JSON de modelo do Elementor ou um arquivo .zip de modelos do Elementor e adicione-os à lista de modelos disponíveis em sua biblioteca."

#: core/kits/documents/tabs/settings-lightbox.php:48
msgid "Open all image links in a lightbox popup window. The lightbox will automatically work on any link that leads to an image file."
msgstr "Abra todos os links de imagem em uma janela pop-up de lightbox. O lightbox funcionará automaticamente em qualquer link que direcione para um arquivo de imagem."

#: core/kits/documents/tabs/settings-lightbox.php:45
msgid "Image Lightbox"
msgstr "Lightbox de imagem"

#: includes/settings/tools.php:420
msgid "Please Note: We do not recommend updating to a beta version on production sites."
msgstr "Observação: Não recomendamos a atualização para uma versão beta em sites de produção."

#: includes/settings/tools.php:412
msgid "Beta Tester"
msgstr "Testador beta"

#: includes/settings/tools.php:399
msgid "Turn-on Beta Tester, to get notified when a new beta version of Elementor or Elementor Pro is available. The Beta version will not install automatically. You always have the option to ignore it."
msgstr "Ative o \"Testador beta\" para ser notificado quando uma nova versão beta do Elementor ou Elementor Pro estiver disponível. A versão beta não será instalada automaticamente. Você sempre terá a opção de ignorá-la."

#: includes/settings/tools.php:396
msgid "Become a Beta Tester"
msgstr "Torne-se um \"Testador beta\""

#: includes/settings/tools.php:389
msgid "Warning: Please backup your database before making the rollback."
msgstr "Atenção: Faça backup do seu banco de dados antes de fazer a reversão."

#: includes/settings/tools.php:381
msgid "Rollback Version"
msgstr "Reverter a versão"

#. translators: %s: Elementor version.
#: includes/settings/tools.php:372
msgid "Experiencing an issue with Elementor version %s? Rollback to a previous version before the issue appeared."
msgstr "Está tendo um problema com a versão %s do Elementor? Reverta para uma versão anterior ao aparecimento do problema."

#: core/common/modules/finder/categories/tools.php:77
#: includes/settings/tools.php:365
msgid "Version Control"
msgstr "Controle de versão"

#: includes/settings/settings.php:341
msgid "Switch Editor Loader Method"
msgstr "Alternar o método de carregamento do editor"

#: core/common/modules/finder/categories/settings.php:54
#: includes/settings/settings.php:304
#: assets/js/packages/editor-app-bar/editor-app-bar.js:2
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:5
msgid "Integrations"
msgstr "Integrações"

#: core/kits/documents/tabs/settings-layout.php:118
msgid "Sets the default space between widgets (Default: 20px)"
msgstr "Define o espaçamento padrão entre widgets (Padrão: 20px)"

#: includes/rollback.php:165 includes/settings/tools.php:193
#: includes/settings/tools.php:368 assets/js/admin.js:2252
msgid "Rollback to Previous Version"
msgstr "Reverter para a versão anterior"

#: includes/elements/column.php:906 includes/elements/container.php:1844
#: includes/elements/section.php:1353 includes/widgets/common-base.php:757
#: modules/floating-buttons/base/widget-contact-button-base.php:1415
#: modules/floating-buttons/base/widget-floating-bars-base.php:916
msgid "Animation Delay"
msgstr "Atraso da animação"

#: includes/elements/column.php:797 includes/elements/container.php:1748
#: includes/elements/section.php:1264 includes/widgets/common-base.php:663
msgid "Z-Index"
msgstr "Z-Index"

#: core/kits/documents/tabs/theme-style-form-fields.php:137
#: core/kits/documents/tabs/theme-style-images.php:203
#: includes/base/element-base.php:1264
#: includes/controls/groups/background.php:673 includes/elements/column.php:361
#: includes/elements/column.php:521 includes/elements/column.php:629
#: includes/elements/container.php:721 includes/elements/container.php:935
#: includes/elements/container.php:1095 includes/elements/section.php:617
#: includes/elements/section.php:804 includes/elements/section.php:911
#: includes/widgets/alert.php:446 includes/widgets/common-base.php:823
#: includes/widgets/common-base.php:938 includes/widgets/google-maps.php:252
#: includes/widgets/heading.php:385 includes/widgets/icon-box.php:475
#: includes/widgets/icon-box.php:710 includes/widgets/icon-list.php:471
#: includes/widgets/icon-list.php:698 includes/widgets/image-box.php:482
#: includes/widgets/image-box.php:610 includes/widgets/image.php:501
#: includes/widgets/text-editor.php:389
#: includes/widgets/traits/button-trait.php:450
#: modules/floating-buttons/base/widget-contact-button-base.php:1492
#: modules/floating-buttons/base/widget-contact-button-base.php:2231
#: modules/nested-tabs/widgets/nested-tabs.php:609
#: modules/shapes/widgets/text-path.php:460
#: modules/shapes/widgets/text-path.php:638
msgid "Transition Duration"
msgstr "Duração da transição"

#: core/kits/documents/tabs/settings-layout.php:101
#: includes/elements/column.php:237
msgid "Widgets Space"
msgstr "Espaço entre widgets"

#: includes/controls/url.php:116
msgid "Add nofollow"
msgstr "Adicionar nofollow (não seguir)"

#: includes/controls/url.php:112
msgid "Open in new window"
msgstr "Abrir em nova janela"

#: includes/controls/url.php:103
msgid "Link Options"
msgstr "Opções de link"

#: includes/controls/groups/box-shadow.php:72
msgctxt "Box Shadow Control"
msgid "Outline"
msgstr "Contorno"

#. Translators: %s: Element Name.
#. Translators: %s: Element name.
#. translators: %s: Element type title.
#: core/document-types/page-base.php:184 assets/js/atomic-widgets-editor.js:854
#: assets/js/editor.js:32538 assets/js/editor.js:32730
#: assets/js/editor.js:35154 assets/js/editor.js:35624
#: assets/js/editor.js:35725 assets/js/editor.js:36051
#: assets/js/editor.js:39419 assets/js/editor.js:50506
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:21
msgid "Edit %s"
msgstr "Editar %s"

#: includes/settings/settings.php:419
msgid "Internal Embedding"
msgstr "Incorporação interna"

#: includes/settings/settings.php:418
msgid "External File"
msgstr "Arquivo externo"

#: includes/settings/settings.php:412
msgid "CSS Print Method"
msgstr "Método de impressão CSS"

#: includes/settings/settings.php:349
msgid "For troubleshooting server configuration conflicts."
msgstr "Para solucionar conflitos de configuração do servidor."

#: core/debug/inspector.php:55 includes/settings/settings.php:347
#: includes/settings/settings.php:359 includes/settings/settings.php:370
#: includes/settings/settings.php:434 includes/settings/settings.php:451
#: includes/settings/settings.php:463 includes/settings/tools.php:418
#: modules/generator-tag/module.php:81
#: modules/nested-tabs/widgets/nested-tabs.php:382
#: modules/safe-mode/module.php:48 assets/js/admin.js:294
#: assets/js/ai-admin.js:64 assets/js/ai-gutenberg.js:1693
#: assets/js/ai-media-library.js:1693 assets/js/ai-unify-product-images.js:1693
#: assets/js/ai.js:1693 assets/js/app-packages.js:2833 assets/js/app.js:3267
#: assets/js/common.js:2146 assets/js/editor.js:42299
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:75
msgid "Enable"
msgstr "Ativar"

#: core/debug/inspector.php:54 includes/settings/settings.php:346
#: includes/settings/settings.php:358 includes/settings/settings.php:371
#: includes/settings/settings.php:435 includes/settings/settings.php:452
#: includes/settings/settings.php:464 includes/settings/tools.php:417
#: modules/element-cache/module.php:150 modules/generator-tag/module.php:82
#: modules/nested-tabs/widgets/nested-tabs.php:381
#: modules/safe-mode/module.php:47
msgid "Disable"
msgstr "Desativar"

#: core/base/document.php:2000 modules/ai/preferences.php:67
#: assets/js/element-manager-admin.js:2282
#: assets/js/element-manager-admin.js:2359
msgid "Status"
msgstr "Status"

#: includes/widgets/common-base.php:1196 includes/widgets/spacer.php:130
#: includes/widgets/text-editor.php:503
msgid "Space"
msgstr "Espaço"

#: includes/widgets/text-editor.php:148 includes/widgets/text-editor.php:410
msgid "Drop Cap"
msgstr "Letra inicial decorativa (capitular)"

#: core/kits/documents/tabs/settings-layout.php:143
msgid "Elementor lets you hide the page title. This works for themes that have \"h1.entry-title\" selector. If your theme's selector is different, please enter it above."
msgstr "O Elementor permite que você oculte o título da página. Isso funciona para temas que têm o seletor \"h1.entry-title\". Se o seletor do seu tema for diferente, digite-o acima."

#: core/kits/documents/tabs/settings-layout.php:139
msgid "Page Title Selector"
msgstr "Seletor do título da página"

#: includes/admin-templates/new-template.php:75
#: includes/settings/controls.php:155
msgid "Select"
msgstr "Selecionar"

#: includes/editor-templates/hotkeys.php:57
msgid "Paste Style"
msgstr "Colar estilo"

#: core/common/modules/finder/categories/edit.php:118 assets/js/editor.js:12712
#: assets/js/editor.js:21412
msgid "Template"
msgstr "Modelo"

#: core/document-types/page-base.php:96
msgid "Hide Title"
msgstr "Ocultar título"

#: core/settings/editor-preferences/model.php:108
msgid "Canvas"
msgstr "Tela"

#: includes/maintenance-mode.php:283
msgid "Maintenance Mode ON"
msgstr "Modo de manutenção ATIVADO"

#: includes/maintenance-mode.php:251
msgid "Choose Template"
msgstr "Escolher modelo"

#: includes/maintenance-mode.php:370
msgid "To enable maintenance mode you have to set a template for the maintenance mode page."
msgstr "Para ativar o modo \"Manutenção\", você deve definir um modelo para a página do modo de manutenção."

#: includes/maintenance-mode.php:292 includes/maintenance-mode.php:369
#: includes/template-library/sources/local.php:231 assets/js/app.js:9600
msgid "Edit Template"
msgstr "Editar modelo"

#: includes/maintenance-mode.php:243
msgid "Roles"
msgstr "Funções"

#: includes/maintenance-mode.php:237
msgid "Logged In"
msgstr "Conectado"

#: includes/maintenance-mode.php:231
msgid "Who Can Access"
msgstr "Quem pode acessar"

#: includes/maintenance-mode.php:226
msgid "Coming Soon returns HTTP 200 code, meaning the site is ready to be indexed."
msgstr "\"Em breve\" retorna o código HTTP 200, o que significa que o site está pronto para ser indexado."

#: includes/maintenance-mode.php:223
msgid "Maintenance Mode returns HTTP 503 code, so search engines know to come back a short time later. It is not recommended to use this mode for more than a couple of days."
msgstr "O \"Modo de manutenção\" retorna o código HTTP 503, para que os mecanismos de pesquisa saibam que devem voltar um pouco mais tarde. Não é recomendável usar esse modo por mais do que alguns dias."

#: includes/maintenance-mode.php:220
msgid "Choose between Coming Soon mode (returning HTTP 200 code) or Maintenance Mode (returning HTTP 503 code)."
msgstr "Escolha entre o modo \"Em breve\" (retornando o código HTTP 200) ou modo \"Manutenção\" (retornando o código HTTP 503)."

#: includes/maintenance-mode.php:217
msgid "Maintenance"
msgstr "Manutenção"

#: includes/maintenance-mode.php:216
msgid "Coming Soon"
msgstr "Em breve"

#: core/kits/documents/kit.php:154 includes/maintenance-mode.php:215
#: assets/js/editor.js:53856
msgid "Disabled"
msgstr "Desativado"

#: includes/maintenance-mode.php:210
msgid "Choose Mode"
msgstr "Escolher modo"

#: includes/maintenance-mode.php:206
msgid "Set your entire website as MAINTENANCE MODE, meaning the site is offline temporarily for maintenance, or set it as COMING SOON mode, meaning the site is offline until it is ready to be launched."
msgstr "Defina todo o seu site como MODO DE MANUTENÇÃO, o que significa que o site estará off-line temporariamente para manutenção, ou defina-o como modo EM BREVE, o que significa que o site estará off-line até que esteja pronto para ser lançado."

#: core/common/modules/finder/categories/tools.php:62
#: includes/maintenance-mode.php:201 includes/maintenance-mode.php:205
msgid "Maintenance Mode"
msgstr "Modo de manutenção"

#: includes/elements/container.php:1320 includes/elements/section.php:1115
msgid "Bring to Front"
msgstr "Trazer para frente"

#: core/kits/documents/tabs/settings-lightbox.php:18
#: includes/widgets/image-carousel.php:366
#: includes/widgets/image-gallery.php:200 includes/widgets/image.php:225
#: includes/widgets/video.php:721 includes/widgets/video.php:869
msgid "Lightbox"
msgstr "Lightbox"

#: includes/controls/groups/flex-container.php:109
#: includes/controls/groups/flex-container.php:222
#: includes/controls/groups/grid-container.php:204
#: includes/controls/groups/grid-container.php:244
#: includes/elements/column.php:191 includes/elements/column.php:219
#: includes/elements/section.php:428 includes/widgets/icon-list.php:238
#: includes/widgets/toggle.php:317
msgid "Space Between"
msgstr "Espaço entre"

#: includes/widgets/icon-list.php:230
msgid "List"
msgstr "Lista"

#: includes/shapes.php:212
msgctxt "Shapes"
msgid "Book"
msgstr "Livro"

#: includes/shapes.php:208
msgctxt "Shapes"
msgid "Split"
msgstr "Dividido"

#: includes/shapes.php:204
msgctxt "Shapes"
msgid "Arrow"
msgstr "Seta"

#: includes/shapes.php:200
msgctxt "Shapes"
msgid "Waves Pattern"
msgstr "Padrão de ondas"

#: includes/shapes.php:196
msgctxt "Shapes"
msgid "Waves Brush"
msgstr "Pincel de ondas"

#: includes/shapes.php:191
msgctxt "Shapes"
msgid "Waves"
msgstr "Ondas"

#: includes/shapes.php:186
msgctxt "Shapes"
msgid "Curve Asymmetrical"
msgstr "Curva assimétrica"

#: includes/shapes.php:182
msgctxt "Shapes"
msgid "Curve"
msgstr "Curva"

#: includes/shapes.php:179
msgctxt "Shapes"
msgid "Fan Opacity"
msgstr "Opacidade em leque"

#: includes/shapes.php:175
msgctxt "Shapes"
msgid "Tilt Opacity"
msgstr "Opacidade em inclinação"

#: includes/shapes.php:170
msgctxt "Shapes"
msgid "Tilt"
msgstr "Inclinação"

#: includes/shapes.php:165
msgctxt "Shapes"
msgid "Triangle Asymmetrical"
msgstr "Triângulo assimétrico"

#: includes/shapes.php:161
msgctxt "Shapes"
msgid "Triangle"
msgstr "Triângulo"

#: includes/shapes.php:156
msgctxt "Shapes"
msgid "Pyramids"
msgstr "Pirâmides"

#: includes/shapes.php:153 includes/widgets/divider.php:189
#: includes/widgets/divider.php:319
msgctxt "Shapes"
msgid "Zigzag"
msgstr "Zigue-zague"

#: includes/shapes.php:147
msgctxt "Shapes"
msgid "Clouds"
msgstr "Nuvens"

#: includes/shapes.php:141
msgctxt "Shapes"
msgid "Drops"
msgstr "Gotas"

#: includes/shapes.php:137
msgctxt "Shapes"
msgid "Mountains"
msgstr "Montanhas"

#: includes/elements/container.php:1307 includes/elements/section.php:1102
msgid "Invert"
msgstr "Inverter"

#: includes/elements/container.php:1293 includes/elements/section.php:1088
msgid "Flip"
msgstr "Girar"

#: includes/elements/container.php:1161 includes/elements/section.php:956
msgid "Shape Divider"
msgstr "Divisor de forma"

#: includes/widgets/tabs.php:275
msgid "Navigation Width"
msgstr "Largura da navegação"

#: core/kits/documents/tabs/theme-style-buttons.php:166
#: core/kits/documents/tabs/theme-style-images.php:134
#: core/kits/documents/tabs/theme-style-typography.php:148
#: includes/base/element-base.php:883 includes/elements/column.php:346
#: includes/elements/column.php:475 includes/elements/column.php:594
#: includes/elements/container.php:706 includes/elements/container.php:887
#: includes/elements/container.php:1048 includes/elements/section.php:602
#: includes/elements/section.php:758 includes/elements/section.php:876
#: includes/widgets/alert.php:429 includes/widgets/common-base.php:808
#: includes/widgets/common-base.php:903 includes/widgets/google-maps.php:237
#: includes/widgets/heading.php:367 includes/widgets/icon-box.php:433
#: includes/widgets/icon-box.php:687 includes/widgets/icon-list.php:451
#: includes/widgets/icon-list.php:679 includes/widgets/icon.php:256
#: includes/widgets/image-box.php:449 includes/widgets/image-box.php:587
#: includes/widgets/image.php:468 includes/widgets/text-editor.php:371
#: includes/widgets/traits/button-trait.php:392
#: modules/floating-buttons/base/widget-contact-button-base.php:1251
#: modules/floating-buttons/base/widget-contact-button-base.php:2010
#: modules/floating-buttons/base/widget-contact-button-base.php:2492
#: modules/floating-buttons/base/widget-contact-button-base.php:2672
#: modules/floating-buttons/base/widget-floating-bars-base.php:690
#: modules/floating-buttons/base/widget-floating-bars-base.php:1394
#: modules/nested-accordion/widgets/nested-accordion.php:663
#: modules/nested-accordion/widgets/nested-accordion.php:726
#: modules/nested-tabs/widgets/nested-tabs.php:545
#: modules/nested-tabs/widgets/nested-tabs.php:773
#: modules/nested-tabs/widgets/nested-tabs.php:954
#: modules/shapes/widgets/text-path.php:433
#: modules/shapes/widgets/text-path.php:572
msgid "Hover"
msgstr "Ao passar o mouse"

#: includes/elements/column.php:818 includes/elements/container.php:1769
#: includes/elements/section.php:1285 includes/widgets/common-base.php:683
#: includes/widgets/traits/button-trait.php:214
#: modules/floating-buttons/base/widget-contact-button-base.php:3097
#: modules/floating-buttons/base/widget-floating-bars-base.php:1505
#: modules/nested-accordion/widgets/nested-accordion.php:138
#: modules/nested-tabs/widgets/nested-tabs.php:160
msgid "Add your custom id WITHOUT the Pound key. e.g: my-id"
msgstr "Adicione seu ID personalizado SEM o símbolo \"#\" - por exemplo: meu-id"

#: includes/elements/column.php:809 includes/elements/container.php:1760
#: includes/elements/section.php:1276 includes/widgets/common-base.php:674
#: modules/floating-buttons/base/widget-contact-button-base.php:3088
#: modules/floating-buttons/base/widget-floating-bars-base.php:1496
#: modules/nested-accordion/widgets/nested-accordion.php:129
#: modules/nested-tabs/widgets/nested-tabs.php:151
msgid "CSS ID"
msgstr "ID CSS"

#: includes/controls/groups/background.php:230
msgctxt "Background Control"
msgid "Type"
msgstr "Tipo"

#: includes/controls/groups/background.php:184
#: includes/controls/groups/background.php:213
msgctxt "Background Control"
msgid "Location"
msgstr "Localização"

#: includes/settings/settings.php:292
msgid "Checking this box will disable Elementor's Default Fonts, and make Elementor inherit the fonts from your theme."
msgstr "Marcar esta caixa desativará as fontes padrão do Elementor e fará com que o Elementor herde as fontes do seu tema."

#: includes/settings/settings.php:284
msgid "Checking this box will disable Elementor's Default Colors, and make Elementor inherit the colors from your theme."
msgstr "Marcar esta caixa desativará as cores padrão do Elementor e fará com que o Elementor herde as cores do seu tema."

#: core/admin/admin.php:378
msgid "Video Tutorials"
msgstr "Tutoriais em vídeo"

#: core/admin/admin.php:378
msgid "View Elementor Video Tutorials"
msgstr "Ver tutoriais em vídeo do Elementor"

#: core/admin/admin.php:377
msgid "Docs & FAQs"
msgstr "Documentação e perguntas frequentes"

#: core/admin/admin.php:377
msgid "View Elementor Documentation"
msgstr "Ver a documentação do Elementor"

#: includes/settings/tools.php:356
msgid "Enter your old and new URLs for your WordPress installation, to update all Elementor data (Relevant for domain transfers or move to 'HTTPS')."
msgstr "Digite os URLs antigos e novos de sua instalação do WordPress para atualizar todos os dados do Elementor (relevante para transferências de domínio ou mudança para \"HTTPS\")."

#: includes/settings/tools.php:352
msgid "Update Site Address (URL)"
msgstr "Atualizar o endereço do site (URL)"

#: core/common/modules/finder/categories/tools.php:56
#: includes/settings/tools.php:334 includes/settings/tools.php:338
#: includes/settings/tools.php:355
msgid "Replace URL"
msgstr "Substituir URL"

#. translators: 1: Minimum recommended_memory, 2: Preferred memory, 3:
#. WordPress wp-config memory documentation.
#: modules/system-info/reporters/server.php:170
msgid "We recommend setting memory to at least %1$s. (%2$s or higher is preferred) For more information, read about <a href=\"%3$s\">how to increase memory allocated to PHP</a>."
msgstr "Recomendamos definir a memória para pelo menos %1$s. (%2$s ou mais é preferível). Para mais informações, leia sobre <a href=\"%3$s\">como aumentar a memória alocada para o PHP</a>."

#. translators: 1: Human readable time difference, 2: Date.
#: modules/history/revisions-manager.php:179
msgid "%1$s ago (%2$s)"
msgstr "%1$s atrás (%2$s)"

#: modules/history/revisions-manager.php:151
msgctxt "revision date format"
msgid "M j @ H:i"
msgstr "j \\d\\e M à\\s H:i"

#: modules/history/revisions-manager.php:160
msgid "Autosave"
msgstr "Salvamento automático"

#: modules/history/revisions-manager.php:163
msgid "Revision"
msgstr "Revisão"

#: modules/history/views/revisions-panel-template.php:28
msgid "It looks like the post revision feature is unavailable in your website."
msgstr "Parece que o recurso de revisão de posts não está disponível em seu site."

#: modules/history/views/revisions-panel-template.php:27
msgid "Start designing your page and you will be able to see the entire revision history here."
msgstr "Comece a projetar sua página e você poderá ver todo o histórico de revisões aqui."

#: modules/history/views/revisions-panel-template.php:26
msgid "Revision history lets you save your previous versions of your work, and restore them any time."
msgstr "O histórico de revisões permite que você salve as versões anteriores do seu trabalho e as restaure a qualquer momento."

#: modules/apps/admin-apps-page.php:177
#: modules/history/views/revisions-panel-template.php:55
msgid "By"
msgstr "Por"

#: modules/history/views/revisions-panel-template.php:39
msgid "No Revisions Saved Yet"
msgstr "Ainda não há revisões salvas"

#: includes/widgets/counter.php:199
msgid "Thousand Separator"
msgstr "Separador de milhar"

#: includes/managers/controls.php:1081
msgid "Custom CSS lets you add CSS code to any widget, and see it render live right in the editor."
msgstr "O CSS personalizado permite que você adicione código CSS a qualquer widget e veja sua renderização ao vivo diretamente no editor."

#: includes/managers/controls.php:1093
msgid "Meet Our Custom CSS"
msgstr "Conheça nosso CSS personalizado"

#: core/kits/documents/tabs/settings-custom-css.php:17
#: includes/managers/controls.php:1075
msgid "Custom CSS"
msgstr "CSS personalizado"

#: includes/editor-templates/panel-elements.php:100
msgid "With this feature, you can save a widget as global, then add it to multiple areas. All areas will be editable from one single place."
msgstr "Com este recurso, você pode salvar um widget como global e adicioná-lo a várias áreas. Todas as áreas podem ser editadas em um único local."

#: includes/editor-templates/panel-elements.php:99
msgid "Meet Our Global Widget"
msgstr "Conheça nosso widget global"

#: modules/promotions/widgets/pro-widget-promotion.php:75
#: assets/js/ai-admin.js:7932 assets/js/ai-gutenberg.js:9780
#: assets/js/ai-layout.js:3413 assets/js/ai-media-library.js:9561
#: assets/js/ai-unify-product-images.js:9561 assets/js/ai.js:11026
msgid "Go Pro"
msgstr "Torne-se Pro"

#: includes/editor-templates/panel-elements.php:28
msgid "Get more with Elementor Pro"
msgstr "Tenha mais recursos com o Elementor Pro"

#: includes/base/element-base.php:927 includes/base/element-base.php:1091
#: includes/widgets/common-base.php:982 includes/widgets/icon-list.php:285
#: includes/widgets/icon.php:329 includes/widgets/text-editor.php:150
#: includes/widgets/video.php:724 modules/shapes/widgets/text-path.php:220
msgid "Off"
msgstr "Desativado"

#: includes/base/element-base.php:926 includes/base/element-base.php:1090
#: includes/widgets/common-base.php:981 includes/widgets/icon-list.php:286
#: includes/widgets/icon.php:330 includes/widgets/text-editor.php:151
#: includes/widgets/video.php:725 modules/shapes/widgets/text-path.php:219
msgid "On"
msgstr "Ativado"

#: includes/widgets/traits/button-trait.php:38
msgid "Extra Large"
msgstr "Extra grande"

#: includes/widgets/traits/button-trait.php:34
msgid "Extra Small"
msgstr "Extra pequeno"

#: includes/settings/settings.php:298
msgid "Improve Elementor"
msgstr "Melhorar o Elementor"

#: includes/frontend.php:1257
msgid "Invalid Data: The Template ID cannot be the same as the currently edited template. Please choose a different one."
msgstr "Dados inválidos: O ID do modelo não pode ser o mesmo do modelo editado atualmente. Escolha um modelo diferente."

#: includes/base/widget-base.php:312 includes/base/widget-base.php:321
msgid "Skin"
msgstr "Aparência"

#: includes/editor-templates/panel.php:209
msgid "%s are disabled"
msgstr "%s estão desativados"

#: includes/editor-templates/panel.php:173
msgid "Update changes to page"
msgstr "Atualizar alterações na página"

#: core/admin/admin-notices.php:241
msgid "No thanks"
msgstr "Não"

#: core/kits/documents/tabs/settings-layout.php:161
msgid "Enter parent element selector to which stretched sections will fit to (e.g. #primary / .wrapper / main etc). Leave blank to fit to page width."
msgstr "Digite o seletor do elemento principal (ascendente) ao qual as seções esticadas se ajustarão (ex: #principal / .wrapper / main etc.). Deixe em branco para ajustar à largura da página."

#: core/kits/documents/tabs/settings-layout.php:158
msgid "Stretched Section Fit To"
msgstr "Seção esticada ajustada para"

#: core/kits/documents/tabs/settings-layout.php:74
msgid "Sets the default width of the content area (Default: 1140px)"
msgstr "Define a largura padrão da área de conteúdo (padrão: 1140px)"

#: includes/elements/section.php:472
msgid "Stretch the section to the full width of the page using JS."
msgstr "Estique a seção até a largura total da página usando JS."

#: includes/elements/section.php:464
msgid "Stretch Section"
msgstr "Esticar seção"

#: core/admin/admin-notices.php:229
msgid "Learn more."
msgstr "Saiba mais."

#: includes/elements/section.php:1384
msgid "Reverse Columns"
msgstr "Inverter colunas"

#: core/settings/editor-preferences/model.php:124
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:4354
msgid "Mobile"
msgstr "Dispositivos móveis"

#: includes/controls/dimensions.php:141 includes/controls/dimensions.php:144
msgid "Link values together"
msgstr "Vincular valores entre si"

#: includes/widgets/shortcode.php:42 includes/widgets/shortcode.php:103
msgid "Shortcode"
msgstr "Shortcode"

#: includes/template-library/sources/remote.php:61
msgid "Remote"
msgstr "Remoto"

#: includes/template-library/sources/local.php:1025
msgid "Import Now"
msgstr "Importar agora"

#: includes/template-library/sources/local.php:1016
msgid "Import Templates"
msgstr "Importar modelos"

#: includes/template-library/sources/local.php:988
msgid "Export Template"
msgstr "Exportar modelo"

#: includes/template-library/sources/cloud.php:106
#: includes/template-library/sources/cloud.php:327
#: includes/template-library/sources/local.php:500
msgid "(no title)"
msgstr "(sem título)"

#: includes/template-library/sources/local.php:280
msgctxt "Template Library"
msgid "Type"
msgstr "Tipo"

#: includes/template-library/sources/local.php:228
msgctxt "Template Library"
msgid "Template"
msgstr "Modelo"

#: includes/template-library/sources/local.php:209
msgid "Local"
msgstr "Local"

#: includes/settings/tools.php:326
msgid "Elementor Library automatically updates on a daily basis. You can also manually update it by clicking on the sync button."
msgstr "A biblioteca do Elementor é atualizada automaticamente diariamente. Você também pode atualizá-la manualmente clicando no botão de sincronização."

#: includes/editor-templates/templates.php:18
#: includes/editor-templates/templates.php:19 includes/settings/tools.php:322
#: includes/settings/tools.php:325
msgid "Sync Library"
msgstr "Sincronizar biblioteca"

#: core/common/modules/finder/categories/tools.php:28
#: core/common/modules/finder/categories/tools.php:50
#: includes/settings/admin-menu-items/tools-menu-item.php:29
#: includes/settings/admin-menu-items/tools-menu-item.php:33
#: includes/settings/tools.php:32 includes/settings/tools.php:33
#: includes/settings/tools.php:453
msgid "Tools"
msgstr "Ferramentas"

#: core/document-types/page.php:58 modules/library/documents/page.php:57
#: assets/js/editor.js:10574
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:2
msgid "Page"
msgstr "Página"

#: includes/editor-templates/templates.php:484
msgid "Enter Template Name"
msgstr "Digite o nome do modelo"

#: includes/editor-templates/templates.php:328
#: includes/editor-templates/templates.php:361
#: includes/editor-templates/templates.php:418
#: includes/template-library/sources/local.php:1203 assets/js/app.js:11321
msgid "Export"
msgstr "Exportar"

#: includes/editor-templates/templates.php:221
msgid "Stay tuned! More awesome templates coming real soon."
msgstr "Fique ligado! Mais modelos incríveis chegando em breve."

#: includes/editor-templates/templates.php:41
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:1473
msgid "Back to Library"
msgstr "Voltar à biblioteca"

#: includes/editor-templates/templates.php:305
#: includes/editor-templates/templates.php:393
#: includes/editor-templates/templates.php:439
#: includes/editor-templates/templates.php:453 assets/js/ai-admin.js:6721
#: assets/js/ai-gutenberg.js:8569 assets/js/ai-media-library.js:8350
#: assets/js/ai-unify-product-images.js:8350 assets/js/ai.js:9815
#: assets/js/editor.js:8516
msgid "Insert"
msgstr "Inserir"

#: core/common/modules/connect/apps/library.php:16 assets/js/editor.js:11713
msgid "Library"
msgstr "Biblioteca"

#: core/base/document.php:172 includes/editor-templates/global.php:21
#: includes/editor-templates/responsive-bar.php:65 includes/frontend.php:1417
#: assets/js/ai-admin.js:588 assets/js/ai-gutenberg.js:2356
#: assets/js/ai-layout.js:420 assets/js/ai-media-library.js:2217
#: assets/js/ai-unify-product-images.js:2217 assets/js/ai.js:3002
#: assets/js/app-packages.js:2027 assets/js/app-packages.js:3994
#: assets/js/app-packages.js:4513 assets/js/app.js:2216 assets/js/app.js:4329
#: assets/js/app.js:4732 assets/js/app.js:6840 assets/js/app.js:7671
#: assets/js/app.js:11222 assets/js/cf70912a0f34653ad242.bundle.js:211
#: assets/js/cf70912a0f34653ad242.bundle.js:212 assets/js/editor.js:49772
#: assets/js/import-export-admin.js:313
msgid "Close"
msgstr "Fechar"

#: core/common/modules/finder/categories/general.php:49
#: includes/template-library/sources/admin-menu-items/saved-templates-menu-item.php:23
#: includes/template-library/sources/local.php:1731 assets/js/app.js:10342
msgid "Saved Templates"
msgstr "Modelos salvos"

#: includes/editor-templates/hotkeys.php:181
#: includes/editor-templates/templates.php:554
#: includes/editor-templates/templates.php:570
#: includes/editor-templates/templates.php:584
msgid "Template Library"
msgstr "Biblioteca de modelos"

#: includes/editor-templates/global.php:45
msgid "Add Template"
msgstr "Adicionar modelo"

#. translators: %s: WordPress child themes documentation.
#: modules/system-info/reporters/theme.php:207
msgid "If you want to modify the source code of your theme, we recommend using a <a href=\"%s\">child theme</a>."
msgstr "Se quiser modificar o código-fonte do seu tema, recomendamos o uso de um <a href=\"%s\">tema filho (descendente)</a>."

#: app/modules/import-export/module.php:152
#: app/modules/import-export/module.php:164 core/admin/admin-notices.php:328
#: modules/apps/admin-apps-page.php:187 modules/safe-mode/module.php:359
#: modules/safe-mode/module.php:368
#: modules/safe-mode/mu-plugin/elementor-safe-mode.php:105
#: assets/js/app-packages.js:2706 assets/js/app-packages.js:5662
#: assets/js/app-packages.js:5770 assets/js/app.js:3140 assets/js/app.js:7124
#: assets/js/app.js:8265 assets/js/app.js:9762 assets/js/app.js:10071
#: assets/js/app.js:10117 assets/js/app.js:11221 assets/js/editor.js:17108
#: assets/js/editor.js:30635 assets/js/editor.js:30666
#: assets/js/editor.js:43038 assets/js/element-manager-admin.js:2169
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3842
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:17
msgid "Learn More"
msgstr "Saiba mais"

#: core/kits/documents/tabs/global-typography.php:28
#: core/kits/documents/tabs/global-typography.php:47 assets/js/app.js:10360
#: assets/js/editor.js:49523
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:222
msgid "Global Fonts"
msgstr "Fontes globais"

#: core/base/traits/shared-widget-controls-trait.php:289
#: includes/widgets/icon-box.php:490 includes/widgets/icon.php:294
#: includes/widgets/image-box.php:503 includes/widgets/image.php:519
#: includes/widgets/social-icons.php:577
#: includes/widgets/traits/button-trait.php:465
#: modules/floating-buttons/base/widget-contact-button-base.php:1474
#: modules/floating-buttons/base/widget-contact-button-base.php:2510
#: modules/floating-buttons/base/widget-floating-bars-base.php:748
#: modules/nested-tabs/widgets/nested-tabs.php:601
#: modules/shapes/widgets/text-path.php:452
msgid "Hover Animation"
msgstr "Animação ao passar o mouse"

#: modules/floating-buttons/base/widget-contact-button-base.php:2214
#: assets/js/ai-admin.js:3607 assets/js/ai-gutenberg.js:5375
#: assets/js/ai-media-library.js:5236 assets/js/ai-unify-product-images.js:5236
#: assets/js/ai.js:6054
msgid "Animation"
msgstr "Animação"

#: includes/elements/column.php:894 includes/elements/container.php:1832
#: includes/elements/section.php:1341 includes/widgets/common-base.php:745
#: modules/floating-buttons/base/widget-contact-button-base.php:1406
#: modules/floating-buttons/base/widget-contact-button-base.php:2789
#: modules/floating-buttons/base/widget-floating-bars-base.php:889
msgid "Fast"
msgstr "Rápido"

#: includes/elements/column.php:892 includes/elements/container.php:1830
#: includes/elements/section.php:1339 includes/widgets/common-base.php:743
#: modules/floating-buttons/base/widget-contact-button-base.php:1404
#: modules/floating-buttons/base/widget-contact-button-base.php:2787
#: modules/floating-buttons/base/widget-floating-bars-base.php:887
msgid "Slow"
msgstr "Lento"

#: includes/settings/settings.php:280
msgid "Disable Default Colors"
msgstr "Desativar cores padrão"

#: includes/elements/column.php:879 includes/elements/container.php:1817
#: includes/elements/section.php:1326 includes/widgets/common-base.php:730
#: includes/widgets/video.php:917
#: modules/floating-buttons/base/widget-contact-button-base.php:1390
#: modules/floating-buttons/base/widget-floating-bars-base.php:873
msgid "Entrance Animation"
msgstr "Animação de entrada"

#: includes/controls/groups/box-shadow.php:73
msgctxt "Box Shadow Control"
msgid "Inset"
msgstr "Interno"

#: includes/controls/box-shadow.php:73 includes/controls/text-shadow.php:76
#: includes/widgets/tabs.php:189
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.strings.js:43
msgid "Vertical"
msgstr "Vertical"

#: includes/controls/box-shadow.php:68 includes/controls/text-shadow.php:71
#: includes/widgets/tabs.php:193
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.strings.js:42
msgid "Horizontal"
msgstr "Horizontal"

#: includes/controls/box-shadow.php:83
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.strings.js:45
msgid "Spread"
msgstr "Espalhar"

#: includes/controls/box-shadow.php:78 includes/controls/text-shadow.php:66
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.strings.js:44
msgid "Blur"
msgstr "Desfocar"

#: includes/widgets/testimonial.php:201
msgid "Aside"
msgstr "Nota"

#: includes/widgets/testimonial.php:46 includes/widgets/testimonial.php:111
msgid "Testimonial"
msgstr "Depoimento"

#: core/kits/documents/tabs/global-colors.php:123
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:185
msgid "Custom Colors"
msgstr "Cores personalizadas"

#: includes/widgets/social-icons.php:212 includes/widgets/social-icons.php:365
msgid "Official Color"
msgstr "Cor oficial"

#: includes/widgets/icon-box.php:152 includes/widgets/icon.php:150
#: includes/widgets/social-icons.php:285
#: modules/floating-buttons/base/widget-contact-button-base.php:2084
#: modules/floating-buttons/base/widget-contact-button-base.php:2175
#: modules/floating-buttons/base/widget-contact-button-base.php:2868
#: modules/floating-buttons/base/widget-floating-bars-base.php:845
#: modules/link-in-bio/base/widget-link-in-bio-base.php:119
msgid "Rounded"
msgstr "Arredondado"

#: includes/widgets/social-icons.php:42 includes/widgets/social-icons.php:107
#: includes/widgets/social-icons.php:250
msgid "Social Icons"
msgstr "Ícones sociais"

#: includes/widgets/progress.php:123
msgid "My Skill"
msgstr "Minha habilidade"

#: includes/widgets/audio.php:240
#: modules/floating-buttons/base/widget-contact-button-base.php:358
#: modules/floating-buttons/base/widget-contact-button-base.php:909
#: modules/link-in-bio/base/widget-link-in-bio-base.php:521
#: modules/link-in-bio/base/widget-link-in-bio-base.php:774
msgid "Username"
msgstr "Nome de usuário"

#: includes/widgets/audio.php:229
msgid "Play Counts"
msgstr "Contagem de reproduções"

#: includes/widgets/audio.php:218
msgid "Comments"
msgstr "Comentários"

#: includes/widgets/audio.php:207
msgid "Share Button"
msgstr "Botão \"Compartilhar\""

#: includes/widgets/audio.php:182 includes/widgets/video.php:561
msgid "Download Button"
msgstr "Botão \"Baixar\""

#: includes/widgets/audio.php:171
msgid "Like Button"
msgstr "Botão \"Curtir\""

#: includes/widgets/audio.php:160
msgid "Buy Button"
msgstr "Botão \"Comprar\""

#: includes/widgets/audio.php:130
msgid "Visual Player"
msgstr "Reprodutor visual"

#: core/base/providers/social-network-provider.php:168
#: includes/widgets/audio.php:53 includes/widgets/audio.php:104
msgid "SoundCloud"
msgstr "SoundCloud"

#: includes/elements/column.php:388 includes/elements/container.php:760
#: includes/elements/section.php:644
#: modules/floating-buttons/base/widget-floating-bars-base.php:981
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1480
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:83
msgid "Background Overlay"
msgstr "Sobreposição de fundo"

#: includes/elements/section.php:290
msgid "Extended"
msgstr "Estendido"

#: core/admin/feedback.php:127
msgid "If you have a moment, please share why you are deactivating Elementor:"
msgstr "Se tiver um momento, compartilhe por que está desativando o Elementor:"

#: core/admin/feedback.php:119
msgid "Quick Feedback"
msgstr "Feedback rápido"

#: core/admin/feedback.php:111
msgid "Please share the reason"
msgstr "Compartilhe o motivo"

#: core/admin/feedback.php:110
msgid "Other"
msgstr "Outro"

#: core/admin/feedback.php:101
msgid "It's a temporary deactivation"
msgstr "É uma desativação temporária"

#: core/admin/feedback.php:97
msgid "I couldn't get the plugin to work"
msgstr "Não consegui fazer o plugin funcionar"

#: core/admin/feedback.php:94
msgid "Please share which plugin"
msgstr "Compartilhe qual plugin"

#: core/admin/feedback.php:93
msgid "I found a better plugin"
msgstr "Encontrei um plugin melhor"

#: core/admin/feedback.php:89
msgid "I no longer need the plugin"
msgstr "Não preciso mais do plugin"

#: core/admin/admin-notices.php:141 core/admin/admin-notices.php:149
#: core/base/db-upgrades-manager.php:97
msgid "Update Now"
msgstr "Atualizar agora"

#. translators: %s: Elementor version.
#: core/admin/admin-notices.php:136
msgid "View Elementor version %s details"
msgstr "Ver detalhes da versão %s do Elementor"

#. translators: 1: Details URL, 2: Accessibility text, 3: Version number, 4:
#. Update URL, 5: Accessibility text.
#: core/admin/admin-notices.php:132
msgid "There is a new version of Elementor Page Builder available. <a href=\"%1$s\" class=\"thickbox open-plugin-details-modal\" aria-label=\"%2$s\">View version %3$s details</a> or <a href=\"%4$s\" class=\"update-link\" aria-label=\"%5$s\">update now</a>."
msgstr "Há uma nova versão disponível do Elementor Page Builder. <a href=\"%1$s\" class=\"thickbox open-plugin-details-modal\" aria-label=\"%2$s\">Ver detalhes da versão %3$s</a> ou <a href=\"%4$s\" class=\"update-link\" aria-label=\"%5$s\">atualizar agora</a>."

#: includes/widgets/image-carousel.php:343 includes/widgets/image.php:198
msgid "Custom URL"
msgstr "URL personalizado"

#: includes/editor-templates/hotkeys.php:49 assets/js/editor.js:32770
#: assets/js/editor.js:35083 assets/js/editor.js:44460
#: assets/js/editor.js:45498
msgid "Paste"
msgstr "Colar"

#: core/kits/documents/tabs/settings-background.php:18
#: includes/elements/column.php:276 includes/elements/container.php:631
#: includes/elements/section.php:533 includes/widgets/accordion.php:326
#: includes/widgets/accordion.php:497 includes/widgets/common-base.php:781
#: includes/widgets/toggle.php:358 includes/widgets/toggle.php:521
#: modules/floating-buttons/base/widget-contact-button-base.php:1676
#: modules/floating-buttons/base/widget-floating-bars-base.php:952
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1461
#: assets/js/ai-admin.js:11330 assets/js/ai-gutenberg.js:13178
#: assets/js/ai-media-library.js:12959
#: assets/js/ai-unify-product-images.js:12959 assets/js/ai.js:14424
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:17
msgid "Background"
msgstr "Plano de fundo"

#: includes/elements/section.php:292
msgid "Wider"
msgstr "Mais amplo"

#: core/common/modules/finder/categories/general.php:29
#: core/role-manager/role-manager.php:69 includes/managers/elements.php:302
#: includes/settings/settings.php:252 includes/settings/settings.php:255
#: includes/settings/tools.php:309
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:22
msgid "General"
msgstr "Geral"

#: includes/widgets/video.php:535
msgid "Intro Byline"
msgstr "Linha de introdução"

#: includes/widgets/accordion.php:463 includes/widgets/divider.php:790
#: includes/widgets/divider.php:957 includes/widgets/image-carousel.php:760
#: includes/widgets/image-carousel.php:894
#: includes/widgets/image-gallery.php:440 includes/widgets/image.php:652
#: includes/widgets/rating.php:85 includes/widgets/social-icons.php:457
#: includes/widgets/star-rating.php:352 includes/widgets/toggle.php:487
#: modules/nested-accordion/widgets/nested-accordion.php:627
#: modules/nested-tabs/widgets/nested-tabs.php:915
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:13
msgid "Spacing"
msgstr "Espaçamento"

#: includes/widgets/image-carousel.php:577
#: includes/widgets/image-carousel.php:640
msgid "Outside"
msgstr "Fora"

#: includes/widgets/image-carousel.php:576
#: includes/widgets/image-carousel.php:641
msgid "Inside"
msgstr "Dentro"

#: includes/controls/groups/background.php:749
#: includes/controls/groups/flex-container.php:29
#: includes/widgets/image-carousel.php:534
#: modules/nested-tabs/widgets/nested-tabs.php:191
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:81
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:184
msgid "Direction"
msgstr "Direção"

#: includes/widgets/image-carousel.php:523
msgid "Animation Speed"
msgstr "Velocidade da animação"

#: includes/elements/container.php:535 includes/widgets/audio.php:143
#: includes/widgets/image-carousel.php:416
msgid "Additional Options"
msgstr "Opções adicionais"

#: includes/widgets/image-carousel.php:215
msgid "Arrows and Dots"
msgstr "Setas e pontos"

#: includes/widgets/image-carousel.php:198
msgid "Image Stretch"
msgstr "Esticar imagem"

#: includes/widgets/image-carousel.php:46
#: includes/widgets/image-carousel.php:125
#: includes/widgets/image-carousel.php:134
msgid "Image Carousel"
msgstr "Carrossel de imagens"

#: includes/elements/column.php:888 includes/elements/container.php:1826
#: includes/elements/section.php:1335 includes/widgets/common-base.php:739
#: includes/widgets/counter.php:188
#: modules/floating-buttons/base/widget-contact-button-base.php:1400
#: modules/floating-buttons/base/widget-contact-button-base.php:2783
#: modules/floating-buttons/base/widget-floating-bars-base.php:883
#: modules/nested-accordion/widgets/nested-accordion.php:367
msgid "Animation Duration"
msgstr "Duração da animação"

#: includes/widgets/alert.php:240
msgid "Left Border Width"
msgstr "Largura da borda esquerda"

#: includes/controls/groups/image-size.php:303
msgid "You can crop the original image size to any custom size. You can also set a single value for height or width in order to keep the original size ratio."
msgstr "Você pode cortar o tamanho da imagem original para qualquer tamanho personalizado. Também é possível definir um único valor para altura ou largura para manter a proporção do tamanho original."

#: includes/widgets/audio.php:251 includes/widgets/video.php:549
msgid "Controls Color"
msgstr "Cor dos controles"

#: includes/widgets/video.php:521
msgid "Intro Portrait"
msgstr "Imagem de introdução"

#: includes/widgets/video.php:507
msgid "Intro Title"
msgstr "Título da introdução"

#: includes/widgets/video.php:375
msgid "Loop"
msgstr "Repetir"

#: includes/widgets/video.php:330
msgid "Video Options"
msgstr "Opções de vídeo"

#: includes/controls/groups/background.php:525
msgid "Video Link"
msgstr "Link do vídeo"

#: core/base/providers/social-network-provider.php:186
#: includes/widgets/video.php:138
msgid "Vimeo"
msgstr "Vimeo"

#: includes/controls/groups/background.php:103 includes/widgets/video.php:45
#: includes/widgets/video.php:126 includes/widgets/video.php:739
msgid "Video"
msgstr "Vídeo"

#: includes/controls/image-dimensions.php:81
msgid "The server does not have ImageMagick or GD installed and/or enabled! Any of these libraries are required for WordPress to be able to resize images. Please contact your server administrator to enable this before continuing."
msgstr "O servidor não tem o ImageMagick ou o GD instalados e/ou ativados! Qualquer uma dessas bibliotecas é necessária para que o WordPress possa redimensionar imagens. Fale com o administrador do seu servidor para ativar isso antes de continuar."

#: includes/widgets/image-carousel.php:511
msgid "Fade"
msgstr "Esmaecer"

#: includes/widgets/image-carousel.php:506
msgid "Effect"
msgstr "Efeito"

#: includes/controls/media.php:362
msgctxt "Image Size Control"
msgid "Full"
msgstr "Completo"

#: includes/controls/gallery.php:92
msgid "Edit gallery"
msgstr "Editar galeria"

#: includes/controls/groups/background.php:107
msgid "Slideshow"
msgstr "Apresentação de slides"

#: includes/elements/column.php:183 includes/widgets/icon-box.php:265
#: includes/widgets/icon-list.php:573 includes/widgets/image-box.php:240
msgid "Vertical Alignment"
msgstr "Alinhamento vertical"

#: includes/controls/groups/background.php:634
#: includes/widgets/image-carousel.php:493
msgid "Infinite Loop"
msgstr "Repetição infinita (loop)"

#: includes/widgets/image-carousel.php:217
msgid "Dots"
msgstr "Pontos"

#: includes/controls/groups/typography.php:321
msgctxt "Typography Control"
msgid "Width"
msgstr "Largura"

#: includes/widgets/accordion.php:243 includes/widgets/counter.php:242
#: includes/widgets/icon-box.php:207 includes/widgets/image-box.php:182
#: includes/widgets/progress.php:131 includes/widgets/toggle.php:246
#: modules/nested-accordion/widgets/nested-accordion.php:270
msgid "Title HTML Tag"
msgstr "Tag HTML do título"

#: includes/widgets/icon-box.php:172 includes/widgets/image-box.php:147
msgid "This is the heading"
msgstr "Este é o título"

#: includes/elements/column.php:837 includes/elements/container.php:1788
#: includes/elements/section.php:1304 includes/widgets/common-base.php:701
#: modules/floating-buttons/base/widget-contact-button-base.php:3114
#: modules/floating-buttons/base/widget-floating-bars-base.php:1522
msgid "Add your custom class WITHOUT the dot. e.g: my-class"
msgstr "Adicione sua classe personalizada SEM o ponto. Por exemplo: my-class"

#: core/kits/documents/tabs/global-typography.php:200
msgid "The list of fonts used if the chosen font is not available."
msgstr "A lista de fontes usadas se a fonte escolhida não estiver disponível."

#: includes/widgets/wordpress.php:242
msgid "Form"
msgstr "Formulário"

#: includes/editor-templates/panel-elements.php:14 assets/js/editor.js:22048
#: assets/js/editor.js:23947 assets/js/editor.js:24356
#: assets/js/editor.js:39581
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:19
#: assets/js/packages/editor-canvas/editor-canvas.js:2
#: assets/js/packages/editor-canvas/editor-canvas.strings.js:6
msgid "Elements"
msgstr "Elementos"

#: core/admin/admin.php:343 core/admin/menu/main.php:75
#: core/common/modules/finder/categories/settings.php:29
#: core/dynamic-tags/base-tag.php:171 includes/editor-templates/panel.php:80
#: includes/managers/controls.php:339
#: includes/settings/admin-menu-items/admin-menu-item.php:28
#: includes/settings/settings.php:216
#: modules/atomic-widgets/elements/div-block/div-block.php:62
#: modules/usage/settings-reporter.php:13 assets/js/editor.js:9572
#: assets/js/editor.js:40481 assets/js/editor.js:49889
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:38
msgid "Settings"
msgstr "Configurações"

#: includes/controls/groups/typography.php:180
msgctxt "Typography Control"
msgid "Oblique"
msgstr "Oblíquo"

#: includes/controls/groups/typography.php:179
msgctxt "Typography Control"
msgid "Italic"
msgstr "Itálico"

#: includes/controls/groups/typography.php:167
msgctxt "Typography Control"
msgid "Capitalize"
msgstr "Capitalizar"

#: includes/controls/groups/typography.php:166
msgctxt "Typography Control"
msgid "Lowercase"
msgstr "Minúsculas"

#: includes/controls/groups/typography.php:165
msgctxt "Typography Control"
msgid "Uppercase"
msgstr "Maiúsculas"

#. Author of the plugin
#: elementor.php
msgid "Elementor.com"
msgstr "Elementor.com"

#: includes/widgets/video.php:670 includes/widgets/video.php:797
#: modules/floating-buttons/base/widget-floating-bars-base.php:267
msgid "Play Icon"
msgstr "Ícone de reprodução"

#: includes/widgets/video.php:623 includes/widgets/video.php:630
#: includes/widgets/video.php:785
msgid "Image Overlay"
msgstr "Sobreposição de imagem"

#: includes/widgets/video.php:387
msgid "Player Controls"
msgstr "Controles do reprodutor"

#: includes/widgets/video.php:491
msgid "Suggested Videos"
msgstr "Vídeos sugeridos"

#: includes/widgets/video.php:747
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:120
msgid "Aspect Ratio"
msgstr "Proporção da tela"

#: core/base/providers/social-network-provider.php:138
#: includes/widgets/video.php:137
msgid "YouTube"
msgstr "YouTube"

#: includes/widgets/toggle.php:147
msgid "Toggle Content"
msgstr "Conteúdo da alternância"

#: includes/widgets/toggle.php:134
msgid "Toggle Title"
msgstr "Título da alternância"

#: includes/widgets/toggle.php:177
msgid "Toggle #2"
msgstr "Alternância Nº 2"

#: includes/widgets/toggle.php:173
msgid "Toggle #1"
msgstr "Alternância Nº 1"

#: includes/widgets/toggle.php:168
msgid "Toggle Items"
msgstr "Itens da alternância"

#: includes/widgets/toggle.php:46 includes/widgets/toggle.php:123
#: includes/widgets/toggle.php:276
msgid "Toggle"
msgstr "Alternância"

#: includes/widgets/text-editor.php:46 includes/widgets/text-editor.php:133
#: includes/widgets/text-editor.php:250
msgid "Text Editor"
msgstr "Editor de texto"

#: includes/widgets/tabs.php:142 includes/widgets/tabs.php:143
msgid "Tab Content"
msgstr "Conteúdo da aba"

#: includes/widgets/tabs.php:129 includes/widgets/tabs.php:130
#: modules/nested-tabs/widgets/nested-tabs.php:115
#: modules/nested-tabs/widgets/nested-tabs.php:116
msgid "Tab Title"
msgstr "Título da aba"

#: includes/widgets/tabs.php:173
#: modules/nested-tabs/widgets/nested-tabs.php:175
msgid "Tab #2"
msgstr "Aba Nº 2"

#: includes/widgets/tabs.php:169
#: modules/nested-tabs/widgets/nested-tabs.php:172
msgid "Tab #1"
msgstr "Aba Nº 1"

#: includes/widgets/tabs.php:164
#: modules/nested-tabs/widgets/nested-tabs.php:167
msgid "Tabs Items"
msgstr "Itens das abas"

#: includes/widgets/tabs.php:46 includes/widgets/tabs.php:118
#: includes/widgets/tabs.php:267 modules/nested-tabs/widgets/nested-tabs.php:34
#: modules/nested-tabs/widgets/nested-tabs.php:107
#: modules/nested-tabs/widgets/nested-tabs.php:444
msgid "Tabs"
msgstr "Abas"

#: includes/widgets/image-carousel.php:445
msgid "Pause on Hover"
msgstr "Pausar ao passar o mouse"

#: includes/widgets/image-carousel.php:478
msgid "Autoplay Speed"
msgstr "Velocidade da reprodução automática"

#: includes/widgets/image-carousel.php:216
#: includes/widgets/image-carousel.php:560
msgid "Arrows"
msgstr "Setas"

#: includes/widgets/image-carousel.php:510
msgid "Slide"
msgstr "Deslizar"

#: includes/widgets/sidebar.php:93 includes/widgets/sidebar.php:113
msgid "Choose Sidebar"
msgstr "Escolha a barra lateral"

#: includes/widgets/sidebar.php:91
msgid "No sidebars were found"
msgstr "Nenhuma barra lateral foi encontrada"

#: includes/widgets/sidebar.php:42 includes/widgets/sidebar.php:106
msgid "Sidebar"
msgstr "Barra lateral"

#: includes/widgets/progress.php:331
msgid "Title Style"
msgstr "Estilo do título"

#: modules/floating-buttons/base/widget-contact-button-base.php:1844
msgid "Chat Background Color"
msgstr "Cor de fundo do bate-papo"

#: includes/widgets/progress.php:222
msgid "Web Designer"
msgstr "Web designer"

#: includes/widgets/progress.php:221
msgid "e.g. Web Designer"
msgstr "exemplo: Web designer"

#: includes/widgets/progress.php:216 includes/widgets/progress.php:290
msgid "Inner Text"
msgstr "Texto interno"

#: includes/widgets/progress.php:204
msgid "Display Percentage"
msgstr "Exibir porcentagem"

#: includes/widgets/progress.php:188
msgid "Percentage"
msgstr "Porcentagem"

#: includes/widgets/progress.php:45 includes/widgets/progress.php:110
#: includes/widgets/progress.php:233
msgid "Progress Bar"
msgstr "Barra de progresso"

#: includes/widgets/menu-anchor.php:120
msgid "For Example: About"
msgstr "Por exemplo: Sobre"

#: includes/widgets/menu-anchor.php:115
msgid "The ID of Menu Anchor."
msgstr "O ID da âncora do menu."

#: includes/widgets/menu-anchor.php:121
msgid "This ID will be the CSS ID you will have to use in your own page, Without #."
msgstr "Este ID será o ID CSS que você terá que usar em sua própria página, sem o #."

#: includes/widgets/menu-anchor.php:43 includes/widgets/menu-anchor.php:108
msgid "Menu Anchor"
msgstr "Âncora do menu"

#: includes/widgets/image-box.php:212 includes/widgets/testimonial.php:196
msgid "Image Position"
msgstr "Posição da imagem"

#: includes/widgets/image-box.php:298 includes/widgets/image-carousel.php:776
msgid "Image Spacing"
msgstr "Espaçamento da imagem"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1648
msgid "Image Size"
msgstr "Tamanho da imagem"

#: includes/widgets/image-box.php:45 includes/widgets/image-box.php:110
msgid "Image Box"
msgstr "Caixa da imagem"

#: includes/widgets/social-icons.php:523
msgid "Icon Hover"
msgstr "Ícone ao passar o mouse"

#: core/base/traits/shared-widget-controls-trait.php:179
#: includes/controls/groups/border.php:77 includes/widgets/accordion.php:281
#: includes/widgets/divider.php:1002 includes/widgets/icon-box.php:584
#: includes/widgets/icon.php:392 includes/widgets/tabs.php:311
#: includes/widgets/text-editor.php:551 includes/widgets/toggle.php:284
#: modules/floating-buttons/base/widget-floating-bars-base.php:777
#: modules/nested-accordion/widgets/nested-accordion.php:517
#: modules/nested-tabs/widgets/nested-tabs.php:525
#: modules/nested-tabs/widgets/nested-tabs.php:583
#: modules/nested-tabs/widgets/nested-tabs.php:667
#: modules/nested-tabs/widgets/nested-tabs.php:1019
msgid "Border Width"
msgstr "Largura da borda"

#: includes/base/element-base.php:890 includes/base/element-base.php:902
#: includes/widgets/divider.php:981 includes/widgets/icon-box.php:562
#: includes/widgets/icon.php:371 modules/shapes/widgets/text-path.php:283
msgid "Rotate"
msgstr "Rotacionar"

#: includes/widgets/divider.php:915 includes/widgets/icon-box.php:415
#: includes/widgets/icon-box.php:457 includes/widgets/icon.php:237
#: includes/widgets/icon.php:277 includes/widgets/social-icons.php:235
#: includes/widgets/social-icons.php:388 includes/widgets/social-icons.php:546
#: includes/widgets/text-editor.php:451
msgid "Secondary Color"
msgstr "Cor secundária"

#: includes/widgets/divider.php:898 includes/widgets/icon-box.php:399
#: includes/widgets/icon-box.php:440 includes/widgets/icon.php:220
#: includes/widgets/icon.php:263 includes/widgets/social-icons.php:221
#: includes/widgets/social-icons.php:374 includes/widgets/social-icons.php:531
#: includes/widgets/text-editor.php:436
msgid "Primary Color"
msgstr "Cor primária"

#: includes/widgets/icon-box.php:151 includes/widgets/icon.php:149
#: includes/widgets/social-icons.php:284
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1676
#: assets/js/ai-admin.js:11383 assets/js/ai-gutenberg.js:13231
#: assets/js/ai-media-library.js:13012
#: assets/js/ai-unify-product-images.js:13012 assets/js/ai.js:14477
msgid "Square"
msgstr "Quadrado"

#: includes/widgets/common-base.php:135 includes/widgets/icon-box.php:153
#: includes/widgets/icon.php:151 includes/widgets/social-icons.php:286
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1675
#: modules/shapes/module.php:45
msgid "Circle"
msgstr "Círculo"

#: includes/widgets/common-base.php:990 includes/widgets/icon-box.php:148
#: includes/widgets/icon.php:146 includes/widgets/social-icons.php:280
msgid "Shape"
msgstr "Forma"

#: includes/widgets/divider.php:837 includes/widgets/icon-box.php:135
#: includes/widgets/icon.php:136 includes/widgets/text-editor.php:426
msgid "Framed"
msgstr "Emoldurado"

#: includes/widgets/divider.php:836 includes/widgets/icon-box.php:134
#: includes/widgets/icon.php:135 includes/widgets/text-editor.php:425
msgid "Stacked"
msgstr "Empilhado"

#: modules/floating-buttons/base/widget-contact-button-base.php:205
#: modules/floating-buttons/base/widget-contact-button-base.php:235
#: modules/floating-buttons/base/widget-contact-button-base.php:1317
#: modules/floating-buttons/base/widget-contact-button-base.php:2017
#: modules/floating-buttons/base/widget-contact-button-base.php:2626
#: modules/floating-buttons/base/widget-contact-button-base.php:2695
#: modules/floating-buttons/base/widget-floating-bars-base.php:1141
msgid "Icon Color"
msgstr "Cor do Ícone"

#: includes/widgets/icon-list.php:145 includes/widgets/icon-list.php:146
msgid "List Item"
msgstr "Item da lista"

#: includes/widgets/icon-list.php:199
msgid "List Item #3"
msgstr "Item Nº 3 da lista"

#: includes/widgets/icon-list.php:192
msgid "List Item #2"
msgstr "Item Nº 2 da lista"

#: includes/widgets/icon-list.php:185
msgid "List Item #1"
msgstr "Item Nº 1 da lista"

#: includes/widgets/icon-list.php:45 includes/widgets/icon-list.php:110
msgid "Icon List"
msgstr "Lista de ícones"

#: includes/widgets/icon-box.php:323
#: includes/widgets/traits/button-trait.php:175
#: modules/floating-buttons/base/widget-contact-button-base.php:1160
#: modules/floating-buttons/base/widget-contact-button-base.php:2416
#: modules/floating-buttons/base/widget-floating-bars-base.php:606
#: modules/floating-buttons/base/widget-floating-bars-base.php:1331
msgid "Icon Spacing"
msgstr "Espaçamento do ícone"

#: includes/widgets/icon-box.php:45 includes/widgets/icon-box.php:110
msgid "Icon Box"
msgstr "Caixa de ícone"

#: includes/widgets/html.php:97 includes/widgets/html.php:104
msgid "HTML Code"
msgstr "Código HTML"

#: includes/widgets/html.php:42
msgid "HTML"
msgstr "HTML"

#: modules/nested-tabs/widgets/nested-tabs.php:230
#: modules/nested-tabs/widgets/nested-tabs.php:272
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:91
msgid "Justify"
msgstr "Justificar"

#: core/base/traits/shared-widget-controls-trait.php:23
#: includes/elements/column.php:264 includes/elements/container.php:576
#: includes/elements/section.php:498 includes/widgets/divider.php:538
#: includes/widgets/heading.php:211
#: modules/atomic-widgets/elements/div-block/div-block.php:65
msgid "HTML Tag"
msgstr "Tag HTML"

#: includes/widgets/alert.php:136 includes/widgets/heading.php:169
#: includes/widgets/icon-box.php:173 includes/widgets/image-box.php:148
#: includes/widgets/progress.php:122
msgid "Enter your title"
msgstr "Digite seu título"

#: includes/widgets/heading.php:47 includes/widgets/heading.php:154
#: includes/widgets/heading.php:243
#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:32
#: modules/link-in-bio/base/widget-link-in-bio-base.php:846
#: modules/link-in-bio/base/widget-link-in-bio-base.php:851
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1292
msgid "Heading"
msgstr "Título"

#: includes/widgets/google-maps.php:150
msgid "London Eye, London, United Kingdom"
msgstr "London Eye, Londres, Reino Unido"

#: includes/widgets/google-maps.php:44 includes/widgets/google-maps.php:125
#: includes/widgets/google-maps.php:212
msgid "Google Maps"
msgstr "Google Maps"

#: core/kits/documents/tabs/settings-lightbox.php:101
#: core/kits/documents/tabs/settings-lightbox.php:118
#: includes/widgets/image-carousel.php:389
#: includes/widgets/image-carousel.php:395
#: includes/widgets/image-carousel.php:822
#: includes/widgets/image-gallery.php:170
#: includes/widgets/image-gallery.php:356 includes/widgets/image.php:158
#: includes/widgets/image.php:565
msgid "Caption"
msgstr "Legenda"

#: core/kits/documents/tabs/theme-style-images.php:21
#: core/kits/documents/tabs/theme-style-images.php:51
#: includes/controls/groups/background.php:623
#: includes/widgets/image-gallery.php:278 assets/js/ai-admin.js:3604
#: assets/js/ai-gutenberg.js:5372 assets/js/ai-media-library.js:5233
#: assets/js/ai-unify-product-images.js:5233 assets/js/ai.js:6051
msgid "Images"
msgstr "Imagens"

#: includes/widgets/image-gallery.php:267
msgid "Random"
msgstr "Aleatório"

#: includes/widgets/image-carousel.php:342
#: includes/widgets/image-gallery.php:190 includes/widgets/image.php:197
msgid "Media File"
msgstr "Arquivo de mídia"

#: includes/widgets/image-gallery.php:191
msgid "Attachment Page"
msgstr "Página de anexo"

#: includes/controls/gallery.php:94 includes/widgets/image-carousel.php:141
#: includes/widgets/image-gallery.php:137
msgid "Add Images"
msgstr "Adicionar imagens"

#: includes/widgets/divider.php:699 includes/widgets/icon-list.php:519
#: includes/widgets/image-gallery.php:286 includes/widgets/star-rating.php:291
msgid "Gap"
msgstr "Espaçamento"

#: core/kits/documents/tabs/theme-style-form-fields.php:78
#: core/kits/documents/tabs/theme-style-typography.php:126
#: core/kits/documents/tabs/theme-style-typography.php:155
#: core/kits/documents/tabs/theme-style-typography.php:200
#: includes/controls/box-shadow.php:104
#: includes/controls/groups/background.php:170
#: includes/controls/text-shadow.php:97 includes/elements/column.php:458
#: includes/elements/container.php:855 includes/elements/container.php:1219
#: includes/elements/section.php:726 includes/elements/section.php:1014
#: includes/widgets/accordion.php:337 includes/widgets/accordion.php:439
#: includes/widgets/accordion.php:508 includes/widgets/alert.php:418
#: includes/widgets/alert.php:435 includes/widgets/divider.php:589
#: includes/widgets/divider.php:733 includes/widgets/heading.php:320
#: includes/widgets/icon-box.php:670 includes/widgets/icon-box.php:694
#: includes/widgets/icon-box.php:757 includes/widgets/icon-list.php:396
#: includes/widgets/icon-list.php:433 includes/widgets/icon-list.php:458
#: includes/widgets/icon-list.php:662 includes/widgets/icon-list.php:686
#: includes/widgets/image-box.php:570 includes/widgets/image-box.php:594
#: includes/widgets/image-box.php:657 includes/widgets/image-carousel.php:609
#: includes/widgets/image-carousel.php:693 includes/widgets/progress.php:241
#: includes/widgets/progress.php:299 includes/widgets/rating.php:110
#: includes/widgets/social-icons.php:208 includes/widgets/social-icons.php:361
#: includes/widgets/star-rating.php:378 includes/widgets/tabs.php:366
#: includes/widgets/tabs.php:459 includes/widgets/toggle.php:370
#: includes/widgets/toggle.php:463 includes/widgets/toggle.php:532
#: includes/widgets/video.php:809
#: modules/floating-buttons/base/widget-contact-button-base.php:1886
#: modules/floating-buttons/base/widget-contact-button-base.php:2285
#: modules/floating-buttons/base/widget-contact-button-base.php:2298
#: modules/floating-buttons/base/widget-contact-button-base.php:2536
#: modules/floating-buttons/base/widget-floating-bars-base.php:422
#: modules/floating-buttons/base/widget-floating-bars-base.php:540
#: modules/floating-buttons/base/widget-floating-bars-base.php:1074
#: modules/floating-buttons/base/widget-floating-bars-base.php:1275
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1204
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1389
#: modules/nested-accordion/widgets/nested-accordion.php:686
#: modules/nested-accordion/widgets/nested-accordion.php:749
#: modules/nested-tabs/widgets/nested-tabs.php:744
#: modules/nested-tabs/widgets/nested-tabs.php:780
#: modules/nested-tabs/widgets/nested-tabs.php:816
#: modules/nested-tabs/widgets/nested-tabs.php:942
#: modules/nested-tabs/widgets/nested-tabs.php:959
#: modules/nested-tabs/widgets/nested-tabs.php:976
#: modules/shapes/widgets/text-path.php:416
#: modules/shapes/widgets/text-path.php:440
#: modules/shapes/widgets/text-path.php:508
#: modules/shapes/widgets/text-path.php:528
#: modules/shapes/widgets/text-path.php:579
#: modules/shapes/widgets/text-path.php:599 assets/js/editor.js:50027
#: assets/js/editor.js:50070
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:38
#: assets/js/packages/editor-controls/editor-controls.strings.js:77
#: assets/js/packages/editor-controls/editor-controls.strings.js:86
msgid "Color"
msgstr "Cor"

#: includes/widgets/divider.php:605 includes/widgets/icon-list.php:319
#: modules/floating-buttons/base/widget-contact-button-base.php:2550
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1215
msgid "Weight"
msgstr "Peso"

#: includes/widgets/spacer.php:42 includes/widgets/spacer.php:123
msgid "Spacer"
msgstr "Espaçador"

#: includes/widgets/divider.php:46 includes/widgets/divider.php:378
#: includes/widgets/divider.php:528 includes/widgets/divider.php:578
#: includes/widgets/icon-list.php:283
msgid "Divider"
msgstr "Divisor"

#: includes/widgets/counter.php:491
#: modules/floating-buttons/base/widget-contact-button-base.php:333
#: modules/floating-buttons/base/widget-contact-button-base.php:885
#: modules/link-in-bio/base/widget-link-in-bio-base.php:481
#: modules/link-in-bio/base/widget-link-in-bio-base.php:728
msgid "Number"
msgstr "Número"

#: includes/widgets/counter.php:235
msgid "Cool Number"
msgstr "Número legal"

#: includes/widgets/counter.php:173
msgid "Number Suffix"
msgstr "Sufixo do número"

#: includes/widgets/counter.php:158
msgid "Number Prefix"
msgstr "Prefixo do número"

#: includes/widgets/counter.php:146
msgid "Ending Number"
msgstr "Número final"

#: includes/widgets/counter.php:134
msgid "Starting Number"
msgstr "Número inicial"

#: core/kits/documents/tabs/settings-lightbox.php:56
#: includes/widgets/counter.php:45 includes/widgets/counter.php:127
#: includes/widgets/counter.php:267
msgid "Counter"
msgstr "Contador"

#: includes/widgets/audio.php:152 includes/widgets/image-carousel.php:432
#: includes/widgets/video.php:339
msgid "Autoplay"
msgstr "Reprodução automática"

#: includes/widgets/image-carousel.php:181
msgid "Slides to Scroll"
msgstr "Slides para rolar"

#: includes/widgets/image-carousel.php:164
msgid "Slides to Show"
msgstr "Slides para mostrar"

#: includes/controls/media.php:189 includes/widgets/image-box.php:117
#: includes/widgets/image.php:133 includes/widgets/testimonial.php:131
#: includes/widgets/video.php:641
#: modules/link-in-bio/base/widget-link-in-bio-base.php:251
#: modules/link-in-bio/base/widget-link-in-bio-base.php:341
#: modules/link-in-bio/base/widget-link-in-bio-base.php:929
#: modules/link-in-bio/base/widget-link-in-bio-base.php:984
msgid "Choose Image"
msgstr "Escolher imagem"

#: includes/controls/groups/background.php:292
#: includes/widgets/common-base.php:1004 includes/widgets/image-box.php:351
#: includes/widgets/image-carousel.php:724 includes/widgets/image.php:45
#: includes/widgets/image.php:126 includes/widgets/image.php:251
#: includes/widgets/testimonial.php:306
#: modules/atomic-widgets/elements/atomic-image/atomic-image.php:31
#: assets/js/packages/editor-controls/editor-controls.js:12
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:29
#: assets/js/packages/editor-controls/editor-controls.strings.js:84
msgid "Image"
msgstr "Imagem"

#: core/base/traits/shared-widget-controls-trait.php:156
#: includes/elements/column.php:547 includes/elements/container.php:979
#: includes/elements/section.php:831 includes/widgets/common-base.php:856
#: modules/floating-buttons/base/widget-floating-bars-base.php:761
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:18
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:241
msgid "Border"
msgstr "Borda"

#: core/dynamic-tags/tag.php:105 includes/widgets/counter.php:283
#: modules/nested-tabs/widgets/nested-tabs.php:203
#: modules/nested-tabs/widgets/nested-tabs.php:865
msgid "After"
msgstr "Depois"

#: core/dynamic-tags/tag.php:95 includes/widgets/counter.php:279
#: modules/nested-tabs/widgets/nested-tabs.php:207
#: modules/nested-tabs/widgets/nested-tabs.php:873
msgid "Before"
msgstr "Antes"

#: includes/widgets/icon-box.php:237
#: includes/widgets/traits/button-trait.php:142
#: modules/floating-buttons/base/widget-contact-button-base.php:1130
#: modules/floating-buttons/base/widget-contact-button-base.php:2396
#: modules/floating-buttons/base/widget-floating-bars-base.php:576
#: modules/floating-buttons/base/widget-floating-bars-base.php:1286
msgid "Icon Position"
msgstr "Posição do ícone"

#: includes/widgets/accordion.php:185 includes/widgets/accordion.php:408
#: includes/widgets/alert.php:173 includes/widgets/divider.php:509
#: includes/widgets/divider.php:561 includes/widgets/divider.php:821
#: includes/widgets/icon-box.php:117 includes/widgets/icon-box.php:379
#: includes/widgets/icon-list.php:156 includes/widgets/icon-list.php:416
#: includes/widgets/icon.php:44 includes/widgets/icon.php:111
#: includes/widgets/icon.php:118 includes/widgets/icon.php:177
#: includes/widgets/rating.php:52 includes/widgets/rating.php:177
#: includes/widgets/social-icons.php:116 includes/widgets/social-icons.php:353
#: includes/widgets/star-rating.php:168 includes/widgets/toggle.php:188
#: includes/widgets/toggle.php:432 includes/widgets/traits/button-trait.php:126
#: includes/widgets/video.php:686
#: modules/floating-buttons/base/widget-contact-button-base.php:495
#: modules/floating-buttons/base/widget-floating-bars-base.php:113
#: modules/floating-buttons/base/widget-floating-bars-base.php:181
#: modules/floating-buttons/base/widget-floating-bars-base.php:325
#: modules/floating-buttons/base/widget-floating-bars-base.php:399
#: modules/floating-buttons/base/widget-floating-bars-base.php:1267
#: modules/nested-accordion/widgets/nested-accordion.php:205
#: modules/nested-accordion/widgets/nested-accordion.php:595
#: modules/nested-tabs/widgets/nested-tabs.php:126
#: modules/nested-tabs/widgets/nested-tabs.php:847
msgid "Icon"
msgstr "Ícone"

#: includes/controls/groups/flex-item.php:125 includes/widgets/alert.php:353
#: includes/widgets/common-base.php:1041 includes/widgets/divider.php:639
#: includes/widgets/divider.php:847 includes/widgets/heading.php:191
#: includes/widgets/icon-box.php:502 includes/widgets/icon-list.php:492
#: includes/widgets/icon.php:306 includes/widgets/image-carousel.php:589
#: includes/widgets/image-carousel.php:673 includes/widgets/rating.php:60
#: includes/widgets/social-icons.php:403 includes/widgets/star-rating.php:327
#: includes/widgets/text-editor.php:474
#: includes/widgets/traits/button-trait.php:114 includes/widgets/video.php:825
#: modules/floating-buttons/base/widget-contact-button-base.php:1114
#: modules/floating-buttons/base/widget-contact-button-base.php:1539
#: modules/floating-buttons/base/widget-contact-button-base.php:1921
#: modules/floating-buttons/base/widget-contact-button-base.php:2271
#: modules/floating-buttons/base/widget-floating-bars-base.php:490
#: modules/floating-buttons/base/widget-floating-bars-base.php:1088
#: modules/floating-buttons/base/widget-floating-bars-base.php:1313
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1400
#: modules/nested-accordion/widgets/nested-accordion.php:603
#: modules/nested-tabs/widgets/nested-tabs.php:895
#: modules/shapes/widgets/text-path.php:251
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:94
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:14
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:171
msgid "Size"
msgstr "Tamanho"

#: includes/elements/column.php:747 includes/elements/section.php:1209
#: includes/widgets/heading.php:267 includes/widgets/icon-box.php:309
#: includes/widgets/image-box.php:284 includes/widgets/image-carousel.php:849
#: includes/widgets/image-gallery.php:383 includes/widgets/image.php:593
#: includes/widgets/star-rating.php:231 includes/widgets/text-editor.php:274
msgid "Justified"
msgstr "Justificado"

#: includes/widgets/accordion.php:419 includes/widgets/divider.php:470
#: includes/widgets/heading.php:251 includes/widgets/icon-box.php:293
#: includes/widgets/icon-list.php:260 includes/widgets/icon.php:185
#: includes/widgets/image-box.php:268 includes/widgets/image-carousel.php:833
#: includes/widgets/image-gallery.php:367 includes/widgets/image.php:259
#: includes/widgets/image.php:577 includes/widgets/rating.php:199
#: includes/widgets/social-icons.php:324 includes/widgets/star-rating.php:215
#: includes/widgets/tabs.php:205 includes/widgets/tabs.php:235
#: includes/widgets/tabs.php:422 includes/widgets/testimonial.php:221
#: includes/widgets/text-editor.php:258 includes/widgets/toggle.php:443
#: includes/widgets/traits/button-trait.php:283
#: modules/shapes/widgets/text-path.php:172
msgid "Alignment"
msgstr "Alinhamento"

#: includes/widgets/heading.php:199
msgid "XXL"
msgstr "XXL"

#: includes/widgets/heading.php:198
msgid "XL"
msgstr "XL"

#: includes/widgets/heading.php:197 includes/widgets/traits/button-trait.php:37
#: modules/floating-buttons/base/widget-contact-button-base.php:1120
#: modules/floating-buttons/base/widget-contact-button-base.php:1545
#: modules/floating-buttons/base/widget-contact-button-base.php:1927
#: modules/floating-buttons/base/widget-contact-button-base.php:2277
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1406
msgid "Large"
msgstr "Grande"

#: includes/widgets/heading.php:196 includes/widgets/traits/button-trait.php:36
#: modules/floating-buttons/base/widget-contact-button-base.php:1119
#: modules/floating-buttons/base/widget-contact-button-base.php:1544
#: modules/floating-buttons/base/widget-contact-button-base.php:1926
#: modules/floating-buttons/base/widget-contact-button-base.php:2276
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1405
msgid "Medium"
msgstr "Médio"

#: includes/widgets/heading.php:195 includes/widgets/traits/button-trait.php:35
#: modules/floating-buttons/base/widget-contact-button-base.php:1118
#: modules/floating-buttons/base/widget-contact-button-base.php:1543
#: modules/floating-buttons/base/widget-contact-button-base.php:1925
#: modules/floating-buttons/base/widget-contact-button-base.php:2275
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1404
msgid "Small"
msgstr "Pequeno"

#: includes/widgets/button.php:48 includes/widgets/button.php:93
#: includes/widgets/button.php:114
#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:32
#: modules/floating-buttons/base/widget-floating-bars-base.php:567
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1095
msgid "Button"
msgstr "Botão"

#: core/common/modules/finder/categories/edit.php:30
#: includes/controls/popover-toggle.php:68 assets/js/ai-admin.js:2334
#: assets/js/ai-admin.js:10213 assets/js/ai-admin.js:10220
#: assets/js/ai-gutenberg.js:4102 assets/js/ai-gutenberg.js:12061
#: assets/js/ai-gutenberg.js:12068 assets/js/ai-media-library.js:3963
#: assets/js/ai-media-library.js:11842 assets/js/ai-media-library.js:11849
#: assets/js/ai-unify-product-images.js:3963
#: assets/js/ai-unify-product-images.js:11842
#: assets/js/ai-unify-product-images.js:11849 assets/js/ai.js:4748
#: assets/js/ai.js:13307 assets/js/ai.js:13314
#: assets/js/element-manager-admin.js:2500
#: assets/js/element-manager-admin.js:2556
msgid "Edit"
msgstr "Editar"

#: core/kits/documents/tabs/theme-style-buttons.php:152
#: core/kits/documents/tabs/theme-style-buttons.php:227
#: core/kits/documents/tabs/theme-style-form-fields.php:233
#: core/kits/documents/tabs/theme-style-images.php:83
#: core/kits/documents/tabs/theme-style-images.php:154
#: includes/elements/column.php:572 includes/elements/column.php:609
#: includes/elements/container.php:1024 includes/elements/container.php:1075
#: includes/elements/section.php:855 includes/elements/section.php:891
#: includes/widgets/common-base.php:881 includes/widgets/common-base.php:918
#: includes/widgets/divider.php:1025 includes/widgets/icon-box.php:599
#: includes/widgets/icon.php:407 includes/widgets/image-box.php:399
#: includes/widgets/image-carousel.php:808
#: includes/widgets/image-gallery.php:342 includes/widgets/image.php:540
#: includes/widgets/progress.php:278 includes/widgets/social-icons.php:509
#: includes/widgets/testimonial.php:344 includes/widgets/text-editor.php:529
#: includes/widgets/traits/button-trait.php:488
#: modules/nested-accordion/widgets/nested-accordion.php:461
#: modules/nested-accordion/widgets/nested-accordion.php:526
#: modules/nested-tabs/widgets/nested-tabs.php:688
#: modules/nested-tabs/widgets/nested-tabs.php:1028
msgid "Border Radius"
msgstr "Raio da borda"

#: core/kits/documents/tabs/settings-lightbox.php:103
#: core/kits/documents/tabs/settings-lightbox.php:113
#: core/kits/documents/tabs/settings-lightbox.php:120
#: includes/compatibility.php:163 includes/widgets/alert.php:302
#: includes/widgets/icon-box.php:181 includes/widgets/icon-box.php:729
#: includes/widgets/image-box.php:156 includes/widgets/image-box.php:629
#: includes/widgets/image-carousel.php:396
#: modules/floating-buttons/base/widget-contact-button-base.php:2340
#: modules/link-in-bio/base/widget-link-in-bio-base.php:892
#: modules/link-in-bio/base/widget-link-in-bio-base.php:897
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1348
msgid "Description"
msgstr "Descrição"

#: core/base/document.php:1978
#: core/kits/documents/tabs/settings-lightbox.php:96
#: core/kits/documents/tabs/settings-lightbox.php:100
#: core/kits/documents/tabs/settings-lightbox.php:117
#: includes/elements/column.php:127 includes/elements/section.php:240
#: includes/widgets/accordion.php:132 includes/widgets/accordion.php:318
#: includes/widgets/alert.php:134 includes/widgets/alert.php:262
#: includes/widgets/common-base.php:191 includes/widgets/counter.php:228
#: includes/widgets/counter.php:542 includes/widgets/heading.php:161
#: includes/widgets/icon-box.php:167 includes/widgets/icon-box.php:625
#: includes/widgets/image-box.php:142 includes/widgets/image-box.php:525
#: includes/widgets/image-carousel.php:394 includes/widgets/progress.php:117
#: includes/widgets/star-rating.php:203 includes/widgets/star-rating.php:247
#: includes/widgets/tabs.php:127 includes/widgets/tabs.php:357
#: includes/widgets/testimonial.php:168 includes/widgets/testimonial.php:404
#: includes/widgets/toggle.php:132 includes/widgets/toggle.php:350
#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:65
#: modules/floating-buttons/base/widget-contact-button-base.php:129
#: modules/floating-buttons/base/widget-contact-button-base.php:210
#: modules/floating-buttons/base/widget-contact-button-base.php:2312
#: modules/link-in-bio/base/widget-link-in-bio-base.php:866
#: modules/nested-accordion/widgets/nested-accordion.php:115
#: modules/nested-accordion/widgets/nested-accordion.php:563
#: modules/nested-tabs/widgets/nested-tabs.php:113
msgid "Title"
msgstr "Título"

#: core/kits/documents/tabs/settings-lightbox.php:130
#: core/kits/documents/tabs/theme-style-form-fields.php:200
#: includes/controls/groups/background.php:174 includes/widgets/alert.php:218
#: includes/widgets/image.php:622 includes/widgets/progress.php:255
#: includes/widgets/tabs.php:345 includes/widgets/video.php:882
#: modules/floating-buttons/base/widget-contact-button-base.php:1235
#: modules/floating-buttons/base/widget-contact-button-base.php:1287
#: modules/floating-buttons/base/widget-contact-button-base.php:1328
#: modules/floating-buttons/base/widget-contact-button-base.php:1374
#: modules/floating-buttons/base/widget-contact-button-base.php:1690
#: modules/floating-buttons/base/widget-contact-button-base.php:1960
#: modules/floating-buttons/base/widget-contact-button-base.php:1996
#: modules/floating-buttons/base/widget-contact-button-base.php:2029
#: modules/floating-buttons/base/widget-contact-button-base.php:2133
#: modules/floating-buttons/base/widget-contact-button-base.php:2159
#: modules/floating-buttons/base/widget-contact-button-base.php:2368
#: modules/floating-buttons/base/widget-contact-button-base.php:2655
#: modules/floating-buttons/base/widget-contact-button-base.php:2724
#: modules/floating-buttons/base/widget-contact-button-base.php:2811
#: modules/floating-buttons/base/widget-contact-button-base.php:2825
#: modules/floating-buttons/base/widget-floating-bars-base.php:674
#: modules/floating-buttons/base/widget-floating-bars-base.php:708
#: modules/floating-buttons/base/widget-floating-bars-base.php:1110
#: modules/floating-buttons/base/widget-floating-bars-base.php:1155
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1125
#: modules/nested-tabs/widgets/nested-tabs.php:506
#: modules/nested-tabs/widgets/nested-tabs.php:564
#: modules/nested-tabs/widgets/nested-tabs.php:648
#: modules/nested-tabs/widgets/nested-tabs.php:1003 assets/js/ai-admin.js:14073
#: assets/js/ai-admin.js:14795 assets/js/ai-gutenberg.js:15921
#: assets/js/ai-gutenberg.js:16643 assets/js/ai-media-library.js:15702
#: assets/js/ai-media-library.js:16424
#: assets/js/ai-unify-product-images.js:15702
#: assets/js/ai-unify-product-images.js:16424 assets/js/ai.js:17167
#: assets/js/ai.js:17889
msgid "Background Color"
msgstr "Cor de fundo"

#: core/settings/editor-preferences/model.php:185
#: includes/base/element-base.php:1399
#: includes/controls/groups/grid-container.php:34
#: includes/widgets/alert.php:164 includes/widgets/audio.php:162
#: includes/widgets/audio.php:173 includes/widgets/audio.php:184
#: includes/widgets/audio.php:195 includes/widgets/audio.php:209
#: includes/widgets/audio.php:220 includes/widgets/audio.php:231
#: includes/widgets/audio.php:242 includes/widgets/counter.php:203
#: includes/widgets/progress.php:157 includes/widgets/progress.php:207
#: includes/widgets/video.php:389 includes/widgets/video.php:404
#: includes/widgets/video.php:431 includes/widgets/video.php:509
#: includes/widgets/video.php:523 includes/widgets/video.php:537
#: includes/widgets/video.php:563 includes/widgets/video.php:632
#: includes/widgets/video.php:673
#: modules/floating-buttons/base/widget-contact-button-base.php:519
#: modules/floating-buttons/base/widget-contact-button-base.php:629
#: modules/floating-buttons/base/widget-contact-button-base.php:694
#: modules/floating-buttons/base/widget-contact-button-base.php:2526
#: modules/floating-buttons/base/widget-floating-bars-base.php:231
#: modules/floating-buttons/base/widget-floating-bars-base.php:298
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.strings.js:51
msgid "Hide"
msgstr "Ocultar"

#: core/settings/editor-preferences/model.php:184
#: includes/base/element-base.php:1400
#: includes/controls/groups/grid-container.php:33
#: includes/widgets/alert.php:163 includes/widgets/audio.php:163
#: includes/widgets/audio.php:174 includes/widgets/audio.php:185
#: includes/widgets/audio.php:196 includes/widgets/audio.php:210
#: includes/widgets/audio.php:221 includes/widgets/audio.php:232
#: includes/widgets/audio.php:243 includes/widgets/counter.php:202
#: includes/widgets/progress.php:156 includes/widgets/progress.php:206
#: includes/widgets/video.php:390 includes/widgets/video.php:405
#: includes/widgets/video.php:432 includes/widgets/video.php:510
#: includes/widgets/video.php:524 includes/widgets/video.php:538
#: includes/widgets/video.php:564 includes/widgets/video.php:633
#: includes/widgets/video.php:674
#: modules/floating-buttons/base/widget-contact-button-base.php:518
#: modules/floating-buttons/base/widget-contact-button-base.php:628
#: modules/floating-buttons/base/widget-contact-button-base.php:693
#: modules/floating-buttons/base/widget-contact-button-base.php:2525
#: modules/floating-buttons/base/widget-floating-bars-base.php:230
#: modules/floating-buttons/base/widget-floating-bars-base.php:297
#: assets/js/element-manager-admin.js:2076
#: assets/js/packages/editor-controls/editor-controls.js:55
#: assets/js/packages/editor-controls/editor-controls.strings.js:50
msgid "Show"
msgstr "Mostrar"

#: includes/widgets/alert.php:151
msgid "I am a description. Click the edit button to change this text."
msgstr "Eu sou uma descrição. Clique no botão \"Editar\" para alterar este texto."

#: includes/widgets/alert.php:125 includes/widgets/progress.php:175
#: includes/widgets/traits/button-trait.php:75
msgid "Danger"
msgstr "Perigo"

#: includes/widgets/alert.php:124 includes/widgets/progress.php:174
#: includes/widgets/traits/button-trait.php:74
msgid "Warning"
msgstr "Atenção"

#: includes/widgets/alert.php:123 includes/widgets/progress.php:173
#: includes/widgets/traits/button-trait.php:73
msgid "Success"
msgstr "Sucesso"

#: includes/widgets/alert.php:122 includes/widgets/progress.php:172
#: includes/widgets/traits/button-trait.php:72 assets/js/app-packages.js:5651
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3437
msgid "Info"
msgstr "Informação"

#: includes/editor-templates/templates.php:198
#: includes/elements/container.php:1192 includes/elements/section.php:987
#: includes/template-library/sources/local.php:1702
#: includes/widgets/alert.php:118 includes/widgets/progress.php:168
#: includes/widgets/traits/button-trait.php:67
#: modules/floating-buttons/base/widget-floating-bars-base.php:563
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1091
msgid "Type"
msgstr "Tipo"

#: includes/widgets/alert.php:46 includes/widgets/alert.php:111
#: includes/widgets/alert.php:210
msgid "Alert"
msgstr "Alerta"

#: includes/widgets/accordion.php:352 includes/widgets/accordion.php:451
#: includes/widgets/image-carousel.php:708 includes/widgets/tabs.php:380
#: includes/widgets/toggle.php:385 includes/widgets/toggle.php:475
msgid "Active Color"
msgstr "Cor ativa"

#: core/base/traits/shared-widget-controls-trait.php:207
#: includes/controls/groups/border.php:90 includes/widgets/accordion.php:303
#: includes/widgets/alert.php:229 includes/widgets/social-icons.php:562
#: includes/widgets/tabs.php:334 includes/widgets/toggle.php:305
#: includes/widgets/traits/button-trait.php:429
#: modules/floating-buttons/base/widget-floating-bars-base.php:722
#: modules/floating-buttons/base/widget-floating-bars-base.php:814
#: modules/nested-accordion/widgets/nested-accordion.php:514
#: modules/nested-tabs/widgets/nested-tabs.php:522
#: modules/nested-tabs/widgets/nested-tabs.php:580
#: modules/nested-tabs/widgets/nested-tabs.php:664
#: modules/nested-tabs/widgets/nested-tabs.php:1016
msgid "Border Color"
msgstr "Cor da borda"

#: includes/widgets/divider.php:832 includes/widgets/icon-box.php:130
#: includes/widgets/icon.php:131 includes/widgets/text-editor.php:421
#: assets/js/editor.js:12719
msgid "View"
msgstr "Ver"

#: includes/widgets/accordion.php:147
msgid "Accordion Content"
msgstr "Conteúdo da sanfona"

#: includes/widgets/accordion.php:134
msgid "Accordion Title"
msgstr "Título da sanfona"

#: includes/widgets/accordion.php:174
msgid "Accordion #2"
msgstr "Sanfona Nº 2"

#: includes/widgets/accordion.php:170
msgid "Accordion #1"
msgstr "Sanfona Nº 1"

#: includes/widgets/accordion.php:165
msgid "Accordion Items"
msgstr "Itens da sanfona"

#: includes/widgets/accordion.php:46 includes/widgets/accordion.php:123
#: includes/widgets/accordion.php:273
#: modules/nested-accordion/widgets/nested-accordion.php:39
#: modules/nested-accordion/widgets/nested-accordion.php:393
msgid "Accordion"
msgstr "Sanfona"

#: core/admin/admin-notices.php:236
msgid "Sure! I'd love to help"
msgstr "Claro! Adoraria ajudar"

#: modules/system-info/module.php:202
msgid "You do not have permission to download this file."
msgstr "Você não tem permissão para baixar este arquivo."

#: modules/system-info/module.php:160 modules/system-info/module.php:184
msgid "Download System Info"
msgstr "Baixar arquivo com as informações do sistema"

#: modules/system-info/module.php:166
msgid "You can copy the below info as simple text with Ctrl+C / Ctrl+V:"
msgstr "Você pode copiar as informações abaixo como texto simples com Ctrl+C / Ctrl+V:"

#: modules/system-info/module.php:164
msgid "Copy & Paste Info"
msgstr "Copiar e colar informações"

#: core/common/modules/finder/categories/general.php:55
#: modules/system-info/module.php:157
#: modules/system-info/system-info-menu-item.php:29
#: modules/system-info/system-info-menu-item.php:33
msgid "System Info"
msgstr "Informação do sistema"

#: core/experiments/manager.php:397 core/experiments/manager.php:687
#: modules/element-cache/module.php:128 assets/js/editor.js:30083
#: assets/js/element-manager-admin.js:2224
msgid "Inactive"
msgstr "Inativo"

#: core/experiments/manager.php:396 core/experiments/manager.php:686
#: modules/element-cache/module.php:129
#: modules/floating-buttons/base/widget-contact-button-base.php:1310
#: modules/nested-accordion/widgets/nested-accordion.php:667
#: modules/nested-accordion/widgets/nested-accordion.php:730
#: modules/nested-tabs/widgets/nested-tabs.php:629
#: modules/nested-tabs/widgets/nested-tabs.php:809
#: modules/nested-tabs/widgets/nested-tabs.php:971 assets/js/editor.js:30085
#: assets/js/element-manager-admin.js:2221
msgid "Active"
msgstr "Ativo"

#: includes/editor-templates/templates.php:194
#: includes/widgets/testimonial.php:153 includes/widgets/testimonial.php:359
#: modules/floating-buttons/base/widget-contact-button-base.php:121
#: modules/floating-buttons/base/widget-contact-button-base.php:209
#: modules/floating-buttons/base/widget-contact-button-base.php:653
#: modules/floating-buttons/base/widget-contact-button-base.php:1735
msgid "Name"
msgstr "Nome"

#: core/common/modules/finder/categories/site.php:64
msgid "Themes"
msgstr "Temas"

#: core/base/traits/shared-widget-controls-trait.php:159
#: core/settings/editor-preferences/model.php:140
#: core/settings/editor-preferences/model.php:152
#: core/settings/editor-preferences/model.php:163
#: core/settings/editor-preferences/model.php:208
#: includes/controls/switcher.php:73 includes/managers/icons.php:469
#: includes/widgets/audio.php:135 includes/widgets/image-carousel.php:202
#: includes/widgets/image-carousel.php:378
#: includes/widgets/image-carousel.php:435
#: includes/widgets/image-carousel.php:448
#: includes/widgets/image-carousel.php:465
#: includes/widgets/image-carousel.php:496
#: includes/widgets/image-gallery.php:212 includes/widgets/image.php:237
#: modules/floating-buttons/base/widget-contact-button-base.php:3046
#: modules/floating-buttons/base/widget-floating-bars-base.php:764
#: modules/floating-buttons/base/widget-floating-bars-base.php:1456
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1554
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1612
#: modules/nested-accordion/widgets/nested-accordion.php:310
#: modules/styleguide/module.php:131
msgid "No"
msgstr "Não"

#: core/base/traits/shared-widget-controls-trait.php:158
#: core/settings/editor-preferences/model.php:139
#: core/settings/editor-preferences/model.php:151
#: core/settings/editor-preferences/model.php:162
#: core/settings/editor-preferences/model.php:207
#: includes/controls/switcher.php:74 includes/managers/icons.php:470
#: includes/widgets/audio.php:134 includes/widgets/image-carousel.php:203
#: includes/widgets/image-carousel.php:377
#: includes/widgets/image-carousel.php:434
#: includes/widgets/image-carousel.php:447
#: includes/widgets/image-carousel.php:464
#: includes/widgets/image-carousel.php:495
#: includes/widgets/image-gallery.php:211 includes/widgets/image.php:236
#: modules/floating-buttons/base/widget-contact-button-base.php:3045
#: modules/floating-buttons/base/widget-floating-bars-base.php:763
#: modules/floating-buttons/base/widget-floating-bars-base.php:1455
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1553
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1611
#: modules/nested-accordion/widgets/nested-accordion.php:309
#: modules/styleguide/module.php:132 assets/js/app.js:8951
msgid "Yes"
msgstr "Sim"

#: core/admin/admin-notices.php:520 includes/controls/gallery.php:123
#: includes/controls/media.php:319
msgid "Activate Plugin"
msgstr "Ativar plugin"

#: core/role-manager/role-manager.php:74
msgid "Exclude Roles"
msgstr "Excluir funções"

#: includes/settings/settings.php:271
msgid "Post Types"
msgstr "Tipos de post"

#: core/base/document.php:1970
#: core/common/modules/finder/categories/settings.php:49
msgid "General Settings"
msgstr "Configurações gerais"

#: core/kits/documents/tabs/global-colors.php:92
#: core/kits/documents/tabs/global-typography.php:149
msgid "Accent"
msgstr "Realce"

#: core/kits/documents/tabs/global-colors.php:87
#: core/kits/documents/tabs/global-typography.php:142
#: includes/widgets/divider.php:505 includes/widgets/divider.php:523
#: includes/widgets/divider.php:722 includes/widgets/icon-list.php:142
#: includes/widgets/icon-list.php:626
#: includes/widgets/traits/button-trait.php:58
#: modules/floating-buttons/base/widget-contact-button-base.php:145
#: modules/floating-buttons/base/widget-contact-button-base.php:1042
#: modules/floating-buttons/base/widget-floating-bars-base.php:63
#: modules/floating-buttons/base/widget-floating-bars-base.php:151
#: modules/floating-buttons/base/widget-floating-bars-base.php:337
#: modules/floating-buttons/base/widget-floating-bars-base.php:1353
#: modules/link-in-bio/base/widget-link-in-bio-base.php:326
#: modules/link-in-bio/base/widget-link-in-bio-base.php:582
#: modules/shapes/widgets/text-path.php:111
#: modules/shapes/widgets/text-path.php:304 assets/js/ai-admin.js:3603
#: assets/js/ai-gutenberg.js:5371 assets/js/ai-media-library.js:5232
#: assets/js/ai-unify-product-images.js:5232 assets/js/ai.js:6050
msgid "Text"
msgstr "Texto"

#: core/kits/documents/tabs/global-colors.php:82
#: core/kits/documents/tabs/global-typography.php:135
msgid "Secondary"
msgstr "Secundário"

#: core/kits/documents/tabs/global-colors.php:77
#: core/kits/documents/tabs/global-typography.php:128
msgid "Primary"
msgstr "Primário"

#: includes/managers/elements.php:351
msgid "WordPress"
msgstr "WordPress"

#: core/document-types/post.php:65
msgid "Posts"
msgstr "Posts"

#: core/settings/editor-preferences/model.php:216
#: includes/widgets/image-carousel.php:211
#: includes/widgets/image-carousel.php:549
msgid "Navigation"
msgstr "Navegação"

#: includes/managers/elements.php:288
msgid "Basic"
msgstr "Básico"

#: includes/elements/container.php:1877 includes/elements/section.php:1396
msgid "Visibility"
msgstr "Visibilidade"

#: includes/widgets/video.php:962
msgid "Content Position"
msgstr "Posição do conteúdo"

#: includes/controls/groups/flex-container.php:214
#: includes/controls/groups/grid-container.php:196
#: includes/controls/groups/grid-container.php:236
#: includes/elements/column.php:189 includes/elements/section.php:407
#: includes/elements/section.php:426 includes/widgets/counter.php:351
#: includes/widgets/icon-box.php:273 includes/widgets/image-box.php:248
#: modules/floating-buttons/base/widget-contact-button-base.php:2996
msgid "Middle"
msgstr "Meio"

#: includes/controls/groups/flex-container.php:145
#: includes/controls/groups/flex-item.php:67
#: includes/controls/groups/grid-container.php:147
#: includes/controls/groups/grid-container.php:175
#: includes/elements/section.php:405 includes/widgets/counter.php:404
#: includes/widgets/tabs.php:221 includes/widgets/tabs.php:251
#: includes/widgets/traits/button-trait.php:267
#: modules/floating-buttons/base/widget-floating-bars-base.php:1201
#: modules/nested-accordion/widgets/nested-accordion.php:185
#: modules/nested-tabs/widgets/nested-tabs.php:246
#: modules/nested-tabs/widgets/nested-tabs.php:288
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:199
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:204
msgid "Stretch"
msgstr "Esticar"

#: includes/elements/section.php:401
msgid "Column Position"
msgstr "Posição da coluna"

#: includes/elements/section.php:337 includes/elements/section.php:377
msgid "Minimum Height"
msgstr "Altura mínima"

#: includes/elements/container.php:472 includes/elements/section.php:327
#: includes/elements/section.php:367
msgid "Min Height"
msgstr "Altura mínima"

#: includes/elements/section.php:326 includes/elements/section.php:366
msgid "Fit To Screen"
msgstr "Ajustar à tela"

#: includes/controls/image-dimensions.php:100
#: includes/elements/container.php:1267 includes/elements/section.php:321
#: includes/elements/section.php:361 includes/elements/section.php:1062
#: includes/widgets/google-maps.php:192 includes/widgets/icon-list.php:363
#: includes/widgets/image.php:354 includes/widgets/progress.php:266
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:115
msgid "Height"
msgstr "Altura"

#: includes/elements/section.php:291
msgid "Wide"
msgstr "Amplo"

#: includes/elements/section.php:289
msgid "Narrow"
msgstr "Estreito"

#: includes/elements/section.php:288
msgid "No Gap"
msgstr "Sem espaçamento"

#: includes/elements/section.php:283 includes/widgets/text-editor.php:202
msgid "Columns Gap"
msgstr "Espaço entre colunas"

#: core/kits/documents/tabs/settings-layout.php:55
#: includes/elements/container.php:379 includes/elements/section.php:249
#: includes/widgets/video.php:942
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1587
msgid "Content Width"
msgstr "Largura do conteúdo"

#: includes/elements/container.php:384 includes/elements/section.php:254
#: includes/widgets/common-base.php:232 includes/widgets/icon-list.php:216
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1551
msgid "Full Width"
msgstr "Largura total"

#: includes/elements/container.php:383 includes/elements/section.php:253
msgid "Boxed"
msgstr "Encaixotado"

#: core/settings/editor-preferences/model.php:91
#: includes/controls/groups/background.php:499
#: includes/controls/image-dimensions.php:95
#: includes/elements/container.php:393 includes/elements/container.php:1233
#: includes/elements/section.php:263 includes/elements/section.php:1028
#: includes/widgets/common-base.php:227 includes/widgets/divider.php:443
#: includes/widgets/icon-list.php:344 includes/widgets/image-box.php:362
#: includes/widgets/image.php:284
#: modules/floating-buttons/base/widget-contact-button-base.php:2840
#: modules/nested-tabs/widgets/nested-tabs.php:312
#: modules/shapes/widgets/text-path.php:540
#: modules/shapes/widgets/text-path.php:611
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:114
msgid "Width"
msgstr "Largura"

#: core/kits/documents/tabs/settings-layout.php:24
#: includes/elements/column.php:118 includes/elements/container.php:1362
#: includes/elements/section.php:231 includes/managers/controls.php:338
#: includes/managers/elements.php:284 includes/widgets/common-base.php:182
#: includes/widgets/icon-list.php:117
#: modules/floating-buttons/base/widget-contact-button-base.php:2926
#: modules/floating-buttons/base/widget-floating-bars-base.php:1425
#: modules/nested-accordion/widgets/nested-accordion.php:107
#: assets/js/editor.js:39392
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:12
msgid "Layout"
msgstr "Layout"

#: includes/elements/column.php:161
msgid "Column Width"
msgstr "Largura da coluna"

#: includes/elements/column.php:827 includes/elements/container.php:1778
#: includes/elements/section.php:1294 includes/widgets/common-base.php:692
#: modules/floating-buttons/base/widget-contact-button-base.php:3105
#: modules/floating-buttons/base/widget-floating-bars-base.php:1513
msgid "CSS Classes"
msgstr "Classes CSS"

#: core/document-types/page-base.php:144
#: core/kits/documents/tabs/theme-style-buttons.php:243
#: core/kits/documents/tabs/theme-style-form-fields.php:159
#: includes/elements/column.php:785 includes/elements/container.php:1382
#: includes/elements/section.php:1252 includes/widgets/accordion.php:394
#: includes/widgets/accordion.php:541 includes/widgets/common-base.php:212
#: includes/widgets/divider.php:870 includes/widgets/icon-box.php:520
#: includes/widgets/icon.php:343 includes/widgets/social-icons.php:422
#: includes/widgets/toggle.php:418 includes/widgets/toggle.php:565
#: includes/widgets/traits/button-trait.php:501
#: modules/floating-buttons/base/widget-contact-button-base.php:1459
#: modules/floating-buttons/base/widget-contact-button-base.php:2184
#: modules/floating-buttons/base/widget-contact-button-base.php:2199
#: modules/floating-buttons/base/widget-contact-button-base.php:2747
#: modules/floating-buttons/base/widget-contact-button-base.php:2887
#: modules/floating-buttons/base/widget-floating-bars-base.php:857
#: modules/floating-buttons/base/widget-floating-bars-base.php:1241
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1176
#: modules/nested-accordion/widgets/nested-accordion.php:474
#: modules/nested-accordion/widgets/nested-accordion.php:538
#: modules/nested-tabs/widgets/nested-tabs.php:701
#: modules/nested-tabs/widgets/nested-tabs.php:1051
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:113
msgid "Padding"
msgstr "Preenchimento"

#: core/document-types/page-base.php:132 includes/elements/column.php:772
#: includes/elements/container.php:1370 includes/elements/section.php:1233
#: includes/widgets/common-base.php:200
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:112
msgid "Margin"
msgstr "Margem"

#: includes/base/element-base.php:1326 includes/base/element-base.php:1354
#: includes/controls/groups/flex-container.php:101
#: includes/controls/groups/flex-container.php:137
#: includes/controls/groups/flex-item.php:59
#: includes/controls/groups/grid-container.php:139
#: includes/controls/groups/grid-container.php:167
#: includes/elements/column.php:217 includes/elements/column.php:739
#: includes/elements/section.php:1201 includes/widgets/common-base.php:412
#: includes/widgets/counter.php:322 includes/widgets/counter.php:396
#: includes/widgets/counter.php:432 includes/widgets/divider.php:478
#: includes/widgets/divider.php:774 includes/widgets/divider.php:940
#: includes/widgets/heading.php:259 includes/widgets/icon-box.php:301
#: includes/widgets/icon-list.php:268 includes/widgets/icon-list.php:550
#: includes/widgets/icon-list.php:581 includes/widgets/icon.php:193
#: includes/widgets/image-box.php:276 includes/widgets/image-carousel.php:740
#: includes/widgets/image-carousel.php:841
#: includes/widgets/image-gallery.php:375 includes/widgets/image.php:267
#: includes/widgets/image.php:585 includes/widgets/rating.php:207
#: includes/widgets/social-icons.php:332 includes/widgets/star-rating.php:223
#: includes/widgets/tabs.php:213 includes/widgets/tabs.php:243
#: includes/widgets/tabs.php:430 includes/widgets/testimonial.php:230
#: includes/widgets/text-editor.php:266
#: includes/widgets/traits/button-trait.php:259
#: includes/widgets/traits/button-trait.php:291 includes/widgets/video.php:966
#: modules/floating-buttons/base/widget-contact-button-base.php:2942
#: modules/floating-buttons/base/widget-floating-bars-base.php:1193
#: modules/nested-accordion/widgets/nested-accordion.php:177
#: modules/nested-tabs/widgets/nested-tabs.php:238
#: modules/nested-tabs/widgets/nested-tabs.php:280
#: modules/nested-tabs/widgets/nested-tabs.php:350
#: modules/shapes/widgets/text-path.php:181
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:89
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:161
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:197
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:202
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:207
msgid "Center"
msgstr "Centro"

#: includes/elements/column.php:731 includes/elements/section.php:1193
msgid "Text Align"
msgstr "Alinhamento do texto"

#: includes/elements/column.php:719 includes/elements/section.php:1181
msgid "Link Hover Color"
msgstr "Cor do link ao passar o mouse"

#: includes/elements/column.php:707 includes/elements/section.php:1169
#: includes/widgets/heading.php:374 includes/widgets/text-editor.php:358
#: includes/widgets/text-editor.php:378
msgid "Link Color"
msgstr "Cor do link"

#: includes/elements/column.php:683 includes/elements/section.php:1145
msgid "Heading Color"
msgstr "Cor do título"

#: core/kits/documents/tabs/settings-lightbox.php:163
#: core/kits/documents/tabs/theme-style-buttons.php:98
#: core/kits/documents/tabs/theme-style-buttons.php:173
#: core/kits/documents/tabs/theme-style-form-fields.php:176
#: core/kits/documents/tabs/theme-style-typography.php:55
#: includes/elements/column.php:695 includes/elements/section.php:1157
#: includes/widgets/alert.php:270 includes/widgets/alert.php:310
#: includes/widgets/counter.php:499 includes/widgets/counter.php:553
#: includes/widgets/heading.php:351 includes/widgets/image-carousel.php:863
#: includes/widgets/image-gallery.php:400 includes/widgets/image.php:607
#: includes/widgets/progress.php:339 includes/widgets/star-rating.php:258
#: includes/widgets/testimonial.php:269 includes/widgets/testimonial.php:367
#: includes/widgets/testimonial.php:412 includes/widgets/text-editor.php:343
#: includes/widgets/traits/button-trait.php:347
#: includes/widgets/traits/button-trait.php:400
#: modules/floating-buttons/base/widget-contact-button-base.php:1581
#: modules/floating-buttons/base/widget-contact-button-base.php:1613
#: modules/floating-buttons/base/widget-contact-button-base.php:1744
#: modules/floating-buttons/base/widget-contact-button-base.php:1775
#: modules/floating-buttons/base/widget-contact-button-base.php:1806
#: modules/floating-buttons/base/widget-contact-button-base.php:2114
#: modules/floating-buttons/base/widget-contact-button-base.php:2321
#: modules/floating-buttons/base/widget-contact-button-base.php:2349
#: modules/floating-buttons/base/widget-contact-button-base.php:2642
#: modules/floating-buttons/base/widget-contact-button-base.php:2711
#: modules/floating-buttons/base/widget-floating-bars-base.php:663
#: modules/floating-buttons/base/widget-floating-bars-base.php:697
#: modules/floating-buttons/base/widget-floating-bars-base.php:1381
#: modules/floating-buttons/base/widget-floating-bars-base.php:1401
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1105
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1301
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1329
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1357
msgid "Text Color"
msgstr "Cor do texto"

#: core/kits/documents/tabs/theme-style-typography.php:18
#: core/kits/documents/tabs/theme-style-typography.php:37
#: includes/controls/groups/typography.php:436 includes/elements/column.php:674
#: includes/elements/section.php:1137 assets/js/editor-modules.js:1431
#: assets/js/editor.js:45170
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:16
msgid "Typography"
msgstr "Tipografia"

#: includes/controls/icons.php:80 includes/controls/media.php:238
msgid "Add"
msgstr "Adicionar"

#: includes/controls/gaps.php:57
#: includes/controls/groups/grid-container.php:119
#: includes/elements/column.php:61
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:34
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:181
msgid "Column"
msgstr "Coluna"

#: includes/elements/section.php:78 modules/library/documents/section.php:43
#: assets/js/container-converter.js:95 assets/js/editor.js:10575
msgid "Section"
msgstr "Seção"

#: includes/editor-templates/hotkeys.php:125
#: includes/editor-templates/navigator.php:38
#: includes/editor-templates/panel.php:86
#: includes/editor-templates/panel.php:90 includes/elements/section.php:511
#: includes/elements/section.php:519 assets/js/editor.js:34004
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:8
msgid "Structure"
msgstr "Estrutura"

#: includes/elements/column.php:924 includes/elements/container.php:1869
#: includes/elements/section.php:1372 includes/managers/controls.php:337
#: includes/widgets/common-base.php:1220
#: modules/floating-buttons/base/widget-contact-button-base.php:3059
#: modules/floating-buttons/base/widget-floating-bars-base.php:1467
msgid "Responsive"
msgstr "Responsivo"

#: core/common/modules/finder/categories/settings.php:59
#: core/dynamic-tags/tag.php:88 includes/elements/column.php:763
#: includes/elements/section.php:1225 includes/managers/controls.php:336
#: includes/settings/settings.php:329 includes/settings/settings.php:332
#: modules/floating-buttons/base/widget-contact-button-base.php:2919
#: modules/floating-buttons/base/widget-floating-bars-base.php:1419
#: modules/floating-buttons/module.php:78
#: modules/floating-buttons/module.php:83 assets/js/editor.js:9578
#: assets/js/editor.js:39389
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3180
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3184
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:1444
msgid "Advanced"
msgstr "Avançado"

#: includes/managers/controls.php:335 includes/widgets/divider.php:385
#: includes/widgets/icon-list.php:297 assets/js/ai-admin.js:10518
#: assets/js/ai-gutenberg.js:12366 assets/js/ai-media-library.js:12147
#: assets/js/ai-unify-product-images.js:12147 assets/js/ai.js:13612
#: assets/js/editor.js:9575 assets/js/editor.js:39386
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:23
msgid "Style"
msgstr "Estilo"

#: includes/managers/controls.php:334 includes/widgets/accordion.php:145
#: includes/widgets/accordion.php:489 includes/widgets/alert.php:148
#: includes/widgets/icon-box.php:617 includes/widgets/image-box.php:517
#: includes/widgets/tabs.php:141 includes/widgets/tabs.php:450
#: includes/widgets/testimonial.php:118 includes/widgets/testimonial.php:261
#: includes/widgets/toggle.php:145 includes/widgets/toggle.php:513
#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:58
#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:62
#: modules/atomic-widgets/elements/atomic-image/atomic-image.php:57
#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:58
#: modules/atomic-widgets/elements/atomic-svg/atomic-svg.php:55
#: modules/nested-accordion/widgets/nested-accordion.php:492
#: modules/nested-tabs/widgets/nested-tabs.php:990 assets/js/app.js:10350
#: assets/js/app.js:10842 assets/js/editor.js:39383
msgid "Content"
msgstr "Conteúdo"

#: includes/controls/groups/grid-container.php:42
#: includes/widgets/image-gallery.php:160 includes/widgets/social-icons.php:295
#: includes/widgets/text-editor.php:177
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:111
msgid "Columns"
msgstr "Colunas"

#: includes/editor-templates/hotkeys.php:135
msgid "Page Settings"
msgstr "Configurações da página"

#: modules/floating-buttons/base/widget-contact-button-base.php:1207
#: modules/floating-buttons/base/widget-contact-button-base.php:1259
#: modules/floating-buttons/base/widget-contact-button-base.php:1346
#: modules/floating-buttons/base/widget-contact-button-base.php:1555
#: modules/floating-buttons/base/widget-contact-button-base.php:1721
#: modules/floating-buttons/base/widget-contact-button-base.php:2611
#: modules/floating-buttons/base/widget-contact-button-base.php:2680
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:444
msgid "Colors"
msgstr "Cores"

#: includes/controls/image-dimensions.php:102
#: includes/editor-templates/panel.php:175
#: includes/editor-templates/templates.php:446
#: modules/history/views/revisions-panel-template.php:14
#: assets/js/editor.js:10276 assets/js/editor.js:40562
msgid "Apply"
msgstr "Aplicar"

#: modules/history/views/revisions-panel-template.php:11
#: assets/js/editor.js:47362
msgid "Discard"
msgstr "Descartar"

#: includes/controls/structure.php:65
msgid "Reset"
msgstr "Redefinir"

#: core/editor/loader/v1/templates/editor-body-v1-view.php:31
#: core/editor/loader/v2/templates/editor-body-v2-view.php:33
#: includes/editor-templates/templates.php:296 assets/js/ai-admin.js:7576
#: assets/js/ai-gutenberg.js:9424 assets/js/ai-layout.js:3057
#: assets/js/ai-media-library.js:9205 assets/js/ai-unify-product-images.js:9205
#: assets/js/ai.js:10670 assets/js/editor.js:30293
msgid "Preview"
msgstr "Pré-visualizar"

#: includes/editor-templates/hotkeys.php:81
#: includes/editor-templates/templates.php:22
#: includes/editor-templates/templates.php:23
#: includes/editor-templates/templates.php:477
#: includes/editor-templates/templates.php:489 assets/js/e-home-screen.js:246
#: assets/js/editor.js:10652 assets/js/editor.js:47361
#: assets/js/element-manager-admin.js:2412
#: assets/js/kit-elements-defaults-editor.js:598
msgid "Save"
msgstr "Salvar"

#: core/admin/admin.php:621 core/admin/menu/main.php:41
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:2
msgid "Help"
msgstr "Ajuda"

#: core/breakpoints/manager.php:314
msgid "Mobile Portrait"
msgstr "Dispositivos móveis no modo retrato"

#: core/breakpoints/manager.php:319
msgid "Mobile Landscape"
msgstr "Dispositivos móveis no modo paisagem"

#: core/settings/editor-preferences/model.php:125
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:4345
msgid "Tablet"
msgstr "Tablet"

#: core/breakpoints/manager.php:334
msgid "Laptop"
msgstr "Notebook"

#: core/base/traits/shared-widget-controls-trait.php:270
#: core/settings/editor-preferences/model.php:126
#: includes/base/element-base.php:1386 includes/editor-templates/panel.php:283
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:4338
#: assets/js/packages/editor-responsive/editor-responsive.js:2
#: assets/js/packages/editor-responsive/editor-responsive.strings.js:1
msgid "Desktop"
msgstr "Desktop"

#: includes/editor-templates/hotkeys.php:172
#: includes/editor-templates/panel.php:99
msgid "Responsive Mode"
msgstr "Modo responsivo"

#: modules/floating-buttons/documents/floating-buttons.php:31
msgid "Go To Dashboard"
msgstr "Acessar o painel"

#: includes/editor-templates/panel.php:74
#: includes/editor-templates/panel.php:75
msgid "Widgets Panel"
msgstr "Painel de widgets"

#: includes/editor-templates/panel.php:69
#: includes/editor-templates/panel.php:70
msgid "Menu"
msgstr "Menu"

#: includes/editor-templates/panel-elements.php:75
msgid "Search Widget..."
msgstr "Pesquisar widget..."

#: includes/editor-templates/global.php:34
msgid "Add New Section"
msgstr "Adicionar nova seção"

#: core/admin/admin.php:242
#: core/editor/loader/v1/templates/editor-body-v1-view.php:23
#: core/editor/loader/v2/templates/editor-body-v2-view.php:23
#: includes/editor-templates/templates.php:54 modules/gutenberg/module.php:123
#: assets/js/ai-admin.js:1741 assets/js/ai-gutenberg.js:3509
#: assets/js/ai-media-library.js:3370 assets/js/ai-unify-product-images.js:3370
#: assets/js/ai.js:4155 assets/js/app-packages.js:5283
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:236
#: assets/js/onboarding.16755744e5ca197ffd37.bundle.js:46
msgid "Loading"
msgstr "Carregando"

#. Plugin Name of the plugin
#: elementor.php app/view.php:23 core/admin/admin.php:292
#: core/admin/admin.php:409 core/admin/admin.php:487
#: core/admin/menu/main.php:17 core/admin/menu/main.php:18
#: core/documents-manager.php:384 core/upgrade/custom-tasks-manager.php:29
#: core/upgrade/manager.php:47 includes/editor-templates/navigator.php:101
#: includes/editor-templates/panel-elements.php:98
#: includes/editor-templates/panel.php:208
#: includes/editor-templates/templates.php:220 includes/plugin.php:867
#: includes/settings/admin-menu-items/admin-menu-item.php:29
#: includes/settings/settings.php:91 includes/settings/settings.php:92
#: includes/settings/settings.php:487 modules/compatibility-tag/module.php:36
#: modules/history/views/history-panel-template.php:23
#: modules/history/views/revisions-panel-template.php:38
#: assets/js/app-packages.js:1821 assets/js/app.js:2010
msgid "Elementor"
msgstr "Elementor"

#: includes/controls/repeater.php:178
#: modules/floating-buttons/base/widget-contact-button-base.php:1005
#: modules/floating-buttons/base/widget-floating-bars-base.php:367
#: modules/nested-accordion/widgets/nested-accordion.php:161
msgid "Add Item"
msgstr "Adicionar item"

#: core/kits/views/trash-kit-confirmation.php:30
#: includes/editor-templates/hotkeys.php:66
#: includes/editor-templates/templates.php:168
#: includes/editor-templates/templates.php:339
#: includes/editor-templates/templates.php:372
#: includes/editor-templates/templates.php:429 assets/js/editor.js:11020
#: assets/js/editor.js:11039 assets/js/editor.js:11200
#: assets/js/editor.js:30354 assets/js/editor.js:32835
#: assets/js/editor.js:50080 assets/js/import-export-admin.js:272
#: assets/js/packages/editor-global-classes/editor-global-classes.js:2
#: assets/js/packages/editor-global-classes/editor-global-classes.js:6
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:5
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:18
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:21
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:29
msgid "Delete"
msgstr "Excluir"

#: includes/controls/icon.php:876 includes/controls/icon.php:877
msgid "Select Icon"
msgstr "Selecionar ícone"

#: core/kits/manager.php:139 includes/controls/groups/background.php:329
#: includes/controls/groups/background.php:487
#: includes/controls/groups/flex-item.php:24
#: includes/controls/groups/flex-item.php:93
#: includes/controls/groups/flex-item.php:142
#: includes/controls/groups/image-size.php:383
#: includes/elements/container.php:1430 includes/elements/container.php:1474
#: includes/elements/section.php:293 includes/maintenance-mode.php:238
#: includes/widgets/common-base.php:144 includes/widgets/common-base.php:234
#: includes/widgets/common-base.php:306 includes/widgets/common-base.php:350
#: includes/widgets/common-base.php:1046 includes/widgets/common-base.php:1103
#: includes/widgets/image-carousel.php:764
#: includes/widgets/image-gallery.php:290 includes/widgets/social-icons.php:213
#: includes/widgets/social-icons.php:366
#: modules/floating-buttons/base/widget-contact-button-base.php:1212
#: modules/floating-buttons/base/widget-contact-button-base.php:1264
#: modules/floating-buttons/base/widget-contact-button-base.php:1351
#: modules/floating-buttons/base/widget-contact-button-base.php:1560
#: modules/floating-buttons/base/widget-contact-button-base.php:1726
#: modules/floating-buttons/base/widget-contact-button-base.php:2290
#: modules/floating-buttons/base/widget-contact-button-base.php:2616
#: modules/floating-buttons/base/widget-contact-button-base.php:2685
#: modules/floating-buttons/base/widget-contact-button-base.php:2816
#: modules/shapes/module.php:52 assets/js/editor.js:47912
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.js:85
#: assets/js/packages/editor-controls/editor-controls.strings.js:47
#: assets/js/packages/editor-controls/editor-controls.strings.js:93
#: assets/js/packages/editor-controls/editor-controls.strings.js:109
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:170
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:177
msgid "Custom"
msgstr "Personalizado"

#: core/kits/documents/tabs/theme-style-buttons.php:91
#: core/kits/documents/tabs/theme-style-form-fields.php:117
#: core/kits/documents/tabs/theme-style-images.php:63
#: core/kits/documents/tabs/theme-style-typography.php:119
#: includes/base/element-base.php:883
#: includes/controls/groups/typography.php:152
#: includes/controls/groups/typography.php:168
#: includes/controls/groups/typography.php:178 includes/elements/column.php:286
#: includes/elements/column.php:401 includes/elements/column.php:450
#: includes/elements/column.php:557 includes/elements/column.php:893
#: includes/elements/container.php:644 includes/elements/container.php:773
#: includes/elements/container.php:847 includes/elements/container.php:992
#: includes/elements/container.php:1831 includes/elements/section.php:543
#: includes/elements/section.php:654 includes/elements/section.php:718
#: includes/elements/section.php:841 includes/elements/section.php:1340
#: includes/widgets/alert.php:412 includes/widgets/common-base.php:744
#: includes/widgets/common-base.php:791 includes/widgets/common-base.php:866
#: includes/widgets/google-maps.php:221 includes/widgets/heading.php:312
#: includes/widgets/heading.php:344 includes/widgets/icon-box.php:392
#: includes/widgets/icon-box.php:663 includes/widgets/icon-list.php:426
#: includes/widgets/icon-list.php:655 includes/widgets/icon.php:213
#: includes/widgets/image-box.php:414 includes/widgets/image-box.php:563
#: includes/widgets/image.php:434 includes/widgets/text-editor.php:336
#: includes/widgets/traits/button-trait.php:339
#: modules/floating-buttons/base/widget-contact-button-base.php:1199
#: modules/floating-buttons/base/widget-contact-button-base.php:1405
#: modules/floating-buttons/base/widget-contact-button-base.php:1977
#: modules/floating-buttons/base/widget-contact-button-base.php:2472
#: modules/floating-buttons/base/widget-contact-button-base.php:2603
#: modules/floating-buttons/base/widget-contact-button-base.php:2788
#: modules/floating-buttons/base/widget-floating-bars-base.php:656
#: modules/floating-buttons/base/widget-floating-bars-base.php:888
#: modules/floating-buttons/base/widget-floating-bars-base.php:1374
#: modules/nested-accordion/widgets/nested-accordion.php:671
#: modules/nested-accordion/widgets/nested-accordion.php:721
#: modules/nested-tabs/widgets/nested-tabs.php:493
#: modules/nested-tabs/widgets/nested-tabs.php:737
#: modules/nested-tabs/widgets/nested-tabs.php:937
#: modules/shapes/widgets/text-path.php:409
#: modules/shapes/widgets/text-path.php:501
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:105
msgid "Normal"
msgstr "Normal"

#: includes/controls/groups/typography.php:173
msgctxt "Typography Control"
msgid "Style"
msgstr "Estilo"

#: includes/controls/groups/typography.php:160
msgctxt "Typography Control"
msgid "Transform"
msgstr "Transformação"

#: includes/controls/groups/typography.php:138
#: includes/controls/groups/typography.php:299
msgctxt "Typography Control"
msgid "Weight"
msgstr "Peso"

#: includes/controls/groups/typography.php:106
msgctxt "Typography Control"
msgid "Family"
msgstr "Família"

#: includes/controls/groups/typography.php:113
msgctxt "Typography Control"
msgid "Size"
msgstr "Tamanho"

#: includes/controls/groups/border.php:68 includes/widgets/divider.php:345
#: includes/widgets/icon-list.php:303
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:223
msgid "Dashed"
msgstr "Tracejado"

#: includes/controls/groups/border.php:67 includes/widgets/divider.php:344
#: includes/widgets/icon-list.php:302
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:224
msgid "Dotted"
msgstr "Pontilhado"

#: includes/controls/groups/border.php:66 includes/widgets/divider.php:343
#: includes/widgets/icon-list.php:301
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:225
msgid "Double"
msgstr "Duplo"

#: includes/controls/groups/border.php:65 includes/widgets/divider.php:342
#: includes/widgets/icon-list.php:300 includes/widgets/star-rating.php:188
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:222
msgid "Solid"
msgstr "Sólido"

#: core/kits/documents/tabs/settings-background.php:79
#: core/kits/documents/tabs/settings-lightbox.php:99
#: core/kits/documents/tabs/settings-lightbox.php:116
#: includes/controls/animation.php:155 includes/controls/groups/border.php:64
#: includes/controls/groups/flex-item.php:130
#: includes/controls/groups/typography.php:193
#: includes/controls/hover-animation.php:129 includes/controls/icons.php:108
#: includes/controls/icons.php:194 includes/elements/container.php:1169
#: includes/elements/section.php:964 includes/widgets/divider.php:501
#: includes/widgets/image-carousel.php:218
#: includes/widgets/image-carousel.php:341
#: includes/widgets/image-carousel.php:393
#: includes/widgets/image-gallery.php:174
#: includes/widgets/image-gallery.php:192 includes/widgets/image.php:161
#: includes/widgets/image.php:196 includes/widgets/video.php:579
#: modules/nested-tabs/widgets/nested-tabs.php:406 assets/js/ai-admin.js:11303
#: assets/js/ai-admin.js:11309 assets/js/ai-admin.js:11321
#: assets/js/ai-admin.js:11332 assets/js/ai-admin.js:11343
#: assets/js/ai-admin.js:11354 assets/js/ai-admin.js:11370
#: assets/js/ai-gutenberg.js:13151 assets/js/ai-gutenberg.js:13157
#: assets/js/ai-gutenberg.js:13169 assets/js/ai-gutenberg.js:13180
#: assets/js/ai-gutenberg.js:13191 assets/js/ai-gutenberg.js:13202
#: assets/js/ai-gutenberg.js:13218 assets/js/ai-media-library.js:12932
#: assets/js/ai-media-library.js:12938 assets/js/ai-media-library.js:12950
#: assets/js/ai-media-library.js:12961 assets/js/ai-media-library.js:12972
#: assets/js/ai-media-library.js:12983 assets/js/ai-media-library.js:12999
#: assets/js/ai-unify-product-images.js:12932
#: assets/js/ai-unify-product-images.js:12938
#: assets/js/ai-unify-product-images.js:12950
#: assets/js/ai-unify-product-images.js:12961
#: assets/js/ai-unify-product-images.js:12972
#: assets/js/ai-unify-product-images.js:12983
#: assets/js/ai-unify-product-images.js:12999 assets/js/ai.js:14397
#: assets/js/ai.js:14403 assets/js/ai.js:14415 assets/js/ai.js:14426
#: assets/js/ai.js:14437 assets/js/ai.js:14448 assets/js/ai.js:14464
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:73
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:82
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:138
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:191
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:192
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:221
msgid "None"
msgstr "Nenhum"

#: includes/controls/groups/background.php:457
msgctxt "Background Control"
msgid "Repeat"
msgstr "Repetir"

#: includes/controls/groups/background.php:434
msgctxt "Background Control"
msgid "Fixed"
msgstr "Fixo"

#: includes/controls/groups/background.php:433
msgctxt "Background Control"
msgid "Scroll"
msgstr "Rolar"

#: includes/controls/groups/background.php:428
msgctxt "Background Control"
msgid "Attachment"
msgstr "Anexo"

#: includes/fonts.php:76
msgid "Google"
msgstr "Google"

#: includes/fonts.php:71
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:243
msgid "System"
msgstr "Sistema"

#: core/kits/documents/tabs/theme-style-typography.php:109
#: includes/elements/container.php:597 includes/widgets/audio.php:111
#: includes/widgets/heading.php:177 includes/widgets/icon-box.php:195
#: includes/widgets/icon-list.php:169 includes/widgets/icon.php:164
#: includes/widgets/image-box.php:170 includes/widgets/image-carousel.php:337
#: includes/widgets/image-carousel.php:351
#: includes/widgets/image-gallery.php:186 includes/widgets/image.php:192
#: includes/widgets/image.php:209 includes/widgets/social-icons.php:194
#: includes/widgets/testimonial.php:183
#: includes/widgets/traits/button-trait.php:99 includes/widgets/video.php:150
#: includes/widgets/video.php:175 includes/widgets/video.php:199
#: modules/floating-buttons/base/widget-contact-button-base.php:418
#: modules/floating-buttons/base/widget-contact-button-base.php:931
#: modules/floating-buttons/base/widget-contact-button-base.php:1059
#: modules/floating-buttons/base/widget-floating-bars-base.php:164
#: modules/floating-buttons/base/widget-floating-bars-base.php:350
#: modules/floating-buttons/base/widget-floating-bars-base.php:568
#: modules/link-in-bio/base/widget-link-in-bio-base.php:263
#: modules/link-in-bio/base/widget-link-in-bio-base.php:405
#: modules/link-in-bio/base/widget-link-in-bio-base.php:640
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1096
#: modules/shapes/widgets/text-path.php:158
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:18
msgid "Link"
msgstr "Link"

#: includes/base/element-base.php:1322 includes/controls/dimensions.php:85
#: includes/elements/column.php:735 includes/elements/container.php:1540
#: includes/elements/section.php:1197 includes/widgets/common-base.php:458
#: includes/widgets/common-base.php:459 includes/widgets/divider.php:474
#: includes/widgets/divider.php:770 includes/widgets/divider.php:936
#: includes/widgets/heading.php:255 includes/widgets/icon-box.php:243
#: includes/widgets/icon-box.php:297 includes/widgets/icon-list.php:264
#: includes/widgets/icon-list.php:546 includes/widgets/icon.php:189
#: includes/widgets/image-box.php:217 includes/widgets/image-box.php:272
#: includes/widgets/image-carousel.php:538
#: includes/widgets/image-carousel.php:837
#: includes/widgets/image-gallery.php:371 includes/widgets/image.php:263
#: includes/widgets/image.php:581 includes/widgets/social-icons.php:328
#: includes/widgets/star-rating.php:219 includes/widgets/tabs.php:426
#: includes/widgets/testimonial.php:226 includes/widgets/text-editor.php:262
#: includes/widgets/traits/button-trait.php:255
#: modules/floating-buttons/base/widget-contact-button-base.php:1134
#: modules/floating-buttons/base/widget-contact-button-base.php:2400
#: modules/floating-buttons/base/widget-contact-button-base.php:2938
#: modules/floating-buttons/base/widget-floating-bars-base.php:452
#: modules/floating-buttons/base/widget-floating-bars-base.php:1053
#: modules/shapes/widgets/text-path.php:177
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:12
#: assets/js/packages/editor-controls/editor-controls.strings.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:150
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:151
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:214
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:218
msgid "Left"
msgstr "Esquerda"

#: includes/base/element-base.php:1358 includes/controls/dimensions.php:84
#: includes/elements/column.php:190 includes/elements/container.php:1178
#: includes/elements/container.php:1660 includes/elements/section.php:408
#: includes/elements/section.php:427 includes/elements/section.php:973
#: includes/widgets/common-base.php:575 includes/widgets/counter.php:355
#: includes/widgets/icon-box.php:277 includes/widgets/image-box.php:252
#: modules/floating-buttons/base/widget-contact-button-base.php:3000
#: modules/floating-buttons/base/widget-floating-bars-base.php:1441
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:14
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:154
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:216
msgid "Bottom"
msgstr "Inferior"

#: includes/base/element-base.php:1330 includes/controls/dimensions.php:83
#: includes/elements/column.php:743 includes/elements/container.php:1541
#: includes/elements/section.php:1205 includes/widgets/common-base.php:458
#: includes/widgets/common-base.php:459 includes/widgets/divider.php:482
#: includes/widgets/divider.php:778 includes/widgets/divider.php:944
#: includes/widgets/heading.php:263 includes/widgets/icon-box.php:251
#: includes/widgets/icon-box.php:305 includes/widgets/icon-list.php:272
#: includes/widgets/icon-list.php:554 includes/widgets/icon.php:197
#: includes/widgets/image-box.php:225 includes/widgets/image-box.php:280
#: includes/widgets/image-carousel.php:539
#: includes/widgets/image-carousel.php:845
#: includes/widgets/image-gallery.php:379 includes/widgets/image.php:271
#: includes/widgets/image.php:589 includes/widgets/social-icons.php:336
#: includes/widgets/star-rating.php:227 includes/widgets/tabs.php:434
#: includes/widgets/testimonial.php:234 includes/widgets/text-editor.php:270
#: includes/widgets/traits/button-trait.php:263
#: modules/floating-buttons/base/widget-contact-button-base.php:1138
#: modules/floating-buttons/base/widget-contact-button-base.php:2404
#: modules/floating-buttons/base/widget-contact-button-base.php:2946
#: modules/floating-buttons/base/widget-floating-bars-base.php:456
#: modules/floating-buttons/base/widget-floating-bars-base.php:1057
#: modules/shapes/widgets/text-path.php:185
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:13
#: assets/js/packages/editor-controls/editor-controls.strings.js:15
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:149
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:152
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:215
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:217
msgid "Right"
msgstr "Direita"

#: includes/base/element-base.php:1350 includes/controls/dimensions.php:82
#: includes/elements/column.php:188 includes/elements/container.php:1177
#: includes/elements/container.php:1656 includes/elements/section.php:406
#: includes/elements/section.php:425 includes/elements/section.php:972
#: includes/widgets/common-base.php:571 includes/widgets/counter.php:347
#: includes/widgets/icon-box.php:247 includes/widgets/icon-box.php:269
#: includes/widgets/image-box.php:221 includes/widgets/image-box.php:244
#: includes/widgets/testimonial.php:205 includes/widgets/video.php:967
#: modules/floating-buttons/base/widget-contact-button-base.php:2992
#: modules/floating-buttons/base/widget-floating-bars-base.php:1437
#: assets/js/packages/editor-controls/editor-controls.js:69
#: assets/js/packages/editor-controls/editor-controls.strings.js:11
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:16
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:153
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:213
msgid "Top"
msgstr "Superior"

#: core/experiments/manager.php:395
#: core/kits/documents/tabs/settings-background.php:78
#: core/settings/editor-preferences/model.php:123
#: includes/base/widget-base.php:297 includes/controls/animation.php:154
#: includes/controls/font.php:66 includes/controls/groups/background.php:319
#: includes/controls/groups/background.php:432
#: includes/controls/groups/background.php:462
#: includes/controls/groups/background.php:483
#: includes/controls/groups/background.php:688
#: includes/controls/groups/background.php:707
#: includes/controls/groups/border.php:63
#: includes/controls/groups/flex-item.php:23
#: includes/controls/groups/typography.php:151
#: includes/controls/groups/typography.php:164
#: includes/controls/groups/typography.php:177
#: includes/controls/groups/typography.php:189
#: includes/editor-templates/panel.php:254 includes/elements/column.php:187
#: includes/elements/column.php:215 includes/elements/column.php:258
#: includes/elements/container.php:547 includes/elements/container.php:570
#: includes/elements/container.php:1528 includes/elements/section.php:287
#: includes/elements/section.php:325 includes/elements/section.php:365
#: includes/elements/section.php:424 includes/elements/section.php:452
#: includes/elements/section.php:492 includes/settings/settings.php:386
#: includes/widgets/common-base.php:231 includes/widgets/common-base.php:448
#: includes/widgets/divider.php:835 includes/widgets/heading.php:194
#: includes/widgets/icon-box.php:133 includes/widgets/icon-list.php:122
#: includes/widgets/icon.php:134 includes/widgets/image-carousel.php:167
#: includes/widgets/image-carousel.php:185
#: includes/widgets/image-carousel.php:376
#: includes/widgets/image-carousel.php:763
#: includes/widgets/image-gallery.php:210
#: includes/widgets/image-gallery.php:266
#: includes/widgets/image-gallery.php:289 includes/widgets/image.php:235
#: includes/widgets/image.php:382 includes/widgets/progress.php:171
#: includes/widgets/text-editor.php:181 includes/widgets/text-editor.php:424
#: includes/widgets/traits/button-trait.php:71
#: modules/element-cache/module.php:127
#: modules/floating-buttons/base/widget-contact-button-base.php:1211
#: modules/floating-buttons/base/widget-contact-button-base.php:1263
#: modules/floating-buttons/base/widget-contact-button-base.php:1350
#: modules/floating-buttons/base/widget-contact-button-base.php:1559
#: modules/floating-buttons/base/widget-contact-button-base.php:1725
#: modules/floating-buttons/base/widget-contact-button-base.php:2289
#: modules/floating-buttons/base/widget-contact-button-base.php:2615
#: modules/floating-buttons/base/widget-contact-button-base.php:2684
#: modules/floating-buttons/base/widget-contact-button-base.php:2815
#: modules/link-in-bio/base/widget-link-in-bio-base.php:179
#: modules/page-templates/module.php:301
#: modules/shapes/widgets/text-path.php:203 assets/js/editor.js:47904
#: assets/js/editor.js:47915
msgid "Default"
msgstr "Padrão"

#: core/admin/admin.php:223 core/admin/admin.php:231 core/base/document.php:651
#: modules/admin-bar/module.php:124 modules/gutenberg/module.php:101
#: modules/gutenberg/module.php:112 modules/gutenberg/module.php:134
#: assets/js/e-wc-product-editor.js:2677
msgid "Edit with Elementor"
msgstr "Editar com Elementor"

#: modules/gutenberg/module.php:98
msgid "&#8592; Back to WordPress Editor"
msgstr "&#8592; Voltar ao editor do WordPress"