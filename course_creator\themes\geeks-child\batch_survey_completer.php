<?php
/**
 * Batch mark certificate survey as completed for specified users
 * and simulate ONE generic NPS response if they haven't completed a survey yet,
 * regardless of their course progress or specific course enrollments for this entry.
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
    die;
}

// --- Helper functions for random answers (panapana_get_random_q1_recomendacao, etc.) ---
// These remain the same.
/**
 * Generates a random answer for Question 1 (Recomendacao 0-10)
 */
function panapana_get_random_q1_recomendacao() {
    $rand = mt_rand(1, 100);
    if ($rand <= 70) { // 70% chance for 10
        return 10;
    } elseif ($rand <= 90) { // 20% chance for 8-9 (71-90)
        return mt_rand(8, 9);
    } else { // 10% chance for 3-7 (91-100)
        return mt_rand(3, 7);
    }
}

/**
 * Generates a random answer for Question 2 (Aprendizado Aplicar)
 */
function panapana_get_random_q2_aprendizado() {
    $rand = mt_rand(1, 1000);
    if ($rand <= 800) { // 80% for Sim
        return 'Sim';
    } elseif ($rand <= 970) { // 17% for Parcialmente
        return 'Parcialmente';
    } else { // 3% for Não
        return 'Não';
    }
}

/**
 * Generates a random answer for Question 3 (Qualidade Geral)
 */
function panapana_get_random_q3_qualidade() {
    $rand = mt_rand(1, 1000);
    if ($rand <= 700) { // 70% for Sim, superou
        return 'Sim, superou';
    } elseif ($rand <= 970) { // 27% for Sim, atendeu
        return 'Sim, atendeu';
    } else { // 3% for Não, ficou abaixo
        return 'Não, ficou abaixo do esperado';
    }
}


/**
 * Handles the batch survey completion process with a generic NPS entry.
 */
function panapana_process_batch_generic_survey_completion() {
    // 1. Security & Capability Checks
    if ( ! isset( $_POST['batch_survey_complete_nonce'] ) || ! wp_verify_nonce( sanitize_key( $_POST['batch_survey_complete_nonce'] ), 'batch_survey_complete_action' ) ) {
        wp_die( 'Security check failed. Invalid nonce.', 'Error', array('response' => 403) );
    }
    if ( ! current_user_can( 'manage_options' ) ) {
        wp_die( 'You do not have sufficient permissions.', 'Error', array('response' => 403) );
    }

    global $wpdb;
    $nps_table_name = $wpdb->prefix . 'nps_responses';

    // 2. User IDs to process
    $user_ids_raw = "70, 165, 170, 171, 178, 179, 189, 200, 203, 206, 208, 212, 213, 216, 217, 218, 221, 222, 228, 236, 237, 240, 243, 244, 246, 247, 248, 249, 250, 254, 256, 258, 259, 260, 263, 265, 266, 267, 272, 274, 275, 276, 279, 280, 281, 282, 285, 286, 288, 289, 293, 299, 301, 302, 304, 309, 313, 314, 321, 322, 323, 324, 327, 328, 329, 334, 338, 339, 342, 343, 344, 346, 347, 349, 355, 357, 359, 362, 363, 366, 371, 372, 375, 376, 379, 380, 383, 386, 387, 389, 390, 391, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 409, 410, 412, 414, 415, 418, 421, 423, 426, 428, 429, 431, 432, 433, 435, 436, 439, 442, 443, 445, 446, 448, 453, 456, 458, 459, 460, 463, 465, 466, 468, 470, 473, 474, 476, 477, 478, 481, 483, 492, 494, 495, 496, 499, 500, 502, 505, 506, 507, 511, 513, 514, 515, 516, 517, 520, 521, 522, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 539, 540, 541, 543, 544, 545, 548, 550, 551, 552, 553, 554, 556, 557, 558, 559, 561, 562, 564, 566, 570, 571, 577, 578, 581, 597, 622, 624, 625, 627, 628, 629, 631, 632, 633, 634, 635, 637, 638, 639, 640, 641, 642, 643, 644, 645, 647, 648, 649, 650, 651, 652, 653, 656, 657, 658, 659, 660, 661, 662";
    $user_ids_to_process = array_map( 'intval', explode( ',', $user_ids_raw ) );
    $user_ids_to_process = array_unique( array_filter( $user_ids_to_process ) );

    $results = [
        'survey_meta_set_and_nps_added' => [],
        'already_had_survey_meta' => [],
        'failed_db_insert' => [],
        'invalid_user' => [],
    ];

    // 3. Process each user
    foreach ( $user_ids_to_process as $user_id ) {
        $user_data = get_userdata( $user_id );
        if ( ! $user_data ) {
            $results['invalid_user'][] = "User ID {$user_id}: Does not exist.";
            continue;
        }

        // Check if the user already has the general survey completion meta
        if ( get_user_meta( $user_id, 'certificate_survey_completed', true ) ) {
            $results['already_had_survey_meta'][] = "User ID {$user_id} ({$user_data->user_login}): Already has 'certificate_survey_completed' meta. Skipped adding new NPS entry.";
            continue;
        }

        // If meta not set, proceed to add ONE generic NPS entry and set the meta
        $q1_answer = panapana_get_random_q1_recomendacao();
        $q2_answer = panapana_get_random_q2_aprendizado();
        $q3_answer = panapana_get_random_q3_qualidade();

        $insert_data = array(
            'user_id'               => $user_id,
            'course_id'             => null, // Storing NULL as this is a generic survey trigger
            'recommended'           => $q1_answer,
            'utilidade_aulas'       => $q2_answer,
            'qualidade_conteudo'    => $q3_answer,
            'sugestoes'             => 'accepted_by_batch_script_v3_generic',
            'ip_address'            => 'BATCH_SCRIPT_V3',
            'submission_date'       => current_time( 'mysql' ),
        );
        // For $wpdb->insert, if a value is null, its corresponding format can be %s or omitted for $wpdb to handle.
        // Or, explicitly use null in the formats array where appropriate if your WP version is older.
        // Modern $wpdb->insert handles null values in $insert_data correctly with %s or %d (becomes 0 for %d).
        // To be explicit for `course_id` = null with a format:
        $insert_formats = array( '%d', null, '%d', '%s', '%s', '%s', '%s', '%s' );


        $insert_result = $wpdb->insert( $nps_table_name, $insert_data, $insert_formats );

        if ( false === $insert_result ) {
            $results['failed_db_insert'][] = "User ID {$user_id} ({$user_data->user_login}): Failed to insert generic NPS response. DB Error: " . $wpdb->last_error;
        } else {
            update_user_meta( $user_id, 'certificate_survey_completed', true ); // Mark general survey as completed
            $results['survey_meta_set_and_nps_added'][] = "User ID {$user_id} ({$user_data->user_login}): 'certificate_survey_completed' meta SET. Generic NPS entry added. Q1:{$q1_answer}, Q2:{$q2_answer}, Q3:{$q3_answer}.";
            
            // IMPORTANT: Since this is a generic survey not tied to a specific course completion event in this script,
            // calling do_action('tutor_course_completed', $course_id, $user_id) here is problematic
            // as we don't have a specific $course_id context for *this* generic survey.
            // If a course completion should be triggered, it needs separate logic or manual intervention
            // or if your "Mark as Complete >X%" button handles it based on this new meta.
        }
    }

    // 4. Display Results
    ob_start();
    ?>
    <div class="wrap" style="padding: 20px; font-family: sans-serif;">
        <h1><span class="dashicons dashicons-universal-access-alt" style="color: #0073aa;"></span> Resultados da Atribuição Genérica de Pesquisa NPS</h1>
        <p><em>Processamento concluído. Para cada usuário na lista que ainda não tinha o metadado 'certificate_survey_completed', o metadado foi definido e uma entrada genérica (sem ID de curso específico) foi adicionada à tabela de respostas NPS.</em></p>
        <hr>

        <?php foreach ( $results as $status => $messages ) : ?>
            <?php if ( ! empty( $messages ) ) : ?>
                <?php
                $status_title = ucwords( str_replace( '_', ' ', $status ) );
                 $color_map = [
                    'survey_meta_set_and_nps_added' => '#4CAF50', // Green
                    'already_had_survey_meta' => '#2196F3', // Blue
                    'failed_db_insert' => '#f44336', // Red
                    'invalid_user' => '#607d8b', // Blue Grey
                ];
                $bg_color_map = [
                    'survey_meta_set_and_nps_added' => '#f0fff0',
                    'already_had_survey_meta' => '#e3f2fd',
                    'failed_db_insert' => '#ffebee',
                    'invalid_user' => '#eceff1',
                ];
                $current_color = $color_map[$status] ?? '#777';
                $current_bg_color = $bg_color_map[$status] ?? '#f9f9f9';
                ?>
                <div style="margin-bottom: 20px; padding: 10px; border-left: 4px solid <?php echo $current_color; ?>; background-color: <?php echo $current_bg_color; ?>;">
                    <h2 style="margin-top: 0; color: <?php echo $current_color; ?>;"><?php echo esc_html( $status_title ); ?> (<?php echo count( $messages ); ?>):</h2>
                    <ul style="list-style-type: disc; margin-left: 20px; max-height: 300px; overflow-y: auto;">
                        <?php foreach ( $messages as $message ) : ?>
                            <li><?php echo esc_html( $message ); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
        <?php endforeach; ?>
        
        <hr>
        <p><a href="<?php echo esc_url( admin_url() ); ?>" class="button button-secondary">Voltar ao Painel</a></p>
    </div>
    <?php
    echo ob_get_clean();
    exit;
}
// Update action hook name for clarity
add_action( 'admin_post_panapana_batch_generic_survey_complete', 'panapana_process_batch_generic_survey_completion' );


/**
 * Shortcode to display the button for batch generic survey completion.
 * Usage: [batch_generic_survey_complete_button]
 */
function panapana_batch_generic_survey_complete_button_shortcode() {
    if ( ! current_user_can( 'manage_options' ) ) {
        return '<p>Você não tem permissão para acessar esta funcionalidade.</p>';
    }
    ob_start();
    ?>
    <div class="panapana-batch-survey-section" style="margin: 20px 0; padding: 20px; border: 1px solid #ccd0d4; background-color: #fff; border-left: 5px solid #d9534f; box-shadow: 0 1px 1px rgba(0,0,0,.04);">
        <h3 style="margin-top:0; color: #d9534f;"><span class="dashicons dashicons-warning" style="color: #d9534f;"></span> Ação em Lote: Definir Pesquisa NPS como Concluída (Genérico)</h3>
        <p style="font-weight: bold; color: #d9534f;">ATENÇÃO: Esta ação irá processar a lista de usuários (pré-definida no código). Para cada usuário que AINDA NÃO completou a pesquisa NPS (não possui o metadado 'certificate_survey_completed'), ela definirá esse metadado e INSERIRÁ UMA entrada genérica de respostas aleatórias (sem ID de curso específico) na tabela NPS. Use com extremo cuidado. Faça um backup do banco de dados antes de prosseguir.</p>
        <form method="post" action="<?php echo esc_url( admin_url('admin-post.php') ); ?>" onsubmit="return confirm('TEM CERTEZA ABSOLUTA que deseja prosseguir? Esta ação irá modificar metadados de usuários e adicionar UMA entrada genérica na tabela de respostas NPS para os usuários aplicáveis. Backup recomendado!');">
            <input type="hidden" name="action" value="panapana_batch_generic_survey_complete">
            <?php wp_nonce_field( 'batch_survey_complete_action', 'batch_survey_complete_nonce' ); // Nonce can be reused ?>
            <?php submit_button( 'Processar Respostas NPS Genéricas em Lote AGORA', 'primary', 'panapana_submit_batch_generic_survey', true, array('style' => 'background-color: #d9534f; border-color: #d43f3a; box-shadow: none; text-shadow: none;') ); ?>
        </form>
        <p class="description"><em>Isso afetará os usuários da lista pré-definida no arquivo <code>batch_survey_completer.php</code>.</em></p>
    </div>
    <?php
    return ob_get_clean();
}
add_shortcode( 'batch_generic_survey_complete_button', 'panapana_batch_generic_survey_complete_button_shortcode' );