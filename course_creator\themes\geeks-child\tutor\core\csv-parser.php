<?php
/**
 * Tutor LMS Course Automation - CSV Parser
 *
 * This file handles CSV file reading, validation, and data organization
 * for the course automation system.
 *
 * @package TutorLMS_CourseAutomation
 * @subpackage Core
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Processes the uploaded CSV file and creates courses.
 *
 * @return string|WP_Error Success message or error object.
 */
function panapana_process_csv_upload() {
	// Validate file upload
	if ( ! isset( $_FILES['csv_file'] ) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK ) {
		return new WP_Error( 'upload_error', 'Erro no upload do arquivo CSV.' );
	}

	$csv_file = $_FILES['csv_file'];

	// Validate file type
	$file_extension = strtolower( pathinfo( $csv_file['name'], PATHINFO_EXTENSION ) );
	if ( $file_extension !== 'csv' ) {
		return new WP_Error( 'invalid_file_type', 'Por favor, envie apenas arquivos CSV.' );
	}

	// Process the uploaded file
	return panapana_parse_csv_and_create_courses( $csv_file['tmp_name'] );
}

/**
 * Parses CSV file and creates courses from the data.
 *
 * @param string $csv_file_path Path to the uploaded CSV file.
 * @return string|WP_Error Success message or error object.
 */
function panapana_parse_csv_and_create_courses( $csv_file_path ) {
	// Read and parse CSV file
	$csv_data = panapana_read_csv_file( $csv_file_path );
	if ( is_wp_error( $csv_data ) ) {
		return $csv_data;
	}

	// Group data by course for processing
	$courses_data = panapana_group_csv_data_by_course( $csv_data );
	if ( is_wp_error( $courses_data ) ) {
		return $courses_data;
	}

	$created_courses = array();
	$total_items = 0;

	// Process each course
	foreach ( $courses_data as $course_title => $course_data ) {
		$result = panapana_create_course_from_data( $course_data );
		if ( is_wp_error( $result ) ) {
			return new WP_Error( 'course_creation_error',
				"Erro ao criar curso '{$course_title}': " . $result->get_error_message() );
		}

		$created_courses[] = $course_title;
		$total_items += count( $course_data['topics'] );
	}

	$course_count = count( $created_courses );
	$course_list = implode( ', ', $created_courses );

	return "Sucesso! {$course_count} curso(s) criado(s): {$course_list}. Total de {$total_items} itens processados.";
}

/**
 * Reads and validates CSV file structure.
 *
 * @param string $file_path Path to CSV file.
 * @return array|WP_Error Array of CSV rows or error object.
 */
function panapana_read_csv_file( $file_path ) {
	if ( ! file_exists( $file_path ) || ! is_readable( $file_path ) ) {
		return new WP_Error( 'file_error', 'Arquivo CSV não encontrado ou não legível.' );
	}

	$csv_data = array();
	$headers = array();
	$row_number = 0;

	if ( ( $handle = fopen( $file_path, 'r' ) ) !== FALSE ) {
		while ( ( $row = fgetcsv( $handle, 1000, ',' ) ) !== FALSE ) {
			$row_number++;

			if ( $row_number === 1 ) {
				// First row should be headers
				$headers = array_map( 'trim', $row );

				// Validate required columns
				$required_columns = array( 'row_type', 'course_title' );
				foreach ( $required_columns as $required_col ) {
					if ( ! in_array( $required_col, $headers ) ) {
						fclose( $handle );
						return new WP_Error( 'invalid_csv', "Coluna obrigatória '{$required_col}' não encontrada no CSV." );
					}
				}
				continue;
			}

			// Skip empty rows
			if ( empty( array_filter( $row ) ) ) {
				continue;
			}

			// Create associative array with headers
			$row_data = array();
			foreach ( $headers as $index => $header ) {
				$row_data[ $header ] = isset( $row[ $index ] ) ? trim( $row[ $index ] ) : '';
			}

			// Validate row type
			$valid_row_types = array( 'course', 'topic', 'lesson', 'quiz', 'question', 'answer' );
			if ( ! in_array( $row_data['row_type'], $valid_row_types ) ) {
				fclose( $handle );
				return new WP_Error( 'invalid_row_type',
					"Tipo de linha inválido '{$row_data['row_type']}' na linha {$row_number}. Tipos válidos: " . implode( ', ', $valid_row_types ) );
			}

			$csv_data[] = $row_data;
		}
		fclose( $handle );
	} else {
		return new WP_Error( 'file_open_error', 'Não foi possível abrir o arquivo CSV.' );
	}

	if ( empty( $csv_data ) ) {
		return new WP_Error( 'empty_csv', 'Arquivo CSV está vazio ou não contém dados válidos.' );
	}

	return $csv_data;
}

/**
 * Groups CSV data by course for organized processing.
 *
 * @param array $csv_data Array of CSV rows.
 * @return array|WP_Error Grouped course data or error object.
 */
function panapana_group_csv_data_by_course( $csv_data ) {
	$courses = array();

	foreach ( $csv_data as $row ) {
		$course_title = $row['course_title'];

		if ( empty( $course_title ) ) {
			return new WP_Error( 'missing_course_title', 'course_title é obrigatório para todas as linhas.' );
		}

		// Initialize course structure if not exists
		if ( ! isset( $courses[ $course_title ] ) ) {
			$courses[ $course_title ] = array(
				'course_info' => null,
				'topics' => array(),
				'current_topic' => null,
				'current_quiz' => null,
				'current_question' => null,
			);
		}

		$course = &$courses[ $course_title ];

		switch ( $row['row_type'] ) {
			case 'course':
				$course['course_info'] = $row;
				break;

			case 'topic':
				$topic_title = $row['topic_title'];
				if ( empty( $topic_title ) ) {
					return new WP_Error( 'missing_topic_title', 'topic_title é obrigatório para linhas do tipo topic.' );
				}

				$course['topics'][ $topic_title ] = array(
					'topic_info' => $row,
					'lessons' => array(),
					'quizzes' => array(),
				);
				$course['current_topic'] = $topic_title;
				break;

			case 'lesson':
				if ( empty( $course['current_topic'] ) ) {
					return new WP_Error( 'lesson_without_topic', 'Aula encontrada sem tópico definido.' );
				}
				$course['topics'][ $course['current_topic'] ]['lessons'][] = $row;
				break;

			case 'quiz':
				if ( empty( $course['current_topic'] ) ) {
					return new WP_Error( 'quiz_without_topic', 'Quiz encontrado sem tópico definido.' );
				}
				$quiz_title = $row['item_title'];
				$course['topics'][ $course['current_topic'] ]['quizzes'][ $quiz_title ] = array(
					'quiz_info' => $row,
					'questions' => array(),
				);
				$course['current_quiz'] = $quiz_title;
				break;

			case 'question':
				if ( empty( $course['current_quiz'] ) ) {
					return new WP_Error( 'question_without_quiz', 'Pergunta encontrada sem quiz definido.' );
				}
				$course['topics'][ $course['current_topic'] ]['quizzes'][ $course['current_quiz'] ]['questions'][] = array(
					'question_info' => $row,
					'answers' => array(),
				);
				$course['current_question'] = count( $course['topics'][ $course['current_topic'] ]['quizzes'][ $course['current_quiz'] ]['questions'] ) - 1;
				break;

			case 'answer':
				if ( $course['current_question'] === null ) {
					return new WP_Error( 'answer_without_question', 'Resposta encontrada sem pergunta definida.' );
				}
				$course['topics'][ $course['current_topic'] ]['quizzes'][ $course['current_quiz'] ]['questions'][ $course['current_question'] ]['answers'][] = $row;
				break;
		}
	}

	// Validate that each course has required data
	foreach ( $courses as $course_title => $course_data ) {
		if ( empty( $course_data['course_info'] ) ) {
			return new WP_Error( 'missing_course_info', "Informações do curso '{$course_title}' não encontradas." );
		}
		if ( empty( $course_data['topics'] ) ) {
			return new WP_Error( 'missing_topics', "Curso '{$course_title}' não possui tópicos definidos." );
		}
	}

	return $courses;
}
