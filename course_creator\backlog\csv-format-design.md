# Unified CSV Input Format Design - Task 2.1

**Date:** 2025-07-04  
**Status:** Design Complete ✅

## Overview

This document defines the unified CSV format for automated course creation in Tutor LMS. The format supports all content types: courses, topics, video lessons, e-book lessons, and quizzes.

## CSV Structure

### Core Columns

| Column | Description | Required | Example Values |
|--------|-------------|----------|----------------|
| `row_type` | Type of content row | Yes | `course`, `topic`, `lesson`, `quiz`, `question`, `answer` |
| `course_title` | Course name (for all rows in same course) | Yes | "Git - Iniciante ao Avançado" |
| `topic_title` | Topic/Module name | For topic/lesson/quiz rows | "Módulo 1: Introdução" |
| `item_title` | Lesson/Quiz title | For lesson/quiz/question rows | "Aula 1: O que é Git?" |
| `item_type` | Content type | For lesson rows | `video`, `ebook`, `attachment` |
| `content` | Main content data | Varies by type | YouTube URL, PDF URL, etc. |
| `description` | Text description | Optional | "Nesta aula você aprenderá..." |
| `order` | Display order within parent | Optional | 1, 2, 3, etc. |

### Extended Columns (Content-Specific)

| Column | Description | Used For | Example |
|--------|-------------|----------|---------|
| `youtube_url` | YouTube video URL | Video lessons | "https://www.youtube.com/watch?v=abc123" |
| `vtt_filename` | VTT subtitle filename | Video lessons | "abc123.pt.vtt" |
| `pdf_url` | PDF file URL | E-book lessons | "https://example.com/ebook.pdf" |
| `quiz_settings` | Quiz configuration | Quiz rows | "time_limit:30,passing_score:80" |
| `question_type` | Question type | Question rows | `single_choice`, `true_false` |
| `question_points` | Points for question | Question rows | 1.00, 2.50 |
| `answer_text` | Answer option text | Answer rows | "Esta é a resposta correta" |
| `is_correct` | Whether answer is correct | Answer rows | 1, 0 |
| `answer_order` | Answer display order | Answer rows | 1, 2, 3, 4 |

## Row Types Specification

### 1. Course Row
```csv
row_type,course_title,topic_title,item_title,item_type,content,description,order
course,"Git - Iniciante ao Avançado","","","","","Aprenda Git do básico ao avançado com exemplos práticos",1
```

### 2. Topic Row
```csv
row_type,course_title,topic_title,item_title,item_type,content,description,order
topic,"Git - Iniciante ao Avançado","Módulo 1: Introdução","","","","Conceitos fundamentais do Git",1
```

### 3. Video Lesson Row
```csv
row_type,course_title,topic_title,item_title,item_type,content,description,order,youtube_url,vtt_filename
lesson,"Git - Iniciante ao Avançado","Módulo 1: Introdução","O que é Git?",video,"","Introdução aos conceitos básicos",1,"https://www.youtube.com/watch?v=abc123","abc123.pt.vtt"
```

### 4. E-book Lesson Row
```csv
row_type,course_title,topic_title,item_title,item_type,content,description,order,pdf_url
lesson,"Git - Iniciante ao Avançado","Módulo 1: Introdução","Manual Git PDF",ebook,"","Manual completo em PDF",2,"https://cursos.institutopanapana.org.br/wp-content/uploads/2025/07/git-manual.pdf"
```

### 5. Quiz Row
```csv
row_type,course_title,topic_title,item_title,item_type,content,description,order,quiz_settings
quiz,"Git - Iniciante ao Avançado","Módulo 1: Introdução","Quiz: Teste seus conhecimentos","","","Avalie seu aprendizado",3,"time_limit:30,passing_score:80,max_attempts:3"
```

### 6. Question Row
```csv
row_type,course_title,topic_title,item_title,item_type,content,description,order,question_type,question_points
question,"Git - Iniciante ao Avançado","Módulo 1: Introdução","Quiz: Teste seus conhecimentos","","Qual é a diferença entre git pull e git fetch?","",1,single_choice,1.00
```

### 7. Answer Rows
```csv
row_type,course_title,topic_title,item_title,item_type,content,description,order,answer_text,is_correct,answer_order
answer,"Git - Iniciante ao Avançado","Módulo 1: Introdução","Quiz: Teste seus conhecimentos","","","",1,"git pull traz e integra mudanças, git fetch apenas traz",1,1
answer,"Git - Iniciante ao Avançado","Módulo 1: Introdução","Quiz: Teste seus conhecimentos","","","",1,"Não há diferença entre os comandos",0,2
answer,"Git - Iniciante ao Avançado","Módulo 1: Introdução","Quiz: Teste seus conhecimentos","","","",1,"git fetch é mais rápido que git pull",0,3
answer,"Git - Iniciante ao Avançado","Módulo 1: Introdução","Quiz: Teste seus conhecimentos","","","",1,"git pull é usado apenas para upload",0,4
```

## Complete Example CSV

```csv
row_type,course_title,topic_title,item_title,item_type,content,description,order,youtube_url,vtt_filename,pdf_url,quiz_settings,question_type,question_points,answer_text,is_correct,answer_order
course,"Git - Iniciante ao Avançado","","","","","Aprenda Git do básico ao avançado",1,"","","","","","","","",""
topic,"Git - Iniciante ao Avançado","Módulo 1: Introdução","","","","Conceitos fundamentais",1,"","","","","","","","",""
lesson,"Git - Iniciante ao Avançado","Módulo 1: Introdução","O que é Git?",video,"","Introdução aos conceitos",1,"https://www.youtube.com/watch?v=abc123","abc123.pt.vtt","","","","","","",""
lesson,"Git - Iniciante ao Avançado","Módulo 1: Introdução","Manual Git",ebook,"","Manual em PDF",2,"","","https://example.com/git.pdf","","","","","",""
quiz,"Git - Iniciante ao Avançado","Módulo 1: Introdução","Quiz Módulo 1","","","Teste seus conhecimentos",3,"","","","time_limit:30,passing_score:80","","","","",""
question,"Git - Iniciante ao Avançado","Módulo 1: Introdução","Quiz Módulo 1","","O que é Git?","",1,"","","","",single_choice,1.00,"","",""
answer,"Git - Iniciante ao Avançado","Módulo 1: Introdução","Quiz Módulo 1","","","",1,"","","","","","","Sistema de controle de versão",1,1
answer,"Git - Iniciante ao Avançado","Módulo 1: Introdução","Quiz Módulo 1","","","",1,"","","","","","","Editor de texto",0,2
```

## Processing Logic

### 1. Course Creation Flow
1. **Course Row** → Create main course post
2. **Topic Rows** → Create topic posts with course as parent
3. **Lesson Rows** → Create lesson posts with topic as parent
4. **Quiz Rows** → Create quiz posts with topic as parent
5. **Question/Answer Rows** → Insert into quiz tables

### 2. Content Type Handling
- **Video Lessons:** Generate `[custom_video_with_vtt]` shortcode from youtube_url + vtt_filename
- **E-book Lessons:** Generate iframe with Google Docs viewer from pdf_url
- **Quizzes:** Parse quiz_settings and create questions/answers in database

### 3. Order Management
- Use `order` column for `menu_order` field in wp_posts
- Maintain hierarchical ordering within each parent

## Validation Rules

### Required Fields by Row Type
- **course:** row_type, course_title, description
- **topic:** row_type, course_title, topic_title, order
- **lesson:** row_type, course_title, topic_title, item_title, item_type, order
- **quiz:** row_type, course_title, topic_title, item_title, order
- **question:** row_type, course_title, topic_title, item_title, content, question_type, question_points
- **answer:** row_type, course_title, topic_title, item_title, answer_text, is_correct, answer_order

### Content Type Validation
- **video lessons:** Must have youtube_url, vtt_filename optional
- **ebook lessons:** Must have pdf_url
- **single_choice questions:** Must have 2-6 answer rows, exactly 1 correct
- **true_false questions:** Must have exactly 2 answer rows (True/False)

## Implementation Notes

### VTT File Handling
- VTT files should be uploaded to: `wp-content/uploads/legendas/`
- Filename format: `{youtube_video_id}.pt.vtt`
- Full URL: `https://cursos.institutopanapana.org.br/wp-content/uploads/legendas/{filename}`

### PDF File Handling
- PDFs should be uploaded to WordPress Media Library
- Use WordPress attachment URLs
- Google Docs viewer format: `https://docs.google.com/gview?url={pdf_url}&embedded=true`

### Quiz Settings Format
- Comma-separated key:value pairs
- Example: `time_limit:30,passing_score:80,max_attempts:3,randomize:1`

This CSV format provides complete flexibility for creating complex courses while maintaining simplicity for basic use cases.
