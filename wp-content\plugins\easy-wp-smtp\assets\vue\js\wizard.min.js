(function(){var e={7680:function(e,t,s){"use strict";s(4114);var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-admin-page"},[t("router-view"),e.blocked?t("div",{staticClass:"easy-wp-smtp-blocked"}):e.loading?t("div",{staticClass:"easy-wp-smtp-loading"},[t("inline-svg",{staticClass:"icon",attrs:{src:s(6644),width:"150"}})],1):e._e()],1)},a=[],n=s(173),r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-welcome"},[t("the-wizard-header"),t("div",{staticClass:"easy-wp-smtp-setup-wizard-container"},[t("main",{staticClass:"easy-wp-smtp-setup-wizard-content"},[t("div",{staticClass:"easy-wp-smtp-setup-wizard-content-container"},[t("content-header",{attrs:{title:e.text_header_title,subtitle:e.text_header_subtitle}}),t("button",{staticClass:"easy-wp-smtp-button easy-wp-smtp-button-main easy-wp-smtp-button-large",attrs:{type:"button"},on:{click:function(t){return t.preventDefault(),e.nextStep.apply(null,arguments)}}},[t("span",{staticClass:"text-with-arrow text-with-arrow-right"},[e._v(" "+e._s(e.text_button)+" "),t("inline-svg",{staticClass:"icon",attrs:{src:s(953),width:"18",height:"22"}})],1)])],1)]),t("footer",[t("p",{staticClass:"easy-wp-smtp-exit-link"},[t("a",{attrs:{href:e.exit_href}},[e._v(e._s(e.text_exit_link))])])])])],1)},o=[],l=s(9007),_=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-content-header"},[t("h2",{domProps:{innerHTML:e._s(e.title)}}),e.subtitle?t("p",{staticClass:"subtitle",domProps:{innerHTML:e._s(e.subtitle)}}):e._e()])},p=[],m={name:"ContentHeader",props:{title:String,subtitle:String}},c=m,d=s(1656),u=(0,d.A)(c,_,p,!1,null,null,null),f=u.exports,h=function(){var e=this,t=e._self._c;return t("header",{staticClass:"easy-wp-smtp-setup-wizard-header"},[t("div",{staticClass:"easy-wp-smtp-logo"},[t("img",{staticClass:"easy-wp-smtp-logo-img",attrs:{src:s(5447),alt:e.text_logo_alt}})])])},y=[],g={name:"TheWizardHeader",data(){return{text_logo_alt:(0,l.__)("Easy WP SMTP logo","easy-wp-smtp")}}},w=g,b=(0,d.A)(w,h,y,!1,null,null,null),v=b.exports,x={name:"SetupWizardWelcome",components:{ContentHeader:f,TheWizardHeader:v},data(){return{text_header_title:(0,l.__)("Welcome to the Easy WP SMTP Setup Wizard!","easy-wp-smtp"),text_header_subtitle:(0,l.__)("We’ll guide you through setting up Easy WP SMTP on your site step by step.","easy-wp-smtp"),text_button:(0,l.__)("Let's Get Started","easy-wp-smtp"),text_exit_link:(0,l.__)("Go back to the Dashboard","easy-wp-smtp"),exit_href:this.$easywpsmtp.exit_url}},methods:{nextStep:function(){this.$store.dispatch("$_wizard/started"),this.$router.push({name:this.$wizard_steps[0]})}}},k=x,S=(0,d.A)(k,r,o,!1,null,null,null),P=S.exports,A=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-step"},[t("the-wizard-header"),t("the-wizard-timeline"),t("div",{staticClass:"easy-wp-smtp-setup-wizard-container"},[t("main",{staticClass:"easy-wp-smtp-setup-wizard-content"},[t("router-view",{on:{displayContentBelow:e.displayContentBelow}})],1),t("footer",[e.content_below.length>0?t("div",{staticClass:"easy-wp-smtp-step-below-content",domProps:{innerHTML:e._s(e.content_below)}}):e._e(),e.display_exit_link?t("p",{staticClass:"easy-wp-smtp-exit-link"},[t("a",{attrs:{href:e.exit_href}},[e._v(e._s(e.text_exit_link))])]):e._e()])])],1)},E=[],$=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-setup-wizard-container"},[t("div",{staticClass:"easy-wp-smtp-setup-wizard-timeline"},[e._l(e.steps,(function(i,a){return[a>0?t("div",{key:a+"line",class:e.lineClass(a)}):e._e(),t("div",{key:a,class:e.stepClass(a)},[t("inline-svg",{staticClass:"icon icon-success",attrs:{src:s(8063),width:"10",height:"10"}}),t("inline-svg",{staticClass:"icon icon-failed",attrs:{src:s(3217),width:"8",height:"11"}})],1)]}))],2)])},C=[],M={name:"TheWizardTimeline",data(){return{steps:this.$wizard_steps}},methods:{stepClass(e){let t="easy-wp-smtp-setup-wizard-timeline-step";const s=this.steps.findIndex((e=>this.$route.name.includes(e)));return(e<s||parseInt(s)===this.steps.length-1&&this.$route.name.includes("_success"))&&(t+=" easy-wp-smtp-setup-wizard-timeline-step-completed"),e===s&&parseInt(s)===this.steps.length-1&&this.$route.name.includes("_failure")&&(t+=" easy-wp-smtp-setup-wizard-timeline-step-failed"),parseInt(e)===parseInt(s)&&(t+=" easy-wp-smtp-setup-wizard-timeline-step-active"),t},lineClass(e){let t="easy-wp-smtp-setup-wizard-timeline-step-line";const s=this.steps.findIndex((e=>this.$route.name.includes(e)));return e<=s&&(t+=" easy-wp-smtp-setup-wizard-timeline-line-active"),t}}},T=M,I=(0,d.A)(T,$,C,!1,null,null,null),F=I.exports,z={name:"SetupWizardSteps",components:{TheWizardHeader:v,TheWizardTimeline:F},data(){return{text_exit_link:(0,l.__)("Close and exit the Setup Wizard","easy-wp-smtp"),exit_href:this.$easywpsmtp.exit_url,content_below:""}},computed:{display_exit_link:function(){return!this.$route.name.includes("check_configuration_step")}},methods:{displayContentBelow:function(e){this.content_below=e}},mounted(){this.$store.dispatch("$_app/start_loading"),Promise.all([this.$store.dispatch("$_settings/getSettings"),this.$store.dispatch("$_plugins/getPlugins")]).finally((()=>{this.$store.dispatch("$_app/stop_loading")}))}},L=z,O=(0,d.A)(L,A,E,!1,null,null,null),R=O.exports,W=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-setup-wizard-step easy-wp-smtp-setup-wizard-step-choose-mailer"},[t("div",{staticClass:"easy-wp-smtp-setup-wizard-content-container"},[t("the-wizard-step-counter"),t("content-header",{attrs:{title:e.text_header_title,subtitle:e.text_header_subtitle}}),e.mailer_set_via_constants?t("div",{staticClass:"easy-wp-smtp-notice easy-wp-smtp-notice--info"},[t("p",{domProps:{innerHTML:e._s(e.text_mailer_set_via_constants)}})]):e._e(),t("div",{staticClass:"easy-wp-smtp-setup-wizard-form"},[t("form",[t("div",{staticClass:"easy-wp-smtp-setup-wizard-form-row easy-wp-smtp-setup-wizard-form-row-highlight"},[t("settings-input-radios-with-icons",{attrs:{name:"choose_mailer",options:e.recommended_options},model:{value:e.selectedMailer,callback:function(t){e.selectedMailer=t},expression:"selectedMailer"}})],1),t("div",{staticClass:"easy-wp-smtp-setup-wizard-form-row"},[t("settings-input-radios-with-icons",{attrs:{name:"choose_mailer",options:e.options},on:{"clicked-disabled":e.clickedDisabledOption},model:{value:e.selectedMailer,callback:function(t){e.selectedMailer=t},expression:"selectedMailer"}})],1)])])],1),t("div",{staticClass:"easy-wp-smtp-separator easy-wp-smtp-separator-no-margin"}),t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-footer"},[t("a",{attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),e.previousStep.apply(null,arguments)}}},[t("span",{staticClass:"text-with-arrow text-with-arrow-left"},[t("inline-svg",{staticClass:"icon",attrs:{src:s(9004),width:"16",height:"22"}}),e._v(e._s(e.text_previous_step)+" ")],1)]),t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-footer-buttons"},[t("button",{staticClass:"easy-wp-smtp-button easy-wp-smtp-button-main",attrs:{type:"submit",name:"next_step",disabled:null===e.selectedMailer||"mail"===e.selectedMailer},on:{click:function(t){return t.preventDefault(),e.handleSubmit.apply(null,arguments)}}},[t("span",{staticClass:"text-with-arrow text-with-arrow-right"},[e._v(" "+e._s(e.text_save)+" "),t("inline-svg",{staticClass:"icon",attrs:{src:s(953),width:"16",height:"19"}})],1)])])])])},N=[],Y=s(5353),D=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-input-radios-with-icons"},e._l(e.options,(function(i){return t("label",{key:i.value,class:e.labelClass(i),attrs:{for:"easy-wp-smtp-settings-radio-"+e.name+"["+i.value+"]"},on:{click:function(t){return e.clicked(i)}}},[t("input",{directives:[{name:"model",rawName:"v-model",value:e.selectedImport,expression:"selectedImport"}],attrs:{id:"easy-wp-smtp-settings-radio-"+e.name+"["+i.value+"]",type:"radio",name:e.name,autocomplete:"off",disabled:i.disabled||!1},domProps:{value:i.value,checked:e.isChecked(i.value),checked:e._q(e.selectedImport,i.value)},on:{change:function(t){e.selectedImport=i.value}}}),t("span",{class:e.titleClass(i.value)}),i.logo.length?t("img",{staticClass:"easy-wp-smtp-logo-icon",attrs:{src:i.logo,alt:i.label}}):e._e(),t("span",{staticClass:"easy-wp-smtp-styled-radio-text"},[e._v(" "+e._s(i.label)+" "),i.description?t("span",{staticClass:"easy-wp-smtp-styled-radio-desc"},[e._v(" "+e._s(i.description)+" ")]):e._e()]),i.is_pro?t("inline-svg",{staticClass:"easy-wp-smtp-pro-badge",attrs:{src:s(3453),width:"28",height:"16"}}):e._e(),i.recommended?t("span",{staticClass:"easy-wp-smtp-recommended-badge"},[e._v(e._s(e.text_recommended))]):e._e()],1)})),0)},U=[],B={name:"SettingsInputRadiosWithIcons",props:{options:Array,name:String,value:String},data(){return{has_error:!1,text_recommended:(0,l.__)("Recommended","easy-wp-smtp")}},computed:{selectedImport:{get(){return this.value},set(e){this.$emit("input",e)}}},methods:{titleClass(e){let t="easy-wp-smtp-styled-radio";return this.isChecked(e)&&(t+=" easy-wp-smtp-styled-radio-checked"),t},labelClass(e){let t="";return this.isChecked(e.value)&&(t+=" easy-wp-smtp-styled-radio-label-checked"),e.disabled&&(t+=" easy-wp-smtp-styled-radio-label-disabled"),e.readonly&&(t+=" easy-wp-smtp-styled-radio-label-readonly"),t},isChecked(e){return e===this.selectedImport},clicked(e){e.is_pro&&this.$emit("clicked-disabled",e)}}},V=B,H=(0,d.A)(V,D,U,!1,null,"7e2553c0",null),K=H.exports,q=function(){var e=this,t=e._self._c;return t("p",{staticClass:"easy-wp-smtp-setup-wizard-step-count"},[e._v(" "+e._s(e.stepValue)+" ")])},G=[],j={name:"TheWizardStepCounter",computed:{stepValue:function(){const e=this.$wizard_steps.findIndex((e=>this.$route.name.includes(e)))+1;return(0,l.nv)((0,l.__)("Step %1$s of %2$s","easy-wp-smtp"),e,this.$wizard_steps.length)}}},Z=j,J=(0,d.A)(Z,q,G,!1,null,"02c612a3",null),X=J.exports,Q={name:"WizardStepChooseMailer",components:{SettingsInputRadiosWithIcons:K,ContentHeader:f,TheWizardStepCounter:X},data(){return{text_header_title:(0,l.__)("Choose Your SMTP Mailer","easy-wp-smtp"),text_header_subtitle:(0,l.nv)((0,l.__)("Select the mailer you would like to use to send emails. Need more information on our mailers? See our %1$scomplete mailer guide%2$s for additional details.","easy-wp-smtp"),'<a href="'+this.$getUTMUrl("https://easywpsmtp.com/docs/a-complete-guide-to-easy-wp-smtp-mailers/",{content:"complete mailer guide"})+'" target="_blank" rel="noopener noreferrer">',"</a>"),text_save:(0,l.__)("Save and Continue","easy-wp-smtp"),text_previous_step:(0,l.__)("Previous Step","easy-wp-smtp"),text_mailer_set_via_constants:(0,l.__)("Your mailer is already configured in a Easy WP SMTP constant, so the options below have been disabled. To change your mailer, please edit or remove the <code>EASY_WP_SMTP_MAILER</code> constant in your <code>wp-config.php</code> file.","easy-wp-smtp"),mailer_set_via_constants:this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER"),recommended_options:[{value:"sendlayer",label:this.$easywpsmtp.mailer_options["sendlayer"].title,logo:s(8295),readonly:this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER"),disabled:this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER"),recommended:this.$easywpsmtp.mailer_options["sendlayer"].recommended,description:(0,l.__)(" - Send your first 200 emails for free.","easy-wp-smtp")}],options:[{value:"amazonses",label:this.$easywpsmtp.mailer_options["amazonses"].title,logo:s(6489),is_pro:!this.$easywpsmtp.is_pro,notice:this.$easywpsmtp.mailer_options["amazonses"].edu_notice,readonly:this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER"),disabled:!this.$easywpsmtp.is_pro||this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER"),recommended:this.$easywpsmtp.mailer_options["amazonses"].recommended},{value:"sendinblue",label:this.$easywpsmtp.mailer_options["sendinblue"].title,logo:s(1466),readonly:this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER"),disabled:this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER"),recommended:this.$easywpsmtp.mailer_options["sendinblue"].recommended},{value:"elasticemail",label:this.$easywpsmtp.mailer_options["elasticemail"].title,logo:s(4689),readonly:this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER"),disabled:this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER")},{value:"gmail",label:this.$easywpsmtp.mailer_options["gmail"].title,logo:s(6848),is_pro:!this.$easywpsmtp.is_pro,notice:this.$easywpsmtp.mailer_options["gmail"].edu_notice,readonly:this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER"),disabled:!this.$easywpsmtp.is_pro||this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER"),recommended:this.$easywpsmtp.mailer_options["gmail"].recommended},{value:"mailersend",label:this.$easywpsmtp.mailer_options["mailersend"].title,logo:s(5136),readonly:this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER"),disabled:this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER")},{value:"mailgun",label:this.$easywpsmtp.mailer_options["mailgun"].title,logo:s(6211),readonly:this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER"),disabled:this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER"),recommended:this.$easywpsmtp.mailer_options["mailgun"].recommended},{value:"mailjet",label:this.$easywpsmtp.mailer_options["mailjet"].title,logo:s(5168),readonly:this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER"),disabled:this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER")},{value:"outlook",label:this.$easywpsmtp.is_pro?(0,l.__)("Microsoft 365 / Outlook","easy-wp-smtp"):this.$easywpsmtp.mailer_options["outlook"].title,logo:s(5423),is_pro:!this.$easywpsmtp.is_pro,readonly:this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER"),disabled:!this.$easywpsmtp.is_pro||this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER"),recommended:this.$easywpsmtp.mailer_options["outlook"].recommended},{value:"postmark",label:this.$easywpsmtp.mailer_options["postmark"].title,logo:s(6959),readonly:this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER"),disabled:this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER")},{value:"sendgrid",label:this.$easywpsmtp.mailer_options["sendgrid"].title,logo:s(5064),readonly:this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER"),disabled:this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER")},{value:"smtpcom",label:this.$easywpsmtp.mailer_options["smtpcom"].title,logo:s(9189),readonly:this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER"),disabled:this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER"),recommended:this.$easywpsmtp.mailer_options["smtpcom"].recommended},{value:"smtp2go",label:this.$easywpsmtp.mailer_options["smtp2go"].title,logo:s(1366),readonly:this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER"),disabled:this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER")},{value:"sparkpost",label:this.$easywpsmtp.mailer_options["sparkpost"].title,logo:s(6675),readonly:this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER"),disabled:this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER")},{value:"zoho",label:this.$easywpsmtp.mailer_options["zoho"].title,logo:s(7936),is_pro:!this.$easywpsmtp.is_pro,readonly:this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER"),disabled:!this.$easywpsmtp.is_pro||this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER")},{value:"smtp",label:this.$easywpsmtp.mailer_options["smtp"].title,logo:s(9682),readonly:this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER"),disabled:this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER"),recommended:this.$easywpsmtp.mailer_options["smtp"].recommended}],selectedMailer:this.currentMailer}},watch:{currentMailer:function(e){this.selectedMailer=e}},computed:{...(0,Y.L8)({currentMailer:"$_settings/mailer"}),selectedMailerOptions:function(){return this.recommended_options.concat(this.options).find((e=>e.value===this.selectedMailer))}},methods:{handleSubmit(e,t=!1){null!==this.selectedMailer&&"mail"!==this.selectedMailer&&(void 0===this.selectedMailerOptions.notice||t?(this.$store.dispatch("$_app/start_loading"),this.$store.dispatch("$_settings/updateSettings",{value:{mail:{mailer:this.selectedMailer}}}).then((e=>{e.success?(this.$store.dispatch("$_settings/setMailer",this.selectedMailer),this.nextStep()):this.$easywpsmtp_error_toast({})})).finally((()=>{this.$store.dispatch("$_app/stop_loading")}))):this.$swal({title:this.selectedMailerOptions.label+" "+(0,l.__)("Mailer","easy-wp-smtp"),html:this.selectedMailerOptions.notice,width:650,showCloseButton:!0,allowOutsideClick:!1,allowEscapeKey:!1,allowEnterKey:!1,customClass:{container:"easy-wp-smtp-swal"},confirmButtonText:(0,l.__)("I Understand, Continue","easy-wp-smtp"),cancelButtonText:(0,l.__)("Choose a Different Mailer","easy-wp-smtp"),showCancelButton:!0,reverseButtons:!0}).then((e=>{e.value?this.handleSubmit(null,!0):void 0===e.dismiss||"cancel"!==e.dismiss||this.$easywpsmtp.defined_constants.includes("EASY_WP_SMTP_MAILER")||(this.selectedMailer=null)})))},nextStep(){const e=this.$wizard_steps.findIndex((e=>this.$route.name.includes(e)))+1;this.$router.push({name:`${this.$wizard_steps[e]}_${this.selectedMailer}`})},previousStep(){this.$previous_step()},clickedDisabledOption(e){var t=/(\?)/.test(this.$easywpsmtp.education.upgrade_url)?"&":"?",i=this.$easywpsmtp.education.upgrade_url+t+"utm_content="+encodeURIComponent(e.value);this.$swal({title:e.label+" "+(0,l.__)("is a PRO Feature","easy-wp-smtp"),html:`<p>${this.$easywpsmtp.education.upgrade_text.replace("%mailer%",e.label)}</p>\n\t\t\t\t\t\t\t<div class="upgrade-bonus">\n\t\t\t\t\t\t\t<svg class="icon" width="16" height="16" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M504 256c0 136.967-111.033 248-248 248S8 392.967 8 256 119.033 8 256 8s248 111.033 248 248zM227.314 387.314l184-184c6.248-6.248 6.248-16.379 0-22.627l-22.627-22.627c-6.248-6.249-16.379-6.249-22.628 0L216 308.118l-70.059-70.059c-6.248-6.248-16.379-6.248-22.628 0l-22.627 22.627c-6.248 6.248-6.248 16.379 0 22.627l104 104c6.249 6.249 16.379 6.249 22.628.001z"></path></svg>\n\t\t\t\t\t\t\t<span>${this.$easywpsmtp.education.upgrade_bonus_short}</span>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<p><a href="${i}" class="easy-wp-smtp-button easy-wp-smtp-button-success easy-wp-smtp-button-small" target="_blank" rel="noopener noreferrer">${this.$easywpsmtp.education.upgrade_button}</a></p>\n\t\t\t\t\t\t\t<p>${this.$easywpsmtp.education.upgrade_doc}</p>`,width:550,imageUrl:s(1312),imageWidth:28,imageHeight:37,showCloseButton:!0,customClass:{container:"easy-wp-smtp-swal easy-wp-smtp-upgrade-popup"},showConfirmButton:!1})}},mounted(){this.selectedMailer=this.currentMailer}},ee=Q,te=(0,d.A)(ee,W,N,!1,null,"201195d9",null),se=te.exports,ie=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-setup-wizard-step easy-wp-smtp-setup-wizard-step-configure-mailer"},[t("div",{staticClass:"easy-wp-smtp-setup-wizard-content-container"},[t("div",{staticClass:"easy-wp-smtp-configure-mailer-header"},[t("div",{staticClass:"easy-wp-smtp-configure-mailer-header-container"},[t("the-wizard-step-counter"),t("content-header",{attrs:{title:e.text_header_title,subtitle:e.text_header_subtitle}})],1),t("span",{staticClass:"easy-wp-smtp-configure-mailer-logo"},[t("inline-svg",{attrs:{src:e.logo(e.mailer),height:"40"}})],1)]),t("div",{staticClass:"easy-wp-smtp-separator easy-wp-smtp-separator-no-margin"}),t("router-view",{ref:"mailerConfiguration"})],1),t("div",{staticClass:"easy-wp-smtp-separator easy-wp-smtp-separator-no-margin"}),t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-footer"},[t("a",{attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),e.previousStep.apply(null,arguments)}}},[t("span",{staticClass:"text-with-arrow text-with-arrow-left"},[t("inline-svg",{staticClass:"icon",attrs:{src:s(9004),width:"16",height:"22"}}),e._v(e._s(e.text_previous_step)+" ")],1)]),t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-footer-buttons"},[t("button",{staticClass:"easy-wp-smtp-button easy-wp-smtp-button-main",attrs:{type:"submit",name:"next_step",disabled:null===e.mailer||!0===e.blocked_step},on:{click:function(t){return t.preventDefault(),e.handleSubmit.apply(null,arguments)}}},[t("span",{staticClass:"text-with-arrow text-with-arrow-right"},[e._v(" "+e._s(e.text_save)+" "),t("inline-svg",{staticClass:"icon",attrs:{src:s(953),width:"16",height:"19"}})],1)])])])])},ae=[],ne=s(7860),re={name:"WizardStepConfigureMailer",components:{ContentHeader:f,TheWizardStepCounter:X},data(){return{text_header_title:(0,l.__)("Configure Mailer Settings","easy-wp-smtp"),text_header_subtitle:(0,l.__)("Fill out the required settings below to set up this mailer.","easy-wp-smtp"),text_save:(0,l.__)("Save and Continue","easy-wp-smtp"),text_previous_step:(0,l.__)("Previous Step","easy-wp-smtp")}},computed:{...(0,Y.L8)({mailer:"$_settings/mailer"}),...(0,ne.YP)("$_wizard",["blocked_step"])},methods:{handleSubmit(){return!this.blocked_step&&(this.$refs.mailerConfiguration.areRequiredFieldsValid()?(this.$store.dispatch("$_app/start_loading"),void this.$store.dispatch("$_settings/saveCurrentSettings").then((e=>{e.success?this.$next_step():this.$easywpsmtp_error_toast({})})).finally((()=>{this.$store.dispatch("$_app/stop_loading")}))):(this.$required_fields_modal(),!1))},previousStep(){this.blocked_step=!1,this.$previous_step()},logo(e){return"mail"===e?e="smtp":"sendinblue"===e&&(e="brevo"),s(3180)(`./${e}.svg`)}}},oe=re,le=(0,d.A)(oe,ie,ae,!1,null,null,null),_e=le.exports,pe=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-setup-wizard-step easy-wp-smtp-setup-wizard-step-plugin-features"},[t("div",{staticClass:"easy-wp-smtp-setup-wizard-content-container"},[t("div",{staticClass:"easy-wp-smtp-plugin-features-header"},[t("the-wizard-step-counter"),t("content-header",{attrs:{title:e.text_header_title,subtitle:e.text_header_subtitle}})],1),t("div",{staticClass:"easy-wp-smtp-plugin-features-list"},[t("settings-input-long-checkbox",{attrs:{value:!0,name:"improved_deliverability",label:e.text_improved_email_deliverability,description:e.text_improved_email_deliverability_desc,disabled:""}}),t("settings-input-long-checkbox",{attrs:{value:!0,name:"error_tracking",label:e.text_error_tracking,description:e.text_error_tracking_desc,disabled:""}}),e.contact_form_already_installed?e._e():t("settings-input-long-checkbox",{attrs:{name:"smart_contact_form",label:e.text_smart_contact_form,description:e.text_smart_contact_form_desc},model:{value:e.smart_contact_form,callback:function(t){e.smart_contact_form=t},expression:"smart_contact_form"}}),e.is_pro?e._e():t("settings-input-long-checkbox",{attrs:{name:"summary_report_email",label:e.text_summary_report_email,description:e.text_summary_report_email_desc},model:{value:e.summary_report_email,callback:function(t){e.summary_report_email=t},expression:"summary_report_email"}}),t("settings-input-long-checkbox",{attrs:{name:"email_log",constant:"EASY_WP_SMTP_LOGS_ENABLED",label:e.text_email_log,description:e.text_email_log_desc,show_pro:!e.is_pro},on:{input:e.emailLogEnabledChanged},model:{value:e.email_log,callback:function(t){e.email_log=t},expression:"email_log"}}),e.email_log||!e.is_pro?t("settings-input-long-checkbox",{attrs:{value:e.complete_email_report,name:"complete_email_report",label:e.text_complete_email_report,description:e.text_complete_email_report_desc,show_pro:!e.is_pro,disabled:!!e.is_pro},model:{value:e.complete_email_report,callback:function(t){e.complete_email_report=t},expression:"complete_email_report"}}):e._e(),e.is_pro&&e.email_log?t("settings-input-long-checkbox",{attrs:{name:"summary_report_email",constant:"EASY_WP_SMTP_SUMMARY_REPORT_EMAIL_DISABLED",label:e.text_summary_report_email,description:e.text_summary_report_email_desc},model:{value:e.summary_report_email,callback:function(t){e.summary_report_email=t},expression:"summary_report_email"}}):e._e(),t("settings-input-long-checkbox",{attrs:{name:"instant_email_alert_input",label:e.text_instant_email_alert,description:e.text_instant_email_alert_desc,constant:"EASY_WP_SMTP_ALERT_EMAIL_SEND_TO",show_pro:!e.is_pro},model:{value:e.instant_email_alert,callback:function(t){e.instant_email_alert=t},expression:"instant_email_alert"}})],1)]),t("div",{staticClass:"easy-wp-smtp-separator easy-wp-smtp-separator-no-margin"}),t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-footer"},[t("a",{attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),e.previousStep.apply(null,arguments)}}},[t("span",{staticClass:"text-with-arrow text-with-arrow-left"},[t("inline-svg",{staticClass:"icon",attrs:{src:s(9004),width:"16",height:"22"}}),e._v(e._s(e.text_previous_step)+" ")],1)]),t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-footer-buttons"},[t("button",{staticClass:"easy-wp-smtp-button easy-wp-smtp-button-main",attrs:{type:"submit",name:"next_step"},on:{click:function(t){return t.preventDefault(),e.handleSubmit.apply(null,arguments)}}},[t("span",{staticClass:"text-with-arrow text-with-arrow-right"},[e._v(" "+e._s(e.text_save)+" "),t("inline-svg",{staticClass:"icon",attrs:{src:s(953),width:"16",height:"19"}})],1)])])])])},me=[],ce=function(){var e=this,t=e._self._c;return t("label",{staticClass:"settings-input-long-checkbox",class:{"settings-input-long-checkbox-checked":e.value,"settings-input-long-checkbox-disabled":e.disabled||e.is_constant_set},attrs:{for:"easy-wp-smtp-settings-long-checkbox-"+e.name}},[t("div",{staticClass:"settings-input-long-checkbox-header"},[t("span",{staticClass:"title-container"},[t("span",{staticClass:"label"},[e._v(e._s(e.label))]),e.show_pro?t("inline-svg",{staticClass:"easy-wp-smtp-pro-badge",attrs:{src:s(3453),width:"28",height:"16"}}):e._e()],1),e.description?t("p",{staticClass:"description"},[e._v(e._s(e.description))]):e._e(),e.is_constant_set?t("p",{staticClass:"description description--constant",domProps:{innerHTML:e._s(e.text_constant)}}):e._e()]),t("span",{staticClass:"settings-input-long-checkbox-container"},[t("input",{attrs:{id:"easy-wp-smtp-settings-long-checkbox-"+e.name,type:"checkbox",name:e.name,disabled:e.disabled||e.is_constant_set},domProps:{checked:e.value},on:{input:function(t){return e.$emit("input",t.target.checked)}}}),t("span",{staticClass:"checkbox",class:{"checkbox-checked":e.value,"checkbox-disabled":e.disabled||e.is_constant_set}})])])},de=[],ue={name:"SettingsInputLongCheckbox",props:{label:String,name:String,value:Boolean,description:String,constant:String,disabled:Boolean,show_pro:Boolean},computed:{is_constant_set:function(){return this.$easywpsmtp.defined_constants.includes(this.constant)},text_constant:function(){return(0,l.__)("This setting is already configured with the Easy WP SMTP constant. To change it, please edit or remove the <code>"+this.constant+"</code> constant in your <code>wp-config.php</code> file.","easy-wp-smtp")}}},fe=ue,he=(0,d.A)(fe,ce,de,!1,null,null,null),ye=he.exports,ge={name:"WizardStepPluginFeatures",components:{ContentHeader:f,TheWizardStepCounter:X,SettingsInputLongCheckbox:ye},data(){return{text_header_title:(0,l.__)("Which email features do you want to enable?","easy-wp-smtp"),text_header_subtitle:(0,l.__)("Get more value out of Easy WP SMTP! Select which of the following features you’d like to use, and we’ll enable them for you.","easy-wp-smtp"),text_save:(0,l.__)("Save and Continue","easy-wp-smtp"),text_previous_step:(0,l.__)("Previous Step","easy-wp-smtp"),text_improved_email_deliverability:(0,l.__)("Improved Email Deliverability","easy-wp-smtp"),text_improved_email_deliverability_desc:(0,l.__)("Send emails from your website successfully and reliably.","easy-wp-smtp"),text_error_tracking:(0,l.__)("Email Error Tracking","easy-wp-smtp"),text_error_tracking_desc:(0,l.__)("Monitor email delivery issues so you can easily resolve them.","easy-wp-smtp"),text_smart_contact_form:(0,l.__)("Smart Contact Form","easy-wp-smtp"),text_smart_contact_form_desc:(0,l.__)("Install the WPForms plugin and create beautiful contact forms with just a few clicks.","easy-wp-smtp"),text_email_log:(0,l.__)("Detailed Email Logs","easy-wp-smtp"),text_email_log_desc:(0,l.__)("Store information from all emails sent from your site.","easy-wp-smtp"),text_instant_email_alert:(0,l.__)("Instant Email Alerts","easy-wp-smtp"),text_instant_email_alert_desc:(0,l.__)("Get notifications via email, SMS, Slack, or webhook when emails fail to send.","easy-wp-smtp"),text_complete_email_report:(0,l.__)("Complete Email Reports","easy-wp-smtp"),text_complete_email_report_desc:(0,l.__)("View your emails’ delivery status, open & click tracking, and deliverability charts.","easy-wp-smtp"),text_summary_report_email:(0,l.__)("Weekly Email Summary","easy-wp-smtp"),text_summary_report_email_desc:(0,l.__)("Receive a weekly email delivery report in your inbox.","easy-wp-smtp"),is_pro:this.$easywpsmtp.is_pro,is_multisite:this.$easywpsmtp.is_multisite,email_log:!1,complete_email_report:!!this.$easywpsmtp.is_pro,summary_report_email:!1}},computed:{...(0,Y.L8)({contact_form_already_installed:"$_plugins/contact_form_plugin_already_installed",email_log_setting:"$_settings/email_log_enabled",summary_report_email_setting:"$_settings/summary_report_email_enabled"}),...(0,ne.YP)("$_plugins",{smart_contact_form:"smart_contact_form_setting"}),...(0,ne.YP)("$_settings",{alert_email_connections:"settings.alert_email.connections",instant_email_alert:"settings.alert_email.enabled"})},watch:{smart_contact_form:function(){if(this.contact_form_already_installed)return!1;this.showPluginInstallFooterNotice()},contact_form_already_installed:function(){this.showPluginInstallFooterNotice()},email_log_setting:function(e){this.email_log=e},summary_report_email_setting:function(e){this.summary_report_email=e}},methods:{handleSubmit(){this.$store.dispatch("$_app/start_loading");let e=[],t={value:{general:{summary_report_email_disabled:!this.summary_report_email}}};if(e.push(this.$store.dispatch("$_settings/setSummaryReportEmail",!this.summary_report_email)),this.is_pro){t.value={...t.value,logs:{enabled:this.email_log}},e.push(this.$store.dispatch("$_settings/setLogs",this.email_log));let s={enabled:this.instant_email_alert};this.instant_email_alert&&0===Object.values(this.alert_email_connections).length&&(s.connections=[{send_to:this.$easywpsmtp.current_user_email}]),t.value={...t.value,alert_email:s}}if(e.push(this.$store.dispatch("$_settings/updateSettings",t)),e.push(Promise.resolve({success:!0}).then((e=>this.smart_contact_form&&!this.contact_form_already_installed?this.$store.dispatch("$_plugins/installPlugin","wpforms-lite"):e))),!this.is_pro){const t=[];this.email_log&&t.push("email_log"),this.complete_email_report&&t.push("complete_email_report"),this.instant_email_alert&&t.push("instant_email_alert"),e.push(this.$store.dispatch("$_settings/savePluginFeatures",t))}Promise.all(e).then((e=>{const t=e.filter((e=>e.success));if(t.length===e.length){this.$emit("displayContentBelow","");let e=this.is_pro&&!this.$store.getters["$_settings/email_log_enabled"]?1:0;this.$next_step(e)}})).finally((()=>{this.$store.dispatch("$_app/stop_loading")}))},previousStep(){this.$emit("displayContentBelow","");const e=this.$wizard_steps.findIndex((e=>this.$route.name.includes(e)))-1;this.$router.push({name:`${this.$wizard_steps[e]}_${this.$store.getters["$_settings/mailer"]}`})},showPluginInstallFooterNotice(){let e=[];this.smart_contact_form&&!this.contact_form_already_installed&&e.push("WPForms");let t="";e.length>0&&(t=(0,l.__)("The following plugin will be installed for free:","easy-wp-smtp"),t=`<p>${t} ${e.join(", ")}</p>`),this.$emit("displayContentBelow",t)},emailLogEnabledChanged(){"0"===this.$easywpsmtp.completed_time&&sessionStorage.setItem("easy_wp_smtp_email_log_enabled_changed","true")}},mounted(){if(this.showPluginInstallFooterNotice(),this.$easywpsmtp.is_pro&&"0"===this.$easywpsmtp.completed_time&&"true"!==sessionStorage.getItem("easy_wp_smtp_email_log_enabled_changed")?this.email_log=!0:this.email_log=this.$store.getters["$_settings/email_log_enabled"],this.summary_report_email=this.$store.getters["$_settings/summary_report_email_enabled"],!this.$easywpsmtp.is_pro){const e=this.$store.getters["$_settings/plugin_features"];e.includes("email_log")&&(this.email_log=!0),e.includes("complete_email_report")&&(this.complete_email_report=!0),e.includes("instant_email_alert")&&(this.instant_email_alert=!0)}}},we=ge,be=(0,d.A)(we,pe,me,!1,null,null,null),ve=be.exports,xe=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-setup-wizard-step easy-wp-smtp-setup-wizard-step-help-improve"},[t("div",{staticClass:"easy-wp-smtp-setup-wizard-content-container"},[t("div",{staticClass:"easy-wp-smtp-help-improve-header"},[t("the-wizard-step-counter"),t("content-header",{attrs:{title:e.text_header_title,subtitle:e.text_header_subtitle}})],1),t("settings-input-text",{attrs:{name:"email",type:"email",label:e.text_email_label,description:e.text_email_description},model:{value:e.current_user_email,callback:function(t){e.current_user_email=t},expression:"current_user_email"}}),t("settings-input-checkbox",{attrs:{name:"usage_tracking",label:e.text_usage_tracking_label,description:e.text_usage_tracking_description,tooltip:e.text_usage_tracking_tooltip},model:{value:e.usage_tracking,callback:function(t){e.usage_tracking=t},expression:"usage_tracking"}})],1),t("div",{staticClass:"easy-wp-smtp-separator easy-wp-smtp-separator-no-margin"}),t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-footer"},[t("a",{attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),e.previousStep.apply(null,arguments)}}},[t("span",{staticClass:"text-with-arrow text-with-arrow-left"},[t("inline-svg",{staticClass:"icon",attrs:{src:s(9004),width:"16",height:"22"}}),e._v(e._s(e.text_previous_step)+" ")],1)]),t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-footer-buttons"},[t("button",{staticClass:"easy-wp-smtp-button easy-wp-smtp-button-tertiary",attrs:{type:"button",name:"skip_step"},domProps:{textContent:e._s(e.text_skip)},on:{click:function(t){return t.preventDefault(),e.nextStep.apply(null,arguments)}}}),t("button",{staticClass:"easy-wp-smtp-button easy-wp-smtp-button-main",attrs:{type:"submit",name:"next_step"},on:{click:function(t){return t.preventDefault(),e.handleSubmit.apply(null,arguments)}}},[t("span",{staticClass:"text-with-arrow text-with-arrow-right"},[e._v(" "+e._s(e.text_save)+" "),t("inline-svg",{staticClass:"icon",attrs:{src:s(953),width:"16",height:"19"}})],1)])])])])},ke=[],Se=function(){var e=this,t=e._self._c;return t("div",{staticClass:"settings-input-text",class:{"settings-input-text-with-copy":e.copy,"input-error":e.has_errors||e.field_error}},[t("label",{staticClass:"settings-input-label-container",attrs:{for:e.id}},[e.label?t("span",{staticClass:"label",domProps:{innerHTML:e._s(e.label)}}):e._e(),e.tooltip?t("settings-info-tooltip",{attrs:{content:e.tooltip}}):e._e()],1),t("span",{staticClass:"settings-input-container"},["checkbox"===e.type?t("input",{directives:[{name:"model",rawName:"v-model",value:e.currentValue,expression:"currentValue"}],ref:"input",attrs:{id:e.id,name:e.name,placeholder:e.placeholder,readonly:e.readonly,disabled:e.disabled||e.is_constant_set,type:"checkbox"},domProps:{checked:Array.isArray(e.currentValue)?e._i(e.currentValue,null)>-1:e.currentValue},on:{change:[function(t){var s=e.currentValue,i=t.target,a=!!i.checked;if(Array.isArray(s)){var n=null,r=e._i(s,n);i.checked?r<0&&(e.currentValue=s.concat([n])):r>-1&&(e.currentValue=s.slice(0,r).concat(s.slice(r+1)))}else e.currentValue=a},e.inputUpdate]}}):"radio"===e.type?t("input",{directives:[{name:"model",rawName:"v-model",value:e.currentValue,expression:"currentValue"}],ref:"input",attrs:{id:e.id,name:e.name,placeholder:e.placeholder,readonly:e.readonly,disabled:e.disabled||e.is_constant_set,type:"radio"},domProps:{checked:e._q(e.currentValue,null)},on:{change:[function(t){e.currentValue=null},e.inputUpdate]}}):t("input",{directives:[{name:"model",rawName:"v-model",value:e.currentValue,expression:"currentValue"}],ref:"input",attrs:{id:e.id,name:e.name,placeholder:e.placeholder,readonly:e.readonly,disabled:e.disabled||e.is_constant_set,type:e.type},domProps:{value:e.currentValue},on:{change:e.inputUpdate,input:function(t){t.target.composing||(e.currentValue=t.target.value)}}}),e.copy?t("button",{staticClass:"easy-wp-smtp-button easy-wp-smtp-button-tertiary easy-wp-smtp-button-small",class:{"easy-wp-smtp-button-copied":e.show_copied},attrs:{title:e.text_copy_button},on:{click:function(t){return t.preventDefault(),e.copyValue.apply(null,arguments)}}},[t("span",{staticClass:"copy-button-container"},[t("inline-svg",{staticClass:"icon",class:{active:!e.show_copied},attrs:{src:s(7726),width:"16",height:"16"}}),t("inline-svg",{staticClass:"icon copied",class:{active:e.show_copied},attrs:{src:s(2452),width:"16",height:"16"}})],1)]):e._e()]),e.has_errors?t("p",{staticClass:"error"},[t("inline-svg",{staticClass:"icon",attrs:{src:s(617),width:"16"}}),t("span",{domProps:{innerHTML:e._s(e.text_error)}})],1):e._e(),e.description?t("p",{staticClass:"description",domProps:{innerHTML:e._s(e.description)}}):e._e(),e.is_constant_set?t("p",{staticClass:"description description--constant",domProps:{innerHTML:e._s(e.text_constant)}}):e._e()])},Pe=[],Ae=function(){var e=this,t=e._self._c;return t("span",{directives:[{name:"tooltip",rawName:"v-tooltip",value:e.tooltip_data,expression:"tooltip_data"}],staticClass:"easy-wp-smtp-info",attrs:{tabindex:"0"}},[t("inline-svg",{staticClass:"icon",attrs:{src:s(4744),width:"16",height:"16"}})],1)},Ee=[],$e={name:"SettingsInfoTooltip",props:{content:String},data(){return{tooltip_data:{content:this.content,autoHide:!1,trigger:"hover focus click"}}}},Ce=$e,Me=(0,d.A)(Ce,Ae,Ee,!1,null,"2a14ae56",null),Te=Me.exports,Ie={name:"SettingsInputText",components:{SettingsInfoTooltip:Te},props:{name:String,value:String,label:String,description:String,constant:String,placeholder:String,type:{type:String,default:"text"},tooltip:String,readonly:Boolean,disabled:Boolean,format:RegExp,error:{type:String,default:""},copy:{type:Boolean,default:!1},is_error:Boolean},data(){return{has_error:!1,id:"input-"+this.name,text_copy_button:(0,l.__)("Copy input value","easy-wp-smtp"),text_copied:(0,l.__)("Copied!","easy-wp-smtp"),show_copied:!1}},computed:{currentValue:{get(){return this.value},set(e){this.$emit("is_error_update",!1),this.$emit("input",e)}},field_error:{get(){return this.is_error},set(e){this.$emit("is_error_update",e)}},has_errors:function(){return this.error.length>0||this.has_error},text_error:function(){return this.error.length>0?this.error:(0,l.__)("The value entered does not match the required format","easy-wp-smtp")},is_constant_set:function(){return this.$easywpsmtp.defined_constants.includes(this.constant)},text_constant:function(){return(0,l.__)("This setting is already configured with the Easy WP SMTP constant. To change it, please edit or remove the <code>"+this.constant+"</code> constant in your <code>wp-config.php</code> file.","easy-wp-smtp")}},methods:{inputUpdate:function(e){if(this.disabled)return!1;if(this.has_error=!1,this.format||this.type&&"email"===this.type){const t=this.format?this.format:/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;if(!t.test(e.target.value))return this.has_error=!0,this.$emit("error_detected",this.text_error),!1}},copyValue:function(){const e=this.$refs.input;e.select(),document.execCommand("copy"),this.show_copied=!0;let t=this;setTimeout((function(){t.show_copied=!1}),1e3)}}},Fe=Ie,ze=(0,d.A)(Fe,Se,Pe,!1,null,null,null),Le=ze.exports,Oe=function(){var e=this,t=e._self._c;return t("div",{staticClass:"settings-input-checkbox",class:{"settings-input-checkbox-checked":e.value,"settings-input-checkbox-disabled":e.disabled}},[t("span",{staticClass:"settings-input-label-container"},[t("span",{staticClass:"label"},[e._v(e._s(e.label))]),e.tooltip?t("settings-info-tooltip",{attrs:{content:e.tooltip}}):e._e()],1),t("label",{staticClass:"settings-input-checkbox-container",attrs:{for:"easy-wp-smtp-settings-checkbox-"+e.name}},[t("input",{attrs:{id:"easy-wp-smtp-settings-checkbox-"+e.name,type:"checkbox",name:e.name,disabled:e.disabled},domProps:{checked:e.value},on:{input:function(t){return e.$emit("input",t.target.checked)}}}),t("span",{staticClass:"checkbox",class:{"checkbox-checked":e.value,"checkbox-disabled":e.disabled}}),e.description?t("span",{staticClass:"input-label"},[e._v(e._s(e.description))]):e._e()])])},Re=[],We={name:"SettingsInputCheckbox",components:{SettingsInfoTooltip:Te},props:{label:String,name:String,value:Boolean,description:String,tooltip:String,disabled:Boolean}},Ne=We,Ye=(0,d.A)(Ne,Oe,Re,!1,null,null,null),De=Ye.exports,Ue={name:"WizardStepHelpImprove",components:{ContentHeader:f,TheWizardStepCounter:X,SettingsInputText:Le,SettingsInputCheckbox:De},data(){return{text_header_title:(0,l.__)("Help Us Improve Easy WP SMTP","easy-wp-smtp"),text_header_subtitle:(0,l.__)("Enter your email address to receive helpful suggestions from Easy WP SMTP. We’ll help you optimize your email deliverability and grow your business.","easy-wp-smtp"),text_save:(0,l.__)("Save and Continue","easy-wp-smtp"),text_skip:(0,l.__)("Skip this Step","easy-wp-smtp"),text_previous_step:(0,l.__)("Previous Step","easy-wp-smtp"),text_email_label:(0,l.__)("Your Email Address","easy-wp-smtp"),text_email_description:(0,l.__)("Your email address is needed if you want to receive recommendations.","easy-wp-smtp"),text_usage_tracking_label:(0,l.__)("Help make Easy WP SMTP better for everyone","easy-wp-smtp"),text_usage_tracking_description:(0,l.__)("Yes, count me in","easy-wp-smtp"),text_usage_tracking_tooltip:(0,l.__)("Allowing us to track usage data enables us to better help you because we know with which WordPress configurations, themes, and plugins to test.","easy-wp-smtp"),is_pro:this.$easywpsmtp.is_pro,usage_tracking:!1}},computed:{...(0,ne.YP)("$_wizard",["current_user_email"])},methods:{handleSubmit(){this.$store.dispatch("$_app/start_loading");let e=[];if(this.current_user_email&&e.push(this.$store.dispatch("$_settings/subscribeToNewsletter",this.current_user_email)),this.usage_tracking){const t={value:{general:{"usage-tracking-enabled":!0}}};e.push(this.$store.dispatch("$_settings/updateSettings",t))}Promise.all(e).then((()=>{this.nextStep()})).finally((()=>{this.$store.dispatch("$_app/stop_loading")}))},nextStep(){this.$next_step()},previousStep(){this.$previous_step()}}},Be=Ue,Ve=(0,d.A)(Be,xe,ke,!1,null,null,null),He=Ve.exports,Ke=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-setup-wizard-step easy-wp-smtp-setup-wizard-step-license"},[t("div",{staticClass:"easy-wp-smtp-setup-wizard-content-container"},[t("div",{staticClass:"easy-wp-smtp-license-header"},[t("the-wizard-step-counter"),t("content-header",{attrs:{title:e.text_header_title,subtitle:e.text_header_subtitle}})],1),e.is_pro?e._e():t("div",{staticClass:"upgrade-content"},[t("p",{domProps:{innerHTML:e._s(e.text_upgrade_paragraph)}}),t("div",{staticClass:"checked-item-list"},[t("span",{staticClass:"checked-item"},[t("inline-svg",{staticClass:"icon",attrs:{src:s(1369),width:"16",height:"12"}}),e._v(" "),t("span",[e._v(e._s(e.text_email_log))])],1),t("span",{staticClass:"checked-item"},[t("inline-svg",{staticClass:"icon",attrs:{src:s(1369),width:"16",height:"12"}}),e._v(" "),t("span",[e._v(e._s(e.text_complete_email_report))])],1),t("span",{staticClass:"checked-item"},[t("inline-svg",{staticClass:"icon",attrs:{src:s(1369),width:"16",height:"12"}}),e._v(" "),t("span",[e._v(e._s(e.text_summary_report_email))])],1)])]),e.verified?t("div",{staticClass:"verified-license"},[t("p",{domProps:{innerHTML:e._s(e.text_verified_license)}})]):t("div",{staticClass:"license-form",class:{"license-form-error":e.license_error}},[t("p",{domProps:{innerHTML:e._s(e.text_license_form)}}),t("div",{staticClass:"license-control"},[t("input",{directives:[{name:"model",rawName:"v-model",value:e.license,expression:"license"}],attrs:{name:"license",type:"password",placeholder:e.text_license_input_placeholder,"aria-label":e.text_aria_label_for_license_input},domProps:{value:e.license},on:{input:function(t){t.target.composing||(e.license=t.target.value)}}}),t("button",{staticClass:"easy-wp-smtp-button easy-wp-smtp-button-main easy-wp-smtp-button-small",attrs:{type:"button"},on:{click:function(t){return t.preventDefault(),e.handleLicenseSubmit.apply(null,arguments)}}},[e._v(" "+e._s(e.text_license_button)+" ")])]),e.license_error?t("p",{staticClass:"error-message",domProps:{textContent:e._s(e.text_license_error)}}):e._e()])]),t("div",{staticClass:"easy-wp-smtp-separator easy-wp-smtp-separator-no-margin"}),t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-footer"},[t("a",{attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),e.previousStep.apply(null,arguments)}}},[t("span",{staticClass:"text-with-arrow text-with-arrow-left"},[t("inline-svg",{staticClass:"icon",attrs:{src:s(9004),width:"16",height:"22"}}),e._v(e._s(e.text_previous_step)+" ")],1)]),t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-footer-buttons"},[e.verified?t("button",{staticClass:"easy-wp-smtp-button easy-wp-smtp-button-main",attrs:{type:"submit",name:"next_step"},on:{click:function(t){return t.preventDefault(),e.handleSubmit.apply(null,arguments)}}},[t("span",{staticClass:"text-with-arrow text-with-arrow-right"},[e._v(" "+e._s(e.text_save)+" "),t("inline-svg",{staticClass:"icon",attrs:{src:s(953),width:"16",height:"19"}})],1)]):t("button",{staticClass:"easy-wp-smtp-button easy-wp-smtp-button-tertiary",attrs:{type:"button",name:"skip_step"},domProps:{textContent:e._s(e.text_skip)},on:{click:function(t){return t.preventDefault(),e.nextStep.apply(null,arguments)}}})])])])},qe=[],Ge=(s(4603),s(7566),s(8721),s(470)),je=s.n(Ge),Ze={name:"WizardStepLicense",components:{ContentHeader:f,TheWizardStepCounter:X},data(){return{text_header_title:(0,l.__)("Enter your Easy WP SMTP License Key","easy-wp-smtp"),text_header_subtitle:this.$easywpsmtp.is_pro?"":(0,l.__)("You're using Easy WP SMTP Lite - no license key required. Enjoy!","easy-wp-smtp"),text_save:(0,l.__)("Continue","easy-wp-smtp"),text_skip:(0,l.__)("Skip this Step","easy-wp-smtp"),text_previous_step:(0,l.__)("Previous Step","easy-wp-smtp"),text_upgrade_paragraph:(0,l.nv)((0,l.__)("To unlock the following features, %1$sUpgrade to Pro%2$s and enter your license key below.","easy-wp-smtp"),'<a href="'+this.$easywpsmtp.upgrade_link+'" target="_blank" rel="noopener noreferrer">',"</a>"),text_email_log:(0,l.__)("Detailed Email Logs","easy-wp-smtp"),text_summary_report_email:(0,l.__)("Enhanced Weekly Email Summary","easy-wp-smtp"),text_license_form_lite:(0,l.nv)((0,l.__)("Already purchased %1$sEasy WP SMTP Pro%2$s? Enter your license key below!","easy-wp-smtp"),"<b>","</b>"),text_license_form_pro:(0,l.__)("Add your license key here to access plugin updates and support.","easy-wp-smtp"),text_license_button:this.$easywpsmtp.is_pro?(0,l.__)("Verify License Key","easy-wp-smtp"):(0,l.__)("Connect","easy-wp-smtp"),text_license_error:(0,l.__)("The License Key format is incorrect. Please enter a valid key and try again.","easy-wp-smtp"),text_verified_license:(0,l.__)("Your license was successfully verified! You are ready for the next step.","easy-wp-smtp"),text_email_log_desc:(0,l.__)("Store information from all emails sent from your site.","easy-wp-smtp"),text_instant_email_alert:(0,l.__)("Instant Email Alerts","easy-wp-smtp"),text_instant_email_alert_desc:(0,l.__)("Get notifications via email, SMS, Slack, or webhook when emails fail to send.","easy-wp-smtp"),text_complete_email_report:(0,l.__)("Complete Email Reports","easy-wp-smtp"),text_complete_email_report_desc:(0,l.__)("View your emails’ delivery status, open & click tracking, and deliverability charts.","easy-wp-smtp"),text_pro_badge:(0,l.__)("Pro badge","easy-wp-smtp"),text_aria_label_for_license_input:(0,l.__)("License key input","easy-wp-smtp"),text_license_input_placeholder:(0,l.__)("Paste your license key here","easy-wp-smtp"),pro_badge:s(3453),is_pro:this.$easywpsmtp.is_pro,verified:!1,license:"",license_error:!1}},computed:{text_license_form:function(){return this.is_pro?this.text_license_form_pro:this.text_license_form_lite},...(0,Y.L8)({selectedProFeatures:"$_settings/plugin_features"})},methods:{handleLicenseSubmit(){return this.license_error=!1,!(!this.is_pro&&0===this.license.length)&&(this.is_pro&&this.license.length<16?(this.license_error=!0,!1):(this.$store.dispatch("$_app/start_loading"),void(this.is_pro?this.$store.dispatch("$_settings/verifyLicense",this.license).then((e=>{e.success?(this.verified=!0,this.$swal({title:(0,l.__)("Successful Verification!","easy-wp-smtp"),html:e.data.message,width:450,showCloseButton:!0,customClass:{container:"easy-wp-smtp-swal easy-wp-smtp-swal-alert"}})):this.$swal({title:(0,l.__)("Verification Error!","easy-wp-smtp"),html:e.data,width:450,showCloseButton:!0,customClass:{container:"easy-wp-smtp-swal easy-wp-smtp-swal-alert"}})})).finally((()=>{this.$store.dispatch("$_app/stop_loading")})):this.$store.dispatch("$_settings/upgradePlugin",this.license).then((e=>{if(e.success&&je()(e,"data.redirect_url"))return window.location=e.data.redirect_url;this.$store.dispatch("$_app/stop_loading"),this.$swal({title:e.success?(0,l.__)("Successful Upgrade!","easy-wp-smtp"):(0,l.__)("Upgrade Failed!","easy-wp-smtp"),html:e.data,width:450,showCloseButton:!0,customClass:{container:"easy-wp-smtp-swal easy-wp-smtp-swal-alert"}})})))))},handleSubmit(){this.nextStep()},nextStep(){this.$next_step()},previousStep(){let e=this.is_pro&&!this.$store.getters["$_settings/email_log_enabled"]?1:0;this.$previous_step(e)},prepareLongCheckbox(e,t){return`<label for="email_log" class="settings-input-long-checkbox settings-input-long-checkbox-checked settings-input-long-checkbox-disabled">\n\t\t\t\t\t\t\t<div class="settings-input-long-checkbox-header">\n\t\t\t\t\t\t\t\t<span class="title-container">\n\t\t\t\t\t\t\t\t\t<span class="label">\n\t\t\t\t\t\t\t\t\t\t${e}\n\t\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t\t\t<img src="${this.pro_badge}" alt="${this.text_pro_badge}" class="easy-wp-smtp-pro-badge">\n\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t\t<p class="description">\n\t\t\t\t\t\t\t\t\t${t}\n\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<span class="settings-input-long-checkbox-container">\n\t\t\t\t\t\t\t\t<span class="checkbox checkbox-checked checkbox-disabled"></span>\n\t\t\t\t\t\t\t\t<input id="email_log" type="checkbox" name="email_log" disabled="disabled">\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t</label>`},prepareProFeaturesHtml(){let e="<div>";return this.selectedProFeatures.includes("email_log")&&(e+=this.prepareLongCheckbox(this.text_email_log,this.text_email_log_desc)),this.selectedProFeatures.includes("complete_email_report")&&(e+=this.prepareLongCheckbox(this.text_complete_email_report,this.text_complete_email_report_desc)),this.selectedProFeatures.includes("instant_email_alert")&&(e+=this.prepareLongCheckbox(this.text_instant_email_alert,this.text_instant_email_alert_desc)),e+"</div>"}},mounted(){if(!this.is_pro&&this.selectedProFeatures.length>0){const e=this.prepareProFeaturesHtml();this.$swal({title:(0,l.__)("Would you like to purchase the following features now?","easy-wp-smtp"),html:`<p class="subtitle">${(0,l.__)("These features are available as part of Easy WP SMTP Pro plan.","easy-wp-smtp")}</p>\n\t\t\t\t\t\t\t${e}\n\t\t\t\t\t\t\t<div class="upgrade-bonus">\n\t\t\t\t\t\t\t<svg class="icon" width="16" height="16" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M504 256c0 136.967-111.033 248-248 248S8 392.967 8 256 119.033 8 256 8s248 111.033 248 248zM227.314 387.314l184-184c6.248-6.248 6.248-16.379 0-22.627l-22.627-22.627c-6.248-6.249-16.379-6.249-22.628 0L216 308.118l-70.059-70.059c-6.248-6.248-16.379-6.248-22.628 0l-22.627 22.627c-6.248 6.248-6.248 16.379 0 22.627l104 104c6.249 6.249 16.379 6.249 22.628.001z"></path></svg>\n\t\t\t\t\t\t\t<span>${this.$easywpsmtp.education.upgrade_bonus_long}</span>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t`,width:850,showCloseButton:!0,allowOutsideClick:!1,allowEscapeKey:!1,allowEnterKey:!1,customClass:{container:"easy-wp-smtp-swal easy-wp-smtp-swal-plugin-upgrade"},confirmButtonText:(0,l.__)("Purchase Now","easy-wp-smtp"),cancelButtonText:(0,l.__)("I'll do it later","easy-wp-smtp"),showCancelButton:!0,reverseButtons:!0}).then((e=>{if(e.value){const e=window.open(this.$easywpsmtp.upgrade_link,"_blank");e.focus()}}))}this.verified=this.$easywpsmtp.license_exists},created(){const e=new URLSearchParams(window.location.search);this.$easywpsmtp.license_exists&&!e.has("upgrade-redirect")&&this.nextStep()}},Je=Ze,Xe=(0,d.A)(Je,Ke,qe,!1,null,null,null),Qe=Xe.exports,et=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-setup-wizard-step easy-wp-smtp-setup-wizard-check-configuration"},[t("div",{staticClass:"easy-wp-smtp-setup-wizard-content-container"},[t("div",{staticClass:"easy-wp-smtp-check-configuration-header"},[t("content-header",{attrs:{title:e.text_header_title,subtitle:e.text_header_subtitle}})],1),t("div",{staticClass:"check-configuration-loading-image-container"},[t("img",{attrs:{src:s(6915),alt:e.text_image_alt}})])])])},tt=[],st={name:"WizardStepCheckConfiguration",components:{ContentHeader:f},data(){return{text_header_title:(0,l.__)("Checking Mailer Configuration","easy-wp-smtp"),text_header_subtitle:(0,l.__)("We're running some tests in the background to make sure everything is set up properly.","easy-wp-smtp"),text_image_alt:(0,l.__)("Checking mailer configuration image","easy-wp-smtp")}},mounted(){this.$store.dispatch("$_wizard/checkMailerConfiguration").then((e=>{e.success?this.$router.push({name:"check_configuration_step_success"}):this.$router.push({name:"check_configuration_step_failure"})}))}},it=st,at=(0,d.A)(it,et,tt,!1,null,null,null),nt=at.exports,rt=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-setup-wizard-step easy-wp-smtp-setup-wizard-configuration-success"},[t("div",{staticClass:"easy-wp-smtp-setup-wizard-content-container"},[t("div",{staticClass:"easy-wp-smtp-configuration-success-header"},[t("the-wizard-step-counter"),t("content-header",{attrs:{title:e.text_header_title}})],1),t("inline-svg",{staticStyle:{display:"block",margin:"0 auto"},attrs:{src:s(6478),width:"275"}})],1),t("div",{staticClass:"easy-wp-smtp-separator easy-wp-smtp-separator-no-margin"}),t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-footer"},[t("button",{staticClass:"easy-wp-smtp-button easy-wp-smtp-button-tertiary",attrs:{type:"button",name:"send_test_email"},domProps:{textContent:e._s(e.text_test_email)},on:{click:function(t){return t.preventDefault(),e.handleTestEmail.apply(null,arguments)}}}),t("button",{staticClass:"easy-wp-smtp-button easy-wp-smtp-button-tertiary",attrs:{type:"button",name:"send_feedback"},domProps:{textContent:e._s(e.text_send_feedback)},on:{click:function(t){return t.preventDefault(),e.handleFeedback.apply(null,arguments)}}}),t("button",{staticClass:"easy-wp-smtp-button easy-wp-smtp-button-main",attrs:{type:"button",name:"finish_setup"},domProps:{textContent:e._s(e.text_finish)},on:{click:function(t){return t.preventDefault(),e.handleFinish.apply(null,arguments)}}})])])},ot=[],lt={name:"WizardStepConfigurationSuccess",components:{ContentHeader:f,TheWizardStepCounter:X},data(){return{text_header_title:(0,l.__)("Congrats, you’ve successfully set up Easy WP SMTP.","easy-wp-smtp"),text_test_email:(0,l.__)("Send a Test Email","easy-wp-smtp"),text_send_feedback:(0,l.__)("Send us Feedback","easy-wp-smtp"),text_finish:(0,l.__)("Finish Setup","easy-wp-smtp"),star_image_html:`<img src="${s(7157)}" alt="${(0,l.__)("Star icon","easy-wp-smtp")}" class="icon" / >`,is_pro:this.$easywpsmtp.is_pro}},computed:{...(0,Y.L8)({plugins:"$_plugins/partner_plugins"})},methods:{handleTestEmail(){return window.location=this.$easywpsmtp.email_test_tab_url},goodFeedback(){this.$swal({title:(0,l.__)("Thanks for the feedback!","easy-wp-smtp"),html:`${(0,l.nv)((0,l.__)("Help us spread the word %1$sby giving Easy WP SMTP a 5-star rating %3$s(%4$s) on WordPress.org%2$s. Thanks for your support and we look forward to bringing you more awesome features.","easy-wp-smtp"),'<span class="medium-bold">',"</span>","<br>",this.star_image_html+""+this.star_image_html+this.star_image_html+this.star_image_html+this.star_image_html)}`,width:650,showCloseButton:!0,allowEnterKey:!1,confirmButtonText:(0,l.__)("Rate on WordPress.org","easy-wp-smtp"),customClass:{container:"easy-wp-smtp-swal easy-wp-smtp-swal-feedback-good"}}).then((e=>{if(e.value){const e=window.open("https://wordpress.org/support/plugin/easy-wp-smtp/reviews/#new-post","_blank");e.focus()}}))},badFeedback(){this.$swal({title:(0,l.__)("What could we do to improve?","easy-wp-smtp"),html:`${(0,l.__)("We're sorry things didn't go smoothly for you, and want to keep improving. Please let us know any specific parts of this process that you think could be better. We really appreciate any details you're willing to share!","easy-wp-smtp")}\n\t\t\t\t\t\t\t\t\t<textarea id="feedback" name="feedback" rows="9"></textarea>\n\t\t\t\t\t\t\t\t\t<span class="permission-container">\n\t\t\t\t\t\t\t\t\t\t<input type="checkbox" id="permission" name="permission">\n\t\t\t\t\t\t\t\t\t\t<label for="permission">${(0,l.__)("Yes, I give Easy WP SMTP permission to contact me for any follow up questions.","easy-wp-smtp")}</label>\n\t\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t\t`,width:650,showCloseButton:!0,allowEnterKey:!1,allowOutsideClick:!1,allowEscapeKey:!1,confirmButtonText:(0,l.__)("Submit Feedback","easy-wp-smtp"),customClass:{container:"easy-wp-smtp-swal easy-wp-smtp-swal-feedback-bad"},preConfirm:()=>[document.getElementById("feedback").value,document.getElementById("permission").checked]}).then((e=>{if(e.value){const t=e.value[0],s=e.value[1];this.$store.dispatch("$_wizard/sendFeedback",{feedback:t,permission:s})}}))},handleFeedback(){this.$swal({title:(0,l.__)("How was your Easy WP SMTP setup experience?","easy-wp-smtp"),text:(0,l.__)("Our goal is to make your SMTP setup as simple and straightforward as possible. We'd love to know how this process went for you!","easy-wp-smtp"),width:650,showCloseButton:!0,allowEnterKey:!1,customClass:{container:"easy-wp-smtp-swal easy-wp-smtp-swal-feedback"},showCancelButton:!0}).then((e=>{e.value?this.goodFeedback():void 0!==e.dismiss&&"cancel"===e.dismiss&&this.badFeedback()}))},handleFinish(){return window.location=this.$easywpsmtp.exit_url},openUpgradePage:function(){const e=window.open(this.$easywpsmtp.upgrade_link,"_blank");e.focus()}}},_t=lt,pt=(0,d.A)(_t,rt,ot,!1,null,null,null),mt=pt.exports,ct=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-setup-wizard-step easy-wp-smtp-setup-wizard-configuration-failure"},[t("div",{staticClass:"easy-wp-smtp-setup-wizard-content-container"},[t("div",{staticClass:"easy-wp-smtp-configuration-failure-header"},[t("the-wizard-step-counter"),t("content-header",{attrs:{title:e.text_header_title,subtitle:e.text_header_subtitle}})],1),t("div",{staticClass:"start-troubleshooting-arrow-container"},[t("inline-svg",{staticClass:"icon",attrs:{src:s(5573),width:"112",height:"112"}})],1)]),t("div",{staticClass:"easy-wp-smtp-separator easy-wp-smtp-separator-no-margin"}),t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-footer"},[t("button",{staticClass:"easy-wp-smtp-button easy-wp-smtp-button-main",attrs:{type:"button",name:"start_troubleshooting"},domProps:{textContent:e._s(e.text_start_troubleshooting)},on:{click:function(t){return t.preventDefault(),e.handleTroubleshooting.apply(null,arguments)}}}),t("button",{staticClass:"easy-wp-smtp-button easy-wp-smtp-button-tertiary",attrs:{type:"button",name:"finish_setup"},domProps:{textContent:e._s(e.text_finish)},on:{click:function(t){return t.preventDefault(),e.handleFinish.apply(null,arguments)}}})])])},dt=[],ut={name:"WizardStepConfigurationFailure",components:{ContentHeader:f,TheWizardStepCounter:X},data(){return{text_header_title:(0,l.__)("Whoops, looks like something isn’t configured quite right.","easy-wp-smtp"),text_header_subtitle:(0,l.__)("We tried sending a test email, but we’re not able to do so. For more details about the issue we’ve found, as well as steps for resolving it, please begin troubleshooting.","easy-wp-smtp"),text_start_troubleshooting:(0,l.__)("Start Troubleshooting","easy-wp-smtp"),text_send_feedback:(0,l.__)("Send us Feedback","easy-wp-smtp"),text_finish:(0,l.__)("Finish Setup","easy-wp-smtp")}},methods:{handleTroubleshooting(){return window.location=`${this.$easywpsmtp.email_test_tab_url}&auto-start=1`},handleFinish(){return window.location=this.$easywpsmtp.exit_url}}},ft=ut,ht=(0,d.A)(ft,ct,dt,!1,null,null,null),yt=ht.exports,gt=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-configure-mailer-settings easy-wp-smtp-setup-wizard-step-configure-mailer-settings-smtp"},[t("div",{staticClass:"mailer-description",domProps:{innerHTML:e._s(e.description)}}),t("div",{staticClass:"easy-wp-smtp-setup-wizard-form"},[t("settings-input-text",{attrs:{name:"host",constant:"EASY_WP_SMTP_SMTP_HOST",label:e.text_host_label,is_error:e.field_errors.includes("host")},on:{is_error_update:function(t){return e.removeFieldError("host")}},model:{value:e.host,callback:function(t){e.host=t},expression:"host"}}),t("settings-input-radio",{attrs:{name:"encryption",constant:"EASY_WP_SMTP_SSL",label:e.text_encryption_label,options:e.encryptionOptions,description:e.text_encryption_description},on:{input:e.encryptionChanged},model:{value:e.encryption,callback:function(t){e.encryption=t},expression:"encryption"}}),t("settings-input-number",{attrs:{name:"port",constant:"EASY_WP_SMTP_SMTP_PORT",label:e.text_port_label,is_error:e.field_errors.includes("port")},on:{is_error_update:function(t){return e.removeFieldError("port")}},model:{value:e.port,callback:function(t){e.port=t},expression:"port"}}),t("settings-input-switch",{directives:[{name:"show",rawName:"v-show",value:e.show_autotls,expression:"show_autotls"}],attrs:{name:"autotls",constant:"EASY_WP_SMTP_SMTP_AUTOTLS",title:e.text_autotls_title,label:e.text_autotls_label,description:e.text_autotls_description},model:{value:e.autotls,callback:function(t){e.autotls=t},expression:"autotls"}}),t("div",{staticClass:"easy-wp-smtp-separator easy-wp-smtp-separator-big-margin"}),t("settings-input-switch",{attrs:{name:"auth",constant:"EASY_WP_SMTP_SMTP_AUTH",title:e.text_auth_title,label:e.text_auth_label},model:{value:e.auth,callback:function(t){e.auth=t},expression:"auth"}}),t("settings-input-text",{directives:[{name:"show",rawName:"v-show",value:e.auth,expression:"auth"}],attrs:{name:"user",constant:"EASY_WP_SMTP_SMTP_USER",label:e.text_user_label,is_error:e.field_errors.includes("user")},on:{is_error_update:function(t){return e.removeFieldError("user")}},model:{value:e.user,callback:function(t){e.user=t},expression:"user"}}),t("settings-input-text",{directives:[{name:"show",rawName:"v-show",value:e.auth,expression:"auth"}],attrs:{name:"pass",constant:"EASY_WP_SMTP_SMTP_PASS",type:"password",label:e.text_pass_label,is_error:e.field_errors.includes("pass")},on:{is_error_update:function(t){return e.removeFieldError("pass")}},model:{value:e.pass,callback:function(t){e.pass=t},expression:"pass"}}),t("div",{staticClass:"easy-wp-smtp-separator easy-wp-smtp-separator-big-margin"}),t("settings-input-text",{attrs:{name:"from_name",constant:"EASY_WP_SMTP_MAIL_FROM_NAME",label:e.text_from_name_label,description:e.text_from_name_description},model:{value:e.from_name,callback:function(t){e.from_name=t},expression:"from_name"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_name_force",constant:"EASY_WP_SMTP_MAIL_FROM_NAME_FORCE",label:e.text_force_from_name_label,description:e.text_force_from_name_description},model:{value:e.from_name_force,callback:function(t){e.from_name_force=t},expression:"from_name_force"}}),t("settings-input-text",{attrs:{name:"from_email",type:"email",constant:"EASY_WP_SMTP_MAIL_FROM",label:e.text_from_email_label,description:e.text_from_email_description,is_error:e.field_errors.includes("from_email")},on:{is_error_update:function(t){return e.removeFieldError("from_email")},error_detected:t=>e.errorDetected(t,"from_email")},model:{value:e.from_email,callback:function(t){e.from_email=t},expression:"from_email"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_email_force",constant:"EASY_WP_SMTP_MAIL_FROM_FORCE",label:e.text_force_from_email_label,description:e.text_force_from_email_description},model:{value:e.from_email_force,callback:function(t){e.from_email_force=t},expression:"from_email_force"}})],1)])},wt=[],bt=function(){var e=this,t=e._self._c;return t("div",{staticClass:"settings-input-radio"},[e.label?t("span",{staticClass:"settings-input-label-container"},[t("span",{staticClass:"label"},[e._v(e._s(e.label))])]):e._e(),t("div",{staticClass:"settings-input-radio-container"},e._l(e.options,(function(s){return t("label",{key:s.value,class:e.labelClass(s.value),attrs:{for:"easy-wp-smtp-settings-radio-"+e.name+"["+s.value+"]"}},[t("input",{directives:[{name:"model",rawName:"v-model",value:e.selected,expression:"selected"}],attrs:{id:"easy-wp-smtp-settings-radio-"+e.name+"["+s.value+"]",type:"radio",name:e.name,autocomplete:"off",readonly:e.disabled,disabled:e.is_constant_set},domProps:{value:s.value,checked:e.isChecked(s.value),checked:e._q(e.selected,s.value)},on:{change:[function(t){e.selected=s.value},e.updateSetting]}}),t("span",{class:e.titleClass(s.value)}),t("span",{staticClass:"input-label"},[e._v(e._s(s.label))])])})),0),e.description?t("p",{staticClass:"description",domProps:{innerHTML:e._s(e.description)}}):e._e(),e.is_constant_set?t("p",{staticClass:"description description--constant",domProps:{innerHTML:e._s(e.text_constant)}}):e._e()])},vt=[],xt={name:"SettingsInputRadio",props:{options:Array,label:String,name:String,value:String,description:String,constant:String,disabled:Boolean},data(){return{has_error:!1}},computed:{selected:{get(){return this.value},set(e){this.$emit("input",e)}},is_constant_set:function(){return this.$easywpsmtp.defined_constants.includes(this.constant)},text_constant:function(){return(0,l.__)("This setting is already configured with the Easy WP SMTP constant. To change it, please edit or remove the <code>"+this.constant+"</code> constant in your <code>wp-config.php</code> file.","easy-wp-smtp")}},methods:{updateSetting:function(){if(this.disabled)return!1},titleClass(e){let t="easy-wp-smtp-styled-radio";return this.isChecked(e)&&(t+=" easy-wp-smtp-styled-radio-checked"),this.is_constant_set&&(t+=" easy-wp-smtp-styled-radio-disabled"),t},labelClass(e){let t="";return this.isChecked(e)&&(t+=" easy-wp-smtp-styled-radio-label-checked"),this.is_constant_set&&(t+=" easy-wp-smtp-styled-radio-label-disabled"),t},isChecked(e){return e===this.selected}}},kt=xt,St=(0,d.A)(kt,bt,vt,!1,null,null,null),Pt=St.exports,At=function(){var e=this,t=e._self._c;return t("div",{staticClass:"settings-input-number",class:{"settings-input-number-error":e.field_error}},[t("label",{staticClass:"settings-input-label-container",attrs:{for:e.id}},[e.label?t("span",{staticClass:"label"},[e._v(e._s(e.label))]):e._e(),e.tooltip?t("settings-info-tooltip",{attrs:{content:e.tooltip}}):e._e()],1),t("input",{directives:[{name:"model",rawName:"v-model",value:e.currentValue,expression:"currentValue"}],attrs:{id:e.id,type:"number",name:e.name,placeholder:e.placeholder,min:e.min,max:e.max,step:e.step,readonly:e.disabled,disabled:e.is_constant_set},domProps:{value:e.currentValue},on:{change:e.inputUpdate,input:function(t){t.target.composing||(e.currentValue=t.target.value)}}}),e.has_error?t("p",{staticClass:"error"},[t("inline-svg",{staticClass:"icon",attrs:{src:s(617),width:"16"}}),t("span",{domProps:{innerHTML:e._s(e.has_error)}})],1):e._e(),e.description?t("p",{staticClass:"description"},[e._v(" "+e._s(e.description)+" ")]):e._e(),e.is_constant_set?t("p",{staticClass:"description description--constant",domProps:{innerHTML:e._s(e.text_constant)}}):e._e()])},Et=[],$t={name:"SettingsInputNumber",components:{SettingsInfoTooltip:Te},props:{name:String,value:[Number,String],label:String,description:String,constant:String,placeholder:String,type:{type:String,default:"text"},tooltip:String,default_value:String,min:Number,max:Number,disabled:Boolean,step:{type:Number,default:1},round:{type:Boolean,default:!1},is_error:Boolean},data(){return{has_error:!1,id:"input-"+this.name,text_error_value:(0,l.nv)((0,l.__)("Please enter a value between %1$s and %2$s","easy-wp-smtp"),"<strong>"+this.min+"</strong>","<strong>"+this.max+"</strong>"),text_error_round:(0,l.__)("Value has to be a round number","easy-wp-smtp")}},computed:{currentValue:{get(){return this.value},set(e){this.$emit("is_error_update",!1),this.$emit("input",parseInt(e,10))}},field_error:{get(){return this.is_error},set(e){this.$emit("is_error_update",e)}},is_constant_set:function(){return this.$easywpsmtp.defined_constants.includes(this.constant)},text_constant:function(){return(0,l.__)("This setting is already configured with the Easy WP SMTP constant. To change it, please edit or remove the <code>"+this.constant+"</code> constant in your <code>wp-config.php</code> file.","easy-wp-smtp")}},methods:{inputUpdate:function(e){if(this.disabled)return!1;this.has_error=!1;const t=parseFloat(e.target.value);return this.round&&t%1!==0?(this.has_error=this.text_error_round,!1):t>this.max||t<this.min?(this.has_error=this.text_error_value,!1):void 0}}},Ct=$t,Mt=(0,d.A)(Ct,At,Et,!1,null,null,null),Tt=Mt.exports,It=function(){var e=this,t=e._self._c;return t("div",{staticClass:"settings-input-switch",class:e.classname},[t("label",{attrs:{for:e.id}},[e.title?t("span",{staticClass:"title settings-input-label-container"},[t("span",{staticClass:"label",domProps:{innerHTML:e._s(e.title)}}),e.tooltip?t("settings-info-tooltip",{attrs:{content:e.tooltip}}):e._e()],1):e._e(),t("span",{staticClass:"control"},[t("input",{directives:[{name:"model",rawName:"v-model",value:e.currentValue,expression:"currentValue"}],attrs:{id:e.id,type:"checkbox",name:e.name,disabled:e.disabled||e.is_constant_set},domProps:{checked:Array.isArray(e.currentValue)?e._i(e.currentValue,null)>-1:e.currentValue},on:{change:[function(t){var s=e.currentValue,i=t.target,a=!!i.checked;if(Array.isArray(s)){var n=null,r=e._i(s,n);i.checked?r<0&&(e.currentValue=s.concat([n])):r>-1&&(e.currentValue=s.slice(0,r).concat(s.slice(r+1)))}else e.currentValue=a},e.inputUpdate]}}),t("span",{class:{"toggle-switch":!0,"toggle-switch-with-label":e.label}}),e.label?t("span",{staticClass:"label-description",domProps:{innerHTML:e._s(e.label)}}):e._e()]),e.description?t("p",{staticClass:"description",domProps:{innerHTML:e._s(e.description)}}):e._e(),e.is_constant_set?t("p",{staticClass:"description description--constant",domProps:{innerHTML:e._s(e.text_constant)}}):e._e()])])},Ft=[],zt={name:"SettingsInputSwitch",components:{SettingsInfoTooltip:Te},props:{name:String,value:Boolean,title:String,label:String,description:String,constant:String,tooltip:String,classname:String,disabled:Boolean},data(){return{has_error:!1,id:"input-"+this.name}},computed:{currentValue:{get(){return this.value},set(e){this.$emit("input",!!e)}},is_constant_set:function(){return this.$easywpsmtp.defined_constants.includes(this.constant)},text_constant:function(){return(0,l.__)("This setting is already configured with the Easy WP SMTP constant. To change it, please edit or remove the <code>"+this.constant+"</code> constant in your <code>wp-config.php</code> file.","easy-wp-smtp")}},methods:{inputUpdate:function(){if(this.disabled)return!1}}},Lt=zt,Ot=(0,d.A)(Lt,It,Ft,!1,null,null,null),Rt=Ot.exports,Wt={name:"WizardStepConfigureMailerSmtp",components:{SettingsInputText:Le,SettingsInputRadio:Pt,SettingsInputNumber:Tt,SettingsInputSwitch:Rt},data(){return{mailer:"smtp",text_host_label:(0,l.__)("SMTP Host","easy-wp-smtp"),text_encryption_label:(0,l.__)("Encryption","easy-wp-smtp"),text_port_label:(0,l.__)("SMTP Port","easy-wp-smtp"),text_autotls_title:(0,l.__)("Auto TLS","easy-wp-smtp"),text_autotls_label:(0,l.__)("Enable Auto TLS","easy-wp-smtp"),text_autotls_description:(0,l.__)("By default, TLS encryption is automatically used if the server supports it (recommended). In some cases, due to server misconfigurations, this can cause issues and may need to be disabled.","easy-wp-smtp"),text_auth_title:(0,l.__)("Authentication","easy-wp-smtp"),text_auth_label:(0,l.__)("Enable Authentication","easy-wp-smtp"),text_user_label:(0,l.__)("SMTP Username","easy-wp-smtp"),text_pass_label:(0,l.__)("SMTP Password","easy-wp-smtp"),text_from_name_label:(0,l.__)("From Name","easy-wp-smtp"),text_force_from_name_label:(0,l.__)("Force From Name","easy-wp-smtp"),text_from_email_label:(0,l.__)("From Email","easy-wp-smtp"),text_force_from_email_label:(0,l.__)("Force From Email","easy-wp-smtp"),text_from_name_description:(0,l.__)("The name that emails are sent from.","easy-wp-smtp"),text_from_email_description:(0,l.__)("The email address that emails are sent from.","easy-wp-smtp"),text_encryption_description:(0,l.__)("For most servers TLS is the recommended option. If your SMTP provider offers both SSL and TLS options, we recommend using TLS.","easy-wp-smtp"),text_force_from_name_description:(0,l.__)("If enabled, your specified From Name will be used for all outgoing emails, regardless of values set by other plugins.","easy-wp-smtp"),text_force_from_email_description:(0,l.__)("If enabled, your specified From Email Address will be used for all outgoing emails, regardless of values set by other plugins.","easy-wp-smtp"),description:this.$easywpsmtp.mailer_options.smtp.description,encryptionOptions:[{label:(0,l.__)("None","easy-wp-smtp"),value:"none",default_port:25},{label:(0,l.__)("SSL","easy-wp-smtp"),value:"ssl",default_port:465},{label:(0,l.__)("TLS","easy-wp-smtp"),value:"tls",default_port:587}],show_autotls:!0,show_user_and_pass:!0,field_errors:[]}},computed:{...(0,ne.YP)("$_settings",["settings.smtp.host","settings.smtp.auth","settings.smtp.port","settings.smtp.encryption","settings.smtp.user","settings.smtp.pass","settings.smtp.autotls","settings.mail.from_email","settings.mail.from_name","settings.mail.from_email_force","settings.mail.from_name_force"])},watch:{encryption:function(e){this.show_autotls="tls"!==e}},methods:{getEncryptionDefaultPort(e){return this.encryptionOptions.find((t=>t.value===e)).default_port},encryptionChanged(e){this.port=this.getEncryptionDefaultPort(e)},areRequiredFieldsValid(){return""===this.host&&this.field_errors.push("host"),(""===this.port||isNaN(this.port))&&this.field_errors.push("port"),this.auth&&(""===this.user&&this.field_errors.push("user"),""===this.pass&&this.field_errors.push("pass")),""===this.from_email&&this.field_errors.push("from_email"),0===this.field_errors.length},removeFieldError(e){this.field_errors=this.field_errors.filter((t=>t!==e))},errorDetected(e,t){this.field_errors.push(t)}},mounted(){"tls"===this.encryption&&(this.show_autotls=!1)}},Nt=Wt,Yt=(0,d.A)(Nt,gt,wt,!1,null,null,null),Dt=Yt.exports,Ut=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-configure-mailer-settings easy-wp-smtp-setup-wizard-step-configure-mailer-settings-sendlayer"},[t("div",{staticClass:"mailer-description",domProps:{innerHTML:e._s(e.description)}}),t("div",{staticClass:"mailer-description mailer-description-links"},[t("a",{staticClass:"easy-wp-smtp-button easy-wp-smtp-button-small easy-wp-smtp-button-secondary",attrs:{href:e.get_started_button_url,target:"_blank",rel:"noopener noreferrer"}},[t("span",{staticClass:"text-with-arrow text-with-arrow-right"},[e._v(" "+e._s(e.text_get_started_button)),t("inline-svg",{staticClass:"icon",attrs:{src:s(953),width:"16",height:"23"}})],1)]),t("a",{staticClass:"easy-wp-smtp-link easy-wp-smtp-link-docs",attrs:{href:e.documentation_link_url,target:"_blank",rel:"noopener noreferrer"}},[e._v(e._s(e.text_documentation_link))])]),t("div",{staticClass:"easy-wp-smtp-setup-wizard-form"},[t("settings-input-text",{attrs:{name:"api_key",type:"password",constant:"EASY_WP_SMTP_SENDLAYER_API_KEY",label:e.text_api_key_label,description:e.text_api_key_description,is_error:e.field_errors.includes("api_key")},on:{is_error_update:function(t){return e.removeFieldError("api_key")}},model:{value:e.api_key,callback:function(t){e.api_key=t},expression:"api_key"}}),t("settings-input-text",{attrs:{name:"from_name",constant:"EASY_WP_SMTP_MAIL_FROM_NAME",label:e.text_from_name_label,description:e.text_from_name_description},model:{value:e.from_name,callback:function(t){e.from_name=t},expression:"from_name"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_name_force",constant:"EASY_WP_SMTP_MAIL_FROM_NAME_FORCE",label:e.text_force_from_name_label,description:e.text_force_from_name_description},model:{value:e.from_name_force,callback:function(t){e.from_name_force=t},expression:"from_name_force"}}),t("settings-input-text",{attrs:{name:"from_email",type:"email",constant:"EASY_WP_SMTP_MAIL_FROM",label:e.text_from_email_label,description:e.text_from_email_description,is_error:e.field_errors.includes("from_email")},on:{is_error_update:function(t){return e.removeFieldError("from_email")},error_detected:t=>e.errorDetected(t,"from_email")},model:{value:e.from_email,callback:function(t){e.from_email=t},expression:"from_email"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_email_force",constant:"EASY_WP_SMTP_MAIL_FROM_FORCE",label:e.text_force_from_email_label,description:e.text_force_from_email_description},model:{value:e.from_email_force,callback:function(t){e.from_email_force=t},expression:"from_email_force"}})],1)])},Bt=[],Vt={name:"WizardStepConfigureMailerSendlayer",components:{SettingsInputText:Le,SettingsInputSwitch:Rt},data(){return{mailer:"sendlayer",text_api_key_label:(0,l.__)("API Key","easy-wp-smtp"),text_api_key_description:(0,l.nv)((0,l.__)("%1$sFollow this link%2$s to get an API Key for SendLayer.","easy-wp-smtp"),'<a href="'+this.$getUTMUrl("https://app.sendlayer.com/settings/api/",{source:"easywpsmtpplugin",medium:"WordPress",content:"Setup Wizard - Get API Key"})+'" target="_blank" rel="noopener noreferrer">',"</a>"),text_from_name_label:(0,l.__)("From Name","easy-wp-smtp"),text_force_from_name_label:(0,l.__)("Force From Name","easy-wp-smtp"),text_from_email_label:(0,l.__)("From Email","easy-wp-smtp"),text_force_from_email_label:(0,l.__)("Force From Email","easy-wp-smtp"),text_force_from_name_description:(0,l.__)("If enabled, your specified From Name will be used for all outgoing emails, regardless of values set by other plugins.","easy-wp-smtp"),text_force_from_email_description:(0,l.__)("If enabled, your specified From Email Address will be used for all outgoing emails, regardless of values set by other plugins.","easy-wp-smtp"),text_from_name_description:(0,l.__)("The name that emails are sent from.","easy-wp-smtp"),text_from_email_description:(0,l.__)("The email address that emails are sent from.","easy-wp-smtp"),text_get_started_button:(0,l.__)("Get Started with SendLayer","easy-wp-smtp"),text_documentation_link:(0,l.__)("Read how to set up SendLayer","easy-wp-smtp"),description:this.$easywpsmtp.mailer_options.sendlayer.description.substr(0,this.$easywpsmtp.mailer_options.sendlayer.description.lastIndexOf("<p>")),get_started_button_url:this.$getUTMUrl("https://sendlayer.com/easy-wp-smtp/",{source:"easywpsmtpplugin",medium:"WordPress",content:"Setup Wizard - Mailer Button"}),documentation_link_url:this.$getUTMUrl("https://easywpsmtp.com/docs/setting-up-the-sendlayer-mailer/",{content:"Read how to set up SendLayer"}),field_errors:[]}},computed:{...(0,ne.YP)("$_settings",["settings.sendlayer.api_key","settings.mail.from_email","settings.mail.from_name","settings.mail.from_email_force","settings.mail.from_name_force"])},methods:{areRequiredFieldsValid(){return""===this.api_key&&this.field_errors.push("api_key"),""===this.from_email&&this.field_errors.push("from_email"),0===this.field_errors.length},removeFieldError(e){this.field_errors=this.field_errors.filter((t=>t!==e))},errorDetected(e,t){this.field_errors.push(t)}}},Ht=Vt,Kt=(0,d.A)(Ht,Ut,Bt,!1,null,null,null),qt=Kt.exports,Gt=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-configure-mailer-settings easy-wp-smtp-setup-wizard-step-configure-mailer-settings-smtp2go"},[t("p",{staticClass:"mailer-description",domProps:{innerHTML:e._s(e.description)}}),t("p",{staticClass:"mailer-description mailer-description-links"},[t("a",{staticClass:"easy-wp-smtp-link easy-wp-smtp-link-docs",attrs:{href:e.documentation_link_url,target:"_blank",rel:"noopener noreferrer"}},[e._v(e._s(e.text_documentation_link))])]),t("div",{staticClass:"easy-wp-smtp-setup-wizard-form"},[t("settings-input-text",{attrs:{name:"api_key",type:"password",constant:"EASY_WP_SMTP_SMTP2GO_API_KEY",label:e.text_api_key_label,description:e.text_api_key_description,is_error:e.field_errors.includes("api_key")},on:{is_error_update:function(t){return e.removeFieldError("api_key")}},model:{value:e.api_key,callback:function(t){e.api_key=t},expression:"api_key"}}),t("settings-input-text",{attrs:{name:"from_name",constant:"EASY_WP_SMTP_MAIL_FROM_NAME",label:e.text_from_name_label,description:e.text_from_name_description},model:{value:e.from_name,callback:function(t){e.from_name=t},expression:"from_name"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_name_force",constant:"EASY_WP_SMTP_MAIL_FROM_NAME_FORCE",title:e.text_force_from_name_title,label:e.text_force_from_name_label},model:{value:e.from_name_force,callback:function(t){e.from_name_force=t},expression:"from_name_force"}}),t("settings-input-text",{attrs:{name:"from_email",type:"email",constant:"EASY_WP_SMTP_MAIL_FROM",label:e.text_from_email_label,description:e.text_from_email_description,is_error:e.field_errors.includes("from_email")},on:{is_error_update:function(t){return e.removeFieldError("from_email")},error_detected:t=>e.errorDetected(t,"from_email")},model:{value:e.from_email,callback:function(t){e.from_email=t},expression:"from_email"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_email_force",constant:"EASY_WP_SMTP_MAIL_FROM_FORCE",title:e.text_force_from_email_title,label:e.text_force_from_email_label},model:{value:e.from_email_force,callback:function(t){e.from_email_force=t},expression:"from_email_force"}})],1)])},jt=[],Zt={name:"WizardStepConfigureMailerSMTP2GO",components:{SettingsInputText:Le,SettingsInputSwitch:Rt},data(){return{mailer:"smtp2go",text_api_key_label:(0,l.__)("API Key","easy-wp-smtp"),text_api_key_description:(0,l.nv)((0,l.__)("Generate an API key on the Sending → API Keys page in your %1$scontrol panel%2$s.","easy-wp-smtp"),'<a href="https://app.smtp2go.com/" target="_blank" rel="noopener noreferrer">',"</a>"),text_from_name_label:(0,l.__)("From Name","easy-wp-smtp"),text_force_from_name_title:(0,l.__)("Force From Name","easy-wp-smtp"),text_from_email_label:(0,l.__)("From Email","easy-wp-smtp"),text_force_from_email_title:(0,l.__)("Force From Email","easy-wp-smtp"),text_force_from_name_label:(0,l.__)("If enabled, the From Name setting above will be used for all emails, ignoring values set by other plugins.","easy-wp-smtp"),text_force_from_email_label:(0,l.__)("If enabled, the From Email setting above will be used for all emails, ignoring values set by other plugins.","easy-wp-smtp"),text_from_name_description:(0,l.__)("The name that emails are sent from.","easy-wp-smtp"),text_from_email_description:(0,l.__)("The email address that emails are sent from.","easy-wp-smtp"),text_documentation_link:(0,l.__)("Read how to set up SMTP2GO","easy-wp-smtp"),description:this.$easywpsmtp.mailer_options.smtp2go.description.substr(0,this.$easywpsmtp.mailer_options.smtp2go.description.lastIndexOf("<br><br>")),documentation_link_url:this.$getUTMUrl("https://easywpsmtp.com/setting-up-the-smtp2go-mailer-in-easy-wp-smtp/",{content:"Read how to set up SMTP2GO"}),field_errors:[]}},computed:{...(0,ne.YP)("$_settings",["settings.smtp2go.api_key","settings.mail.from_email","settings.mail.from_name","settings.mail.from_email_force","settings.mail.from_name_force"])},methods:{areRequiredFieldsValid(){return""===this.api_key&&this.field_errors.push("api_key"),""===this.from_email&&this.field_errors.push("from_email"),0===this.field_errors.length},removeFieldError(e){this.field_errors=this.field_errors.filter((t=>t!==e))},errorDetected(e,t){this.field_errors.push(t)}}},Jt=Zt,Xt=(0,d.A)(Jt,Gt,jt,!1,null,null,null),Qt=Xt.exports,es=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-configure-mailer-settings easy-wp-smtp-setup-wizard-step-configure-mailer-settings-smtpcom"},[t("div",{staticClass:"mailer-description",domProps:{innerHTML:e._s(e.description)}}),t("div",{staticClass:"mailer-description mailer-description-links"},[t("a",{staticClass:"easy-wp-smtp-button easy-wp-smtp-button-small easy-wp-smtp-button-secondary",attrs:{href:"https://easywpsmtp.com/go/smtp/",target:"_blank",rel:"noopener noreferrer"}},[t("span",{staticClass:"text-with-arrow text-with-arrow-right"},[e._v(" "+e._s(e.text_get_started_button)),t("inline-svg",{staticClass:"icon",attrs:{src:s(953),width:"16",height:"23"}})],1)]),t("a",{staticClass:"easy-wp-smtp-link easy-wp-smtp-link-docs",attrs:{href:e.documentation_link_url,target:"_blank",rel:"noopener noreferrer"}},[e._v(e._s(e.text_documentation_link))]),t("span",{directives:[{name:"tooltip",rawName:"v-tooltip",value:e.disclosure_tooltip_data,expression:"disclosure_tooltip_data"}],staticClass:"mailer-offer-link-disclosure"},[e._v(e._s(e.text_disclosure))])]),t("div",{staticClass:"easy-wp-smtp-setup-wizard-form"},[t("settings-input-text",{attrs:{name:"api_key",type:"password",constant:"EASY_WP_SMTP_SMTPCOM_API_KEY",label:e.text_api_key_label,description:e.text_api_key_description,is_error:e.field_errors.includes("api_key")},on:{is_error_update:function(t){return e.removeFieldError("api_key")}},model:{value:e.api_key,callback:function(t){e.api_key=t},expression:"api_key"}}),t("settings-input-text",{attrs:{name:"channel",constant:"EASY_WP_SMTP_SMTPCOM_CHANNEL",label:e.text_channel_label,description:e.text_channel_description,is_error:e.field_errors.includes("channel")},on:{is_error_update:function(t){return e.removeFieldError("channel")}},model:{value:e.channel,callback:function(t){e.channel=t},expression:"channel"}}),t("settings-input-text",{attrs:{name:"from_name",constant:"EASY_WP_SMTP_MAIL_FROM_NAME",label:e.text_from_name_label,description:e.text_from_name_description},model:{value:e.from_name,callback:function(t){e.from_name=t},expression:"from_name"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_name_force",constant:"EASY_WP_SMTP_MAIL_FROM_NAME_FORCE",label:e.text_force_from_name_label,description:e.text_force_from_name_description},model:{value:e.from_name_force,callback:function(t){e.from_name_force=t},expression:"from_name_force"}}),t("settings-input-text",{attrs:{name:"from_email",type:"email",constant:"EASY_WP_SMTP_MAIL_FROM",label:e.text_from_email_label,description:e.text_from_email_description,is_error:e.field_errors.includes("from_email")},on:{is_error_update:function(t){return e.removeFieldError("from_email")},error_detected:t=>e.errorDetected(t,"from_email")},model:{value:e.from_email,callback:function(t){e.from_email=t},expression:"from_email"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_email_force",constant:"EASY_WP_SMTP_MAIL_FROM_FORCE",label:e.text_force_from_email_label,description:e.text_force_from_email_description},model:{value:e.from_email_force,callback:function(t){e.from_email_force=t},expression:"from_email_force"}})],1)])},ts=[],ss={name:"WizardStepConfigureMailerSmtpCom",components:{SettingsInputText:Le,SettingsInputSwitch:Rt},data(){return{mailer:"smtpcom",text_api_key_label:(0,l.__)("API Key","easy-wp-smtp"),text_channel_label:(0,l.__)("Sender Name","easy-wp-smtp"),text_api_key_description:(0,l.nv)((0,l.__)("%1$sFollow this link%2$s to get an API Key for SMTP.com.","easy-wp-smtp"),'<a href="https://my.smtp.com/settings/api" target="_blank" rel="noopener noreferrer">',"</a>"),text_channel_description:(0,l.nv)((0,l.__)("%1$sFollow this link%2$s to get a Sender Name for SMTP.com.","easy-wp-smtp"),'<a href="https://my.smtp.com/senders/" target="_blank" rel="noopener noreferrer">',"</a>"),text_from_name_label:(0,l.__)("From Name","easy-wp-smtp"),text_force_from_name_label:(0,l.__)("Force From Name","easy-wp-smtp"),text_from_email_label:(0,l.__)("From Email","easy-wp-smtp"),text_force_from_email_label:(0,l.__)("Force From Email","easy-wp-smtp"),text_force_from_name_description:(0,l.__)("If enabled, your specified From Name will be used for all outgoing emails, regardless of values set by other plugins.","easy-wp-smtp"),text_force_from_email_description:(0,l.__)("If enabled, your specified From Email Address will be used for all outgoing emails, regardless of values set by other plugins.","easy-wp-smtp"),text_from_name_description:(0,l.__)("The name that emails are sent from.","easy-wp-smtp"),text_from_email_description:(0,l.__)("The email address that emails are sent from.","easy-wp-smtp"),text_get_started_button:(0,l.__)("Get Started with SMTP.com","easy-wp-smtp"),text_documentation_link:(0,l.__)("Read how to set up SMTP.com","easy-wp-smtp"),text_disclosure:(0,l.__)("Transparency and Disclosure","easy-wp-smtp"),disclosure_tooltip_data:{content:(0,l.__)("We believe in full transparency. The SMTP.com links above are tracking links as part of our partnership with SMTP (j2 Global). We can recommend just about any SMTP service, but we only recommend products that we believe will add value to our users.","easy-wp-smtp"),autoHide:!0,trigger:"hover"},description:this.$easywpsmtp.mailer_options.smtpcom.description.substr(0,this.$easywpsmtp.mailer_options.smtpcom.description.lastIndexOf("<p>")),documentation_link_url:this.$getUTMUrl("https://easywpsmtp.com/docs/setting-up-the-smtp-com-mailer",{content:"Read how to set up SMTP.com"}),field_errors:[]}},computed:{...(0,ne.YP)("$_settings",["settings.smtpcom.api_key","settings.smtpcom.channel","settings.mail.from_email","settings.mail.from_name","settings.mail.from_email_force","settings.mail.from_name_force"])},methods:{areRequiredFieldsValid(){return""===this.api_key&&this.field_errors.push("api_key"),""===this.channel&&this.field_errors.push("channel"),""===this.from_email&&this.field_errors.push("from_email"),0===this.field_errors.length},removeFieldError(e){this.field_errors=this.field_errors.filter((t=>t!==e))},errorDetected(e,t){this.field_errors.push(t)}}},is=ss,as=(0,d.A)(is,es,ts,!1,null,null,null),ns=as.exports,rs=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-configure-mailer-settings easy-wp-smtp-setup-wizard-step-configure-mailer-settings-sendinblue"},[t("div",{staticClass:"mailer-description",domProps:{innerHTML:e._s(e.description)}}),t("div",{staticClass:"mailer-description mailer-description-links"},[t("a",{staticClass:"easy-wp-smtp-button easy-wp-smtp-button-small easy-wp-smtp-button-secondary",attrs:{href:"https://easywpsmtp.com/go/sendinblue/",target:"_blank",rel:"noopener noreferrer"}},[t("span",{staticClass:"text-with-arrow text-with-arrow-right"},[e._v(" "+e._s(e.text_get_started_button)),t("inline-svg",{staticClass:"icon",attrs:{src:s(953),width:"16",height:"23"}})],1)]),t("a",{staticClass:"easy-wp-smtp-link easy-wp-smtp-link-docs",attrs:{href:e.documentation_link_url,target:"_blank",rel:"noopener noreferrer"}},[e._v(e._s(e.text_documentation_link))]),t("span",{directives:[{name:"tooltip",rawName:"v-tooltip",value:e.disclosure_tooltip_data,expression:"disclosure_tooltip_data"}],staticClass:"mailer-offer-link-disclosure"},[e._v(e._s(e.text_disclosure))])]),t("div",{staticClass:"easy-wp-smtp-setup-wizard-form"},[t("settings-input-text",{attrs:{name:"api_key",type:"password",constant:"EASY_WP_SMTP_SENDINBLUE_API_KEY",label:e.text_api_key_label,description:e.text_api_key_description,is_error:e.field_errors.includes("api_key")},on:{is_error_update:function(t){return e.removeFieldError("api_key")}},model:{value:e.api_key,callback:function(t){e.api_key=t},expression:"api_key"}}),t("settings-input-text",{attrs:{name:"domain",constant:"EASY_WP_SMTP_SENDINBLUE_DOMAIN",label:e.text_domain_label,description:e.text_domain_description},model:{value:e.domain,callback:function(t){e.domain=t},expression:"domain"}}),t("settings-input-text",{attrs:{name:"from_name",constant:"EASY_WP_SMTP_MAIL_FROM_NAME",label:e.text_from_name_label,description:e.text_from_name_description},model:{value:e.from_name,callback:function(t){e.from_name=t},expression:"from_name"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_name_force",constant:"EASY_WP_SMTP_MAIL_FROM_NAME_FORCE",label:e.text_force_from_name_label,description:e.text_force_from_name_description},model:{value:e.from_name_force,callback:function(t){e.from_name_force=t},expression:"from_name_force"}}),t("settings-input-text",{attrs:{name:"from_email",type:"email",constant:"EASY_WP_SMTP_MAIL_FROM",label:e.text_from_email_label,description:e.text_from_email_description,is_error:e.field_errors.includes("from_email")},on:{is_error_update:function(t){return e.removeFieldError("from_email")},error_detected:t=>e.errorDetected(t,"from_email")},model:{value:e.from_email,callback:function(t){e.from_email=t},expression:"from_email"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_email_force",constant:"EASY_WP_SMTP_MAIL_FROM_FORCE",label:e.text_force_from_email_label,description:e.text_force_from_email_description},model:{value:e.from_email_force,callback:function(t){e.from_email_force=t},expression:"from_email_force"}})],1)])},os=[],ls={name:"WizardStepConfigureMailerSendinblue",components:{SettingsInputText:Le,SettingsInputSwitch:Rt},data(){return{mailer:"sendinblue",text_api_key_label:(0,l.__)("API Key","easy-wp-smtp"),text_domain_label:(0,l.__)("Sending Domain","easy-wp-smtp"),text_api_key_description:(0,l.nv)((0,l.__)("%1$sFollow this link%2$s to get an API Key for Brevo.","easy-wp-smtp"),'<a href="https://app.brevo.com/settings/keys/api" target="_blank" rel="noopener noreferrer">',"</a>"),text_domain_description:(0,l.nv)((0,l.__)("Please input the sending domain/subdomain you configured in your Brevo dashboard. More information can be found in our %1$sBrevo documentation%2$s","easy-wp-smtp"),'<a href="'+this.$getUTMUrl("https://easywpsmtp.com/docs/setting-up-the-sendinblue-mailer#setup-smtp",{content:"Brevo documentation"})+'" target="_blank" rel="noopener noreferrer">',"</a>"),text_from_name_label:(0,l.__)("From Name","easy-wp-smtp"),text_force_from_name_label:(0,l.__)("Force From Name","easy-wp-smtp"),text_from_email_label:(0,l.__)("From Email","easy-wp-smtp"),text_force_from_email_label:(0,l.__)("Force From Email","easy-wp-smtp"),text_force_from_name_description:(0,l.__)("If enabled, your specified From Name will be used for all outgoing emails, regardless of values set by other plugins.","easy-wp-smtp"),text_force_from_email_description:(0,l.__)("If enabled, your specified From Email Address will be used for all outgoing emails, regardless of values set by other plugins.","easy-wp-smtp"),text_from_name_description:(0,l.__)("The name that emails are sent from.","easy-wp-smtp"),text_from_email_description:(0,l.__)("The email address that emails are sent from.","easy-wp-smtp"),text_get_started_button:(0,l.__)("Get Started with Brevo","easy-wp-smtp"),text_documentation_link:(0,l.__)("Read how to set up Brevo","easy-wp-smtp"),text_disclosure:(0,l.__)("Transparency and Disclosure","easy-wp-smtp"),disclosure_tooltip_data:{content:(0,l.__)("We believe in full transparency. The Brevo links above are tracking links as part of our partnership with Brevo. We can recommend just about any SMTP service, but we only recommend products that we believe will add value to our users.","easy-wp-smtp"),autoHide:!0,trigger:"hover"},description:this.$easywpsmtp.mailer_options.sendinblue.description.substr(0,this.$easywpsmtp.mailer_options.sendinblue.description.lastIndexOf("<p>")),documentation_link_url:this.$getUTMUrl("https://easywpsmtp.com/docs/setting-up-the-sendinblue-mailer",{content:"Read how to set up Brevo"}),field_errors:[]}},computed:{...(0,ne.YP)("$_settings",["settings.sendinblue.api_key","settings.sendinblue.domain","settings.mail.from_email","settings.mail.from_name","settings.mail.from_email_force","settings.mail.from_name_force"])},methods:{areRequiredFieldsValid(){return""===this.api_key&&this.field_errors.push("api_key"),""===this.from_email&&this.field_errors.push("from_email"),0===this.field_errors.length},removeFieldError(e){this.field_errors=this.field_errors.filter((t=>t!==e))},errorDetected(e,t){this.field_errors.push(t)}}},_s=ls,ps=(0,d.A)(_s,rs,os,!1,null,null,null),ms=ps.exports,cs=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-configure-mailer-settings easy-wp-smtp-setup-wizard-step-configure-mailer-settings-mailersend"},[t("p",{staticClass:"mailer-description",domProps:{innerHTML:e._s(e.description)}}),t("p",{staticClass:"mailer-description mailer-description-links"},[t("a",{staticClass:"easy-wp-smtp-link",attrs:{href:e.documentation_link_url,target:"_blank",rel:"noopener noreferrer"}},[e._v(e._s(e.text_documentation_link))])]),t("div",{staticClass:"easy-wp-smtp-setup-wizard-form"},[t("settings-input-text",{attrs:{name:"api_key",type:"password",constant:"EASY_WP_SMTP_MAILERSEND_API_KEY",label:e.text_api_key_label,description:e.text_api_key_description,is_error:e.field_errors.includes("api_key")},on:{is_error_update:function(t){return e.removeFieldError("api_key")}},model:{value:e.api_key,callback:function(t){e.api_key=t},expression:"api_key"}}),t("settings-input-switch",{attrs:{name:"has_pro_plan",constant:"EASY_WP_SMTP_MAILERSEND_HAS_PRO_PLAN",title:e.text_pro_plan_title,label:e.text_pro_plan_label},model:{value:e.has_pro_plan,callback:function(t){e.has_pro_plan=t},expression:"has_pro_plan"}}),t("settings-input-text",{attrs:{name:"from_name",constant:"EASY_WP_SMTP_MAIL_FROM_NAME",label:e.text_from_name_label,description:e.text_from_name_description},model:{value:e.from_name,callback:function(t){e.from_name=t},expression:"from_name"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_name_force",constant:"EASY_WP_SMTP_MAIL_FROM_NAME_FORCE",title:e.text_force_from_name_title,label:e.text_force_from_name_label},model:{value:e.from_name_force,callback:function(t){e.from_name_force=t},expression:"from_name_force"}}),t("settings-input-text",{attrs:{name:"from_email",type:"email",constant:"EASY_WP_SMTP_MAIL_FROM",label:e.text_from_email_label,description:e.text_from_email_description,is_error:e.field_errors.includes("from_email")},on:{is_error_update:function(t){return e.removeFieldError("from_email")},error_detected:t=>e.errorDetected(t,"from_email")},model:{value:e.from_email,callback:function(t){e.from_email=t},expression:"from_email"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_email_force",constant:"EASY_WP_SMTP_MAIL_FROM_FORCE",title:e.text_force_from_email_title,label:e.text_force_from_email_label},model:{value:e.from_email_force,callback:function(t){e.from_email_force=t},expression:"from_email_force"}})],1)])},ds=[],us={name:"WizardStepConfigureMailerMailerSend",components:{SettingsInputText:Le,SettingsInputSwitch:Rt},data(){return{mailer:"mailersend",text_api_key_label:(0,l.__)("API Key","easy-wp-smtp"),text_pro_plan_title:(0,l.__)("Pro Plan Features","easy-wp-smtp"),text_api_key_description:(0,l.nv)((0,l.__)("%1$sFollow this link%2$s to get an API Key for MailerSend.","easy-wp-smtp"),'<a href="https://app.mailersend.com/api-tokens" target="_blank" rel="noopener noreferrer">',"</a>"),text_pro_plan_label:(0,l.__)("Enable if you have a Pro plan or higher to use advanced features like custom headers.","easy-wp-smtp"),text_from_name_label:(0,l.__)("From Name","easy-wp-smtp"),text_force_from_name_title:(0,l.__)("Force From Name","easy-wp-smtp"),text_from_email_label:(0,l.__)("From Email","easy-wp-smtp"),text_force_from_email_title:(0,l.__)("Force From Email","easy-wp-smtp"),text_force_from_name_label:(0,l.__)("If enabled, the From Name setting above will be used for all emails, ignoring values set by other plugins.","easy-wp-smtp"),text_force_from_email_label:(0,l.__)("If enabled, the From Email setting above will be used for all emails, ignoring values set by other plugins.","easy-wp-smtp"),text_from_name_description:(0,l.__)("The name that emails are sent from.","easy-wp-smtp"),text_from_email_description:(0,l.__)("The email address that emails are sent from.","easy-wp-smtp"),text_documentation_link:(0,l.__)("Read how to set up MailerSend","easy-wp-smtp"),description:this.$easywpsmtp.mailer_options.mailersend.description.substr(0,this.$easywpsmtp.mailer_options.mailersend.description.indexOf("<br>")),documentation_link_url:this.$getUTMUrl("https://easywpsmtp.com/docs/setting-up-the-mailersend-mailer/",{content:"Read how to set up MailerSend"}),field_errors:[]}},computed:{...(0,ne.YP)("$_settings",["settings.mailersend.api_key","settings.mailersend.has_pro_plan","settings.mail.from_email","settings.mail.from_name","settings.mail.from_email_force","settings.mail.from_name_force"])},methods:{areRequiredFieldsValid(){return""===this.api_key&&this.field_errors.push("api_key"),""===this.from_email&&this.field_errors.push("from_email"),0===this.field_errors.length},removeFieldError(e){this.field_errors=this.field_errors.filter((t=>t!==e))},errorDetected(e,t){this.field_errors.push(t)}}},fs=us,hs=(0,d.A)(fs,cs,ds,!1,null,null,null),ys=hs.exports,gs=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-configure-mailer-settings easy-wp-smtp-setup-wizard-step-configure-mailer-settings-mailgun"},[t("div",{staticClass:"mailer-description",domProps:{innerHTML:e._s(e.description)}}),t("div",{staticClass:"mailer-description mailer-description-links"},[t("a",{staticClass:"easy-wp-smtp-link",attrs:{href:e.documentation_link_url,target:"_blank",rel:"noopener noreferrer"}},[e._v(e._s(e.text_documentation_link))])]),t("div",{staticClass:"easy-wp-smtp-setup-wizard-form"},[t("settings-input-text",{attrs:{name:"api_key",type:"password",constant:"EASY_WP_SMTP_MAILGUN_API_KEY",label:e.text_api_key_label,description:e.text_api_key_description,is_error:e.field_errors.includes("api_key")},on:{is_error_update:function(t){return e.removeFieldError("api_key")}},model:{value:e.api_key,callback:function(t){e.api_key=t},expression:"api_key"}}),t("settings-input-text",{attrs:{name:"domain",constant:"EASY_WP_SMTP_MAILGUN_DOMAIN",label:e.text_domain_label,description:e.text_domain_description,is_error:e.field_errors.includes("domain")},on:{is_error_update:function(t){return e.removeFieldError("domain")}},model:{value:e.domain,callback:function(t){e.domain=t},expression:"domain"}}),t("settings-input-radio",{attrs:{name:"region",constant:"EASY_WP_SMTP_MAILGUN_REGION",label:e.text_region_label,options:e.regionOptions,description:e.text_region_description},model:{value:e.region,callback:function(t){e.region=t},expression:"region"}}),t("settings-input-text",{attrs:{name:"from_name",constant:"EASY_WP_SMTP_MAIL_FROM_NAME",label:e.text_from_name_label,description:e.text_from_name_description},model:{value:e.from_name,callback:function(t){e.from_name=t},expression:"from_name"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_name_force",constant:"EASY_WP_SMTP_MAIL_FROM_NAME_FORCE",label:e.text_force_from_name_label,description:e.text_force_from_name_description},model:{value:e.from_name_force,callback:function(t){e.from_name_force=t},expression:"from_name_force"}}),t("settings-input-text",{attrs:{name:"from_email",type:"email",constant:"EASY_WP_SMTP_MAIL_FROM",label:e.text_from_email_label,description:e.text_from_email_description,is_error:e.field_errors.includes("from_email")},on:{is_error_update:function(t){return e.removeFieldError("from_email")},error_detected:t=>e.errorDetected(t,"from_email")},model:{value:e.from_email,callback:function(t){e.from_email=t},expression:"from_email"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_email_force",constant:"EASY_WP_SMTP_MAIL_FROM_FORCE",label:e.text_force_from_email_label,description:e.text_force_from_email_description},model:{value:e.from_email_force,callback:function(t){e.from_email_force=t},expression:"from_email_force"}})],1)])},ws=[],bs={name:"WizardStepConfigureMailerMailgun",components:{SettingsInputText:Le,SettingsInputRadio:Pt,SettingsInputSwitch:Rt},data(){return{mailer:"mailgun",text_api_key_label:(0,l.__)("Mailgun API Key","easy-wp-smtp"),text_domain_label:(0,l.__)("Domain Name","easy-wp-smtp"),text_region_label:(0,l.__)("Region","easy-wp-smtp"),text_api_key_description:(0,l.nv)((0,l.__)('%1$sFollow this link%2$s to get a Mailgun API Key. Generate a key in the "Mailgun API Keys" section.',"easy-wp-smtp"),'<a href="https://app.mailgun.com/settings/api_security" target="_blank" rel="noopener noreferrer">',"</a>"),text_domain_description:(0,l.nv)((0,l.__)("%1$sFollow this link%2$s to get a Domain Name from Mailgun.","easy-wp-smtp"),'<a href="https://app.mailgun.com/mg/sending/domains" target="_blank" rel="noopener noreferrer">',"</a>"),text_region_description:(0,l.nv)((0,l.__)("Define which endpoint you want to use for sending messages. If you are operating under EU laws, you may be required to use EU region. %1$sMore information%2$s on Mailgun.com.","easy-wp-smtp"),'<a href="https://www.mailgun.com/regions" target="_blank" rel="noopener noreferrer">',"</a>"),text_from_name_label:(0,l.__)("From Name","easy-wp-smtp"),text_force_from_name_label:(0,l.__)("Force From Name","easy-wp-smtp"),text_from_email_label:(0,l.__)("From Email","easy-wp-smtp"),text_force_from_email_label:(0,l.__)("Force From Email","easy-wp-smtp"),text_force_from_name_description:(0,l.__)("If enabled, your specified From Name will be used for all outgoing emails, regardless of values set by other plugins.","easy-wp-smtp"),text_force_from_email_description:(0,l.__)("If enabled, your specified From Email Address will be used for all outgoing emails, regardless of values set by other plugins.","easy-wp-smtp"),text_from_name_description:(0,l.__)("The name that emails are sent from.","easy-wp-smtp"),text_from_email_description:(0,l.__)("The email address that emails are sent from.","easy-wp-smtp"),text_documentation_link:(0,l.__)("Read how to set up Mailgun","easy-wp-smtp"),description:this.$easywpsmtp.mailer_options.mailgun.description.substr(0,this.$easywpsmtp.mailer_options.mailgun.description.lastIndexOf("<p>")),documentation_link_url:this.$getUTMUrl("https://easywpsmtp.com/docs/setting-up-the-mailgun-mailer/",{content:"Read how to set up Mailgun"}),regionOptions:[{label:(0,l.__)("US","easy-wp-smtp"),value:"US"},{label:(0,l.__)("EU","easy-wp-smtp"),value:"EU"}],field_errors:[]}},computed:{...(0,ne.YP)("$_settings",["settings.mailgun.api_key","settings.mailgun.domain","settings.mailgun.region","settings.mail.from_email","settings.mail.from_name","settings.mail.from_email_force","settings.mail.from_name_force"])},methods:{areRequiredFieldsValid(){return""===this.api_key&&this.field_errors.push("api_key"),""===this.domain&&this.field_errors.push("domain"),""===this.from_email&&this.field_errors.push("from_email"),0===this.field_errors.length},removeFieldError(e){this.field_errors=this.field_errors.filter((t=>t!==e))},errorDetected(e,t){this.field_errors.push(t)}}},vs=bs,xs=(0,d.A)(vs,gs,ws,!1,null,null,null),ks=xs.exports,Ss=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-configure-mailer-settings easy-wp-smtp-setup-wizard-step-configure-mailer-settings-mailjet"},[t("p",{staticClass:"mailer-description",domProps:{innerHTML:e._s(e.description)}}),t("p",{staticClass:"mailer-description mailer-description-links"},[t("a",{staticClass:"easy-wp-smtp-link easy-wp-smtp-link-docs",attrs:{href:e.documentation_link_url,target:"_blank",rel:"noopener noreferrer"}},[e._v(e._s(e.text_documentation_link))])]),t("div",{staticClass:"easy-wp-smtp-setup-wizard-form"},[t("settings-input-text",{attrs:{name:"api_key",type:"password",constant:"EASY_WP_SMTP_MAILJET_API_KEY",label:e.text_api_key_label,description:e.text_api_key_description,is_error:e.field_errors.includes("api_key")},on:{is_error_update:function(t){return e.removeFieldError("api_key")}},model:{value:e.api_key,callback:function(t){e.api_key=t},expression:"api_key"}}),t("settings-input-text",{attrs:{name:"secret_key",type:"password",constant:"EASY_WP_SMTP_MAILJET_SECRET_KEY",label:e.text_secret_key_label,description:e.text_secret_key_description,is_error:e.field_errors.includes("secret_key")},on:{is_error_update:function(t){return e.removeFieldError("secret_key")}},model:{value:e.secret_key,callback:function(t){e.secret_key=t},expression:"secret_key"}}),t("settings-input-text",{attrs:{name:"from_name",constant:"EASY_WP_SMTP_MAIL_FROM_NAME",label:e.text_from_name_label,description:e.text_from_name_description},model:{value:e.from_name,callback:function(t){e.from_name=t},expression:"from_name"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_name_force",constant:"EASY_WP_SMTP_MAIL_FROM_NAME_FORCE",title:e.text_force_from_name_title,label:e.text_force_from_name_label},model:{value:e.from_name_force,callback:function(t){e.from_name_force=t},expression:"from_name_force"}}),t("settings-input-text",{attrs:{name:"from_email",type:"email",constant:"EASY_WP_SMTP_MAIL_FROM",label:e.text_from_email_label,description:e.text_from_email_description,is_error:e.field_errors.includes("from_email")},on:{is_error_update:function(t){return e.removeFieldError("from_email")},error_detected:t=>e.errorDetected(t,"from_email")},model:{value:e.from_email,callback:function(t){e.from_email=t},expression:"from_email"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_email_force",constant:"EASY_WP_SMTP_MAIL_FROM_FORCE",title:e.text_force_from_email_title,label:e.text_force_from_email_label},model:{value:e.from_email_force,callback:function(t){e.from_email_force=t},expression:"from_email_force"}})],1)])},Ps=[],As={name:"WizardStepConfigureMailerMailjet",components:{SettingsInputText:Le,SettingsInputSwitch:Rt},data(){return{mailer:"mailjet",text_api_key_label:(0,l.__)("API Key","easy-wp-smtp"),text_api_key_description:(0,l.nv)((0,l.__)("Follow this link to get the API key from Mailjet: %1$sAPI Key Management%2$s.","easy-wp-smtp"),'<a href="https://app.mailjet.com/account/apikeys" target="_blank" rel="noopener noreferrer">',"</a>"),text_secret_key_label:(0,l.__)("Secret Key","easy-wp-smtp"),text_secret_key_description:(0,l.nv)((0,l.__)("Follow this link to get the Secret key from Mailjet: %1$sAPI Key Management%2$s.","easy-wp-smtp"),'<a href="https://app.mailjet.com/account/apikeys" target="_blank" rel="noopener noreferrer">',"</a>"),text_from_name_label:(0,l.__)("From Name","easy-wp-smtp"),text_force_from_name_title:(0,l.__)("Force From Name","easy-wp-smtp"),text_from_email_label:(0,l.__)("From Email","easy-wp-smtp"),text_force_from_email_title:(0,l.__)("Force From Email","easy-wp-smtp"),text_force_from_name_label:(0,l.__)("If enabled, the From Name setting above will be used for all emails, ignoring values set by other plugins.","easy-wp-smtp"),text_force_from_email_label:(0,l.__)("If enabled, the From Email setting above will be used for all emails, ignoring values set by other plugins.","easy-wp-smtp"),text_from_name_description:(0,l.__)("The name that emails are sent from.","easy-wp-smtp"),text_from_email_description:(0,l.__)("The email address that emails are sent from.","easy-wp-smtp"),text_documentation_link:(0,l.__)("Read how to set up Mailjet","easy-wp-smtp"),description:this.$easywpsmtp.mailer_options.mailjet.description.substr(0,this.$easywpsmtp.mailer_options.mailjet.description.lastIndexOf("<br><br>")),documentation_link_url:this.$getUTMUrl("https://easywpsmtp.com/docs/setting-up-the-mailjet-mailer/",{content:"Read how to set up Mailjet"}),field_errors:[]}},computed:{...(0,ne.YP)("$_settings",["settings.mailjet.api_key","settings.mailjet.secret_key","settings.mail.from_email","settings.mail.from_name","settings.mail.from_email_force","settings.mail.from_name_force"])},methods:{areRequiredFieldsValid(){return""===this.api_key&&this.field_errors.push("api_key"),""===this.from_email&&this.field_errors.push("from_email"),0===this.field_errors.length},removeFieldError(e){this.field_errors=this.field_errors.filter((t=>t!==e))},errorDetected(e,t){this.field_errors.push(t)}}},Es=As,$s=(0,d.A)(Es,Ss,Ps,!1,null,null,null),Cs=$s.exports,Ms=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-configure-mailer-settings easy-wp-smtp-setup-wizard-step-configure-mailer-settings-sendgrid"},[t("p",{staticClass:"mailer-description",domProps:{innerHTML:e._s(e.description)}}),t("p",{staticClass:"mailer-description mailer-description-links"},[t("a",{staticClass:"easy-wp-smtp-link",attrs:{href:e.documentation_link_url,target:"_blank",rel:"noopener noreferrer"}},[e._v(e._s(e.text_documentation_link))])]),t("div",{staticClass:"easy-wp-smtp-setup-wizard-form"},[t("settings-input-text",{attrs:{name:"api_key",type:"password",constant:"EASY_WP_SMTP_SENDGRID_API_KEY",label:e.text_api_key_label,description:e.text_api_key_description,is_error:e.field_errors.includes("api_key")},on:{is_error_update:function(t){return e.removeFieldError("api_key")}},model:{value:e.api_key,callback:function(t){e.api_key=t},expression:"api_key"}}),t("settings-input-text",{attrs:{name:"domain",constant:"EASY_WP_SMTP_SENDGRID_DOMAIN",label:e.text_domain_label,description:e.text_domain_description},model:{value:e.domain,callback:function(t){e.domain=t},expression:"domain"}}),t("settings-input-text",{attrs:{name:"from_name",constant:"EASY_WP_SMTP_MAIL_FROM_NAME",label:e.text_from_name_label,description:e.text_from_name_description},model:{value:e.from_name,callback:function(t){e.from_name=t},expression:"from_name"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_name_force",constant:"EASY_WP_SMTP_MAIL_FROM_NAME_FORCE",label:e.text_force_from_name_label,description:e.text_force_from_name_description},model:{value:e.from_name_force,callback:function(t){e.from_name_force=t},expression:"from_name_force"}}),t("settings-input-text",{attrs:{name:"from_email",type:"email",constant:"EASY_WP_SMTP_MAIL_FROM",label:e.text_from_email_label,description:e.text_from_email_description,is_error:e.field_errors.includes("from_email")},on:{is_error_update:function(t){return e.removeFieldError("from_email")},error_detected:t=>e.errorDetected(t,"from_email")},model:{value:e.from_email,callback:function(t){e.from_email=t},expression:"from_email"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_email_force",constant:"EASY_WP_SMTP_MAIL_FROM_FORCE",label:e.text_force_from_email_label,description:e.text_force_from_email_description},model:{value:e.from_email_force,callback:function(t){e.from_email_force=t},expression:"from_email_force"}})],1)])},Ts=[],Is={name:"WizardStepConfigureMailerSendgrid",components:{SettingsInputText:Le,SettingsInputSwitch:Rt},data(){return{mailer:"sendgrid",text_api_key_label:(0,l.__)("API Key","easy-wp-smtp"),text_domain_label:(0,l.__)("Sending Domain","easy-wp-smtp"),text_api_key_description:(0,l.nv)((0,l.__)("%1$sFollow this link%2$s to get an API Key for SendGrid.","easy-wp-smtp"),'<a href="https://app.sendgrid.com/settings/api_keys" target="_blank" rel="noopener noreferrer">',"</a>")+"<br>"+(0,l.nv)((0,l.__)("To send emails you will need only a %1$sMail Send%2$s access level for this API key.","easy-wp-smtp"),"<i>","</i>"),text_domain_description:(0,l.nv)((0,l.__)("Please input the sending domain/subdomain you configured in your SendGrid dashboard. More information can be found in our %1$sSendGrid documentation%2$s","easy-wp-smtp"),'<a href="'+this.$getUTMUrl("https://easywpsmtp.com/docs/setting-up-the-sendgrid-mailer/#setup",{content:"SendGrid documentation"})+'" target="_blank" rel="noopener noreferrer">',"</a>"),text_from_name_label:(0,l.__)("From Name","easy-wp-smtp"),text_force_from_name_label:(0,l.__)("Force From Name","easy-wp-smtp"),text_from_email_label:(0,l.__)("From Email","easy-wp-smtp"),text_force_from_email_label:(0,l.__)("Force From Email","easy-wp-smtp"),text_force_from_name_description:(0,l.__)("If enabled, the From Name setting above will be used for all emails, ignoring values set by other plugins.","easy-wp-smtp"),text_force_from_email_description:(0,l.__)("If enabled, the From Email setting above will be used for all emails, ignoring values set by other plugins.","easy-wp-smtp"),text_from_name_description:(0,l.__)("The name that emails are sent from.","easy-wp-smtp"),text_from_email_description:(0,l.__)("The email address that emails are sent from.","easy-wp-smtp"),text_documentation_link:(0,l.__)("Read how to set up SendGrid","easy-wp-smtp"),description:this.$easywpsmtp.mailer_options.sendgrid.description.substr(0,this.$easywpsmtp.mailer_options.sendgrid.description.lastIndexOf("<p>")),documentation_link_url:this.$getUTMUrl("https://easywpsmtp.com/docs/setting-up-the-sendgrid-mailer/",{content:"Read how to set up Sendgrid"}),field_errors:[]}},computed:{...(0,ne.YP)("$_settings",["settings.sendgrid.api_key","settings.sendgrid.domain","settings.mail.from_email","settings.mail.from_name","settings.mail.from_email_force","settings.mail.from_name_force"])},methods:{areRequiredFieldsValid(){return""===this.api_key&&this.field_errors.push("api_key"),""===this.from_email&&this.field_errors.push("from_email"),0===this.field_errors.length},removeFieldError(e){this.field_errors=this.field_errors.filter((t=>t!==e))},errorDetected(e,t){this.field_errors.push(t)}}},Fs=Is,zs=(0,d.A)(Fs,Ms,Ts,!1,null,null,null),Ls=zs.exports,Os=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-configure-mailer-settings easy-wp-smtp-setup-wizard-step-configure-mailer-settings-postmark"},[t("p",{staticClass:"mailer-description",domProps:{innerHTML:e._s(e.description)}}),t("p",{staticClass:"mailer-description mailer-description-links"},[t("a",{staticClass:"easy-wp-smtp-link easy-wp-smtp-link-docs",attrs:{href:e.documentation_link_url,target:"_blank",rel:"noopener noreferrer"}},[e._v(e._s(e.text_documentation_link))])]),t("div",{staticClass:"easy-wp-smtp-setup-wizard-form"},[t("settings-input-text",{attrs:{name:"server_api_token",type:"password",constant:"EASY_WP_SMTP_POSTMARK_SERVER_API_TOKEN",label:e.text_server_api_token_label,description:e.text_server_api_token_description,is_error:e.field_errors.includes("server_api_token")},on:{is_error_update:function(t){return e.removeFieldError("server_api_token")}},model:{value:e.server_api_token,callback:function(t){e.server_api_token=t},expression:"server_api_token"}}),t("settings-input-text",{attrs:{name:"message_stream",constant:"EASY_WP_SMTP_POSTMARK_MESSAGE_STREAM",label:e.text_message_stream_label,description:e.text_message_stream_description},model:{value:e.message_stream,callback:function(t){e.message_stream=t},expression:"message_stream"}}),t("settings-input-text",{attrs:{name:"from_name",constant:"EASY_WP_SMTP_MAIL_FROM_NAME",label:e.text_from_name_label,description:e.text_from_name_description},model:{value:e.from_name,callback:function(t){e.from_name=t},expression:"from_name"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_name_force",constant:"EASY_WP_SMTP_MAIL_FROM_NAME_FORCE",label:e.text_force_from_name_label,description:e.text_force_from_name_description},model:{value:e.from_name_force,callback:function(t){e.from_name_force=t},expression:"from_name_force"}}),t("settings-input-text",{attrs:{name:"from_email",type:"email",constant:"EASY_WP_SMTP_MAIL_FROM",label:e.text_from_email_label,description:e.text_from_email_description,is_error:e.field_errors.includes("from_email")},on:{is_error_update:function(t){return e.removeFieldError("from_email")},error_detected:t=>e.errorDetected(t,"from_email")},model:{value:e.from_email,callback:function(t){e.from_email=t},expression:"from_email"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_email_force",constant:"EASY_WP_SMTP_MAIL_FROM_FORCE",label:e.text_force_from_email_lable,description:e.text_force_from_email_description},model:{value:e.from_email_force,callback:function(t){e.from_email_force=t},expression:"from_email_force"}})],1)])},Rs=[],Ws={name:"WizardStepConfigureMailerPostmark",components:{SettingsInputText:Le,SettingsInputSwitch:Rt},data(){return{mailer:"postmark",text_server_api_token_label:(0,l.__)("Server API Token","easy-wp-smtp"),text_message_stream_label:(0,l.__)("Message Stream ID","easy-wp-smtp"),text_server_api_token_description:(0,l.nv)((0,l.__)("%1$sFollow this link%2$s to get a Server API Token for Postmark.","easy-wp-smtp"),'<a href="https://account.postmarkapp.com/api_tokens" target="_blank" rel="noopener noreferrer">',"</a>"),text_message_stream_description:(0,l.nv)((0,l.__)("Message Stream ID is <strong>optional</strong>. By default <strong>outbound</strong> (Default Transactional Stream) will be used. More information can be found in our %1$sPostmark documentation%2$s.","easy-wp-smtp"),'<a href="'+this.$getUTMUrl("https://easywpsmtp.com/docs/setting-up-the-postmark-mailer/#message-stream",{content:"Postmark documentation"})+'" target="_blank" rel="noopener noreferrer">',"</a>"),text_from_name_label:(0,l.__)("From Name","easy-wp-smtp"),text_force_from_name_label:(0,l.__)("Force From Name","easy-wp-smtp"),text_from_email_label:(0,l.__)("From Email","easy-wp-smtp"),text_force_from_email_lable:(0,l.__)("Force From Email","easy-wp-smtp"),text_force_from_name_description:(0,l.__)("If enabled, the From Name setting above will be used for all emails, ignoring values set by other plugins.","easy-wp-smtp"),text_force_from_email_description:(0,l.__)("If enabled, the From Email setting above will be used for all emails, ignoring values set by other plugins.","easy-wp-smtp"),text_from_name_description:(0,l.__)("The name that emails are sent from.","easy-wp-smtp"),text_from_email_description:(0,l.__)("The email address that emails are sent from.","easy-wp-smtp"),text_documentation_link:(0,l.__)("Read how to set up Postmark","easy-wp-smtp"),description:this.$easywpsmtp.mailer_options.postmark.description.substr(0,this.$easywpsmtp.mailer_options.postmark.description.lastIndexOf("<p>")),documentation_link_url:this.$getUTMUrl("https://easywpsmtp.com/docs/setting-up-the-postmark-mailer/",{content:"Read how to set up Postmark"}),field_errors:[]}},computed:{...(0,ne.YP)("$_settings",["settings.postmark.server_api_token","settings.postmark.message_stream","settings.mail.from_email","settings.mail.from_name","settings.mail.from_email_force","settings.mail.from_name_force"])},methods:{areRequiredFieldsValid(){return""===this.server_api_token&&this.field_errors.push("server_api_token"),""===this.from_email&&this.field_errors.push("from_email"),0===this.field_errors.length},removeFieldError(e){this.field_errors=this.field_errors.filter((t=>t!==e))},errorDetected(e,t){this.field_errors.push(t)}}},Ns=Ws,Ys=(0,d.A)(Ns,Os,Rs,!1,null,null,null),Ds=Ys.exports,Us=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-configure-mailer-settings easy-wp-smtp-setup-wizard-step-configure-mailer-settings-sparkpost"},[t("p",{staticClass:"mailer-description",domProps:{innerHTML:e._s(e.description)}}),t("p",{staticClass:"mailer-description mailer-description-links"},[t("a",{staticClass:"easy-wp-smtp-link easy-wp-smtp-link-docs",attrs:{href:e.documentation_link_url,target:"_blank",rel:"noopener noreferrer"}},[e._v(e._s(e.text_documentation_link))])]),t("div",{staticClass:"easy-wp-smtp-setup-wizard-form"},[t("settings-input-text",{attrs:{name:"api_key",type:"password",constant:"EASY_WP_SMTP_SPARKPOST_API_KEY",label:e.text_api_key_label,description:e.text_api_key_description,is_error:e.field_errors.includes("api_key")},on:{is_error_update:function(t){return e.removeFieldError("api_key")}},model:{value:e.api_key,callback:function(t){e.api_key=t},expression:"api_key"}}),t("settings-input-radio",{attrs:{name:"region",constant:"EASY_WP_SMTP_SPARKPOST_REGION",label:e.text_region_label,options:e.regionOptions,description:e.text_region_description},model:{value:e.region,callback:function(t){e.region=t},expression:"region"}}),t("settings-input-text",{attrs:{name:"from_name",constant:"EASY_WP_SMTP_MAIL_FROM_NAME",label:e.text_from_name_label,description:e.text_from_name_description},model:{value:e.from_name,callback:function(t){e.from_name=t},expression:"from_name"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_name_force",constant:"EASY_WP_SMTP_MAIL_FROM_NAME_FORCE",label:e.text_force_from_name_label,description:e.text_force_from_name_description},model:{value:e.from_name_force,callback:function(t){e.from_name_force=t},expression:"from_name_force"}}),t("settings-input-text",{attrs:{name:"from_email",type:"email",constant:"EASY_WP_SMTP_MAIL_FROM",label:e.text_from_email_label,description:e.text_from_email_description,is_error:e.field_errors.includes("from_email")},on:{is_error_update:function(t){return e.removeFieldError("from_email")},error_detected:t=>e.errorDetected(t,"from_email")},model:{value:e.from_email,callback:function(t){e.from_email=t},expression:"from_email"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_email_force",constant:"EASY_WP_SMTP_MAIL_FROM_FORCE",label:e.text_force_from_email_label,description:e.text_force_from_email_description},model:{value:e.from_email_force,callback:function(t){e.from_email_force=t},expression:"from_email_force"}})],1)])},Bs=[],Vs={name:"WizardStepConfigureMailerSparkPost",components:{SettingsInputText:Le,SettingsInputRadio:Pt,SettingsInputSwitch:Rt},data(){return{mailer:"sparkpost",text_api_key_label:(0,l.__)("API Key","easy-wp-smtp"),text_region_label:(0,l.__)("Region","easy-wp-smtp"),text_region_description:(0,l.nv)((0,l.__)("Select your SparkPost account region. %1$sMore information%2$s on SparkPost.","easy-wp-smtp"),'<a href="https://www.sparkpost.com/docs/getting-started/getting-started-sparkpost" target="_blank" rel="noopener noreferrer">',"</a>"),text_from_name_label:(0,l.__)("From Name","easy-wp-smtp"),text_force_from_name_label:(0,l.__)("Force From Name","easy-wp-smtp"),text_from_email_label:(0,l.__)("From Email","easy-wp-smtp"),text_force_from_email_label:(0,l.__)("Force From Email","easy-wp-smtp"),text_force_from_name_description:(0,l.__)("If enabled, the From Name setting above will be used for all emails, ignoring values set by other plugins.","easy-wp-smtp"),text_force_from_email_description:(0,l.__)("If enabled, the From Email setting above will be used for all emails, ignoring values set by other plugins.","easy-wp-smtp"),text_from_name_description:(0,l.__)("The name that emails are sent from.","easy-wp-smtp"),text_from_email_description:(0,l.__)("The email address that emails are sent from.","easy-wp-smtp"),text_documentation_link:(0,l.__)("Read how to set up SparkPost","easy-wp-smtp"),description:this.$easywpsmtp.mailer_options.sparkpost.description.substr(0,this.$easywpsmtp.mailer_options.sparkpost.description.lastIndexOf("<p>")),documentation_link_url:this.$getUTMUrl("https://easywpsmtp.com/docs/setting-up-the-sparkpost-mailer/",{content:"Read how to set up SparkPost"}),regionOptions:[{label:(0,l.__)("US","easy-wp-smtp"),value:"US"},{label:(0,l.__)("EU","easy-wp-smtp"),value:"EU"}],field_errors:[]}},computed:{...(0,ne.YP)("$_settings",["settings.sparkpost.api_key","settings.sparkpost.region","settings.mail.from_email","settings.mail.from_name","settings.mail.from_email_force","settings.mail.from_name_force"]),text_api_key_description:function(){let e="EU"===this.region?"eu.":"";return(0,l.nv)((0,l.__)("%1$sFollow this link%2$s to get an API Key for SparkPost.","easy-wp-smtp"),'<a href="https://app.'+e+'sparkpost.com/account/api-keys" target="_blank" rel="noopener noreferrer">',"</a>")}},methods:{areRequiredFieldsValid(){return""===this.api_key&&this.field_errors.push("api_key"),""===this.from_email&&this.field_errors.push("from_email"),0===this.field_errors.length},removeFieldError(e){this.field_errors=this.field_errors.filter((t=>t!==e))},errorDetected(e,t){this.field_errors.push(t)}}},Hs=Vs,Ks=(0,d.A)(Hs,Us,Bs,!1,null,null,null),qs=Ks.exports,Gs=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-configure-mailer-settings easy-wp-smtp-setup-wizard-step-configure-mailer-settings-amazonses"},[t("div",{staticClass:"mailer-description",domProps:{innerHTML:e._s(e.description)}}),t("div",{staticClass:"mailer-description mailer-description-links"},[t("b",[t("a",{staticClass:"easy-wp-smtp-link",attrs:{href:e.documentation_link_url,target:"_blank",rel:"noopener noreferrer"}},[e._v(e._s(e.text_documentation_link))])])]),e.is_ssl?t("div",{staticClass:"easy-wp-smtp-setup-wizard-form"},[t("settings-input-text",{attrs:{name:"client_id",constant:"EASY_WP_SMTP_AMAZONSES_CLIENT_ID",label:e.text_client_id_label,is_error:e.field_errors.includes("client_id")},on:{is_error_update:function(t){return e.removeFieldError("client_id")}},model:{value:e.client_id,callback:function(t){e.client_id=t},expression:"client_id"}}),t("settings-input-text",{attrs:{name:"client_secret",type:"password",constant:"EASY_WP_SMTP_AMAZONSES_CLIENT_SECRET",label:e.text_client_secret_label,is_error:e.field_errors.includes("client_secret")},on:{is_error_update:function(t){return e.removeFieldError("client_secret")}},model:{value:e.client_secret,callback:function(t){e.client_secret=t},expression:"client_secret"}}),t("settings-input-select",{attrs:{name:"region",constant:"EASY_WP_SMTP_AMAZONSES_REGION",label:e.text_region_label,options:e.regionOptions,description:e.text_region_description,is_error:e.field_errors.includes("region")},on:{is_error_update:function(t){return e.removeFieldError("region")}},model:{value:e.region,callback:function(t){e.region=t},expression:"region"}}),e.is_api_auth_missing?e._e():[e.display_identities?t("div",[t("div",{staticClass:"easy-wp-smtp-separator easy-wp-smtp-separator-big-margin"}),t("settings-amazon-s-e-s-identities",{attrs:{options:e.identities,label:e.text_identities_label,columns:e.identities_columns}})],1):e._e(),t("div",{staticClass:"easy-wp-smtp-separator easy-wp-smtp-separator-big-margin"}),t("settings-input-text",{attrs:{name:"from_name",constant:"EASY_WP_SMTP_MAIL_FROM_NAME",label:e.text_from_name_label,description:e.text_from_name_description},model:{value:e.from_name,callback:function(t){e.from_name=t},expression:"from_name"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_name_force",constant:"EASY_WP_SMTP_MAIL_FROM_NAME_FORCE",label:e.text_force_from_name_label,description:e.text_force_from_name_description},model:{value:e.from_name_force,callback:function(t){e.from_name_force=t},expression:"from_name_force"}}),t("settings-input-text",{attrs:{name:"from_email",type:"email",constant:"EASY_WP_SMTP_MAIL_FROM",label:e.text_from_email_label,description:e.text_from_email_description,is_error:e.field_errors.includes("from_email")},on:{is_error_update:function(t){return e.removeFieldError("from_email")},error_detected:t=>e.errorDetected(t,"from_email")},model:{value:e.from_email,callback:function(t){e.from_email=t},expression:"from_email"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_email_force",constant:"EASY_WP_SMTP_MAIL_FROM_FORCE",label:e.text_force_from_email_label,description:e.text_force_from_email_description},model:{value:e.from_email_force,callback:function(t){e.from_email_force=t},expression:"from_email_force"}})]],2):t("div",{staticClass:"easy-wp-smtp-setup-wizard-form"},[t("div",{staticClass:"easy-wp-smtp-notice easy-wp-smtp-notice--error"},[t("p",[t("span",[e._v(e._s(e.text_no_ssl))]),e._v(" "),t("a",{attrs:{href:"https://www.wpbeginner.com/wp-tutorials/how-to-add-ssl-and-https-in-wordpress/",target:"_blank",rel:"noopener"}},[e._v(e._s(e.text_no_ssl_link_text))]),e._v(".")]),t("p",[e._v(e._s(e.text_no_ssl_diff_mailer))])])])])},js=[],Zs=s(181),Js=s.n(Zs),Xs=function(){var e=this,t=e._self._c;return t("div",{staticClass:"settings-input-select",class:{"settings-input-select-error":e.field_error}},[t("label",{staticClass:"settings-input-label-container",attrs:{for:`easy-wp-smtp-settings-select-${e.name}`}},[t("span",{staticClass:"label"},[e._v(e._s(e.label))])]),t("div",{staticClass:"settings-input-select-container"},[t("select",{directives:[{name:"model",rawName:"v-model",value:e.selected,expression:"selected"}],attrs:{id:`easy-wp-smtp-settings-select-${e.name}`,name:e.name,readonly:e.disabled,disabled:e.is_constant_set},on:{change:function(t){var s=Array.prototype.filter.call(t.target.options,(function(e){return e.selected})).map((function(e){var t="_value"in e?e._value:e.value;return t}));e.selected=t.target.multiple?s:s[0]}}},e._l(e.options,(function(s){return t("option",{key:s.value,domProps:{value:s.value}},[e._v(" "+e._s(s.label)+" ")])})),0)]),e.description?t("p",{staticClass:"description",domProps:{innerHTML:e._s(e.description)}}):e._e(),e.is_constant_set?t("p",{staticClass:"description description--constant",domProps:{innerHTML:e._s(e.text_constant)}}):e._e()])},Qs=[],ei={name:"SettingsInputSelect",props:{options:Array,label:String,name:String,value:String,description:String,constant:String,disabled:Boolean,is_error:Boolean},computed:{selected:{get(){return this.value},set(e){this.$emit("is_error_update",!1),this.$emit("input",e)}},field_error:{get(){return this.is_error},set(e){this.$emit("is_error_update",e)}},is_constant_set:function(){return this.$easywpsmtp.defined_constants.includes(this.constant)},text_constant:function(){return(0,l.__)("This setting is already configured with the Easy WP SMTP constant. To change it, please edit or remove the <code>"+this.constant+"</code> constant in your <code>wp-config.php</code> file.","easy-wp-smtp")}}},ti=ei,si=(0,d.A)(ti,Xs,Qs,!1,null,null,null),ii=si.exports,ai=function(){var e=this,t=e._self._c;return t("div",{staticClass:"settings-amazon-ses-identities"},[t("label",{staticClass:"settings-input-label-container"},[t("span",{staticClass:"label"},[e._v(e._s(e.label))]),e.tooltip?t("settings-info-tooltip",{attrs:{content:e.tooltip}}):e._e()],1),e.options?t("div",[e.options&&0!==e.options.length?t("p",{staticClass:"description"},[e._v(" "+e._s(e.text_identities_table_description)+" ")]):t("p",{staticClass:"description"},[t("strong",[e._v(e._s(e.text_no_registered_identities_title))]),e._v(" "+e._s(e.text_no_registered_identities_content)+" ")]),t("div",{staticClass:"ses-identities-container"},[e.options&&e.options.length>0?t("div",{staticClass:"ses-identities-table-container"},[t("table",[e.columns?t("tr",{staticClass:"ses-identity-columns"},e._l(e.filtered_columns,(function(s){return t("th",{key:s.key,class:`ses-identity-column ses-identity-column-${s.key}`},[e._v(" "+e._s(s.label)+" ")])})),0):e._e(),e._l(e.options,(function(s,i){return t("tr",{key:i},[t("td",[e._v(" "+e._s(s.value)+" ")]),t("td",[e._v(" "+e._s(s.type)+" ")]),t("td",[e._v(" "+e._s(s.status)+" ")])])})),e.show_identity_form?e._e():t("button",{staticClass:"easy-wp-smtp-button easy-wp-smtp-button-main easy-wp-smtp-button-small",attrs:{type:"button"},on:{click:function(t){return t.preventDefault(),e.addNewIdentity.apply(null,arguments)}}},[e._v(" "+e._s(e.text_add_new_identity)+" ")])],2)]):e._e(),e.show_identity_form||!e.options||0===e.options.length?t("div",{staticClass:"easy-wp-smtp-amazonses-identity-form"},[e.options&&0!==e.options.length?e._e():t("h3",[e._v(" "+e._s(e.text_verify_identity)+" ")]),t("div",{directives:[{name:"show",rawName:"v-show",value:1===e.verify_identity_step,expression:"verify_identity_step === 1"}],staticClass:"amazonses-identity-form-step"},[t("settings-input-radio",{attrs:{name:"identity_type",options:e.identity_type_options},model:{value:e.identity_type,callback:function(t){e.identity_type=t},expression:"identity_type"}}),t("p",{domProps:{textContent:e._s(e.verify_identity_text)}}),t("settings-input-text",{attrs:{name:"identity_value",placeholder:e.identity_value_placeholder},model:{value:e.identity_value,callback:function(t){e.identity_value=t},expression:"identity_value"}}),t("button",{staticClass:"easy-wp-smtp-button easy-wp-smtp-button-main easy-wp-smtp-button-small easy-wp-smtp-button-verify",attrs:{type:"button"},on:{click:function(t){return t.preventDefault(),e.verifyIdentity.apply(null,arguments)}}},[e.loading_verify_identity?t("spin-loader",{attrs:{color:"white"}}):t("span",[e._v(e._s(e.text_verify))])],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:2===e.verify_identity_step&&"domain"===e.verify_identity_result.type,expression:"verify_identity_step === 2 && verify_identity_result.type === 'domain'"}],staticClass:"amazonses-identity-form-step amazonses-identity-form-step-domain"},[t("p",{domProps:{innerHTML:e._s(e.text_verify_identity_step2_domain_text)}}),t("div",{staticClass:"amazonses-dns-records"},[t("div",{staticClass:"amazonses-dns-records__row amazonses-dns-records__row--heading"},[t("div",{staticClass:"amazonses-dns-records__col amazonses-dns-records__col--heading"},[e._v(" "+e._s(e.text_name)+" ")]),t("div",{staticClass:"amazonses-dns-records__col amazonses-dns-records__col--heading"},[e._v(" "+e._s(e.text_value)+" ")])]),e._l(e.verify_identity_result.domain_dkim_dns_records,(function(e,s){return t("div",{key:e.value,staticClass:"amazonses-dns-records__row amazonses-dns-records__row--record"},[t("div",{staticClass:"amazonses-dns-records__col amazonses-dns-records__col--record"},[t("settings-input-text",{attrs:{name:`dns_record_name[${s}]`,value:e.name,readonly:"",copy:""}})],1),t("div",{staticClass:"amazonses-dns-records__col amazonses-dns-records__col--record"},[t("settings-input-text",{attrs:{name:`dns_record_value[${s}]`,value:e.value,readonly:"",copy:""}})],1)])}))],2)]),t("div",{directives:[{name:"show",rawName:"v-show",value:2===e.verify_identity_step&&"email"===e.verify_identity_result.type,expression:"verify_identity_step === 2 && verify_identity_result.type === 'email'"}],staticClass:"amazonses-identity-form-step"},[t("p",{staticClass:"ses-identities-email-success-notice"},[t("inline-svg",{staticClass:"icon",attrs:{src:s(9318),width:"16",height:"16"}}),e._v(" "),t("span",{domProps:{innerHTML:e._s(e.text_verify_identity_step2_email_text)}})],1)])]):e._e()])]):t("spin-loader",{attrs:{size:"md"}})],1)},ni=[],ri=function(){var e=this,t=e._self._c;return t("img",{class:`easy-wp-smtp-loader easy-wp-smtp-loader-${e.size}`,attrs:{src:e.image,alt:e.text_loading}})},oi=[],li={name:"SpinLoader",props:{color:{type:String,default:""},size:{type:String,default:"sm"}},data(){return{image:s(3159)(`./loading${this.color.length?"-"+this.color:""}.svg`),text_loading:(0,l.__)("Loading","easy-wp-smtp")}}},_i=li,pi=(0,d.A)(_i,ri,oi,!1,null,null,null),mi=pi.exports,ci={name:"SettingsAmazonSESIdentities",components:{SettingsInfoTooltip:Te,SettingsInputRadio:Pt,SettingsInputText:Le,SpinLoader:mi},props:{options:Array,columns:Array,label:String,tooltip:String},computed:{filtered_columns:function(){return this.columns.filter((e=>"action"!==e.key))},identity_value_placeholder:function(){return"domain"===this.identity_type?(0,l.__)("Please enter a domain","easy-wp-smtp"):(0,l.__)("Please enter a valid email address","easy-wp-smtp")},verify_identity_text:function(){return"domain"===this.identity_type?(0,l.__)("Enter the domain name to verify it on Amazon SES and generate the required DNS CNAME records.","easy-wp-smtp"):(0,l.__)("Enter a valid email address. A verification email will be sent to the email address you entered.","easy-wp-smtp")},text_verify_identity_step2_email_text:function(){return(0,l.nv)((0,l.__)("Please check the inbox of <b>%s</b> for a confirmation email.","easy-wp-smtp"),this.verify_identity_result.value)},text_verify:function(){return"domain"===this.identity_type?(0,l.__)("Verify Domain","easy-wp-smtp"):(0,l.__)("Verify Email","easy-wp-smtp")}},data(){return{text_no_registered_identities_title:(0,l.__)("No registered domains or emails.","easy-wp-smtp"),text_no_registered_identities_content:(0,l.__)("You will not be able to send emails until you verify at least one domain or email address for the selected Amazon SES Region.","easy-wp-smtp"),text_view_dns:(0,l.__)("View DNS","easy-wp-smtp"),text_resend:(0,l.__)("Resend","easy-wp-smtp"),text_identities_table_description:(0,l.__)("Here are the domains and email addresses that have been verified and can be used as the From Email.","easy-wp-smtp"),text_verify_identity:(0,l.__)("Verify SES Identity","easy-wp-smtp"),text_add_new_identity:(0,l.__)("Add New SES Identity","easy-wp-smtp"),text_name:(0,l.__)("Name","easy-wp-smtp"),text_value:(0,l.__)("Value","easy-wp-smtp"),text_verify_identity_step2_domain_text:(0,l.nv)((0,l.__)("Please add these CNAME records to your domain's DNS settings. For information on how to add CNAME DNS records, please refer to the %1$sAmazon SES documentation%2$s.","easy-wp-smtp"),'<a href="https://docs.aws.amazon.com/ses/latest/dg/creating-identities.html#verify-domain-procedure" target="_blank" rel="noopener noreferrer">',"</a>"),show_identity_form:!1,identity_type:"domain",identity_type_options:[{label:(0,l.__)("Verify Domain","easy-wp-smtp"),value:"domain"},{label:(0,l.__)("Verify Email Address","easy-wp-smtp"),value:"email"}],identity_value:"",verify_identity_step:1,verify_identity_result:{},loading_verify_identity:!1}},methods:{verifyIdentity:function(){if(this.loading_verify_identity)return;this.loading_verify_identity=!0;const e=this;this.$store.dispatch("$_settings/amazonSESRegisterIdentity",{value:this.identity_value,type:this.identity_type}).then((function(t){e.loading_verify_identity=!1,t.success&&t.data&&(e.verify_identity_result=t.data,e.verify_identity_step=2)}))},addNewIdentity:function(){this.show_identity_form=!0}}},di=ci,ui=(0,d.A)(di,ai,ni,!1,null,null,null),fi=ui.exports,hi={name:"WizardStepConfigureMailerAmazonSES",components:{SettingsInputText:Le,SettingsInputSelect:ii,SettingsInputSwitch:Rt,SettingsAmazonSESIdentities:fi},data(){return{mailer:"amazonses",text_client_id_label:(0,l.__)("Access Key ID","easy-wp-smtp"),text_client_secret_label:(0,l.__)("Secret Access Key",{NODE_ENV:"production",VUE_APP_TEXTDOMAIN:"easy-wp-smtp",VUE_APP_PRODUCT_NAME:"EasyWPSMTP",BASE_URL:""}.VUE_APP_TEXTclient_id),text_region_label:(0,l.__)("Region","easy-wp-smtp"),text_identities_label:(0,l.__)("SES Identities","easy-wp-smtp"),text_region_description:(0,l.__)("Please select the Amazon SES API region which is the closest to where your website is hosted. This can help to decrease network latency between your site and Amazon SES, which will speed up email sending.","easy-wp-smtp"),text_from_name_label:(0,l.__)("From Name","easy-wp-smtp"),text_force_from_name_label:(0,l.__)("Force From Name","easy-wp-smtp"),text_from_email_label:(0,l.__)("From Email","easy-wp-smtp"),text_force_from_email_label:(0,l.__)("Force From Email","easy-wp-smtp"),text_force_from_name_description:(0,l.__)("If enabled, your specified From Name will be used for all outgoing emails, regardless of values set by other plugins.","easy-wp-smtp"),text_force_from_email_description:(0,l.__)("If enabled, your specified From Email Address will be used for all outgoing emails, regardless of values set by other plugins.","easy-wp-smtp"),text_from_name_description:(0,l.__)("The name that emails are sent from.","easy-wp-smtp"),text_from_email_description:(0,l.__)("The email address that emails are sent from.","easy-wp-smtp"),text_documentation_link:(0,l.__)("Read how to set up Amazon SES","easy-wp-smtp"),text_no_ssl:(0,l.__)("Amazon SES requires an SSL certificate, and so is not currently compatible with your site. Please contact your host to request a SSL certificate, or check out ","easy-wp-smtp"),text_no_ssl_link_text:(0,l.__)("WPBeginner's tutorial on how to set up SSL","easy-wp-smtp"),text_no_ssl_diff_mailer:(0,l.__)("If you'd prefer not to set up SSL, or need an SMTP solution in the meantime, please go back and select a different mailer option.","easy-wp-smtp"),description:this.$easywpsmtp.mailer_options.amazonses.description.substr(0,this.$easywpsmtp.mailer_options.amazonses.description.lastIndexOf("<p>")),documentation_link_url:this.$getUTMUrl("https://easywpsmtp.com/docs/setting-up-the-amazon-ses-mailer/",{content:"Read how to set up Amazon SES"}),regionOptions:this.$easywpsmtp.mailer_options.amazonses.region_options||[],fetching_identities:!1,is_ssl:this.$easywpsmtp.is_ssl,field_errors:[]}},computed:{...(0,ne.YP)("$_settings",["settings.amazonses.client_id","settings.amazonses.client_secret","settings.amazonses.region","settings.mail.from_email","settings.mail.from_name","settings.mail.from_email_force","settings.mail.from_name_force"]),...(0,ne.YP)("$_settings",{identities_columns:"amazonses_identities.columns",identities:"amazonses_identities.data",display_identities:"amazonses_display_identities"}),...(0,ne.YP)("$_wizard",["blocked_step"]),is_api_auth_missing:function(){return!this.client_id||!this.client_secret||!this.region}},watch:{client_id:function(){this.getIdentitiesDelayed()},client_secret:function(){this.getIdentitiesDelayed()},region:function(){this.getIdentities()}},methods:{getIdentities:function(){this.display_identities&&(this.fetching_identities||this.client_id.length<20||this.client_secret.length<40||!this.region||(this.fetching_identities=!0,this.$store.dispatch("$_app/start_loading"),this.$store.dispatch("$_settings/getAmazonSESIdentities").then((()=>{this.fetching_identities=!1})).finally((()=>{this.$store.dispatch("$_app/stop_loading")}))))},getIdentitiesDelayed:Js()((function(){this.getIdentities()}),500),areRequiredFieldsValid(){return""===this.client_id&&this.field_errors.push("client_id"),""===this.client_secret&&this.field_errors.push("client_secret"),""===this.region&&this.field_errors.push("region"),""===this.from_email&&this.field_errors.push("from_email"),0===this.field_errors.length},removeFieldError(e){this.field_errors=this.field_errors.filter((t=>t!==e))},errorDetected(e,t){this.field_errors.push(t)}},mounted(){this.getIdentities(),this.$easywpsmtp.is_ssl||(this.blocked_step=!0)}},yi=hi,gi=(0,d.A)(yi,Gs,js,!1,null,"08fd3607",null),wi=gi.exports,bi=function(){var e=this,t=e._self._c;return t("div",[e.license_verified||e.is_valid_license?e._e():t("div",{staticClass:"license-form",class:{"license-form-error":e.license_error}},[t("p",{domProps:{innerHTML:e._s(e.text_license_form)}}),t("div",{staticClass:"license-control"},[t("input",{directives:[{name:"model",rawName:"v-model",value:e.license,expression:"license"}],attrs:{name:"license",type:"password",placeholder:e.text_license_input_placeholder,"aria-label":e.text_aria_label_for_license_input},domProps:{value:e.license},on:{input:function(t){t.target.composing||(e.license=t.target.value)}}}),t("button",{staticClass:"easy-wp-smtp-button easy-wp-smtp-button-main easy-wp-smtp-button-small",attrs:{type:"button"},on:{click:function(t){return t.preventDefault(),e.handleLicenseSubmit.apply(null,arguments)}}},[e._v(" "+e._s(e.text_license_button)+" ")])]),e.license_error?t("p",{staticClass:"error-message",domProps:{textContent:e._s(e.text_license_error)}}):e._e()]),t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-configure-mailer-settings easy-wp-smtp-setup-wizard-step-configure-mailer-settings-gmail",class:{"easy-wp-smtp-setup-wizard-step-configure-mailer-settings--blocked":!e.license_verified&&!e.is_valid_license}},[t("p",{staticClass:"mailer-description",domProps:{innerHTML:e._s(e.description)}}),t("p",{staticClass:"mailer-description mailer-description-links"},[t("b",[t("a",{staticClass:"easy-wp-smtp-link",attrs:{href:e.documentation_link_url,target:"_blank",rel:"noopener noreferrer"}},[e._v(e._s(e.text_documentation_link))])])]),t("div",{staticClass:"easy-wp-smtp-setup-wizard-form"},[t("settings-o-auth-connection",{attrs:{label:e.text_authorization_label,mailer:e.mailer,connected_email:e.connected_email_address,is_auth_required:e.is_auth_required}}),t("div",{staticClass:"easy-wp-smtp-separator easy-wp-smtp-separator-big-margin"}),t("settings-input-text",{attrs:{name:"from_name",constant:"EASY_WP_SMTP_MAIL_FROM_NAME",label:e.text_from_name_label,description:e.text_from_name_description},model:{value:e.from_name,callback:function(t){e.from_name=t},expression:"from_name"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_name_force",constant:"EASY_WP_SMTP_MAIL_FROM_NAME_FORCE",label:e.text_force_from_name_label,description:e.text_force_from_name_description},model:{value:e.from_name_force,callback:function(t){e.from_name_force=t},expression:"from_name_force"}}),t("settings-input-text",{attrs:{name:"from_email",type:"email",constant:"EASY_WP_SMTP_MAIL_FROM",label:e.text_from_email_label,description:e.text_from_email_description,is_error:e.field_errors.includes("from_email")},on:{is_error_update:function(t){return e.removeFieldError("from_email")},error_detected:t=>e.errorDetected(t,"from_email")},model:{value:e.from_email,callback:function(t){e.from_email=t},expression:"from_email"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_email_force",constant:"EASY_WP_SMTP_MAIL_FROM_FORCE",label:e.text_force_from_email_label,description:e.text_force_from_email_description},model:{value:e.from_email_force,callback:function(t){e.from_email_force=t},expression:"from_email_force"}})],1)])])},vi=[],xi=function(){var e=this,t=e._self._c;return t("div",{staticClass:"settings-oauth-connection"},[t("label",{staticClass:"settings-input-label-container"},[t("span",{staticClass:"label"},[e._v(e._s(e.label))]),e.tooltip?t("settings-info-tooltip",{attrs:{content:e.tooltip}}):e._e()],1),e.is_auth_required?t("div",{staticClass:"add-authorization-container"},[t("p",{staticClass:"description",domProps:{textContent:e._s(e.text_authorization_button_description)}}),"gmail"===e.mailer?t("button",{staticClass:"easy-wp-smtp-one-click-sign-in-btn easy-wp-smtp-one-click-sign-in-btn__google",attrs:{type:"button",disabled:!e.are_client_details_ready},on:{click:function(t){return t.preventDefault(),e.authorize.apply(null,arguments)}}},[t("span",{staticClass:"easy-wp-smtp-one-click-sign-in-btn__icon"},[t("inline-svg",{staticClass:"icon",attrs:{src:s(3321),width:"46",height:"46"}})],1),t("span",{staticClass:"easy-wp-smtp-one-click-sign-in-btn__text"},[e._v(" "+e._s(e.text_google_authorization_button)+" ")])]):"outlook"===e.mailer&&e.outlook_one_click_setup_enabled?t("button",{staticClass:"easy-wp-smtp-one-click-sign-in-btn easy-wp-smtp-sign-in-btn__outlook",attrs:{type:"button",disabled:!e.are_client_details_ready||e.disabled},on:{click:function(t){return t.preventDefault(),e.authorize.apply(null,arguments)}}},[t("span",{staticClass:"easy-wp-smtp-one-click-sign-in-btn__icon"},[t("inline-svg",{staticClass:"icon",attrs:{src:s(6458),width:"46",height:"46"}})],1),t("span",{staticClass:"easy-wp-smtp-one-click-sign-in-btn__text"},[e._v(" "+e._s(e.text_outlook_authorization_button)+" ")])]):t("button",{staticClass:"easy-wp-smtp-button easy-wp-smtp-button-main easy-wp-smtp-button-small",attrs:{type:"button",disabled:!e.are_client_details_ready},on:{click:function(t){return t.preventDefault(),e.authorize.apply(null,arguments)}}},[e._v(" "+e._s(e.text_authorization_button)+" ")])]):t("div",{staticClass:"remove-authorization-container"},[e.connected_email?t("p",{staticClass:"description connected-as"},[t("span",{domProps:{innerHTML:e._s(e.text_connected_as_with_email)}}),e._v(" "),t("inline-svg",{staticClass:"icon",attrs:{src:s(5636),width:"16",height:"16"}})],1):e._e(),"gmail"===e.mailer?t("p",{staticClass:"description",domProps:{innerHTML:e._s(e.text_remove_authorization_button_description_google)}}):e._e(),t("p",{staticClass:"description",domProps:{innerHTML:e._s(e.text_remove_authorization_button_description)}}),t("button",{staticClass:"easy-wp-smtp-button easy-wp-smtp-button-red easy-wp-smtp-button-small",attrs:{type:"button"},on:{click:function(t){return t.preventDefault(),e.removeAuthorization.apply(null,arguments)}}},[e._v(" "+e._s(e.text_remove_authorization_button)+" ")])])])},ki=[],Si={name:"SettingsOAuthConnection",components:{SettingsInfoTooltip:Te},props:{label:String,mailer:String,connected_email:String,is_auth_required:Boolean,client_id:String,client_secret:String,tooltip:String,disabled:Boolean},data(){return{text_allow_button:(0,l.__)("Connect to %s","easy-wp-smtp"),text_google_authorization_button:(0,l.__)("Sign in with Google","easy-wp-smtp"),text_outlook_authorization_button:(0,l.__)("Sign in with Outlook","easy-wp-smtp"),text_authorization_button_description_general:(0,l.__)("Before continuing, you'll need to allow this plugin to send emails using your %s account.","easy-wp-smtp"),text_remove_authorization_button:(0,l.__)("Remove OAuth Connection","easy-wp-smtp"),text_remove_authorization_button_description_google:(0,l.nv)((0,l.__)("If you want to use a different From Email address you can setup a Google email alias. %1$sFollow these instructions%2$s, then input the alias address in the From Email section below.","easy-wp-smtp"),'<a href="'+this.$getUTMUrl("https://easywpsmtp.com/docs/sending-wordpress-emails-from-a-gmail-alias/",{content:"Gmail aliases description - Follow these instructions"})+'" target="_blank" rel="noopener noreferrer">',"</a>"),text_remove_authorization_button_desc_template:(0,l.__)("Removing this OAuth connection will give you the ability to redo the OAuth connection or connect to different %s account.","easy-wp-smtp"),text_connected_as:(0,l.__)("Connected as","easy-wp-smtp")}},computed:{...(0,Y.L8)({outlook_one_click_setup_enabled:"$_settings/outlook_one_click_setup_enabled"}),are_client_details_ready:function(){return"gmail"===this.mailer||(!("outlook"!==this.mailer||!this.outlook_one_click_setup_enabled)||!!this.client_id&&!!this.client_secret)},mailer_name:function(){let e="Google";return"outlook"===this.mailer?e="Microsoft Outlook":"zoho"===this.mailer&&(e="Zoho Mail"),e},text_authorization_button:function(){return(0,l.nv)(this.text_allow_button,this.mailer_name)},text_authorization_button_description:function(){return(0,l.nv)(this.text_authorization_button_description_general,this.mailer_name)},text_remove_authorization_button_description:function(){return(0,l.nv)(this.text_remove_authorization_button_desc_template,this.mailer_name)},text_connected_as_with_email:function(){return`${this.text_connected_as} <b>${this.connected_email}</b>`}},methods:{authorize:function(){this.$store.dispatch("$_app/start_loading"),this.$store.dispatch("$_settings/getAuthUrl",this.mailer).then((function(e){e.success&&e.data.oauth_url&&(window.location.href=e.data.oauth_url)})).finally((()=>{this.$store.dispatch("$_app/stop_loading")}))},removeAuthorization:function(){this.$store.dispatch("$_app/start_loading"),this.$store.dispatch("$_settings/removeAuth",this.mailer).finally((()=>{this.$store.dispatch("$_app/stop_loading")}))},removeUrlParam:function(e,t,s){t.delete(s),e.search=t.toString(),window.history.replaceState({},document.title,e.toString())},catchAuthNotice:function(){const e=new URL(window.location.href),t=new URLSearchParams(e.search);let s="",i="",a=!1;switch(t.has("success")?(s=t.get("success"),a=!0,this.removeUrlParam(e,t,"success")):t.has("error")&&(s=t.get("error"),this.removeUrlParam(e,t,"error")),s){case"oauth_invalid_state":i=(0,l.__)("There was an error while processing the authentication request. The state key is invalid. Please try again.","easy-wp-smtp");break;case"zoho_access_denied":i=(0,l.__)("There was an error while processing the authentication request. Please try again.","easy-wp-smtp");break;case"google_unsuccessful_oauth":i=(0,l.__)("There was an error while processing the authentication request.","easy-wp-smtp");break;case"microsoft_unsuccessful_oauth":case"zoho_no_clients":i=(0,l.__)("There was an error while processing the authentication request. Please recheck your Client ID and Client Secret and try again.","easy-wp-smtp");break;case"zoho_unsuccessful_oauth":i=(0,l.__)("There was an error while processing the authentication request. Please recheck your Region, Client ID and Client Secret and try again.","easy-wp-smtp");break;case"microsoft_invalid_nonce":case"zoho_invalid_nonce":i=(0,l.__)("There was an error while processing the authentication request. The nonce is invalid. Please try again.","easy-wp-smtp");break;case"microsoft_no_code":case"zoho_no_code":i=(0,l.__)("There was an error while processing the authentication request. The authorization code is missing. Please try again.","easy-wp-smtp");break;case"google_site_linked":i=(0,l.__)("You have successfully connected your site with your Gmail account. This site will now send emails via your Gmail account.","easy-wp-smtp");break;case"microsoft_site_linked":i=(0,l.__)("You have successfully linked the current site with your Microsoft API project. Now you can start sending emails through Outlook.","easy-wp-smtp");break;case"outlook_one_click_setup_site_linked":i=(0,l.__)("You have successfully connected your site with your Outlook account. Now you can start sending emails through Outlook.","easy-wp-smtp");break;case"zoho_site_linked":i=(0,l.__)("You have successfully linked the current site with your Zoho Mail API project. Now you can start sending emails through Zoho Mail.","easy-wp-smtp");break}i.length>0&&this.$swal({title:a?(0,l.__)("Successful Authorization","easy-wp-smtp"):(0,l.__)("Authorization Error!","easy-wp-smtp"),text:i,width:550,showCloseButton:!0,customClass:{container:"easy-wp-smtp-swal easy-wp-smtp-swal-alert"}})}},mounted(){this.catchAuthNotice()}},Pi=Si,Ai=(0,d.A)(Pi,xi,ki,!1,null,null,null),Ei=Ai.exports,$i={name:"WizardStepConfigureMailerGmail",components:{SettingsInputText:Le,SettingsInputSwitch:Rt,SettingsOAuthConnection:Ei},data(){return{mailer:"gmail",text_authorization_label:(0,l.__)("Authorization","easy-wp-smtp"),text_from_name_label:(0,l.__)("From Name","easy-wp-smtp"),text_force_from_name_label:(0,l.__)("Force From Name","easy-wp-smtp"),text_from_email_label:(0,l.__)("From Email","easy-wp-smtp"),text_force_from_name_description:(0,l.__)("If enabled, the From Name setting above will be used for all emails, ignoring values set by other plugins.","easy-wp-smtp"),text_from_name_description:(0,l.__)("The name that emails are sent from.","easy-wp-smtp"),text_from_email_description:(0,l.__)("The email address that emails are sent from.","easy-wp-smtp"),text_force_from_email_label:(0,l.__)("Force From Email","easy-wp-smtp"),text_force_from_email_description:(0,l.__)("If enabled, your specified From Email Address will be used for all outgoing emails, regardless of values set by other plugins.","easy-wp-smtp"),text_documentation_link:(0,l.__)("Read how to set up the Gmail mailer","easy-wp-smtp"),description:this.$easywpsmtp.mailer_options.gmail.description.substr(0,this.$easywpsmtp.mailer_options.gmail.description.lastIndexOf("<p>")),documentation_link_url:this.$getUTMUrl("https://easywpsmtp.com/docs/setting-up-the-gmail-mailer/#create-app",{content:"Read how to set up the Gmail mailer"}),redirect_uri:this.$easywpsmtp.mailer_options.gmail.redirect_uri,possible_send_as_emails:[],field_errors:[],license_verified:!1,license:"",license_error:!1,text_license_form:(0,l.__)("Gmail mailer requires a valid Easy WP SMTP Pro license. Please activate your license key.","easy-wp-smtp"),text_license_input_placeholder:(0,l.__)("Paste your license key here","easy-wp-smtp"),text_aria_label_for_license_input:(0,l.__)("License key input","easy-wp-smtp"),text_license_button:(0,l.__)("Verify License Key","easy-wp-smtp"),text_license_error:(0,l.__)("The License Key format is incorrect. Please enter a valid key and try again.","easy-wp-smtp")}},computed:{...(0,ne.YP)("$_settings",["settings.gmail.relay_credentials","settings.mail.from_email","settings.mail.from_name","settings.mail.from_email_force","settings.mail.from_name_force"]),...(0,ne.YP)("$_wizard",["blocked_step"]),...(0,Y.L8)({connected_email_address:"$_settings/gmail_email",is_valid_license:"$_settings/is_valid_license"}),is_auth_required:function(){return!this.relay_credentials?.key||!this.relay_credentials?.token}},watch:{is_auth_required:function(e){this.blocked_step=e}},methods:{areRequiredFieldsValid(){let e=!0;return""===this.from_email&&(e=!1,this.field_errors.push("from_email")),e},removeFieldError(e){this.field_errors=this.field_errors.filter((t=>t!==e))},handleLicenseSubmit(){if(this.license_error=!1,this.license.length<16)return this.license_error=!0,!1;this.$store.dispatch("$_app/start_loading"),this.$store.dispatch("$_settings/verifyLicense",this.license).then((e=>{e.success?(this.license_verified=!0,this.$swal({title:(0,l.__)("Successful Verification!","easy-wp-smtp"),html:(0,l.__)("Now you can continue mailer configuration.","easy-wp-smtp"),width:450,showCloseButton:!0,customClass:{container:"easy-wp-smtp-swal easy-wp-smtp-swal-alert"}})):this.$swal({title:(0,l.__)("Verification Error!","easy-wp-smtp"),html:e.data,width:450,showCloseButton:!0,customClass:{container:"easy-wp-smtp-swal easy-wp-smtp-swal-alert"}})})).finally((()=>{this.$store.dispatch("$_app/stop_loading")}))}},mounted(){this.is_auth_required&&(this.blocked_step=!0)}},Ci=$i,Mi=(0,d.A)(Ci,bi,vi,!1,null,null,null),Ti=Mi.exports,Ii=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-configure-mailer-settings easy-wp-smtp-setup-wizard-step-configure-mailer-settings-outlook"},[t("div",{staticClass:"mailer-description",domProps:{innerHTML:e._s(e.description)}}),t("div",{staticClass:"mailer-description mailer-description-links"},[t("a",{staticClass:"easy-wp-smtp-link",attrs:{href:e.documentation_link_url,target:"_blank",rel:"noopener noreferrer"}},[e._v(e._s(e.text_documentation_link))])]),e.is_ssl?t("div",{staticClass:"easy-wp-smtp-setup-wizard-form"},[t("div",{staticClass:"easy-wp-smtp-separator easy-wp-smtp-separator-big-margin"}),t("settings-input-switch",{attrs:{classname:"easy-wp-smtp-one-click-setup-switch",name:"one_click_setup_enabled",title:e.text_one_click_setup_title,label:e.one_click_setup_enabled?e.text_enabled:e.text_disabled,description:e.text_one_click_setup_description},model:{value:e.one_click_setup_enabled,callback:function(t){e.one_click_setup_enabled=t},expression:"one_click_setup_enabled"}}),e.one_click_setup_enabled?[t("div",{staticClass:"easy-wp-smtp-separator easy-wp-smtp-separator-big-margin"}),e.license_verified||e.is_valid_license?e._e():t("div",{staticClass:"license-form",class:{"license-form-error":e.license_error}},[t("p",{domProps:{innerHTML:e._s(e.text_license_form)}}),t("div",{staticClass:"license-control"},[t("input",{directives:[{name:"model",rawName:"v-model",value:e.license,expression:"license"}],attrs:{name:"license",type:"password",placeholder:e.text_license_input_placeholder,"aria-label":e.text_aria_label_for_license_input},domProps:{value:e.license},on:{input:function(t){t.target.composing||(e.license=t.target.value)}}}),t("button",{staticClass:"easy-wp-smtp-button easy-wp-smtp-button-main easy-wp-smtp-button-small",attrs:{type:"button"},on:{click:function(t){return t.preventDefault(),e.handleLicenseSubmit.apply(null,arguments)}}},[e._v(" "+e._s(e.text_license_button)+" ")])]),e.license_error?t("p",{staticClass:"error-message",domProps:{textContent:e._s(e.text_license_error)}}):e._e()]),t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-configure-mailer-settings easy-wp-smtp-setup-wizard-step-configure-mailer-settings-outlook",class:{"easy-wp-smtp-setup-wizard-step-configure-mailer-settings--blocked":!e.license_verified&&!e.is_valid_license}},[t("settings-o-auth-connection",{attrs:{hide_description:!0,mailer:e.mailer,connected_email:e.one_click_setup_connected_email_address,is_auth_required:e.is_auth_required}})],1)]:[t("div",{staticClass:"easy-wp-smtp-separator wp-mail-smtp-separator-big-margin"}),t("settings-input-text",{attrs:{name:"client_id",constant:"EASY_WP_SMTP_OUTLOOK_CLIENT_ID",label:e.text_client_id_label,is_error:e.field_errors.includes("client_id")},on:{is_error_update:function(t){return e.removeFieldError("client_id")}},model:{value:e.client_id,callback:function(t){e.client_id=t},expression:"client_id"}}),t("settings-input-text",{attrs:{name:"client_secret",type:"password",constant:"EASY_WP_SMTP_OUTLOOK_CLIENT_SECRET",label:e.text_client_secret_label,is_error:e.field_errors.includes("client_secret")},on:{is_error_update:function(t){return e.removeFieldError("client_secret")}},model:{value:e.client_secret,callback:function(t){e.client_secret=t},expression:"client_secret"}}),t("settings-input-text",{attrs:{value:e.redirect_uri,name:"redirect_uri",label:e.text_redirect_uri_label,copy:"",readonly:""}}),t("settings-o-auth-connection",{attrs:{label:e.text_authorization_label,mailer:e.mailer,connected_email:e.connected_email_address,is_auth_required:e.is_auth_required,client_id:e.client_id,client_secret:e.client_secret}})],e.is_auth_required?e._e():[t("div",{staticClass:"easy-wp-smtp-separator easy-wp-smtp-separator-big-margin"}),t("settings-input-text",{attrs:{name:"from_email",type:"email",constant:"EASY_WP_SMTP_MAIL_FROM",label:e.text_from_email_label,description:e.text_from_email_description,is_error:e.field_errors.includes("from_email")},on:{is_error_update:function(t){return e.removeFieldError("from_email")},error_detected:t=>e.errorDetected(t,"from_email")},model:{value:e.from_email,callback:function(t){e.from_email=t},expression:"from_email"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_email_force",constant:"EASY_WP_SMTP_MAIL_FROM_FORCE",label:e.text_force_from_email_label,description:e.text_force_from_email_description},model:{value:e.from_email_force,callback:function(t){e.from_email_force=t},expression:"from_email_force"}})]],2):t("div",{staticClass:"easy-wp-smtp-setup-wizard-form"},[t("div",{staticClass:"easy-wp-smtp-notice easy-wp-smtp-notice--error"},[t("p",[t("span",[e._v(e._s(e.text_no_ssl))]),e._v(" "),t("a",{attrs:{href:"https://www.wpbeginner.com/wp-tutorials/how-to-add-ssl-and-https-in-wordpress/",target:"_blank",rel:"noopener"}},[e._v(e._s(e.text_no_ssl_link_text))]),e._v(".")]),t("p",[e._v(e._s(e.text_no_ssl_diff_mailer))])])])])},Fi=[],zi={name:"WizardStepConfigureMailerOutlook",components:{SettingsInputText:Le,SettingsInputSwitch:Rt,SettingsOAuthConnection:Ei},data(){return{mailer:"outlook",text_one_click_setup_title:(0,l.__)("One-Click Setup","easy-wp-smtp"),text_one_click_setup_description:(0,l.__)("Provides a quick and easy way to connect to Outlook that doesn't require creating your own app.","easy-wp-smtp"),text_client_id_label:(0,l.__)("Application ID","easy-wp-smtp"),text_client_secret_label:(0,l.__)("Application Password","easy-wp-smtp"),text_redirect_uri_label:(0,l.__)("Redirect URI","easy-wp-smtp"),text_authorization_label:(0,l.__)("Authorization","easy-wp-smtp"),text_documentation_link:(0,l.__)("Read how to set up Microsoft Outlook / 365","easy-wp-smtp"),text_enabled:(0,l.__)("Enabled","easy-wp-smtp"),text_disabled:(0,l.__)("Disabled","easy-wp-smtp"),text_no_ssl:(0,l.__)("Outlook / 365 requires an SSL certificate, and so is not currently compatible with your site. Please contact your host to request a SSL certificate, or check out ","easy-wp-smtp"),text_no_ssl_link_text:(0,l.__)("WPBeginner's tutorial on how to set up SSL","easy-wp-smtp"),text_no_ssl_diff_mailer:(0,l.__)("If you'd prefer not to set up SSL, or need an SMTP solution in the meantime, please go back and select a different mailer option.","easy-wp-smtp"),text_from_email_label:(0,l.__)("From Email","easy-wp-smtp"),text_from_email_description:(0,l.__)("The email address that emails are sent from.","easy-wp-smtp"),text_force_from_email_label:(0,l.__)("Force From Email","easy-wp-smtp"),text_force_from_email_description:(0,l.__)("If enabled, the From Email setting above will be used for all emails, ignoring values set by other plugins.","easy-wp-smtp"),description:this.$easywpsmtp.mailer_options.outlook.description.substr(0,this.$easywpsmtp.mailer_options.outlook.description.lastIndexOf("<p>")),documentation_link_url:this.$getUTMUrl("https://easywpsmtp.com/docs/setting-up-the-outlook-mailer/#microsoft-setup",{content:"Read how to set up Microsoft Outlook / 365"}),redirect_uri:this.$easywpsmtp.mailer_options.outlook.redirect_uri,field_errors:[],is_ssl:this.$easywpsmtp.is_ssl,one_click_setup_enabled:!1,license:"",license_verified:!1,license_error:!1,text_license_form:(0,l.__)("One-Click Setup for Microsoft Outlook requires an active license. Verify your license to proceed with this One-Click Setup, please.","easy-wp-smtp"),text_license_input_placeholder:(0,l.__)("Paste your license key here","easy-wp-smtp"),text_aria_label_for_license_input:(0,l.__)("License key input","easy-wp-smtp"),text_license_button:(0,l.__)("Verify License Key","easy-wp-smtp"),text_license_error:(0,l.__)("The License Key format is incorrect. Please enter a valid key and try again.","easy-wp-smtp")}},computed:{...(0,ne.YP)("$_settings",["settings.outlook.client_id","settings.outlook.client_secret","settings.outlook.access_token","settings.outlook.refresh_token","settings.outlook.one_click_setup_credentials","settings.mail.from_email","settings.mail.from_email_force"]),...(0,ne.YP)("$_wizard",["blocked_step"]),...(0,Y.L8)({is_valid_license:"$_settings/is_valid_license",one_click_setup_enabled_setting:"$_settings/outlook_one_click_setup_enabled",connected_email_address:"$_settings/outlook_email",one_click_setup_connected_email_address:"$_settings/outlook_one_click_setup_email"}),is_auth_required:function(){return this.one_click_setup_enabled?!this.one_click_setup_credentials?.access_token||!this.one_click_setup_credentials?.refresh_token:!this.access_token||!this.refresh_token}},watch:{is_auth_required:function(e){this.blocked_step=e},one_click_setup_enabled:function(e){this.$store.dispatch("$_settings/setOutlookUseOneClickSetup",e)},one_click_setup_enabled_setting:function(e){this.one_click_setup_enabled=e}},methods:{areRequiredFieldsValid(){let e=!0;return""===this.from_email&&(e=!1,this.field_errors.push("from_email")),this.one_click_setup_enabled||(""===this.client_id&&(e=!1,this.field_errors.push("client_id")),""===this.client_secret&&(e=!1,this.field_errors.push("client_secret"))),e},removeFieldError(e){this.field_errors=this.field_errors.filter((t=>t!==e))},errorDetected(e,t){this.field_errors.push(t)},handleLicenseSubmit(){if(this.license_error=!1,this.license.length<16)return this.license_error=!0,!1;this.$store.dispatch("$_app/start_loading"),this.$store.dispatch("$_settings/verifyLicense",this.license).then((e=>{e.success?(this.license_verified=!0,this.$swal({title:(0,l.__)("Successful Verification!","easy-wp-smtp"),html:(0,l.__)("Now you can continue mailer configuration.","easy-wp-smtp"),width:450,showCloseButton:!0,customClass:{container:"easy-wp-smtp-swal easy-wp-smtp-swal-alert"}})):this.$swal({title:(0,l.__)("Verification Error!","easy-wp-smtp"),html:e.data,width:450,showCloseButton:!0,customClass:{container:"easy-wp-smtp-swal easy-wp-smtp-swal-alert"}})})).finally((()=>{this.$store.dispatch("$_app/stop_loading")}))}},mounted(){this.is_auth_required&&(this.blocked_step=!0),this.$easywpsmtp.is_ssl||(this.blocked_step=!0),this.one_click_setup_enabled=this.one_click_setup_enabled_setting}},Li=zi,Oi=(0,d.A)(Li,Ii,Fi,!1,null,"521c2de1",null),Ri=Oi.exports,Wi=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-configure-mailer-settings easy-wp-smtp-setup-wizard-step-configure-mailer-settings-zoho"},[t("p",{staticClass:"mailer-description",domProps:{innerHTML:e._s(e.description)}}),t("p",{staticClass:"mailer-description mailer-description-links"},[t("a",{staticClass:"easy-wp-smtp-link",attrs:{href:e.documentation_link_url,target:"_blank",rel:"noopener noreferrer"}},[e._v(e._s(e.text_documentation_link))])]),t("div",{staticClass:"easy-wp-smtp-setup-wizard-form"},[t("settings-input-select",{attrs:{name:"domain",constant:"EASY_WP_SMTP_ZOHO_DOMAIN",label:e.text_domain_label,options:e.domain_options,description:e.text_domain_description,is_error:e.field_errors.includes("domain")},on:{is_error_update:function(t){return e.removeFieldError("domain")}},model:{value:e.domain,callback:function(t){e.domain=t},expression:"domain"}}),t("settings-input-text",{attrs:{name:"client_id",constant:"EASY_WP_SMTP_ZOHO_CLIENT_ID",label:e.text_client_id_label,is_error:e.field_errors.includes("client_id")},on:{is_error_update:function(t){return e.removeFieldError("client_id")}},model:{value:e.client_id,callback:function(t){e.client_id=t},expression:"client_id"}}),t("settings-input-text",{attrs:{name:"client_secret",constant:"EASY_WP_SMTP_ZOHO_CLIENT_SECRET",type:"password",label:e.text_client_secret_label,is_error:e.field_errors.includes("client_secret")},on:{is_error_update:function(t){return e.removeFieldError("client_secret")}},model:{value:e.client_secret,callback:function(t){e.client_secret=t},expression:"client_secret"}}),t("settings-input-text",{attrs:{value:e.redirect_uri,name:"redirect_uri",label:e.text_redirect_uri_label,copy:"",readonly:""}}),t("settings-o-auth-connection",{attrs:{label:e.text_authorization_label,mailer:e.mailer,connected_email:e.connected_email_address,is_auth_required:e.is_auth_required,client_id:e.client_id,client_secret:e.client_secret}}),e.is_auth_required?e._e():[t("settings-input-text",{attrs:{name:"from_name",constant:"EASY_WP_SMTP_MAIL_FROM_NAME",label:e.text_from_name_label,description:e.text_from_name_description},model:{value:e.from_name,callback:function(t){e.from_name=t},expression:"from_name"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_name_force",constant:"EASY_WP_SMTP_MAIL_FROM_NAME_FORCE",label:e.text_force_from_name_label,description:e.text_force_from_name_description},model:{value:e.from_name_force,callback:function(t){e.from_name_force=t},expression:"from_name_force"}})]],2)])},Ni=[],Yi={name:"WizardStepConfigureMailerZoho",components:{SettingsInputText:Le,SettingsInputSwitch:Rt,SettingsInputSelect:ii,SettingsOAuthConnection:Ei},data(){return{mailer:"zoho",text_domain_label:(0,l.__)("Region","easy-wp-smtp"),text_domain_description:(0,l.__)("The data center location used by your Zoho account.","easy-wp-smtp"),text_client_id_label:(0,l.__)("Client ID","easy-wp-smtp"),text_client_secret_label:(0,l.__)("Client Secret","easy-wp-smtp"),text_redirect_uri_label:(0,l.__)("Redirect URI","easy-wp-smtp"),text_authorization_label:(0,l.__)("Authorization","easy-wp-smtp"),text_from_name_label:(0,l.__)("From Name","easy-wp-smtp"),text_force_from_name_label:(0,l.__)("Force From Name","easy-wp-smtp"),text_force_from_name_description:(0,l.__)("If enabled, the From Name setting above will be used for all emails, ignoring values set by other plugins.","easy-wp-smtp"),text_from_name_description:(0,l.__)("The name that emails are sent from.","easy-wp-smtp"),text_documentation_link:(0,l.__)("Read how to set up Zoho Mail","easy-wp-smtp"),description:this.$easywpsmtp.mailer_options.zoho.description.substr(0,this.$easywpsmtp.mailer_options.zoho.description.lastIndexOf("<p>")),documentation_link_url:this.$getUTMUrl("https://easywpsmtp.com/docs/setting-up-the-zoho-mailer/#zoho-account",{content:"Read how to set up Zoho Mail"}),redirect_uri:this.$easywpsmtp.mailer_options.zoho.redirect_uri,domain_options:this.$easywpsmtp.mailer_options.zoho.domain_options,field_errors:[]}},computed:{...(0,ne.YP)("$_settings",["settings.zoho.domain","settings.zoho.client_id","settings.zoho.client_secret","settings.zoho.access_token","settings.zoho.refresh_token","settings.mail.from_name","settings.mail.from_name_force"]),...(0,ne.YP)("$_wizard",["blocked_step"]),...(0,Y.L8)({connected_email_address:"$_settings/zoho_email"}),is_auth_required:function(){return!this.access_token||!this.refresh_token}},watch:{is_auth_required:function(e){this.blocked_step=e}},methods:{areRequiredFieldsValid(){let e=!0;return""===this.domain&&(e=!1,this.field_errors.push("domain")),""===this.client_id&&(e=!1,this.field_errors.push("client_id")),""===this.client_secret&&(e=!1,this.field_errors.push("client_secret")),e},removeFieldError(e){this.field_errors=this.field_errors.filter((t=>t!==e))}},mounted(){this.is_auth_required&&(this.blocked_step=!0)}},Di=Yi,Ui=(0,d.A)(Di,Wi,Ni,!1,null,null,null),Bi=Ui.exports,Vi=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-configure-mailer-settings easy-wp-smtp-setup-wizard-step-configure-mailer-settings-elasticemail"},[t("div",{staticClass:"mailer-description",domProps:{innerHTML:e._s(e.description)}}),t("div",{staticClass:"mailer-description mailer-description-links"},[t("a",{staticClass:"easy-wp-smtp-link easy-wp-smtp-link-docs",attrs:{href:e.documentation_link_url,target:"_blank",rel:"noopener noreferrer"}},[e._v(e._s(e.text_documentation_link))])]),t("div",{staticClass:"easy-wp-smtp-setup-wizard-form"},[t("settings-input-text",{attrs:{name:"api_key",type:"password",constant:"EASY_WP_SMTP_ELASTICEMAIL_API_KEY",label:e.text_api_key_label,description:e.text_api_key_description,is_error:e.field_errors.includes("api_key")},on:{is_error_update:function(t){return e.removeFieldError("api_key")}},model:{value:e.api_key,callback:function(t){e.api_key=t},expression:"api_key"}}),t("settings-input-text",{attrs:{name:"from_name",constant:"EASY_WP_SMTP_MAIL_FROM_NAME",label:e.text_from_name_label,description:e.text_from_name_description},model:{value:e.from_name,callback:function(t){e.from_name=t},expression:"from_name"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_name_force",constant:"EASY_WP_SMTP_MAIL_FROM_NAME_FORCE",title:e.text_force_from_name_title,label:e.text_force_from_name_label},model:{value:e.from_name_force,callback:function(t){e.from_name_force=t},expression:"from_name_force"}}),t("settings-input-text",{attrs:{name:"from_email",type:"email",constant:"EASY_WP_SMTP_MAIL_FROM",label:e.text_from_email_label,description:e.text_from_email_description,is_error:e.field_errors.includes("from_email")},on:{is_error_update:function(t){return e.removeFieldError("from_email")},error_detected:t=>e.errorDetected(t,"from_email")},model:{value:e.from_email,callback:function(t){e.from_email=t},expression:"from_email"}}),t("settings-input-switch",{attrs:{classname:"sub_setting",name:"from_email_force",constant:"EASY_WP_SMTP_MAIL_FROM_FORCE",title:e.text_force_from_email_title,label:e.text_force_from_email_label},model:{value:e.from_email_force,callback:function(t){e.from_email_force=t},expression:"from_email_force"}})],1)])},Hi=[],Ki={name:"WizardStepConfigureMailerElasticEmail",components:{SettingsInputText:Le,SettingsInputSwitch:Rt},data(){return{mailer:"elasticemail",text_api_key_label:(0,l.__)("API Key","easy-wp-smtp"),text_api_key_description:(0,l.nv)((0,l.__)("%1$sFollow this link%2$s to get an API Key for Elastic Email.","easy-wp-smtp"),'<a href="https://app.elasticemail.com/marketing/settings/new/manage-api" target="_blank" rel="noopener noreferrer">',"</a>"),text_from_name_label:(0,l.__)("From Name","easy-wp-smtp"),text_force_from_name_title:(0,l.__)("Force From Name","easy-wp-smtp"),text_from_email_label:(0,l.__)("From Email","easy-wp-smtp"),text_force_from_email_title:(0,l.__)("Force From Email","easy-wp-smtp"),text_force_from_name_label:(0,l.__)("If enabled, the From Name setting above will be used for all emails, ignoring values set by other plugins.","easy-wp-smtp"),text_force_from_email_label:(0,l.__)("If enabled, the From Email setting above will be used for all emails, ignoring values set by other plugins.","easy-wp-smtp"),text_from_name_description:(0,l.__)("The name that emails are sent from.","easy-wp-smtp"),text_from_email_description:(0,l.__)("The email address that emails are sent from.","easy-wp-smtp"),text_documentation_link:(0,l.__)("Read how to set up Elastic Email","easy-wp-smtp"),description:this.$easywpsmtp.mailer_options.elasticemail.description.substr(0,this.$easywpsmtp.mailer_options.elasticemail.description.lastIndexOf("<br><br>")),documentation_link_url:this.$getUTMUrl("https://easywpsmtp.com/docs/setting-up-the-elastic-email-mailer/",{content:"Read how to set up Elastic Email"}),field_errors:[]}},computed:{...(0,ne.YP)("$_settings",["settings.elasticemail.api_key","settings.mail.from_email","settings.mail.from_name","settings.mail.from_email_force","settings.mail.from_name_force"])},methods:{areRequiredFieldsValid(){return""===this.api_key&&this.field_errors.push("api_key"),""===this.from_email&&this.field_errors.push("from_email"),0===this.field_errors.length},removeFieldError(e){this.field_errors=this.field_errors.filter((t=>t!==e))},errorDetected(e,t){this.field_errors.push(t)}}},qi=Ki,Gi=(0,d.A)(qi,Vi,Hi,!1,null,null,null),ji=Gi.exports,Zi=function(){var e=this,t=e._self._c;return t("div",{staticClass:"easy-wp-smtp-setup-wizard-step easy-wp-smtp-setup-wizard-step-configure-email-logs"},[t("div",{staticClass:"easy-wp-smtp-setup-wizard-content-container"},[t("div",{staticClass:"easy-wp-smtp-configure-email-logs-header"},[t("the-wizard-step-counter"),t("content-header",{attrs:{title:e.text_header_title,subtitle:e.text_header_subtitle}})],1),t("div",{staticClass:"easy-wp-smtp-plugin-configure-email-logs"},[t("settings-input-long-checkbox",{attrs:{name:"log_email_content",label:e.text_log_email_content,description:e.text_log_email_content_desc},model:{value:e.log_email_content,callback:function(t){e.log_email_content=t},expression:"log_email_content"}}),t("settings-input-long-checkbox",{attrs:{name:"save_attachments",label:e.text_save_attachments,description:e.text_save_attachments_desc},model:{value:e.save_attachments,callback:function(t){e.save_attachments=t},expression:"save_attachments"}}),t("settings-input-long-checkbox",{attrs:{name:"open_email_tracking",label:e.text_open_email_tracking,description:e.text_open_email_tracking_desc},model:{value:e.open_email_tracking,callback:function(t){e.open_email_tracking=t},expression:"open_email_tracking"}}),t("settings-input-long-checkbox",{attrs:{name:"click_link_tracking",label:e.text_click_link_tracking,description:e.text_click_link_tracking_desc},model:{value:e.click_link_tracking,callback:function(t){e.click_link_tracking=t},expression:"click_link_tracking"}})],1)]),t("div",{staticClass:"easy-wp-smtp-separator easy-wp-smtp-separator-no-margin"}),t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-footer"},[t("a",{attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),e.previousStep.apply(null,arguments)}}},[t("span",{staticClass:"text-with-arrow text-with-arrow-left"},[t("inline-svg",{staticClass:"icon",attrs:{src:s(9004),width:"16",height:"22"}}),e._v(e._s(e.text_previous_step)+" ")],1)]),t("div",{staticClass:"easy-wp-smtp-setup-wizard-step-footer-buttons"},[t("button",{staticClass:"easy-wp-smtp-button easy-wp-smtp-button-main",attrs:{type:"submit",name:"next_step"},on:{click:function(t){return t.preventDefault(),e.handleSubmit.apply(null,arguments)}}},[t("span",{staticClass:"text-with-arrow text-with-arrow-right"},[e._v(" "+e._s(e.text_save)+" "),t("inline-svg",{staticClass:"icon",attrs:{src:s(953),width:"16",height:"19"}})],1)])])])])},Ji=[],Xi={name:"WizardStepConfigureEmailLogs",components:{ContentHeader:f,TheWizardStepCounter:X,SettingsInputLongCheckbox:ye},data(){return{text_header_title:(0,l.__)("Configure Email Logs","easy-wp-smtp"),text_header_subtitle:(0,l.__)("You’ve chosen to enable email logging. Please select which additional email logging features you would like to use.","easy-wp-smtp"),text_save:(0,l.__)("Save and Continue","easy-wp-smtp"),text_previous_step:(0,l.__)("Previous Step","easy-wp-smtp"),text_log_email_content:(0,l.__)("Store the content for all sent emails","easy-wp-smtp"),text_log_email_content_desc:(0,l.__)("Email content may include sensitive information, such as plain text passwords. For security purposes, consider carefully whether to enable this option. All email content will be stored in your site's database. To resend emails from our Email Log, this option must be enabled.","easy-wp-smtp"),text_save_attachments:(0,l.__)("Save file attachments sent from WordPress","easy-wp-smtp"),text_save_attachments_desc:(0,l.__)("When enabled, all sent attachments will be saved to your WordPress Uploads folder. For sites that send a high volume of unique large attachments, this option could result in a disk space issue.","easy-wp-smtp"),text_open_email_tracking:(0,l.__)("Track when an email is opened","easy-wp-smtp"),text_open_email_tracking_desc:(0,l.__)("When enabled, the email log will note whether or not an email has been opened.","easy-wp-smtp"),text_click_link_tracking:(0,l.__)("Track when a link in an email is clicked","easy-wp-smtp"),text_click_link_tracking_desc:(0,l.__)("When enabled, the email log will note whether or not a link has been clicked in the specified email.","easy-wp-smtp")}},computed:{...(0,ne.YP)("$_settings",["settings.logs.log_email_content","settings.logs.save_attachments","settings.logs.open_email_tracking","settings.logs.click_link_tracking"])},methods:{handleSubmit(){this.$store.dispatch("$_app/start_loading");let e={value:{logs:{log_email_content:this.log_email_content,save_attachments:this.save_attachments,open_email_tracking:this.open_email_tracking,click_link_tracking:this.click_link_tracking}}};this.$store.dispatch("$_settings/updateSettings",e).then((e=>{e.success?this.nextStep():this.$easywpsmtp_error_toast({})})).finally((()=>{this.$store.dispatch("$_app/stop_loading")}))},previousStep(){this.$previous_step()},nextStep(){this.$next_step()}}},Qi=Xi,ea=(0,d.A)(Qi,Zi,Ji,!1,null,null,null),ta=ea.exports,sa=new n.Ay({routes:[{path:"*",redirect:"/"},{path:"/",name:"welcome",component:P},{path:"/step",name:"step",component:R,children:[{path:"choose_mailer",name:"choose_mailer_step",component:se},{path:"configure_mailer",name:"configure_mailer_step",component:_e,children:[{path:"smtp",name:"configure_mailer_step_smtp",component:Dt},{path:"sendlayer",name:"configure_mailer_step_sendlayer",component:qt},{path:"smtpcom",name:"configure_mailer_step_smtpcom",component:ns},{path:"sendinblue",name:"configure_mailer_step_sendinblue",component:ms},{path:"mailersend",name:"configure_mailer_step_mailersend",component:ys},{path:"mailgun",name:"configure_mailer_step_mailgun",component:ks},{path:"mailjet",name:"configure_mailer_step_mailjet",component:Cs},{path:"sendgrid",name:"configure_mailer_step_sendgrid",component:Ls},{path:"postmark",name:"configure_mailer_step_postmark",component:Ds},{path:"sparkpost",name:"configure_mailer_step_sparkpost",component:qs},{path:"amazoneses",name:"configure_mailer_step_amazonses",component:wi},{path:"gmail",name:"configure_mailer_step_gmail",component:Ti},{path:"outlook",name:"configure_mailer_step_outlook",component:Ri},{path:"smtp2go",name:"configure_mailer_step_smtp2go",component:Qt},{path:"zoho",name:"configure_mailer_step_zoho",component:Bi},{path:"elasticemail",name:"configure_mailer_step_elasticemail",component:ji}]},{path:"plugin_features",name:"plugin_features_step",component:ve},{path:"configure_email_logs",name:"configure_email_logs_step",component:ta},{path:"help_improve",name:"help_improve_step",component:He},{path:"license",name:"license_step",component:Qe},{path:"check_configuration",name:"check_configuration_step",component:nt},{path:"successful_configuration",name:"check_configuration_step_success",component:mt},{path:"failed_configuration",name:"check_configuration_step_failure",component:yt}]}],scrollBehavior(){return{x:0,y:0}}}),ia={name:"SetupWizardApp",router:sa,computed:{...(0,Y.L8)({blocked:"$_app/blocked",loading:"$_app/loading"})}},aa=ia,na=(0,d.A)(aa,i,a,!1,null,null,null),ra=na.exports,oa=s(5471),la=s(1823);const _a={install(e){window.easy_wp_smtp_vue&&(e.prototype.$easywpsmtp=window.easy_wp_smtp_vue),e.prototype.$isPro=pa,e.prototype.$addQueryArg=ma,e.prototype.$getUTMUrl=ca}};function pa(){return window.easy_wp_smtp_vue.is_pro}function ma(e,t,s){var i=new RegExp("([?&])"+t+"=.*?(&|#|$)","i");if(e.match(i))return e.replace(i,"$1"+t+"="+s+"$2");var a="";-1!==e.indexOf("#")&&(a=e.replace(/.*#/,"#"),e=e.replace(/#.*/,""));var n=-1!==e.indexOf("?")?"&":"?";return e+n+t+"="+s+a}function ca(e,t){t={source:"WordPress",medium:"setup-wizard",campaign:pa()?"plugin":"liteplugin",content:"general",...t};for(const[s,i]of Object.entries(t))e=ma(e,`utm_${s}`,encodeURIComponent(i));return e}var da=_a;const ua={install(e){e.prototype.$next_step=function(t=0){const s=e.prototype.$wizard_steps.findIndex((e=>this.$route.name.includes(e)))+1+t;this.$router.push({name:e.prototype.$wizard_steps[s]})},e.prototype.$previous_step=function(t=0){let s="welcome";const i=e.prototype.$wizard_steps.findIndex((e=>this.$route.name.includes(e)))-1-t;i>=0&&(s=e.prototype.$wizard_steps[i]),this.$router.push({name:s})},e.prototype.$swal&&(e.prototype.$easywpsmtp_success_toast=function(t){let{animation:s=!1,toast:i=!0,position:a="top-end",showConfirmButton:n=!1,icon:r="success",timer:o=3e3,showCloseButton:_=!0,title:p=(0,l.__)("Settings Updated","easy-wp-smtp"),showCancelButton:m=!1,confirmButtonText:c="",cancelButtonText:d="",text:u=""}=t;return e.prototype.$swal({animation:s,toast:i,position:a,showConfirmButton:n,icon:r,showCloseButton:_,title:p,timer:o,showCancelButton:m,confirmButtonText:c,cancelButtonText:d,text:u})},e.prototype.$easywpsmtp_error_toast=function(t){let{animation:s=!1,toast:i=!0,position:a="top-end",showConfirmButton:n=!1,icon:r="error",showCloseButton:o=!0,title:_=(0,l.__)("Could Not Save Changes","easy-wp-smtp"),text:p=""}=t;return e.prototype.$swal({animation:s,toast:i,position:a,showConfirmButton:n,icon:r,showCloseButton:o,title:_,text:p,onOpen:function(){e.prototype.$swal.hideLoading()}})},e.prototype.$easywpsmtp_error_modal=function(t){let{position:s="center",width:i=650,showConfirmButton:a=!0,confirmButtonText:n=(0,l.__)("Return to Mailer Settings","easy-wp-smtp"),customClass:r={container:"easy-wp-smtp-swal easy-wp-smtp-swal-error"},showCloseButton:o=!0,title:_=(0,l.__)("Whoops, we found an issue!","easy-wp-smtp"),subtitle:p=(0,l.__)("It looks like something went wrong...","easy-wp-smtp"),detailedError:m=""}=t;return e.prototype.$swal({position:s,width:i,showConfirmButton:a,confirmButtonText:n,customClass:r,showCloseButton:o,title:_,html:`\n\t\t\t\t\t\t<p class="subtitle">${p}</p>\n\t\t\t\t\t\t<div class="detailed-error">\n\t\t\t\t\t\t\t<h3>${(0,l.__)("Error Message:","easy-wp-smtp")}</h3>\n\t\t\t\t\t\t\t<div>${m}</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t`,allowEscapeKey:!1,allowOutsideClick:!1,onOpen:function(){e.prototype.$swal.hideLoading()}})},e.prototype.$required_fields_modal=function(){return e.prototype.$swal({position:"center",width:450,showConfirmButton:!0,confirmButtonText:(0,l.__)("OK","easy-wp-smtp"),customClass:{container:"easy-wp-smtp-swal easy-wp-smtp-swal-alert"},showCloseButton:!0,title:(0,l.__)("Heads up!","easy-wp-smtp"),text:(0,l.__)("Please fill out all the required fields to continue.","easy-wp-smtp"),allowEscapeKey:!1,allowOutsideClick:!1})})}};var fa=ua,ha=s(4335);const ya=function(e,t){return new Promise(((s,i)=>{let a=new FormData;a.append("action","easy_wp_smtp_vue_install_plugin"),a.append("nonce",oa.Ay.prototype.$easywpsmtp.nonce),a.append("slug",t),ha.A.post(oa.Ay.prototype.$easywpsmtp.ajax_url,a).then((t=>{if(t.data.success)e.commit("PLUGIN_INSTALLED",t.data);else{let e="";je()(t.data,"data[0].message")?e=t.data.data[0].message:je()(t.data,"data")&&(e=t.data.data),oa.Ay.prototype.$easywpsmtp_error_modal({subtitle:(0,l.__)("It looks like the plugin installation failed!","easy-wp-smtp"),detailedError:e})}s(t.data)})).catch((function(e){if(i(e),e.response){const t=e.response;return oa.Ay.prototype.$easywpsmtp_error_modal({subtitle:(0,l.__)("It looks like we can't install the plugin.","easy-wp-smtp"),detailedError:(0,l.nv)((0,l.__)("%1$s, %2$s","easy-wp-smtp"),t.status,t.statusText)})}oa.Ay.prototype.$easywpsmtp_error_toast({title:(0,l.__)("You appear to be offline. Plugin not installed.","easy-wp-smtp")})}))}))},ga=function(e){return new Promise(((t,s)=>{let i=new FormData;i.append("action","easy_wp_smtp_vue_get_partner_plugins_info"),i.append("nonce",oa.Ay.prototype.$easywpsmtp.nonce),ha.A.post(oa.Ay.prototype.$easywpsmtp.ajax_url,i).then((s=>{s.data.success?e.commit("PLUGINS_FETCHED",s.data):oa.Ay.prototype.$easywpsmtp_error_toast({title:(0,l.__)("Can't fetch plugins information.","easy-wp-smtp")}),t(s.data)})).catch((function(e){if(s(e),e.response){const t=e.response;return oa.Ay.prototype.$easywpsmtp_error_modal({subtitle:(0,l.__)("It looks like we can't fetch plugins information.","easy-wp-smtp"),detailedError:(0,l.nv)((0,l.__)("%1$s, %2$s","easy-wp-smtp"),t.status,t.statusText)})}oa.Ay.prototype.$easywpsmtp_error_toast({title:(0,l.__)("You appear to be offline. Plugin information not retrieved.","easy-wp-smtp")})}))}))};var wa={installPlugin:ya,fetchPlugins:ga};const ba=function(e,t){return wa.installPlugin(e,t)},va=function(e){return wa.fetchPlugins(e)};var xa={installPlugin:ba,getPlugins:va};const ka=e=>e.plugins,Sa=e=>e.plugins.filter((e=>"wpforms-lite"!==e.slug)),Pa=e=>e.contact_form_plugin_already_installed;var Aa={getField:ne.VI,plugins:ka,partner_plugins:Sa,contact_form_plugin_already_installed:Pa};const Ea=(e,t)=>{e.plugins.map((s=>(s.slug===t.data.slug&&(s.is_installed=t.data.is_installed,s.is_activated=t.data.is_activated),"wpforms-lite"===t.data.slug&&(e.contact_form_plugin_already_installed=!0),s)))},$a=(e,t)=>{e.plugins=t.data.plugins,e.contact_form_plugin_already_installed=t.data.contact_form_plugin_already_installed};var Ca={updateField:ne.cP,PLUGIN_INSTALLED:Ea,PLUGINS_FETCHED:$a};const Ma={plugins:[],contact_form_plugin_already_installed:!1,smart_contact_form_setting:!0};var Ta={namespaced:!0,state:Ma,actions:xa,getters:Aa,mutations:Ca};const Ia=e=>new Promise(((t,s)=>{let i=new FormData;i.append("action","easy_wp_smtp_vue_get_settings"),i.append("nonce",oa.Ay.prototype.$easywpsmtp.nonce),ha.A.post(oa.Ay.prototype.$easywpsmtp.ajax_url,i).then((e=>{e.data.success?t(e.data):s(e.data)})).catch((function(t){if(e.dispatch("$_app/block",!1,{root:!0}),t.response){const e=t.response;return oa.Ay.prototype.$easywpsmtp_error_modal({subtitle:(0,l.__)("It looks like we can't load the settings.","easy-wp-smtp"),detailedError:(0,l.nv)((0,l.__)("%1$s, %2$s","easy-wp-smtp"),e.status,e.statusText)})}oa.Ay.prototype.$easywpsmtp_error_toast({title:(0,l.__)("You appear to be offline.","easy-wp-smtp")})}))})),Fa=(e,t)=>new Promise(((e,s)=>{let i=new FormData;i.append("action","easy_wp_smtp_vue_get_amazon_ses_identities"),i.append("nonce",oa.Ay.prototype.$easywpsmtp.nonce),!1!==t&&i.append("value",JSON.stringify(t)),ha.A.post(oa.Ay.prototype.$easywpsmtp.ajax_url,i).then((t=>{t.data.success?e(t.data):s(t.data)})).catch((function(e){if(e.response){const t=e.response;return oa.Ay.prototype.$easywpsmtp_error_modal({subtitle:(0,l.__)("It looks like we can't retrieve Amazon SES Identities.","easy-wp-smtp"),detailedError:(0,l.nv)((0,l.__)("%1$s, %2$s","easy-wp-smtp"),t.status,t.statusText)})}oa.Ay.prototype.$easywpsmtp_error_toast({title:(0,l.__)("Can't retrieve Amazon SES Identities.","easy-wp-smtp")})}))})),za=(e,t)=>new Promise(((e,s)=>{let i=new FormData;i.append("action","easy_wp_smtp_vue_amazon_ses_identity_registration"),i.append("nonce",oa.Ay.prototype.$easywpsmtp.nonce),!1!==t.value&&i.append("value",t.value),!1!==t.value&&i.append("type",t.type),ha.A.post(oa.Ay.prototype.$easywpsmtp.ajax_url,i).then((t=>{t.data.success?e(t.data):s(t.data)})).catch((function(e){if(e.response){const t=e.response;return oa.Ay.prototype.$easywpsmtp_error_modal({subtitle:(0,l.__)("It looks like we can't register the Amazon SES Identity.","easy-wp-smtp"),detailedError:(0,l.nv)((0,l.__)("%1$s, %2$s","easy-wp-smtp"),t.status,t.statusText)})}oa.Ay.prototype.$easywpsmtp_error_toast({title:(0,l.__)("Can't register the Amazon SES Identity","easy-wp-smtp")})}))})),La=(e,t)=>new Promise((e=>{let s=new FormData;s.append("action","easy_wp_smtp_vue_update_settings"),s.append("nonce",oa.Ay.prototype.$easywpsmtp.nonce),void 0!==t.overwrite&&s.append("overwrite",t.overwrite),!1!==t.value&&s.append("value",JSON.stringify(t.value)),ha.A.post(oa.Ay.prototype.$easywpsmtp.ajax_url,s).then((t=>{e(t.data)})).catch((function(e){if(e.response){const t=e.response;return oa.Ay.prototype.$easywpsmtp_error_modal({subtitle:(0,l.__)("It looks like we can't save the settings.","easy-wp-smtp"),detailedError:(0,l.nv)((0,l.__)("%1$s, %2$s","easy-wp-smtp"),t.status,t.statusText)})}oa.Ay.prototype.$easywpsmtp_error_toast({title:(0,l.__)("Network error encountered. Settings not saved.","easy-wp-smtp")})}))})),Oa=(e,t)=>new Promise((e=>{let s=new FormData;s.append("action","easy_wp_smtp_vue_import_settings"),s.append("nonce",oa.Ay.prototype.$easywpsmtp.nonce),!1!==t.value&&s.append("value",t.value),ha.A.post(oa.Ay.prototype.$easywpsmtp.ajax_url,s).then((t=>{e(t.data)})).catch((function(e){if(e.response){const t=e.response;return oa.Ay.prototype.$easywpsmtp_error_modal({subtitle:(0,l.__)("It looks like we can't import the plugin settings.","easy-wp-smtp"),detailedError:(0,l.nv)((0,l.__)("%1$s, %2$s","easy-wp-smtp"),t.status,t.statusText)})}oa.Ay.prototype.$easywpsmtp_error_toast({title:(0,l.__)("Network error encountered. SMTP plugin import failed!","easy-wp-smtp")})}))})),Ra=function(e,t){return new Promise(((s,i)=>{let a=new FormData;a.append("action","easy_wp_smtp_vue_get_oauth_url"),a.append("nonce",oa.Ay.prototype.$easywpsmtp.nonce),!1!==e&&a.append("mailer",e),!1!==t&&a.append("settings",JSON.stringify(t)),ha.A.post(oa.Ay.prototype.$easywpsmtp.ajax_url,a).then((e=>{e.data.success?s(e.data):i(e.data)})).catch((function(e){if(e.response){const t=e.response;return oa.Ay.prototype.$easywpsmtp_error_modal({subtitle:(0,l.__)("It looks like we can't load authentication details.","easy-wp-smtp"),detailedError:(0,l.nv)((0,l.__)("%1$s, %2$s","easy-wp-smtp"),t.status,t.statusText)})}oa.Ay.prototype.$easywpsmtp_error_toast({title:(0,l.__)("You appear to be offline.","easy-wp-smtp")})}))}))},Wa=function(e){return new Promise(((t,s)=>{let i=new FormData;i.append("action","easy_wp_smtp_vue_remove_oauth_connection"),i.append("nonce",oa.Ay.prototype.$easywpsmtp.nonce),!1!==e&&i.append("mailer",e),ha.A.post(oa.Ay.prototype.$easywpsmtp.ajax_url,i).then((e=>{e.data.success?t(e.data):s(e.data)})).catch((function(e){if(e.response){const t=e.response;return oa.Ay.prototype.$easywpsmtp_error_modal({subtitle:(0,l.__)("It looks like we can't remove OAuth connection.","easy-wp-smtp"),detailedError:(0,l.nv)((0,l.__)("%1$s, %2$s","easy-wp-smtp"),t.status,t.statusText)})}oa.Ay.prototype.$easywpsmtp_error_toast({title:(0,l.__)("You appear to be offline.","easy-wp-smtp")})}))}))},Na=function(e){return new Promise((t=>{let s=new FormData;s.append("action","easy_wp_smtp_vue_subscribe_to_newsletter"),s.append("nonce",oa.Ay.prototype.$easywpsmtp.nonce),s.append("email",e),ha.A.post(oa.Ay.prototype.$easywpsmtp.ajax_url,s).then((e=>{t(e.data)}))}))},Ya=function(e){return new Promise((t=>{let s=new FormData;s.append("action","easy_wp_smtp_vue_verify_license_key"),s.append("nonce",oa.Ay.prototype.$easywpsmtp.nonce),s.append("license_key",e),ha.A.post(oa.Ay.prototype.$easywpsmtp.ajax_url,s).then((e=>{t(e.data)}))}))},Da=function(e){return new Promise((t=>{let s=new FormData;s.append("action","easy_wp_smtp_vue_upgrade_plugin"),s.append("nonce",oa.Ay.prototype.$easywpsmtp.nonce),s.append("license_key",e),ha.A.post(oa.Ay.prototype.$easywpsmtp.ajax_url,s).then((e=>{t(e.data)}))}))},Ua=function(e){return new Promise(((t,s)=>{let i=new FormData;e?i.append("action","easy_wp_smtp_vue_remove_outlook_one_click_setup_oauth_connection"):i.append("action","easy_wp_smtp_vue_remove_oauth_connection"),i.append("nonce",oa.Ay.prototype.$easywpsmtp.nonce),i.append("mailer","outlook"),ha.A.post(oa.Ay.prototype.$easywpsmtp.ajax_url,i).then((e=>{e.data.success?t(e.data):s(e.data)})).catch((function(e){if(e.response){const t=e.response;return oa.Ay.prototype.$easywpsmtp_error_modal({subtitle:(0,l.__)("It looks like we can't remove OAuth connection.","easy-wp-smtp"),detailedError:(0,l.nv)((0,l.__)("%1$s, %2$s","easy-wp-smtp"),t.status,t.statusText)})}oa.Ay.prototype.$easywpsmtp_error_toast({title:(0,l.__)("You appear to be offline.","easy-wp-smtp")})}))}))};var Ba={fetchSettings:Ia,saveSettings:La,importOtherPluginSettings:Oa,fetchAmazonSESIdentities:Fa,amazonSESRegisterIdentity:za,getAuthRedirect:Ra,removeAuth:Wa,removeOutlookAuth:Ua,subscribeToNewsletter:Na,verifyLicense:Ya,upgradePlugin:Da};const Va=e=>Ba.fetchSettings(e).then((t=>{e.commit("SETTINGS_UPDATED",t.data)})).catch((e=>{if(e.data)return oa.Ay.prototype.$easywpsmtp_error_modal({subtitle:(0,l.__)("It looks like we can't load existing settings.","easy-wp-smtp"),detailedError:e.data})})),Ha=e=>{const t=e.getters.settings.amazonses;if(0!==Object.keys(t).length)return Ba.fetchAmazonSESIdentities(e,t).then((t=>{e.commit("AMAZONSES_IDENTITIES_UPDATED",t),oa.Ay.swal.close()})).catch((e=>{oa.Ay.prototype.$easywpsmtp_error_modal({subtitle:(0,l.__)("It looks like we can't retrieve the Amazon SES Identities.","easy-wp-smtp"),detailedError:e.data?e.data:""})}))},Ka=(e,t)=>Ba.amazonSESRegisterIdentity(e,t).catch((e=>{oa.Ay.prototype.$easywpsmtp_error_modal({subtitle:(0,l.__)("It looks like we can't register the Amazon SES Identity.","easy-wp-smtp"),detailedError:e.data})})),qa=(e,t)=>{e.commit("MAILER_UPDATE",t)},Ga=(e,t)=>new Promise((function(s){e.commit("LOGS_UPDATE",t),s({success:!0})})),ja=(e,t)=>new Promise((function(s){e.commit("SUMMARY_REPORT_EMAIL_UPDATE",t),s({success:!0})})),Za=(e,t)=>{e.commit("SETTINGS_SAVE_START");let s=Ba.saveSettings(e,t);return s.then((function(){e.commit("SETTINGS_SAVE_END")})),s},Ja=e=>{const t=e.getters.settings;e.commit("SETTINGS_SAVE_START");let s=Ba.saveSettings(e,{value:t});return s.then((function(){e.commit("SETTINGS_SAVE_END")})),s},Xa=(e,t)=>(e.commit("SETTINGS_SAVE_START"),new Promise((function(s){Ba.importOtherPluginSettings(e,t).then((function(t){e.commit("SETTINGS_SAVE_END"),t.success?Va(e).then((function(){s(!0)})):s(!1)}))}))),Qa=(e,t)=>Ba.getAuthRedirect(t,e.getters.settings[t]).catch((e=>{oa.Ay.prototype.$easywpsmtp_error_modal({subtitle:(0,l.__)("It looks like we can't load oAuth redirect.","easy-wp-smtp"),detailedError:e.data})})),en=(e,t)=>{let s;return s="outlook"===t?Ba.removeOutlookAuth(e.getters.outlook_one_click_setup_enabled).then((function(){e.commit("SETTINGS_REMOVE_OUTLOOK_AUTH",e.getters.outlook_one_click_setup_enabled)})):Ba.removeAuth(t).then((function(){e.commit("SETTINGS_REMOVE_AUTH",t)})),s.catch((e=>{oa.Ay.prototype.$easywpsmtp_error_modal({subtitle:(0,l.__)("It looks like we can't remove oAuth connection.","easy-wp-smtp"),detailedError:e.data})})),s},tn=(e,t)=>new Promise((s=>{e.commit("SETTINGS_SAVE_PLUGIN_FEATURES",t),s({success:!0,features:t})})),sn=(e,t)=>Ba.subscribeToNewsletter(t),an=(e,t)=>Ba.verifyLicense(t),nn=(e,t)=>Ba.upgradePlugin(t),rn=(e,t)=>{e.commit("OUTLOOK_ONE_CLICK_SETUP_ENABLED_UPDATE",t)};var on={getSettings:Va,updateSettings:Za,importOtherPlugin:Xa,setMailer:qa,setLogs:Ga,setSummaryReportEmail:ja,saveCurrentSettings:Ja,getAmazonSESIdentities:Ha,amazonSESRegisterIdentity:Ka,getAuthUrl:Qa,removeAuth:en,savePluginFeatures:tn,subscribeToNewsletter:sn,verifyLicense:an,upgradePlugin:nn,setOutlookUseOneClickSetup:rn};const ln=e=>e.settings,_n=e=>e.settings.mail.mailer,pn=e=>e.settings.outlook.user_details?e.settings.outlook.user_details.email:null,mn=e=>e.settings.outlook.one_click_setup_user_details?e.settings.outlook.one_click_setup_user_details.email:null,cn=e=>e.settings.gmail.user_details?e.settings.gmail.user_details.email:null,dn=e=>e.settings.zoho.user_details?e.settings.zoho.user_details.email:null,un=e=>e.plugin_features,fn=e=>!!e.settings.logs.enabled&&e.settings.logs.enabled,hn=e=>!e.settings.general.summary_report_email_disabled,yn=e=>"string"===typeof e.settings.license.key&&e.settings.license.key.length>0&&!e.settings.license.is_expired&&!e.settings.license.is_disabled&&!e.settings.license.is_invalid&&!e.settings.license.is_limit_reached,gn=e=>t=>{let s=!1;const i=e.amazonses_identities.data,a=t.split("@").pop();return void 0!==i&&(i.forEach((e=>{("email"===e.type&&e.value===t||"domain"===e.type&&e.value===a)&&(s=!0)})),s)},wn=e=>e.settings.outlook.one_click_setup_enabled;var bn={getField:ne.VI,settings:ln,mailer:_n,outlook_email:pn,outlook_one_click_setup_email:mn,gmail_email:cn,zoho_email:dn,plugin_features:un,amazonses_is_email_registered:gn,email_log_enabled:fn,summary_report_email_enabled:hn,is_valid_license:yn,outlook_one_click_setup_enabled:wn},vn=s(182),xn=s.n(vn);const kn=(e,t)=>{e.is_saving=!1,e.settings=xn()(e.settings,t)},Sn=(e,t)=>{e.amazonses_identities=t.data},Pn=(e,t)=>{e.settings.mail.mailer=t},An=(e,t)=>{e.settings.logs.enabled=t},En=(e,t)=>{e.settings.general.summary_report_email_disabled=t},$n=e=>{e.is_saving=!0},Cn=e=>{e.is_saving=!1},Mn=(e,t)=>{const s=e.settings[t];e.settings[t]={client_id:s.client_id,client_secret:s.client_secret},"zoho"===t&&(e.settings[t].domain=s.domain)},Tn=(e,t)=>{t?(e.settings.outlook.one_click_setup_credentials={access_token:"",refresh_token:"",expires:""},e.settings.outlook.one_click_setup_user_details={email:"",name:""}):(e.settings.outlook.access_token="",e.settings.outlook.refresh_token="",e.settings.outlook.user_details={email:"",name:""})},In=(e,t)=>{e.plugin_features=t},Fn=(e,t)=>{e.settings.outlook.one_click_setup_enabled=t};var zn={updateField:ne.cP,SETTINGS_UPDATED:kn,SETTINGS_SAVE_START:$n,SETTINGS_SAVE_END:Cn,MAILER_UPDATE:Pn,LOGS_UPDATE:An,SUMMARY_REPORT_EMAIL_UPDATE:En,AMAZONSES_IDENTITIES_UPDATED:Sn,SETTINGS_REMOVE_AUTH:Mn,SETTINGS_REMOVE_OUTLOOK_AUTH:Tn,SETTINGS_SAVE_PLUGIN_FEATURES:In,OUTLOOK_ONE_CLICK_SETUP_ENABLED_UPDATE:Fn};const Ln={settings:{mail:{mailer:"mail",from_email:"",from_name:"",return_path:!1,from_email_force:!0,from_name_force:!1},smtp:{host:"",port:"587",encryption:"tls",autotls:!0,auth:!0,user:"",pass:""},sendlayer:{api_key:""},smtpcom:{api_key:"",channel:""},sendinblue:{api_key:"",domain:""},mailgun:{api_key:"",domain:"",region:"US"},mailjet:{api_key:"",secret_key:""},mailersend:{api_key:"",has_pro_plan:!1},sendgrid:{api_key:"",domain:""},postmark:{server_api_token:"",message_stream:""},sparkpost:{api_key:"",region:"US"},amazonses:{client_id:"",client_secret:"",region:"us-east-1"},outlook:{client_id:"",client_secret:"",access_token:{},refresh_token:"",user_details:{email:""},one_click_setup_enabled:!1,one_click_setup_credentials:{access_token:"",refresh_token:"",expires:""},one_click_setup_user_details:{email:""}},elasticemail:{api_key:""},gmail:{relay_credentials:{key:"",token:"",project_id:""},user_details:{email:""}},smtp2go:{api_key:""},zoho:{client_id:"",client_secret:"",domain:"com",access_token:{},refresh_token:"",user_details:{email:""}},logs:{enabled:!1,log_email_content:!1,save_attachments:!1,open_email_tracking:!1,click_link_tracking:!1},general:{summary_report_email_disabled:!1},license:{key:"",is_expired:!1,is_disabled:!1,is_invalid:!1,is_limit_reached:!1},alert_email:{enabled:!1,connections:{}}},amazonses_identities:{},amazonses_display_identities:window.easy_wp_smtp_vue.mailer_options.amazonses.display_identities,plugin_features:[]};var On={namespaced:!0,state:Ln,actions:on,getters:bn,mutations:zn};const Rn=()=>new Promise((e=>{let t=new FormData;t.append("action","easy_wp_smtp_vue_check_mailer_configuration"),t.append("nonce",oa.Ay.prototype.$easywpsmtp.nonce),ha.A.post(oa.Ay.prototype.$easywpsmtp.ajax_url,t).then((t=>{e(t.data)})).catch((function(e){if(e.response){const t=e.response;return oa.Ay.prototype.$easywpsmtp_error_modal({subtitle:(0,l.__)("It looks like we can't perform the mailer configuration check.","easy-wp-smtp"),detailedError:(0,l.nv)((0,l.__)("%1$s, %2$s","easy-wp-smtp"),t.status,t.statusText)})}oa.Ay.prototype.$easywpsmtp_error_toast({title:(0,l.__)("You appear to be offline.","easy-wp-smtp")})}))})),Wn=e=>{let t=new FormData;t.append("action","easy_wp_smtp_vue_send_feedback"),t.append("nonce",oa.Ay.prototype.$easywpsmtp.nonce),t.append("data",JSON.stringify(e)),ha.A.post(oa.Ay.prototype.$easywpsmtp.ajax_url,t).catch((function(e){if(e.response){const t=e.response;return oa.Ay.prototype.$easywpsmtp_error_modal({subtitle:(0,l.__)("It looks like we can't send the feedback.","easy-wp-smtp"),detailedError:(0,l.nv)((0,l.__)("%1$s, %2$s","easy-wp-smtp"),t.status,t.statusText)})}oa.Ay.prototype.$easywpsmtp_error_toast({title:(0,l.__)("You appear to be offline.","easy-wp-smtp")})}))},Nn=()=>{let e=new FormData;e.append("action","easy_wp_smtp_vue_wizard_steps_started"),e.append("nonce",oa.Ay.prototype.$easywpsmtp.nonce),ha.A.post(oa.Ay.prototype.$easywpsmtp.ajax_url,e)};var Yn={checkMailerConfiguration:Rn,sendFeedback:Wn,started:Nn};const Dn=()=>Yn.checkMailerConfiguration(),Un=(e,t)=>{Yn.sendFeedback(t)},Bn=()=>{Yn.started()};var Vn={checkMailerConfiguration:Dn,sendFeedback:Un,started:Bn};const Hn=e=>e.blocked_step,Kn=e=>e.current_user_email;var qn={getField:ne.VI,blocked_step:Hn,current_user_email:Kn},Gn={updateField:ne.cP};const jn={blocked_step:!1,current_user_email:window.easy_wp_smtp_vue.current_user_email};var Zn={namespaced:!0,state:jn,actions:Vn,getters:qn,mutations:Gn};const Jn=e=>{e.commit("INIT")},Xn=e=>{e.commit("BLOCK_APP")},Qn=e=>{e.commit("UNBLOCK_APP")},er=e=>{e.commit("APP_LOADING_START")},tr=e=>{e.commit("APP_LOADING_STOP")};var sr={init:Jn,block:Xn,unblock:Qn,start_loading:er,stop_loading:tr};const ir=e=>e.blocked,ar=e=>e.loading,nr=e=>e.easywpsmtp;var rr={blocked:ir,loading:ar,easywpsmtp:nr};const or=()=>{},lr=e=>{e.blocked=!0},_r=e=>{e.blocked=!1},pr=e=>{e.loading=!0},mr=e=>{e.loading=!1};var cr={INIT:or,BLOCK_APP:lr,UNBLOCK_APP:_r,APP_LOADING_START:pr,APP_LOADING_STOP:mr};const dr={blocked:!1,loading:!1,easywpsmtp:window.easy_wp_smtp_vue?window.easy_wp_smtp_vue:{}};var ur={namespaced:!0,state:dr,actions:sr,getters:rr,mutations:cr};const fr=e=>{e.subscribe(((t,s)=>{if("$_app/INIT"===t.type){const t=s["$_app"].easywpsmtp.versions;let i="",a="";t.wp_version_below_52&&(i=(0,l.__)("Yikes! WordPress Update Required","easy-wp-smtp"),a=(0,l.nv)((0,l.__)("Easy WP SMTP has detected that your site is running an outdated version of WordPress (%s). Easy WP SMTP requires at least WordPress version 5.2.","easy-wp-smtp"),t.wp_version)),oa.Ay.prototype.$swal&&i.length&&(e.dispatch("$_app/block"),oa.Ay.prototype.$swal.close(),oa.Ay.prototype.$swal({title:i,html:`<p>${a}</p><p><a href="${oa.Ay.prototype.$easywpsmtp.exit_url}">${(0,l.__)("Return to Plugin Settings","easy-wp-smtp")}</a></p>`,customClass:{container:"easy-wp-smtp-swal easy-wp-smtp-swal-alert"},allowOutsideClick:!1,allowEscapeKey:!1,allowEnterKey:!1,showConfirmButton:!1,onOpen:function(){oa.Ay.prototype.$swal.hideLoading()}}))}}))};var hr=fr;oa.Ay.use(Y.Ay);const yr=[hr];var gr=new Y.Ay.Store({modules:{$_app:ur,$_plugins:Ta,$_settings:On,$_wizard:Zn},plugins:yr}),wr=s(5395),br=(s(3987),s(596));const vr=document.getElementById("easy-wp-smtp-vue-setup-wizard");oa.Ay.config.productionTip=!1,s.p=window.easy_wp_smtp_vue.public_url,oa.Ay.use(br.l_),oa.Ay.use(n.Ay),oa.Ay.use(la.A),oa.Ay.use(wr.Ay,{defaultTemplate:'<div class="easy-wp-smtp-tooltip" role="tooltip"><div class="easy-wp-smtp-tooltip-arrow"></div><div class="easy-wp-smtp-tooltip-inner"></div></div>',defaultArrowSelector:".easy-wp-smtp-tooltip-arrow, .easy-wp-smtp-tooltip__arrow",defaultInnerSelector:".easy-wp-smtp-tooltip-inner, .easy-wp-smtp-tooltip__inner"}),oa.Ay.use(da),(0,l.fh)(window.easy_wp_smtp_vue.translations,"easy-wp-smtp");const xr={install(e){e.prototype.$wizard_steps=[],e.prototype.$wizard_steps.push("choose_mailer_step"),e.prototype.$wizard_steps.push("configure_mailer_step"),e.prototype.$wizard_steps.push("plugin_features_step"),e.prototype.$easywpsmtp&&e.prototype.$easywpsmtp.is_pro&&e.prototype.$wizard_steps.push("configure_email_logs_step"),e.prototype.$easywpsmtp&&!e.prototype.$easywpsmtp.is_pro&&e.prototype.$wizard_steps.push("help_improve_step"),e.prototype.$wizard_steps.push("license_step"),e.prototype.$wizard_steps.push("check_configuration_step")}};oa.Ay.use(xr),oa.Ay.use(fa),new oa.Ay({store:gr,mounted:()=>{gr.dispatch("$_app/init")},render:e=>e(ra)}).$mount(vr)},3159:function(e,t,s){var i={"./loading-white.svg":4075,"./loading.svg":6283};function a(e){var t=n(e);return s(t)}function n(e){if(!s.o(i,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return i[e]}a.keys=function(){return Object.keys(i)},a.resolve=n,e.exports=a,a.id=3159},3180:function(e,t,s){var i={"./amazonses.svg":6489,"./brevo.svg":1466,"./elasticemail.svg":4689,"./gmail.svg":6848,"./mailersend.svg":5136,"./mailgun.svg":6211,"./mailjet.svg":5168,"./outlook.svg":5423,"./postmark.svg":6959,"./sendgrid.svg":5064,"./sendlayer.svg":8295,"./smtp.svg":9682,"./smtp2go.svg":1366,"./smtpcom.svg":9189,"./sparkpost.svg":6675,"./zoho.svg":7936};function a(e){var t=n(e);return s(t)}function n(e){if(!s.o(i,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return i[e]}a.keys=function(){return Object.keys(i)},a.resolve=n,e.exports=a,a.id=3180},4075:function(e,t,s){"use strict";e.exports=s.p+"img/loading-white.svg"},6283:function(e,t,s){"use strict";e.exports=s.p+"img/loading.svg"},6644:function(e,t,s){"use strict";e.exports=s.p+"img/loading-ESMTP.svg"},5573:function(e,t,s){"use strict";e.exports=s.p+"img/arrow.svg"},3321:function(e,t,s){"use strict";e.exports=s.p+"img/gmail-sign-in-btn.svg"},2452:function(e,t,s){"use strict";e.exports=s.p+"img/check-circle-solid-white.svg"},5636:function(e,t,s){"use strict";e.exports=s.p+"img/check-circle-solid.svg"},1369:function(e,t,s){"use strict";e.exports=s.p+"img/check-solid-thin.svg"},8063:function(e,t,s){"use strict";e.exports=s.p+"img/check-solid.svg"},7726:function(e,t,s){"use strict";e.exports=s.p+"img/copy-solid.svg"},617:function(e,t,s){"use strict";e.exports=s.p+"img/exclamation-circle-solid.svg"},9318:function(e,t,s){"use strict";e.exports=s.p+"img/info-circle-solid.svg"},1312:function(e,t,s){"use strict";e.exports=s.p+"img/lock-solid.svg"},9004:function(e,t,s){"use strict";e.exports=s.p+"img/long-arrow-alt-left-regular.svg"},953:function(e,t,s){"use strict";e.exports=s.p+"img/long-arrow-alt-right-regular.svg"},4744:function(e,t,s){"use strict";e.exports=s.p+"img/question-circle.svg"},7157:function(e,t,s){"use strict";e.exports=s.p+"img/star-solid.svg"},3217:function(e,t,s){"use strict";e.exports=s.p+"img/times-solid.svg"},5447:function(e,t,s){"use strict";e.exports=s.p+"img/logo.svg"},6489:function(e,t,s){"use strict";e.exports=s.p+"img/amazonses.svg"},1466:function(e,t,s){"use strict";e.exports=s.p+"img/brevo.svg"},4689:function(e,t,s){"use strict";e.exports=s.p+"img/elasticemail.svg"},6848:function(e,t,s){"use strict";e.exports=s.p+"img/gmail.svg"},5136:function(e,t,s){"use strict";e.exports=s.p+"img/mailersend.svg"},6211:function(e,t,s){"use strict";e.exports=s.p+"img/mailgun.svg"},5168:function(e,t,s){"use strict";e.exports=s.p+"img/mailjet.svg"},5423:function(e,t,s){"use strict";e.exports=s.p+"img/outlook.svg"},6959:function(e,t,s){"use strict";e.exports=s.p+"img/postmark.svg"},5064:function(e,t,s){"use strict";e.exports=s.p+"img/sendgrid.svg"},8295:function(e,t,s){"use strict";e.exports=s.p+"img/sendlayer.svg"},9682:function(e,t,s){"use strict";e.exports=s.p+"img/smtp.svg"},1366:function(e,t,s){"use strict";e.exports=s.p+"img/smtp2go.svg"},9189:function(e,t,s){"use strict";e.exports=s.p+"img/smtpcom.svg"},6675:function(e,t,s){"use strict";e.exports=s.p+"img/sparkpost.svg"},7936:function(e,t,s){"use strict";e.exports=s.p+"img/zoho.svg"},6458:function(e,t,s){"use strict";e.exports=s.p+"img/outlook-sign-in-btn.svg"},3453:function(e,t,s){"use strict";e.exports=s.p+"img/pro-badge.svg"},6478:function(e,t,s){"use strict";e.exports=s.p+"img/success-configuration.svg"},6915:function(e,t,s){"use strict";e.exports=s.p+"img/working.svg"}},t={};function s(i){var a=t[i];if(void 0!==a)return a.exports;var n=t[i]={id:i,loaded:!1,exports:{}};return e[i].call(n.exports,n,n.exports,s),n.loaded=!0,n.exports}s.m=e,function(){var e=[];s.O=function(t,i,a,n){if(!i){var r=1/0;for(p=0;p<e.length;p++){i=e[p][0],a=e[p][1],n=e[p][2];for(var o=!0,l=0;l<i.length;l++)(!1&n||r>=n)&&Object.keys(s.O).every((function(e){return s.O[e](i[l])}))?i.splice(l--,1):(o=!1,n<r&&(r=n));if(o){e.splice(p--,1);var _=a();void 0!==_&&(t=_)}}return t}n=n||0;for(var p=e.length;p>0&&e[p-1][2]>n;p--)e[p]=e[p-1];e[p]=[i,a,n]}}(),function(){s.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return s.d(t,{a:t}),t}}(),function(){s.d=function(e,t){for(var i in t)s.o(t,i)&&!s.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}}(),function(){s.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){s.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){s.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){s.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e}}(),function(){s.p=""}(),function(){var e={678:0};s.O.j=function(t){return 0===e[t]};var t=function(t,i){var a,n,r=i[0],o=i[1],l=i[2],_=0;if(r.some((function(t){return 0!==e[t]}))){for(a in o)s.o(o,a)&&(s.m[a]=o[a]);if(l)var p=l(s)}for(t&&t(i);_<r.length;_++)n=r[_],s.o(e,n)&&e[n]&&e[n][0](),e[n]=0;return s.O(p)},i=self["easywpsmtpjsonp"]=self["easywpsmtpjsonp"]||[];i.forEach(t.bind(null,0)),i.push=t.bind(null,i.push.bind(i))}();var i=s.O(void 0,[504],(function(){return s(7680)}));i=s.O(i)})();