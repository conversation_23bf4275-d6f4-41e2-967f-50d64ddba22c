<?php return array(
    'root' => array(
        'name' => 'updraftplus/all-in-one-wp-security-and-firewall',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => 'bf6a97d4891dc49b8518ee6763cc87f0a234501a',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => false,
    ),
    'versions' => array(
        'mlocati/ip-lib' => array(
            'pretty_version' => '1.20.0',
            'version' => '********',
            'reference' => 'fd45fc3bf08ed6c7e665e2e70562082ac954afd4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mlocati/ip-lib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'team-updraft/common-libs' => array(
            'pretty_version' => '3.0.3',
            'version' => '*******',
            'reference' => 'cd30bf3b65e2cadcea1cca01deb32f5cd21a0b63',
            'type' => 'library',
            'install_path' => __DIR__ . '/../team-updraft/common-libs',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'updraftplus/all-in-one-wp-security-and-firewall' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => 'bf6a97d4891dc49b8518ee6763cc87f0a234501a',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
