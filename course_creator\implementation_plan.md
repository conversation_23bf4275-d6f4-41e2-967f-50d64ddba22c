# Tutor LMS Course Creation Automation Plan

## Phase 1: Database Investigation & Proof of Concept

**Goal:** Understand the database schema for all required course components and create a basic, functional script to generate a simple course.

*   **Task 1.1: Analyze Course & Module Structure.**
    *   **Action:** Use the information from `export_course_videos.php` to confirm the relationship between `courses` and `topics` post types.
    *   **Verification:** Manually create a test course with one module and check the `wp_posts` table to see the `post_parent` and `menu_order` values.

*   **Task 1.2: Analyze Lesson & Video/eBook Structure.**
    *   **Action:** Investigate how video lessons and PDF/eBook lessons are stored.
    *   **Hypothesis:** A `lesson` post's `post_content` will hold the shortcode or iframe. The `_video` post meta will be used for <PERSON><PERSON>'s native video features. We need to confirm which is best for our shortcode method.
    *   **Verification:** Create a lesson with a video shortcode and another with an iframe. Inspect the `post_content` in the `wp_posts` table and any relevant entries in `wp_postmeta` for that lesson's ID.

*   **Task 1.3: Analyze Quiz Structure.**
    *   **Action:** This is the most complex part. We need to reverse-engineer how quizzes are stored.
    *   **Steps:**
        1.  Create a simple quiz manually in Tutor LMS with a few questions of different types.
        2.  Find the `tutor_quiz` post in the `wp_posts` table.
        3.  Search the `wp_postmeta` table for all entries with the quiz's post ID.
        4.  Search for custom Tutor LMS tables that might store question and answer data (e.g., `wp_tutor_quiz_questions`, `wp_tutor_quiz_question_answers`).
    *   **Verification:** Document the exact database tables, columns, and relationships required to construct a quiz from scratch.

*   **Task 1.4: Create a Proof-of-Concept (PoC) PHP Script.**
    *   **Action:** Write a standalone PHP script (`tutor_course_importer.php`) that uses WordPress's `wp_insert_post()` and `update_post_meta()` functions.
    *   **Functionality:** Hardcode the data for one course, one module, and one video lesson. The script will programmatically insert these into the database.
    *   **Verification:** Run the script and confirm that the new course appears correctly in the WordPress admin dashboard.

## Phase 2: CSV-Based Importer

**Goal:** Develop a robust script that can parse a structured CSV file to create a complete course.

*   **Task 2.1: Define the CSV Structure.**
    *   **Action:** Design a clear and logical CSV format. It should be able to define the type of content (course, module, lesson, quiz), its title, its content (like a shortcode), and its order.

*   **Task 2.2: Build the CSV Parsing Logic.**
    *   **Action:** Write the PHP code to read the CSV file row by row.

*   **Task 2.3: Integrate Parsing with WordPress Functions.**
    *   **Action:** Combine the CSV parser with the WordPress functions from the PoC. The script will now dynamically create posts based on the CSV data.

## Phase 3: User Interface & Automation (Future Goal)

**Goal:** Create a user-friendly interface within the WordPress admin area to manage the import process.

*   **Task 3.1: Create a WordPress Admin Page.**
*   **Task 3.2: Handle YouTube URL Processing.**
*   **Task 3.3: Provide User Feedback.