<?php
function custom_edit_profile_form() {
    if (!is_user_logged_in()) {
        return 'Você precisa estar logado para editar seu perfil.';
    }

    $current_user = wp_get_current_user();

    if (isset($_POST['submit'])) {
        $errors = [];

        if (empty($_POST['nome'])) {
            $errors[] = 'Nome é obrigatório.';
        }

        if (empty($_POST['sobrenome'])) {
            $errors[] = 'Sobrenome é obrigatório.';
        }

        if (!filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Email inválido.';
        }

        if (empty($_POST['data_nascimento'])) {
            $errors[] = 'Data de Nascimento é obrigatória.';
        }

        if (empty($_POST['telefone']) || !preg_match('/^\(\d{2}\) \d{5}-\d{4}$/', $_POST['telefone'])) {
            $errors[] = 'Telefone inválido.';
        }

        if (empty($_POST['endereco'])) {
            $errors[] = 'Endereço é obrigatório.';
        }

        if (empty($_POST['bairro'])) {
            $errors[] = 'Bairro é obrigatório.';
        }

        if (empty($_POST['escola'])) {
            $errors[] = 'Escola é obrigatória.';
        }

        if (!empty($errors)) {
            echo '<ul class="errors">';
            foreach ($errors as $error) {
                echo '<li class="erro">' . $error . '</li>';
            }
            echo '</ul>';
            echo '<script>window.location.hash = "#edit-profile-form";</script>';
        } else {
            // Atualizar dados do usuário
            wp_update_user([
                'ID' => $current_user->ID,
                'user_email' => sanitize_email($_POST['email']),
            ]);

            update_user_meta($current_user->ID, 'nome', sanitize_text_field($_POST['nome']));
            update_user_meta($current_user->ID, 'sobrenome', sanitize_text_field($_POST['sobrenome']));
            update_user_meta($current_user->ID, 'data_nascimento', sanitize_text_field($_POST['data_nascimento']));
            update_user_meta($current_user->ID, 'telefone', sanitize_text_field($_POST['telefone']));
            update_user_meta($current_user->ID, 'endereco', sanitize_text_field($_POST['endereco']));
            update_user_meta($current_user->ID, 'bairro', sanitize_text_field($_POST['bairro']));
            update_user_meta($current_user->ID, 'escola', sanitize_text_field($_POST['escola']));

            echo '<p class="success">Perfil atualizado com sucesso!</p>';
        }
    }

    ob_start();
    ?>

    <form method="POST" class="flex-col" id="edit-profile-form">
        <div class="flex-row">
            <div class="half-width">
                <label for="nome">Nome</label>
                <input type="text" id="nome" name="nome" value="<?php echo esc_attr(get_user_meta($current_user->ID, 'nome', true)); ?>" required>
            </div>
            <div class="half-width">
                <label for="sobrenome">Sobrenome</label>
                <input type="text" id="sobrenome" name="sobrenome" value="<?php echo esc_attr(get_user_meta($current_user->ID, 'sobrenome', true)); ?>" required>
            </div>
        </div>

        <div class="flex-row">
            <div class="half-width">
                <label for="telefone">Telefone Whatsapp</label>
                <input type="text" id="telefone" name="telefone" value="<?php echo esc_attr(get_user_meta($current_user->ID, 'telefone', true)); ?>" required>
            </div>
            <div class="half-width">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" value="<?php echo esc_attr($current_user->user_email); ?>" required>
            </div>
        </div>

        <div class="flex-row">
            <div class="half-width">
                <label for="data_nascimento">Data de Nascimento</label>
                <input type="date" id="data_nascimento" name="data_nascimento" value="<?php echo esc_attr(get_user_meta($current_user->ID, 'data_nascimento', true)); ?>" required>
            </div>
        </div>

        <label for="endereco">Endereço Completo</label>
        <input type="text" id="endereco" name="endereco" value="<?php echo esc_attr(get_user_meta($current_user->ID, 'endereco', true)); ?>" required>

        <div class="flex-row">
            <div class="half-width">
                <label for="bairro">Bairro</label>
                <select id="bairro" name="bairro" required>
                    <option value="">Selecione um bairro</option>
                    <?php
                    $bairros = [
                        'Antares', 'Barro Duro', 'Bebedouro', 'Benedito Bentes', 'Bom Parto', 'Canaã', 'Centro', 'Chá de Jaqueira',
                        'Cidade Universitária', 'Clima Bom', 'Cruz das Almas', 'Farol', 'Feitosa', 'Fernão Velho', 'Garça Torta',
                        'Gruta de Lourdes', 'Guaxumá', 'Ipioca', 'Jacarecica', 'Jacintinho', 'Jaraguá', 'Jardim Petrópolis', 'Jatiúca',
                        'Levada', 'Magabeiras', 'Mutange', 'Ouro Preto', 'Pajuçara', 'Pescaria', 'Petrópolis', 'Pinheiro', 'Pitanguinha',
                        'Poço', 'Ponta da Terra', 'Ponta Grossa', 'Ponta Verde', 'Pontal da Praça', 'Prado', 'Riacho Doce', 'Rio Novo',
                        'Santa Amélia', 'Santa Lúcia', 'Santo Amaro', 'Santos Dumont', 'São Jorge', 'Serraria', 'Tabuleiro dos Martins',
                        'Trapiche da Barra', 'Vergel do Lago', 'Zona Rural'
                    ];

                    $current_bairro = get_user_meta($current_user->ID, 'bairro', true);
                    foreach ($bairros as $bairro) {
                        echo '<option value="' . esc_attr($bairro) . '"' . selected($current_bairro, $bairro, false) . '>' . esc_html($bairro) . '</option>';
                    }
                    ?>
                </select>
            </div>
            <div class="half-width">
                <label for="escola">Escola em que é matriculado</label>
                <input type="text" id="escola" name="escola" value="<?php echo esc_attr(get_user_meta($current_user->ID, 'escola', true)); ?>" required>
            </div>
        </div>

        <input type="submit" name="submit" value="Atualizar Perfil" class="submit-btn">
    </form>

    <?php
    wp_enqueue_script('editProfile-script', get_stylesheet_directory() . '/js/editProfile.js', array(), false, true);
    
    wp_enqueue_style('editProfile-style', get_stylesheet_directory() . '/css/editProfile.css', array(), false);
    return ob_get_clean();
}

add_shortcode('custom_edit_profile', 'custom_edit_profile_form');

function enqueue_edit_profile() {
    global $post;
    // Only enqueue on singular pages that contain the shortcode
    if ( is_singular() && is_a( $post, 'WP_Post' ) && has_shortcode( $post->post_content, 'custom_edit_profile' ) ) {
        // Original enqueue lines (assuming the path with -child was a typo and correcting)
        wp_enqueue_script('editProfile-script', get_stylesheet_directory_uri() . '/js/editProfile.js', array('jquery'), '6.8.1', true);
        wp_enqueue_style('editProfile-style', get_stylesheet_directory_uri() . '/css/editProfile.css', array(), '6.8.1'); // Added version
    }
}
add_action('wp_enqueue_scripts', 'enqueue_edit_profile');

add_action('show_user_profile', 'show_extra_profile_fields');
add_action('edit_user_profile', 'show_extra_profile_fields');

// Save the custom fields
add_action('personal_options_update', 'save_extra_profile_fields');
add_action('edit_user_profile_update', 'save_extra_profile_fields');

// Adicionar os campos personalizados via ajax
function editProfileForm($hook_suffix) { // Accept the hook suffix argument
    // Only enqueue on the user profile page ('profile.php') and user edit page ('user-edit.php')
    if ( $hook_suffix == 'profile.php' || $hook_suffix == 'user-edit.php' ) {
        // Registrar estilo
        wp_enqueue_style('editProfile-style', get_stylesheet_directory_uri() . '/css/editProfile.css', array(), '6.8.1'); // Added version

        // Registrar script
        wp_enqueue_script('editProfile-script', get_stylesheet_directory_uri() . '/js/editProfile.js', array('jquery'), '6.8.1', true);
    }
}
add_action('admin_enqueue_scripts', 'editProfileForm'); // Hook to admin scripts

// function add_user_profile_fields(){

    // Registrar estilo
    // wp_enqueue_style('editProfile-style', get_stylesheet_directory() . '/css/editProfile.css', array(), false, 'all');

    // Registrar script
    // wp_enqueue_script('editProfile-script', get_template_directory_uri() . '-child/js/editProfile.js', array(), false, true);

// }
// add_action( 'admin_enqueue_scripts', 'add_user_profile_fields' );