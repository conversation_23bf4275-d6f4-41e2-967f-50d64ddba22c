# Database Restore Script for Course Creator Project
# Usage: .\restore-database.ps1 -BackupFile "backup_file.sql" [-WordPressPath ".."]

param(
    [Parameter(Mandatory=$true)]
    [string]$BackupFile,
    [string]$WordPressPath = ".."
)

# Colors for output
$Green = "Green"
$Red = "Red"
$Yellow = "Yellow"
$Cyan = "Cyan"

Write-Host "🔄 Course Creator - Database Restore Script" -ForegroundColor $Cyan
Write-Host "=============================================" -ForegroundColor $Cyan

# Check if backup file exists
if (-not (Test-Path $BackupFile)) {
    Write-Host "❌ Backup file not found: $BackupFile" -ForegroundColor $Red
    Write-Host "💡 Available backups:" -ForegroundColor $Yellow
    
    $BackupDir = "backups"
    if (Test-Path $BackupDir) {
        Get-ChildItem $BackupDir -Filter "*.sql" | ForEach-Object {
            Write-Host "   - $($_.Name)" -ForegroundColor $White
        }
    } else {
        Write-Host "   No backups directory found" -ForegroundColor $Yellow
    }
    exit 1
}

Write-Host "📄 Backup file: $BackupFile" -ForegroundColor $Green

# Resolve WordPress path
$WordPressFullPath = Resolve-Path $WordPressPath -ErrorAction SilentlyContinue
if (-not $WordPressFullPath) {
    Write-Host "❌ WordPress path not found: $WordPressPath" -ForegroundColor $Red
    exit 1
}

Write-Host "📁 WordPress Path: $WordPressFullPath" -ForegroundColor $Green

# Check if wp-config.php exists
$WpConfigPath = Join-Path $WordPressFullPath "wp-config.php"
if (-not (Test-Path $WpConfigPath)) {
    Write-Host "❌ wp-config.php not found at: $WpConfigPath" -ForegroundColor $Red
    exit 1
}

# Extract database configuration from wp-config.php
Write-Host "🔍 Reading database configuration..." -ForegroundColor $Yellow

try {
    $WpConfigContent = Get-Content $WpConfigPath -Raw
    
    # Extract database settings using regex
    $DbName = [regex]::Match($WpConfigContent, "define\s*\(\s*['\"]DB_NAME['\"]\s*,\s*['\"]([^'\"]+)['\"]").Groups[1].Value
    $DbUser = [regex]::Match($WpConfigContent, "define\s*\(\s*['\"]DB_USER['\"]\s*,\s*['\"]([^'\"]+)['\"]").Groups[1].Value
    $DbPassword = [regex]::Match($WpConfigContent, "define\s*\(\s*['\"]DB_PASSWORD['\"]\s*,\s*['\"]([^'\"]+)['\"]").Groups[1].Value
    $DbHost = [regex]::Match($WpConfigContent, "define\s*\(\s*['\"]DB_HOST['\"]\s*,\s*['\"]([^'\"]+)['\"]").Groups[1].Value
    
    if (-not $DbName) {
        Write-Host "❌ Could not extract database name from wp-config.php" -ForegroundColor $Red
        exit 1
    }
    
    Write-Host "✅ Database: $DbName" -ForegroundColor $Green
    Write-Host "✅ User: $DbUser" -ForegroundColor $Green
    Write-Host "✅ Host: $DbHost" -ForegroundColor $Green
    
} catch {
    Write-Host "❌ Error reading wp-config.php: $($_.Exception.Message)" -ForegroundColor $Red
    exit 1
}

# Check if mysql is available
try {
    $null = Get-Command mysql -ErrorAction Stop
    Write-Host "✅ mysql found" -ForegroundColor $Green
} catch {
    Write-Host "❌ mysql not found. Please ensure MySQL is installed and in PATH." -ForegroundColor $Red
    Write-Host "💡 For XAMPP: Add C:\xampp\mysql\bin to your PATH" -ForegroundColor $Yellow
    exit 1
}

# Warning about data loss
Write-Host "`n⚠️  WARNING: This will replace ALL data in database '$DbName'" -ForegroundColor $Yellow
Write-Host "⚠️  This action cannot be undone!" -ForegroundColor $Yellow
Write-Host "`n📋 What will be restored:" -ForegroundColor $Cyan
Write-Host "   - All WordPress content (posts, pages, courses)" -ForegroundColor $White
Write-Host "   - User accounts and permissions" -ForegroundColor $White
Write-Host "   - Plugin settings and configurations" -ForegroundColor $White
Write-Host "   - Course automation test data" -ForegroundColor $White

# Confirmation prompt
$Confirmation = Read-Host "`nDo you want to continue? Type 'YES' to proceed"
if ($Confirmation -ne "YES") {
    Write-Host "❌ Restore cancelled by user" -ForegroundColor $Yellow
    exit 0
}

# Create a backup of current database before restore
Write-Host "`n🛡️  Creating safety backup of current database..." -ForegroundColor $Yellow
$SafetyBackupName = "safety_backup_before_restore_$(Get-Date -Format 'yyyy-MM-dd_HH-mm-ss')"
$SafetyBackupPath = "backups\${SafetyBackupName}.sql"

try {
    # Ensure backups directory exists
    if (-not (Test-Path "backups")) {
        New-Item -ItemType Directory -Path "backups" | Out-Null
    }
    
    # Build mysqldump command for safety backup
    $MysqlDumpArgs = @(
        "-h", $DbHost,
        "-u", $DbUser
    )
    
    if ($DbPassword) {
        $MysqlDumpArgs += "-p$DbPassword"
    }
    
    $MysqlDumpArgs += @(
        "--single-transaction",
        "--routines",
        "--triggers",
        $DbName
    )
    
    $SafetyBackupContent = & mysqldump @MysqlDumpArgs 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        $SafetyBackupContent | Out-File -FilePath $SafetyBackupPath -Encoding UTF8
        Write-Host "✅ Safety backup created: $SafetyBackupPath" -ForegroundColor $Green
    } else {
        Write-Host "⚠️  Could not create safety backup, but continuing..." -ForegroundColor $Yellow
    }
} catch {
    Write-Host "⚠️  Could not create safety backup: $($_.Exception.Message)" -ForegroundColor $Yellow
}

# Restore database
Write-Host "`n🔄 Restoring database from backup..." -ForegroundColor $Yellow
Write-Host "📄 Source: $BackupFile" -ForegroundColor $Cyan

try {
    # Build mysql command
    $MysqlArgs = @(
        "-h", $DbHost,
        "-u", $DbUser
    )
    
    if ($DbPassword) {
        $MysqlArgs += "-p$DbPassword"
    }
    
    $MysqlArgs += $DbName
    
    # Execute restore
    Get-Content $BackupFile | & mysql @MysqlArgs 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Database restore completed successfully!" -ForegroundColor $Green
        
        Write-Host "`n🎉 Restore Summary:" -ForegroundColor $Cyan
        Write-Host "   ✅ Database '$DbName' restored from $BackupFile" -ForegroundColor $Green
        Write-Host "   ✅ Safety backup saved to $SafetyBackupPath" -ForegroundColor $Green
        Write-Host "   ✅ WordPress should now reflect the restored state" -ForegroundColor $Green
        
        Write-Host "`n📝 Next Steps:" -ForegroundColor $Yellow
        Write-Host "   1. Visit your WordPress admin to verify the restore" -ForegroundColor $White
        Write-Host "   2. Check that courses and content are as expected" -ForegroundColor $White
        Write-Host "   3. Test the course automation functionality" -ForegroundColor $White
        
    } else {
        Write-Host "❌ Database restore failed!" -ForegroundColor $Red
        Write-Host "💡 Your original database is backed up at: $SafetyBackupPath" -ForegroundColor $Yellow
        exit 1
    }
    
} catch {
    Write-Host "❌ Error during restore: $($_.Exception.Message)" -ForegroundColor $Red
    Write-Host "💡 Your original database is backed up at: $SafetyBackupPath" -ForegroundColor $Yellow
    exit 1
}

Write-Host "`n🎉 Restore process completed!" -ForegroundColor $Green
