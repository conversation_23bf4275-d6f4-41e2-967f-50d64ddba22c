<?php
function get_candidate_counts_ajax_handler() {
    global $wpdb;
    $candidates = get_users(array('role' => 'candidato'));
    
    $incomplete_count = 0;
    $complete_count = 0;
    
    foreach ($candidates as $candidate) {
        $user_id = $candidate->ID;
        
        $quiz_3_completed = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}mlw_results WHERE user = %d AND quiz_id = 3",
            $user_id
        ));
        
        $quiz_2_completed = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}mlw_results WHERE user = %d AND quiz_id = 2",
            $user_id
        ));
        
        $next_quiz_id = get_user_meta($user_id, 'next_quiz', true);
        $next_quiz_completed = $next_quiz_id ? $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}mlw_results WHERE user = %d AND quiz_id = %d",
            $user_id,
            $next_quiz_id
        )) : 1;
        
        $is_complete = ($quiz_3_completed > 0 && $quiz_2_completed > 0 && $next_quiz_completed > 0);
        
        if ($is_complete) {
            $complete_count++;
        } else {
            $incomplete_count++;
        }
    }
    
    wp_send_json_success(array('incomplete' => $incomplete_count, 'complete' => $complete_count));
}
add_action('wp_ajax_get_candidate_counts', 'get_candidate_counts_ajax_handler');
add_action('wp_ajax_nopriv_get_candidate_counts', 'get_candidate_counts_ajax_handler');