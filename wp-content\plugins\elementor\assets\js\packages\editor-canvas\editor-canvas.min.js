/*! For license information please see editor-canvas.min.js.LICENSE.txt */
!function(){"use strict";var e,t,n={1020:function(e,t,n){var r=n(7557),o=Symbol.for("react.element"),i=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),l=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function a(e,t,n){var r,a={},c=null,u=null;for(r in void 0!==n&&(c=""+n),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)i.call(t,r)&&!s.hasOwnProperty(r)&&(a[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===a[r]&&(a[r]=t[r]);return{$$typeof:o,type:e,key:c,ref:u,props:a,_owner:l.current}}t.jsx=a,t.jsxs=a},4848:function(e,t,n){e.exports=n(1020)},7557:function(e){e.exports=window.React}},r={};function o(e){var t=r[e];if(void 0!==t)return t.exports;var i=r[e]={exports:{}};return n[e](i,i.exports,o),i.exports}t=Object.getPrototypeOf?function(e){return Object.getPrototypeOf(e)}:function(e){return e.__proto__},o.t=function(n,r){if(1&r&&(n=this(n)),8&r)return n;if("object"==typeof n&&n){if(4&r&&n.__esModule)return n;if(16&r&&"function"==typeof n.then)return n}var i=Object.create(null);o.r(i);var l={};e=e||[null,t({}),t([]),t(t)];for(var s=2&r&&n;"object"==typeof s&&!~e.indexOf(s);s=t(s))Object.getOwnPropertyNames(s).forEach((function(e){l[e]=function(){return n[e]}}));return l.default=function(){return n},o.d(i,l),i},o.d=function(e,t){for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var i={};o.r(i),o.d(i,{createPropsResolver:function(){return Bt},createTransformer:function(){return en},createTransformersRegistry:function(){return zt},init:function(){return Vn},settingsTransformersRegistry:function(){return Zt},styleTransformersRegistry:function(){return Wt}});var l=window.elementorV2.editor,s=o(7557),a=o.t(s,2),c=window.elementorV2.editorElements,u=window.elementorV2.editorV1Adapters,f=window.elementorV2.ui;function d(){return"undefined"!=typeof window}function m(e){return h(e)?(e.nodeName||"").toLowerCase():"#document"}function p(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function g(e){var t;return null==(t=(h(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function h(e){return!!d()&&(e instanceof Node||e instanceof p(e).Node)}function v(e){return!!d()&&(e instanceof Element||e instanceof p(e).Element)}function y(e){return!!d()&&(e instanceof HTMLElement||e instanceof p(e).HTMLElement)}function w(e){return!(!d()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof p(e).ShadowRoot)}function b(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=_(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function E(e){return["table","td","th"].includes(m(e))}function x(e){return[":popover-open",":modal"].some((t=>{try{return e.matches(t)}catch(e){return!1}}))}function R(e){const t=S(),n=v(e)?_(e):e;return["transform","translate","scale","rotate","perspective"].some((e=>!!n[e]&&"none"!==n[e]))||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some((e=>(n.willChange||"").includes(e)))||["paint","layout","strict","content"].some((e=>(n.contain||"").includes(e)))}function S(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function k(e){return["html","body","#document"].includes(m(e))}function _(e){return p(e).getComputedStyle(e)}function T(e){return v(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function C(e){if("html"===m(e))return e;const t=e.assignedSlot||e.parentNode||w(e)&&e.host||g(e);return w(t)?t.host:t}function O(e){const t=C(e);return k(t)?e.ownerDocument?e.ownerDocument.body:e.body:y(t)&&b(t)?t:O(t)}function L(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);const o=O(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=p(o);if(i){const e=A(l);return t.concat(l,l.visualViewport||[],b(o)?o:[],e&&n?L(e):[])}return t.concat(o,L(o,[],n))}function A(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function P(e,t){if(!e||!t)return!1;const n=null==t.getRootNode?void 0:t.getRootNode();if(e.contains(t))return!0;if(n&&w(n)){let n=t;for(;n;){if(e===n)return!0;n=n.parentNode||n.host}}return!1}function $(e,t){const n=["mouse","pen"];return t||n.push("",void 0),n.includes(e)}function M(e){return(null==e?void 0:e.ownerDocument)||document}var j=o(4848),I=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"].join(","),D="undefined"==typeof Element,N=D?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,V=!D&&Element.prototype.getRootNode?function(e){var t;return null==e||null===(t=e.getRootNode)||void 0===t?void 0:t.call(e)}:function(e){return null==e?void 0:e.ownerDocument},F=function e(t,n){var r;void 0===n&&(n=!0);var o=null==t||null===(r=t.getAttribute)||void 0===r?void 0:r.call(t,"inert");return""===o||"true"===o||n&&t&&e(t.parentNode)},B=function e(t,n,r){for(var o=[],i=Array.from(t);i.length;){var l=i.shift();if(!F(l,!1))if("SLOT"===l.tagName){var s=l.assignedElements(),a=e(s.length?s:l.children,!0,r);r.flatten?o.push.apply(o,a):o.push({scopeParent:l,candidates:a})}else{N.call(l,I)&&r.filter(l)&&(n||!t.includes(l))&&o.push(l);var c=l.shadowRoot||"function"==typeof r.getShadowRoot&&r.getShadowRoot(l),u=!F(c,!1)&&(!r.shadowRootFilter||r.shadowRootFilter(l));if(c&&u){var f=e(!0===c?l.children:c.children,!0,r);r.flatten?o.push.apply(o,f):o.push({scopeParent:l,candidates:f})}else i.unshift.apply(i,l.children)}}return o},z=function(e){return!isNaN(parseInt(e.getAttribute("tabindex"),10))},W=function(e){if(!e)throw new Error("No node provided");return e.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(e.tagName)||function(e){var t,n=null==e||null===(t=e.getAttribute)||void 0===t?void 0:t.call(e,"contenteditable");return""===n||"true"===n}(e))&&!z(e)?0:e.tabIndex},H=function(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex},U=function(e){return"INPUT"===e.tagName},Y=function(e){var t=e.getBoundingClientRect(),n=t.width,r=t.height;return 0===n&&0===r},q=function(e,t){return!(t.disabled||F(t)||function(e){return U(e)&&"hidden"===e.type}(t)||function(e,t){var n=t.displayCheck,r=t.getShadowRoot;if("hidden"===getComputedStyle(e).visibility)return!0;var o=N.call(e,"details>summary:first-of-type")?e.parentElement:e;if(N.call(o,"details:not([open]) *"))return!0;if(n&&"full"!==n&&"legacy-full"!==n){if("non-zero-area"===n)return Y(e)}else{if("function"==typeof r){for(var i=e;e;){var l=e.parentElement,s=V(e);if(l&&!l.shadowRoot&&!0===r(l))return Y(e);e=e.assignedSlot?e.assignedSlot:l||s===e.ownerDocument?l:s.host}e=i}if(function(e){var t,n,r,o,i=e&&V(e),l=null===(t=i)||void 0===t?void 0:t.host,s=!1;if(i&&i!==e)for(s=!!(null!==(n=l)&&void 0!==n&&null!==(r=n.ownerDocument)&&void 0!==r&&r.contains(l)||null!=e&&null!==(o=e.ownerDocument)&&void 0!==o&&o.contains(e));!s&&l;){var a,c,u;s=!(null===(c=l=null===(a=i=V(l))||void 0===a?void 0:a.host)||void 0===c||null===(u=c.ownerDocument)||void 0===u||!u.contains(l))}return s}(e))return!e.getClientRects().length;if("legacy-full"!==n)return!0}return!1}(t,e)||function(e){return"DETAILS"===e.tagName&&Array.prototype.slice.apply(e.children).some((function(e){return"SUMMARY"===e.tagName}))}(t)||function(e){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(var t=e.parentElement;t;){if("FIELDSET"===t.tagName&&t.disabled){for(var n=0;n<t.children.length;n++){var r=t.children.item(n);if("LEGEND"===r.tagName)return!!N.call(t,"fieldset[disabled] *")||!r.contains(e)}return!0}t=t.parentElement}return!1}(t))},K=function(e,t){return!(function(e){return function(e){return U(e)&&"radio"===e.type}(e)&&!function(e){if(!e.name)return!0;var t,n=e.form||V(e),r=function(e){return n.querySelectorAll('input[type="radio"][name="'+e+'"]')};if("undefined"!=typeof window&&void 0!==window.CSS&&"function"==typeof window.CSS.escape)t=r(window.CSS.escape(e.name));else try{t=r(e.name)}catch(e){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",e.message),!1}var o=function(e,t){for(var n=0;n<e.length;n++)if(e[n].checked&&e[n].form===t)return e[n]}(t,e.form);return!o||o===e}(e)}(t)||W(t)<0||!q(e,t))},X=function(e){var t=parseInt(e.getAttribute("tabindex"),10);return!!(isNaN(t)||t>=0)},G=function e(t){var n=[],r=[];return t.forEach((function(t,o){var i=!!t.scopeParent,l=i?t.scopeParent:t,s=function(e,t){var n=W(e);return n<0&&t&&!z(e)?0:n}(l,i),a=i?e(t.candidates):l;0===s?i?n.push.apply(n,a):n.push(l):r.push({documentOrder:o,tabIndex:s,item:t,isScope:i,content:a})})),r.sort(H).reduce((function(e,t){return t.isScope?e.push.apply(e,t.content):e.push(t.content),e}),[]).concat(n)},J=function(e,t){var n;return n=(t=t||{}).getShadowRoot?B([e],t.includeContainer,{filter:K.bind(null,t),flatten:!1,getShadowRoot:t.getShadowRoot,shadowRootFilter:X}):function(e,t,n){if(F(e))return[];var r=Array.prototype.slice.apply(e.querySelectorAll(I));return t&&N.call(e,I)&&r.unshift(e),r.filter(n)}(e,t.includeContainer,K.bind(null,t)),G(n)},Q=window.ReactDOM;const Z=Math.min,ee=Math.max,te=Math.round,ne=Math.floor,re=e=>({x:e,y:e});function oe(e,t){return"function"==typeof e?e(t):e}function ie(e){return e.split("-")[0]}function le(e){return e.split("-")[1]}function se(e){return["top","bottom"].includes(ie(e))?"y":"x"}function ae(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function ce(e,t,n){let{reference:r,floating:o}=e;const i=se(t),l=function(e){return"x"===se(e)?"y":"x"}(t),s="y"===l?"height":"width",a=ie(t),c="y"===i,u=r.x+r.width/2-o.width/2,f=r.y+r.height/2-o.height/2,d=r[s]/2-o[s]/2;let m;switch(a){case"top":m={x:u,y:r.y-o.height};break;case"bottom":m={x:u,y:r.y+r.height};break;case"right":m={x:r.x+r.width,y:f};break;case"left":m={x:r.x-o.width,y:f};break;default:m={x:r.x,y:r.y}}switch(le(t)){case"start":m[l]-=d*(n&&c?-1:1);break;case"end":m[l]+=d*(n&&c?-1:1)}return m}function ue(e){const t=_(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=y(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,s=te(n)!==i||te(r)!==l;return s&&(n=i,r=l),{width:n,height:r,$:s}}function fe(e){return v(e)?e:e.contextElement}function de(e){const t=fe(e);if(!y(t))return re(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=ue(t);let l=(i?te(n.width):n.width)/r,s=(i?te(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),s&&Number.isFinite(s)||(s=1),{x:l,y:s}}const me=re(0);function pe(e){const t=p(e);return S()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:me}function ge(e,t,n,r){void 0===t&&(t=!1),void 0===n&&(n=!1);const o=e.getBoundingClientRect(),i=fe(e);let l=re(1);t&&(r?v(r)&&(l=de(r)):l=de(e));const s=function(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==p(e))&&t}(i,n,r)?pe(i):re(0);let a=(o.left+s.x)/l.x,c=(o.top+s.y)/l.y,u=o.width/l.x,f=o.height/l.y;if(i){const e=p(i),t=r&&v(r)?p(r):r;let n=e,o=A(n);for(;o&&r&&t!==n;){const e=de(o),t=o.getBoundingClientRect(),r=_(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;a*=e.x,c*=e.y,u*=e.x,f*=e.y,a+=i,c+=l,n=p(o),o=A(n)}}return ae({width:u,height:f,x:a,y:c})}function he(e,t){const n=T(e).scrollLeft;return t?t.left+n:ge(g(e)).left+n}function ve(e,t,n){void 0===n&&(n=!1);const r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:he(e,r)),y:r.top+t.scrollTop}}function ye(e,t,n){let r;if("viewport"===t)r=function(e,t){const n=p(e),r=g(e),o=n.visualViewport;let i=r.clientWidth,l=r.clientHeight,s=0,a=0;if(o){i=o.width,l=o.height;const e=S();(!e||e&&"fixed"===t)&&(s=o.offsetLeft,a=o.offsetTop)}return{width:i,height:l,x:s,y:a}}(e,n);else if("document"===t)r=function(e){const t=g(e),n=T(e),r=e.ownerDocument.body,o=ee(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=ee(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let l=-n.scrollLeft+he(e);const s=-n.scrollTop;return"rtl"===_(r).direction&&(l+=ee(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:s}}(g(e));else if(v(t))r=function(e,t){const n=ge(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=y(e)?de(e):re(1);return{width:e.clientWidth*i.x,height:e.clientHeight*i.y,x:o*i.x,y:r*i.y}}(t,n);else{const n=pe(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return ae(r)}function we(e,t){const n=C(e);return!(n===t||!v(n)||k(n))&&("fixed"===_(n).position||we(n,t))}function be(e,t,n){const r=y(t),o=g(t),i="fixed"===n,l=ge(e,!0,i,t);let s={scrollLeft:0,scrollTop:0};const a=re(0);if(r||!r&&!i)if(("body"!==m(t)||b(o))&&(s=T(t)),r){const e=ge(t,!0,i,t);a.x=e.x+t.clientLeft,a.y=e.y+t.clientTop}else o&&(a.x=he(o));const c=!o||r||i?re(0):ve(o,s);return{x:l.left+s.scrollLeft-a.x-c.x,y:l.top+s.scrollTop-a.y-c.y,width:l.width,height:l.height}}function Ee(e){return"static"===_(e).position}function xe(e,t){if(!y(e)||"fixed"===_(e).position)return null;if(t)return t(e);let n=e.offsetParent;return g(e)===n&&(n=n.ownerDocument.body),n}function Re(e,t){const n=p(e);if(x(e))return n;if(!y(e)){let t=C(e);for(;t&&!k(t);){if(v(t)&&!Ee(t))return t;t=C(t)}return n}let r=xe(e,t);for(;r&&E(r)&&Ee(r);)r=xe(r,t);return r&&k(r)&&Ee(r)&&!R(r)?n:r||function(e){let t=C(e);for(;y(t)&&!k(t);){if(R(t))return t;if(x(t))return null;t=C(t)}return null}(e)||n}const Se={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i="fixed"===o,l=g(r),s=!!t&&x(t.floating);if(r===l||s&&i)return n;let a={scrollLeft:0,scrollTop:0},c=re(1);const u=re(0),f=y(r);if((f||!f&&!i)&&(("body"!==m(r)||b(l))&&(a=T(r)),y(r))){const e=ge(r);c=de(r),u.x=e.x+r.clientLeft,u.y=e.y+r.clientTop}const d=!l||f||i?re(0):ve(l,a,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-a.scrollLeft*c.x+u.x+d.x,y:n.y*c.y-a.scrollTop*c.y+u.y+d.y}},getDocumentElement:g,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[..."clippingAncestors"===n?x(t)?[]:function(e,t){const n=t.get(e);if(n)return n;let r=L(e,[],!1).filter((e=>v(e)&&"body"!==m(e))),o=null;const i="fixed"===_(e).position;let l=i?C(e):e;for(;v(l)&&!k(l);){const t=_(l),n=R(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&o&&["absolute","fixed"].includes(o.position)||b(l)&&!n&&we(e,l))?r=r.filter((e=>e!==l)):o=t,l=C(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=i[0],s=i.reduce(((e,n)=>{const r=ye(t,n,o);return e.top=ee(r.top,e.top),e.right=Z(r.right,e.right),e.bottom=Z(r.bottom,e.bottom),e.left=ee(r.left,e.left),e}),ye(t,l,o));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:Re,getElementRects:async function(e){const t=this.getOffsetParent||Re,n=this.getDimensions,r=await n(e.floating);return{reference:be(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){const{width:t,height:n}=ue(e);return{width:t,height:n}},getScale:de,isElement:v,isRTL:function(e){return"rtl"===_(e).direction}};function ke(e,t,n,r){void 0===r&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:l="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:a=!1}=r,c=fe(e),u=o||i?[...c?L(c):[],...L(t)]:[];u.forEach((e=>{o&&e.addEventListener("scroll",n,{passive:!0}),i&&e.addEventListener("resize",n)}));const f=c&&s?function(e,t){let n,r=null;const o=g(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function l(s,a){void 0===s&&(s=!1),void 0===a&&(a=1),i();const{left:c,top:u,width:f,height:d}=e.getBoundingClientRect();if(s||t(),!f||!d)return;const m={rootMargin:-ne(u)+"px "+-ne(o.clientWidth-(c+f))+"px "+-ne(o.clientHeight-(u+d))+"px "+-ne(c)+"px",threshold:ee(0,Z(1,a))||1};let p=!0;function g(e){const t=e[0].intersectionRatio;if(t!==a){if(!p)return l();t?l(!1,t):n=setTimeout((()=>{l(!1,1e-7)}),1e3)}p=!1}try{r=new IntersectionObserver(g,{...m,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(g,m)}r.observe(e)}(!0),i}(c,n):null;let d,m=-1,p=null;l&&(p=new ResizeObserver((e=>{let[r]=e;r&&r.target===c&&p&&(p.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame((()=>{var e;null==(e=p)||e.observe(t)}))),n()})),c&&!a&&p.observe(c),p.observe(t));let h=a?ge(e):null;return a&&function t(){const r=ge(e);!h||r.x===h.x&&r.y===h.y&&r.width===h.width&&r.height===h.height||n(),h=r,d=requestAnimationFrame(t)}(),n(),()=>{var e;u.forEach((e=>{o&&e.removeEventListener("scroll",n),i&&e.removeEventListener("resize",n)})),null==f||f(),null==(e=p)||e.disconnect(),p=null,a&&cancelAnimationFrame(d)}}const _e=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:l,middlewareData:s}=t,a=await async function(e,t){const{placement:n,platform:r,elements:o}=e,i=await(null==r.isRTL?void 0:r.isRTL(o.floating)),l=ie(n),s=le(n),a="y"===se(n),c=["left","top"].includes(l)?-1:1,u=i&&a?-1:1,f=oe(t,e);let{mainAxis:d,crossAxis:m,alignmentAxis:p}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return s&&"number"==typeof p&&(m="end"===s?-1*p:p),a?{x:m*u,y:d*c}:{x:d*c,y:m*u}}(t,e);return l===(null==(n=s.offset)?void 0:n.placement)&&null!=(r=s.arrow)&&r.alignmentOffset?{}:{x:o+a.x,y:i+a.y,data:{...a,placement:l}}}}},Te=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:i,platform:l,elements:s}=t,{apply:a=()=>{},...c}=oe(e,t),u=await async function(e,t){var n;void 0===t&&(t={});const{x:r,y:o,platform:i,rects:l,elements:s,strategy:a}=e,{boundary:c="clippingAncestors",rootBoundary:u="viewport",elementContext:f="floating",altBoundary:d=!1,padding:m=0}=oe(t,e),p=function(e){return"number"!=typeof e?function(e){return{top:0,right:0,bottom:0,left:0,...e}}(e):{top:e,right:e,bottom:e,left:e}}(m),g=s[d?"floating"===f?"reference":"floating":f],h=ae(await i.getClippingRect({element:null==(n=await(null==i.isElement?void 0:i.isElement(g)))||n?g:g.contextElement||await(null==i.getDocumentElement?void 0:i.getDocumentElement(s.floating)),boundary:c,rootBoundary:u,strategy:a})),v="floating"===f?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,y=await(null==i.getOffsetParent?void 0:i.getOffsetParent(s.floating)),w=await(null==i.isElement?void 0:i.isElement(y))&&await(null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},b=ae(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:v,offsetParent:y,strategy:a}):v);return{top:(h.top-b.top+p.top)/w.y,bottom:(b.bottom-h.bottom+p.bottom)/w.y,left:(h.left-b.left+p.left)/w.x,right:(b.right-h.right+p.right)/w.x}}(t,c),f=ie(o),d=le(o),m="y"===se(o),{width:p,height:g}=i.floating;let h,v;"top"===f||"bottom"===f?(h=f,v=d===(await(null==l.isRTL?void 0:l.isRTL(s.floating))?"start":"end")?"left":"right"):(v=f,h="end"===d?"top":"bottom");const y=g-u.top-u.bottom,w=p-u.left-u.right,b=Z(g-u[h],y),E=Z(p-u[v],w),x=!t.middlewareData.shift;let R=b,S=E;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(S=w),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(R=y),x&&!d){const e=ee(u.left,0),t=ee(u.right,0),n=ee(u.top,0),r=ee(u.bottom,0);m?S=p-2*(0!==e||0!==t?e+t:ee(u.left,u.right)):R=g-2*(0!==n||0!==r?n+r:ee(u.top,u.bottom))}await a({...t,availableWidth:S,availableHeight:R});const k=await l.getDimensions(s.floating);return p!==k.width||g!==k.height?{reset:{rects:!0}}:{}}}},Ce=(e,t,n)=>{const r=new Map,o={platform:Se,...n},i={...o.platform,_c:r};return(async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,s=i.filter(Boolean),a=await(null==l.isRTL?void 0:l.isRTL(t));let c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:u,y:f}=ce(c,r,a),d=r,m={},p=0;for(let n=0;n<s.length;n++){const{name:i,fn:g}=s[n],{x:h,y:v,data:y,reset:w}=await g({x:u,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:m,rects:c,platform:l,elements:{reference:e,floating:t}});u=null!=h?h:u,f=null!=v?v:f,m={...m,[i]:{...m[i],...y}},w&&p<=50&&(p++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(c=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),({x:u,y:f}=ce(c,d,a))),n=-1)}return{x:u,y:f,placement:d,strategy:o,middlewareData:m}})(e,t,{...o,platform:i})};var Oe="undefined"!=typeof document?s.useLayoutEffect:s.useEffect;function Le(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;0!=r--;)if(!Le(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){const n=o[r];if(!("_owner"===n&&e.$$typeof||Le(e[n],t[n])))return!1}return!0}return e!=e&&t!=t}function Ae(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Pe(e,t){const n=Ae(e);return Math.round(t*n)/n}function $e(e){const t=s.useRef(e);return Oe((()=>{t.current=e})),t}const Me=(e,t)=>({..._e(e),options:[e,t]}),je=(e,t)=>({...Te(e),options:[e,t]}),Ie={...a},De=Ie.useInsertionEffect||(e=>e());function Ne(e){const t=s.useRef((()=>{}));return De((()=>{t.current=e})),s.useCallback((function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)}),[])}var Ve="undefined"!=typeof document?s.useLayoutEffect:s.useEffect;let Fe=!1,Be=0;const ze=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+Be++,We=Ie.useId||function(){const[e,t]=s.useState((()=>Fe?ze():void 0));return Ve((()=>{null==e&&t(ze())}),[]),s.useEffect((()=>{Fe=!0}),[]),e};const He=s.createContext(null),Ue=s.createContext(null),Ye=()=>{var e;return(null==(e=s.useContext(He))?void 0:e.id)||null},qe=()=>s.useContext(Ue);function Ke(e){return"data-floating-ui-"+e}function Xe(e){-1!==e.current&&(clearTimeout(e.current),e.current=-1)}function Ge(e){const t=(0,s.useRef)(e);return Ve((()=>{t.current=e})),t}const Je=Ke("safe-polygon");function Qe(e,t,n){return n&&!$(n)?0:"number"==typeof e?e:null==e?void 0:e[t]}function Ze(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,dataRef:o,events:i,elements:l}=e,{enabled:a=!0,delay:c=0,handleClose:u=null,mouseOnly:f=!1,restMs:d=0,move:m=!0}=t,p=qe(),g=Ye(),h=Ge(u),y=Ge(c),w=Ge(n),b=s.useRef(),E=s.useRef(-1),x=s.useRef(),R=s.useRef(-1),S=s.useRef(!0),k=s.useRef(!1),_=s.useRef((()=>{})),T=s.useRef(!1),C=s.useCallback((()=>{var e;const t=null==(e=o.current.openEvent)?void 0:e.type;return(null==t?void 0:t.includes("mouse"))&&"mousedown"!==t}),[o]);s.useEffect((()=>{if(a)return i.on("openchange",e),()=>{i.off("openchange",e)};function e(e){let{open:t}=e;t||(Xe(E),Xe(R),S.current=!0,T.current=!1)}}),[a,i]),s.useEffect((()=>{if(!a)return;if(!h.current)return;if(!n)return;function e(e){C()&&r(!1,e,"hover")}const t=M(l.floating).documentElement;return t.addEventListener("mouseleave",e),()=>{t.removeEventListener("mouseleave",e)}}),[l.floating,n,r,a,h,C]);const O=s.useCallback((function(e,t,n){void 0===t&&(t=!0),void 0===n&&(n="hover");const o=Qe(y.current,"close",b.current);o&&!x.current?(Xe(E),E.current=window.setTimeout((()=>r(!1,e,n)),o)):t&&(Xe(E),r(!1,e,n))}),[y,r]),L=Ne((()=>{_.current(),x.current=void 0})),A=Ne((()=>{if(k.current){const e=M(l.floating).body;e.style.pointerEvents="",e.removeAttribute(Je),k.current=!1}})),j=Ne((()=>!!o.current.openEvent&&["click","mousedown"].includes(o.current.openEvent.type)));s.useEffect((()=>{if(a&&v(l.domReference)){var e;const r=l.domReference;return n&&r.addEventListener("mouseleave",s),null==(e=l.floating)||e.addEventListener("mouseleave",s),m&&r.addEventListener("mousemove",t,{once:!0}),r.addEventListener("mouseenter",t),r.addEventListener("mouseleave",i),()=>{var e;n&&r.removeEventListener("mouseleave",s),null==(e=l.floating)||e.removeEventListener("mouseleave",s),m&&r.removeEventListener("mousemove",t),r.removeEventListener("mouseenter",t),r.removeEventListener("mouseleave",i)}}function t(e){if(Xe(E),S.current=!1,f&&!$(b.current)||d>0&&!Qe(y.current,"open"))return;const t=Qe(y.current,"open",b.current);t?E.current=window.setTimeout((()=>{w.current||r(!0,e,"hover")}),t):n||r(!0,e,"hover")}function i(e){if(j())return;_.current();const t=M(l.floating);if(Xe(R),T.current=!1,h.current&&o.current.floatingContext){n||Xe(E),x.current=h.current({...o.current.floatingContext,tree:p,x:e.clientX,y:e.clientY,onClose(){A(),L(),j()||O(e,!0,"safe-polygon")}});const r=x.current;return t.addEventListener("mousemove",r),void(_.current=()=>{t.removeEventListener("mousemove",r)})}("touch"!==b.current||!P(l.floating,e.relatedTarget))&&O(e)}function s(e){j()||o.current.floatingContext&&(null==h.current||h.current({...o.current.floatingContext,tree:p,x:e.clientX,y:e.clientY,onClose(){A(),L(),j()||O(e)}})(e))}}),[l,a,e,f,d,m,O,L,A,r,n,w,p,y,h,o,j]),Ve((()=>{var e;if(a&&n&&null!=(e=h.current)&&e.__options.blockPointerEvents&&C()){k.current=!0;const e=l.floating;if(v(l.domReference)&&e){var t;const n=M(l.floating).body;n.setAttribute(Je,"");const r=l.domReference,o=null==p||null==(t=p.nodesRef.current.find((e=>e.id===g)))||null==(t=t.context)?void 0:t.elements.floating;return o&&(o.style.pointerEvents=""),n.style.pointerEvents="none",r.style.pointerEvents="auto",e.style.pointerEvents="auto",()=>{n.style.pointerEvents="",r.style.pointerEvents="",e.style.pointerEvents=""}}}}),[a,n,g,l,p,h,C]),Ve((()=>{n||(b.current=void 0,T.current=!1,L(),A())}),[n,L,A]),s.useEffect((()=>()=>{L(),Xe(E),Xe(R),A()}),[a,l.domReference,L,A]);const I=s.useMemo((()=>{function e(e){b.current=e.pointerType}return{onPointerDown:e,onPointerEnter:e,onMouseMove(e){const{nativeEvent:t}=e;function o(){S.current||w.current||r(!0,t,"hover")}f&&!$(b.current)||n||0===d||T.current&&e.movementX**2+e.movementY**2<2||(Xe(R),"touch"===b.current?o():(T.current=!0,R.current=window.setTimeout(o,d)))}}}),[f,r,n,w,d]),D=s.useMemo((()=>({onMouseEnter(){Xe(E)},onMouseLeave(e){j()||O(e.nativeEvent,!1)}})),[O,j]);return s.useMemo((()=>a?{reference:I,floating:D}:{}),[a,I,D])}const et=()=>({getShadowRoot:!0,displayCheck:"function"==typeof ResizeObserver&&ResizeObserver.toString().includes("[native code]")?"full":"none"});function tt(e,t){const n=J(e,et());"prev"===t&&n.reverse();const r=n.indexOf(function(e){let t=e.activeElement;for(;null!=(null==(n=t)||null==(n=n.shadowRoot)?void 0:n.activeElement);){var n;t=t.shadowRoot.activeElement}return t}(M(e)));return n.slice(r+1)[0]}function nt(e,t){const n=t||e.currentTarget,r=e.relatedTarget;return!r||!P(n,r)}function rt(e){J(e,et()).forEach((e=>{e.dataset.tabindex=e.getAttribute("tabindex")||"",e.setAttribute("tabindex","-1")}))}function ot(e){e.querySelectorAll("[data-tabindex]").forEach((e=>{const t=e.dataset.tabindex;delete e.dataset.tabindex,t?e.setAttribute("tabindex",t):e.removeAttribute("tabindex")}))}const it={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"fixed",whiteSpace:"nowrap",width:"1px",top:0,left:0},lt=s.forwardRef((function(e,t){const[n,r]=s.useState();Ve((()=>{/apple/i.test(navigator.vendor)&&r("button")}),[]);const o={ref:t,tabIndex:0,role:n,"aria-hidden":!n||void 0,[Ke("focus-guard")]:"",style:it};return(0,j.jsx)("span",{...e,...o})})),st=s.createContext(null),at=Ke("portal");function ct(e){const{children:t,id:n,root:r,preserveTabOrder:o=!0}=e,i=function(e){void 0===e&&(e={});const{id:t,root:n}=e,r=We(),o=ut(),[i,l]=s.useState(null),a=s.useRef(null);return Ve((()=>()=>{null==i||i.remove(),queueMicrotask((()=>{a.current=null}))}),[i]),Ve((()=>{if(!r)return;if(a.current)return;const e=t?document.getElementById(t):null;if(!e)return;const n=document.createElement("div");n.id=r,n.setAttribute(at,""),e.appendChild(n),a.current=n,l(n)}),[t,r]),Ve((()=>{if(null===n)return;if(!r)return;if(a.current)return;let e=n||(null==o?void 0:o.portalNode);e&&!v(e)&&(e=e.current),e=e||document.body;let i=null;t&&(i=document.createElement("div"),i.id=t,e.appendChild(i));const s=document.createElement("div");s.id=r,s.setAttribute(at,""),e=i||e,e.appendChild(s),a.current=s,l(s)}),[t,n,r,o]),i}({id:n,root:r}),[l,a]=s.useState(null),c=s.useRef(null),u=s.useRef(null),f=s.useRef(null),d=s.useRef(null),m=null==l?void 0:l.modal,p=null==l?void 0:l.open,g=!!l&&!l.modal&&l.open&&o&&!(!r&&!i);return s.useEffect((()=>{if(i&&o&&!m)return i.addEventListener("focusin",e,!0),i.addEventListener("focusout",e,!0),()=>{i.removeEventListener("focusin",e,!0),i.removeEventListener("focusout",e,!0)};function e(e){i&&nt(e)&&("focusin"===e.type?ot:rt)(i)}}),[i,o,m]),s.useEffect((()=>{i&&(p||ot(i))}),[p,i]),(0,j.jsxs)(st.Provider,{value:s.useMemo((()=>({preserveTabOrder:o,beforeOutsideRef:c,afterOutsideRef:u,beforeInsideRef:f,afterInsideRef:d,portalNode:i,setFocusManagerState:a})),[o,i]),children:[g&&i&&(0,j.jsx)(lt,{"data-type":"outside",ref:c,onFocus:e=>{if(nt(e,i)){var t;null==(t=f.current)||t.focus()}else{const e=tt(M(n=l?l.domReference:null).body,"prev")||n;null==e||e.focus()}var n}}),g&&i&&(0,j.jsx)("span",{"aria-owns":i.id,style:it}),i&&Q.createPortal(t,i),g&&i&&(0,j.jsx)(lt,{"data-type":"outside",ref:u,onFocus:e=>{if(nt(e,i)){var t;null==(t=d.current)||t.focus()}else{const t=tt(M(n=l?l.domReference:null).body,"next")||n;null==t||t.focus(),(null==l?void 0:l.closeOnFocusOut)&&(null==l||l.onOpenChange(!1,e.nativeEvent,"focus-out"))}var n}})]})}const ut=()=>s.useContext(st),ft="data-floating-ui-focusable";function dt(e){void 0===e&&(e={});const{nodeId:t}=e,n=function(e){const{open:t=!1,onOpenChange:n,elements:r}=e,o=We(),i=s.useRef({}),[l]=s.useState((()=>function(){const e=new Map;return{emit(t,n){var r;null==(r=e.get(t))||r.forEach((e=>e(n)))},on(t,n){e.set(t,[...e.get(t)||[],n])},off(t,n){var r;e.set(t,(null==(r=e.get(t))?void 0:r.filter((e=>e!==n)))||[])}}}())),a=null!=Ye(),[c,u]=s.useState(r.reference),f=Ne(((e,t,r)=>{i.current.openEvent=e?t:void 0,l.emit("openchange",{open:e,event:t,reason:r,nested:a}),null==n||n(e,t,r)})),d=s.useMemo((()=>({setPositionReference:u})),[]),m=s.useMemo((()=>({reference:c||r.reference||null,floating:r.floating||null,domReference:r.reference})),[c,r.reference,r.floating]);return s.useMemo((()=>({dataRef:i,open:t,onOpenChange:f,elements:m,events:l,floatingId:o,refs:d})),[t,f,m,l,o,d])}({...e,elements:{reference:null,floating:null,...e.elements}}),r=e.rootContext||n,o=r.elements,[i,l]=s.useState(null),[a,c]=s.useState(null),u=(null==o?void 0:o.domReference)||i,f=s.useRef(null),d=qe();Ve((()=>{u&&(f.current=u)}),[u]);const m=function(e){void 0===e&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:l}={},transform:a=!0,whileElementsMounted:c,open:u}=e,[f,d]=s.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[m,p]=s.useState(r);Le(m,r)||p(r);const[g,h]=s.useState(null),[v,y]=s.useState(null),w=s.useCallback((e=>{e!==R.current&&(R.current=e,h(e))}),[]),b=s.useCallback((e=>{e!==S.current&&(S.current=e,y(e))}),[]),E=i||g,x=l||v,R=s.useRef(null),S=s.useRef(null),k=s.useRef(f),_=null!=c,T=$e(c),C=$e(o),O=$e(u),L=s.useCallback((()=>{if(!R.current||!S.current)return;const e={placement:t,strategy:n,middleware:m};C.current&&(e.platform=C.current),Ce(R.current,S.current,e).then((e=>{const t={...e,isPositioned:!1!==O.current};A.current&&!Le(k.current,t)&&(k.current=t,Q.flushSync((()=>{d(t)})))}))}),[m,t,n,C,O]);Oe((()=>{!1===u&&k.current.isPositioned&&(k.current.isPositioned=!1,d((e=>({...e,isPositioned:!1}))))}),[u]);const A=s.useRef(!1);Oe((()=>(A.current=!0,()=>{A.current=!1})),[]),Oe((()=>{if(E&&(R.current=E),x&&(S.current=x),E&&x){if(T.current)return T.current(E,x,L);L()}}),[E,x,L,T,_]);const P=s.useMemo((()=>({reference:R,floating:S,setReference:w,setFloating:b})),[w,b]),$=s.useMemo((()=>({reference:E,floating:x})),[E,x]),M=s.useMemo((()=>{const e={position:n,left:0,top:0};if(!$.floating)return e;const t=Pe($.floating,f.x),r=Pe($.floating,f.y);return a?{...e,transform:"translate("+t+"px, "+r+"px)",...Ae($.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}}),[n,a,$.floating,f.x,f.y]);return s.useMemo((()=>({...f,update:L,refs:P,elements:$,floatingStyles:M})),[f,L,P,$,M])}({...e,elements:{...o,...a&&{reference:a}}}),p=s.useCallback((e=>{const t=v(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),getClientRects:()=>e.getClientRects(),contextElement:e}:e;c(t),m.refs.setReference(t)}),[m.refs]),g=s.useCallback((e=>{(v(e)||null===e)&&(f.current=e,l(e)),(v(m.refs.reference.current)||null===m.refs.reference.current||null!==e&&!v(e))&&m.refs.setReference(e)}),[m.refs]),h=s.useMemo((()=>({...m.refs,setReference:g,setPositionReference:p,domReference:f})),[m.refs,g,p]),y=s.useMemo((()=>({...m.elements,domReference:u})),[m.elements,u]),w=s.useMemo((()=>({...m,...r,refs:h,elements:y,nodeId:t})),[m,h,y,t,r]);return Ve((()=>{r.dataRef.current.floatingContext=w;const e=null==d?void 0:d.nodesRef.current.find((e=>e.id===t));e&&(e.context=w)})),s.useMemo((()=>({...m,context:w,refs:h,elements:y})),[m,h,y,w])}const mt="active",pt="selected";function gt(e,t,n){const r=new Map,o="item"===n;let i=e;if(o&&e){const{[mt]:t,[pt]:n,...r}=e;i=r}return{..."floating"===n&&{tabIndex:-1,[ft]:""},...i,...t.map((t=>{const r=t?t[n]:null;return"function"==typeof r?e?r(e):null:r})).concat(e).reduce(((e,t)=>t?(Object.entries(t).forEach((t=>{let[n,i]=t;var l;o&&[mt,pt].includes(n)||(0===n.indexOf("on")?(r.has(n)||r.set(n,[]),"function"==typeof i&&(null==(l=r.get(n))||l.push(i),e[n]=function(){for(var e,t=arguments.length,o=new Array(t),i=0;i<t;i++)o[i]=arguments[i];return null==(e=r.get(n))?void 0:e.map((e=>e(...o))).find((e=>void 0!==e))})):e[n]=i)})),e):e),{})}}var ht=window.elementorV2.editorStylesRepository,vt=window.elementorV2.editorStyles,yt=window.elementorV2.editorProps,wt=window.elementorV2.editorResponsive,bt=window.elementorV2.utils,Et=window.elementorV2.wpMedia,xt=window.elementorV2.twing,Rt=window.elementorV2.editorNotifications,St=window.wp.i18n;var kt="elementor-preview-responsive-wrapper",_t=(0,f.styled)(f.Box,{shouldForwardProp:e=>"isSelected"!==e&&"isSmallerOffset"!==e})((({theme:e,isSelected:t,isSmallerOffset:n})=>({outline:`${t?"2px":"1px"} solid ${e.palette.primary.light}`,outlineOffset:t&&!n?"-2px":"-1px",pointerEvents:"none"})));function Tt({element:e,isSelected:t,id:n}){const{context:r,floating:o,isVisible:i}=function({element:e,isSelected:t}){const[n,r]=(0,s.useState)(!1),{refs:o,floatingStyles:i,context:l}=dt({open:n||t,onOpenChange:r,whileElementsMounted:ke,middleware:[je({apply({elements:e,rects:t}){Object.assign(e.floating.style,{width:`${t.reference.width+2}px`,height:`${t.reference.height+2}px`})}}),Me((({rects:e})=>-e.reference.height/2-e.floating.height/2))]});return(0,s.useEffect)((()=>{o.setReference(e)}),[e,o]),{isVisible:n||t,context:l,floating:{setRef:o.setFloating,ref:o.floating,styles:i}}}({element:e,isSelected:t}),{getFloatingProps:l,getReferenceProps:a}=function(e){void 0===e&&(e=[]);const t=e.map((e=>null==e?void 0:e.reference)),n=e.map((e=>null==e?void 0:e.floating)),r=e.map((e=>null==e?void 0:e.item)),o=s.useCallback((t=>gt(t,e,"reference")),t),i=s.useCallback((t=>gt(t,e,"floating")),n),l=s.useCallback((t=>gt(t,e,"item")),r);return s.useMemo((()=>({getReferenceProps:o,getFloatingProps:i,getItemProps:l})),[o,i,l])}([Ze(r)]);!function(e,t){(0,s.useEffect)((()=>{const n=e,{events:r,attrs:o}=function(e){const t=/^on(?=[A-Z])/;return Object.entries(e).reduce(((e,[n,r])=>{if(!t.test(n))return e.attrs.push([n,r]),e;const o=n.replace(t,"").toLowerCase(),i=r;return e.events.push([o,i]),e}),{events:[],attrs:[]})}(t());return r.forEach((([e,t])=>n.addEventListener(e,t))),o.forEach((([e,t])=>n.setAttribute(e,t))),()=>{r.forEach((([e,t])=>n.removeEventListener(e,t))),o.forEach((([e])=>n.removeAttribute(e)))}}),[t,e])}(e,a);const c=e.offsetHeight<=1;return i&&s.createElement(ct,{id:kt},s.createElement(_t,{ref:o.setRef,isSelected:t,style:o.styles,"data-element-overlay":n,role:"presentation",isSmallerOffset:c,...l()}))}function Ct(){const e=(0,c.useSelectedElement)(),t=(0,u.__privateUseListenTo)([(0,u.windowEvent)("elementor/editor/element-rendered"),(0,u.windowEvent)("elementor/editor/element-destroyed")],(()=>(0,c.getElements)().filter((e=>Ot in(e.view?.el?.dataset??{}))).map((e=>[e.id,e.view?.getDomElement?.()?.get?.(0)])).filter((e=>!!e[1])))),n="edit"===(0,u.useEditMode)(),r=(0,u.__privateUseIsRouteActive)("panel/global");return n&&!r&&t.map((([t,n])=>s.createElement(Tt,{key:t,id:t,element:n,isSelected:e.element?.id===t})))}var Ot="atomic";function Lt(){const e=window;return e.elementor?.$preview?.[0]?.contentDocument}var At="data-e-removed",Pt="data-elementor-id",$t="elementor-post-",Mt="-css";function jt(e){const t=[...e.attributes].map((e=>[e.name,e.value]));return Object.fromEntries(t)}function It(e,t=[]){return{then:n=>(t.push(n),It(e,t)),execute:async()=>{let n;for(const r of t){if(e.aborted)break;n=await r(n,e)}}}}var Dt=e=>!!e&&"object"==typeof e&&"$$multi-props"in e&&!0===e["$$multi-props"],Nt=e=>({"$$multi-props":!0,value:e}),Vt=e=>e.value,Ft=3;function Bt({transformers:e,schema:t,onPropResolve:n}){async function r({props:e,schema:r,signal:i}){r=r??t;const l=Promise.all(Object.entries(r).map((async([t,r])=>{const l=e[t]??r.default,s=await o({value:l,key:t,type:r,signal:i});return n?.({key:t,value:s}),Dt(s)?Vt(s):{[t]:s}})));return Object.assign({},...(await l).filter(Boolean))}async function o({value:t,key:n,type:i,signal:l,depth:s=0}){if(null==t)return null;if(!(0,yt.isTransformable)(t))return t;if(s>Ft)return null;if(!0===t.disabled)return null;if("union"===i.kind&&!(i=i.prop_types[t.$$type]))return null;if(t.$$type!==i.key)return null;let a=t.value;"object"===i.kind&&(a=await r({props:a,schema:i.shape,signal:l})),"array"===i.kind&&(a=await Promise.all(a.map((e=>o({value:e,key:n,type:i.item_prop_type,depth:s,signal:l})))));const c=e.get(t.$$type);if(!c)return null;try{return o({value:await c(a,{key:n,signal:l}),key:n,type:i,signal:l,depth:s+1})}catch{return null}}return r}function zt(){const e={};let t=null;return{register(t,n){return e[t]=n,this},registerFallback(e){return t=e,this},get(n){return e[n]??t},all(){return{...e}}}}var Wt=zt(),Ht=(e,t="preview")=>{const n=window;return n.elementor?.helpers?.enqueueFont?.(e,t)??null},Ut=(0,bt.createError)({code:"unknown_style_type",message:"Unknown style type"}),Yt={class:"."};function qt({resolve:e,breakpoints:t,selectorPrefix:n=""}){return async({styles:r,signal:o})=>{const i=r.map((async r=>{const i=Object.values(r.variants).map((async i=>{const l=await async function({props:e,resolve:t,signal:n}){const r=await t({props:e,signal:n});return Object.entries(r).reduce(((e,[t,n])=>(null===n||e.push(t+":"+n+";"),e)),[]).join("")}({props:i.props,resolve:e,signal:o});return Kt().for(r.cssName,r.type).withPrefix(n).withState(i.meta.state).withMediaQuery(i.meta.breakpoint?t[i.meta.breakpoint]:null).wrap(l)})),l=await Promise.all(i);return{id:r.id,value:l.join("")}}));return await Promise.all(i)}}function Kt(e="",t){return{for:(n,r)=>{const o=Yt[r];if(!o)throw new Ut({context:{type:r}});return Kt(`${e}${o}${n}`,t)},withPrefix:n=>Kt([n,e].filter(Boolean).join(" "),t),withState:n=>Kt(n?`${e}:${n}`:e,t),withMediaQuery:n=>{if(!n?.type)return Kt(e,t);const r=`${n.type}:${n.width}px`;return Kt(e,(e=>`@media(${r}){${e}}`))},wrap:n=>{const r=`${e}{${n}}`;return t?t(r):r}}}var Xt=".elementor";function Gt(){const e=function(e){const t=(0,wt.useBreakpointsMap)();return(0,s.useMemo)((()=>qt({selectorPrefix:Xt,breakpoints:t,resolve:e})),[e,t])}((0,s.useMemo)((()=>Bt({transformers:Wt,schema:(0,vt.getStylesSchema)(),onPropResolve:({key:e,value:t})=>{"font-family"===e&&"string"==typeof t&&Ht(t)}})),[])),[t,n]=(0,s.useState)({}),r=(0,s.useMemo)((()=>ht.stylesRepository.getProviders().map((t=>({provider:t,subscriber:Jt({provider:t,renderStyles:e,setStyleItems:n})})))),[e]);return(0,s.useEffect)((()=>{const e=r.map((({provider:e,subscriber:t})=>e.subscribe(t)));return()=>{e.forEach((e=>e()))}}),[r]),function(){const e=(0,s.useRef)(!1);(0,s.useEffect)((()=>{e.current||(e.current=!0,(0,u.registerDataHook)("after","editor/documents/attach-preview",(async()=>{const e=r.map((async({subscriber:e})=>e()));await Promise.all(e)})))}),[])}(),Object.values(t).sort((({provider:e},{provider:t})=>e.priority-t.priority)).flatMap((({items:e})=>e))}function Jt({provider:e,renderStyles:t,setStyleItems:n}){return function(){let r=null;return(...o)=>(r&&r.abort(),r=new AbortController,(r=>It(r.signal).then(((n,r)=>{const o=e.actions.all().map(((t,n,r)=>{const o=r[r.length-1-n];return{...o,cssName:e.actions.resolveCssName(o.id)}}));return t({styles:o,signal:r})})).then((t=>{n((n=>({...n,[e.getKey()]:{provider:e,items:t}})))})).execute())(r,...o))}()}function Qt(){const e=(0,u.__privateUseListenTo)((0,u.commandEndEvent)("editor/documents/attach-preview"),(()=>Lt()?.head)),t=Gt(),n=(0,u.__privateUseListenTo)((0,u.commandEndEvent)("editor/documents/attach-preview"),(()=>{const e=Lt();if(!e)return[];const t=function(e){return[...e.body.querySelectorAll(`[${Pt}]`)??[]].map((e=>e.getAttribute(Pt)||""))}(e).map((e=>`${$t}${e}${Mt}`)),n=function(e){return[...e.head.querySelectorAll(`link[rel="stylesheet"][id^=${$t}][id$=${Mt}]`)??[]]}(e).filter((e=>t.includes(e.getAttribute("id")??"")));return n.forEach((e=>{e.hasAttribute(At)||e.remove()})),n.map((e=>({...jt(e),id:e.getAttribute("id")??"",[At]:!0})))}));return e?s.createElement(f.Portal,{container:e},t.map((e=>s.createElement("style",{"data-e-style-id":e.id,key:e.id},e.value))),n.map((e=>s.createElement("link",{...e,key:e.id})))):null}var Zt=zt();function en(e){return e}var tn=({destination:e,isTargetBlank:t})=>({href:"number"==typeof e?"#post-id-"+e:e,target:t?"_blank":"_self"}),nn=e=>({id:e.id??null,url:e.url??null}),rn=async e=>{const{src:t,size:n}=e;if(!t?.id)return t?.url?{src:t.url}:null;const r=await(0,Et.getMediaAttachment)({id:t.id}),o=r?.sizes?.[n??""];return o?{src:o.url,height:o.height,width:o.width}:r?{src:r.url,height:r.height,width:r.width}:null},on=e=>e,ln=e=>{const{color:t=null}=e;return t?`linear-gradient(${t}, ${t})`:null},sn=e=>"radial"===e.type?`radial-gradient(circle at ${e.positions}, ${e.stops})`:`linear-gradient(${e.angle}deg, ${e.stops})`,an=e=>{const{image:t,size:n=null,position:r=null,repeat:o=null,attachment:i=null}=e;return t?{src:t.src?`url(${t.src})`:null,repeat:o,attachment:i,size:n,position:r}:null},cn=({width:e,height:t})=>`${e??"auto"} ${t??"auto"}`,un=e=>{if(!e||0===e.length)return null;const t=function(e){const t=(0,u.isExperimentActive)("e_v_3_30"),n=e.map((e=>"string"==typeof e?{src:e,repeat:null,attachment:null,size:null,position:null}:e));return t?n.filter((e=>e&&!!e.src)):n}(e);return 0===t.length?null:{"background-image":fn(t,"src","none",!0),"background-repeat":fn(t,"repeat","repeat"),"background-attachment":fn(t,"attachment","scroll"),"background-size":fn(t,"size","auto auto"),"background-position":fn(t,"position","0% 0%")}};function fn(e,t,n,r=!1){const o=(0,u.isExperimentActive)("e_v_3_30");if(0===e.filter((e=>e?.[t])).length)return o?n:null;const i=e.map((e=>e[t]??n));return!r&&i.every((e=>e===i[0]))?i[0]:i.join(",")}var dn=e=>{const{color:t=null,"background-overlay":n=null}=e;return Nt({...n,"background-color":t})},mn=e=>`${e?.color} ${e?.offset??0}%`,pn=e=>t=>t.filter(Boolean).join(e),gn=(e,t)=>(n,{key:r})=>{const o=e.filter((e=>n[e])).map((e=>[t({propKey:r,key:e}),n[e]]));return Nt(Object.fromEntries(o))},hn=e=>e?.length<1?null:e.map(vn).join(" "),vn=e=>"radius"in e?`blur(${e.radius})`:"amount"in e?`brightness(${e.amount})`:"",yn=({x:e,y:t})=>`${e??"0px"} ${t??"0px"}`,wn=e=>[e.hOffset,e.vOffset,e.blur,e.spread,e.color,e.position].filter(Boolean).join(" "),bn=e=>"custom"===e.unit?e.size:`${e.size}${e.unit}`,En=e=>{const t={"-webkit-text-stroke":`${e.width} ${e.color}`,stroke:`${e.color}`,"stroke-width":`${e.width}`};return Nt(t)},xn=e=>`translate3d(${e.x}, ${e.y}, ${e.z})`,Rn=e=>e?.length<1?null:e.join(" ");function Sn(e){return["a","article","aside","button","div","footer","h1","h2","h3","h4","h5","h6","header","main","nav","p","section","span"].includes(e)?e:"div"}function kn(e){const t=["http:","https:","mailto:","tel:"];try{const n=new URL(e);return t.includes(n.protocol)?e:""}catch{return""}}function _n(){const e=window;return class extends e.elementor.modules.elements.views.Widget{onRender(...e){super.onRender(...e),this.#e("elementor/preview/atomic-widget/render"),this.#t("elementor/element/render")}onDestroy(...e){super.onDestroy(...e),this.#e("elementor/preview/atomic-widget/destroy"),this.#t("elementor/element/destroy")}attributes(){return{...super.attributes(),"data-atomic":"",style:"display: contents !important;"}}behaviors(){const e=["InlineEditing","Draggable","Resizable"],t=Object.entries(super.behaviors()).filter((([t])=>!e.includes(t)));return Object.fromEntries(t)}getDomElement(){return this.$el.find(":first-child")}getHandlesOverlay(){return null}#e(e){window.top?.dispatchEvent(new CustomEvent(e,{detail:{id:this.model.get("id")}}))}#t(t){e.elementor?.$preview?.[0]?.contentWindow.dispatchEvent(new CustomEvent(t,{detail:{id:this.model.get("id"),type:this.model.get("widgetType"),element:this.getDomElement().get(0)}}))}getContextMenuGroups(){return super.getContextMenuGroups().filter((e=>"save"!==e.name))}}}function Tn(){(0,u.__privateListenTo)((0,u.v1ReadyEvent)(),(()=>{const e=(0,c.getWidgetsCache)()??{},t=window,n=function(){const e=(0,xt.createArrayLoader)({}),t=(0,xt.createEnvironment)(e);return t.registerEscapingStrategy(Sn,"html_tag"),t.registerEscapingStrategy(kn,"full_url"),{register:e.setTemplate,render:t.render}}();Object.entries(e).forEach((([e,r])=>{if(!r.atomic)return;const o=function(e){return!!(e.atomic_props_schema&&e.twig_templates&&e.twig_main_template&&e.base_styles_dictionary)}(r)?function({type:e,renderer:t,element:n}){const r=window;Object.entries(n.twig_templates).forEach((([e,n])=>{t.register(e,n)}));const o=Bt({transformers:Zt,schema:n.atomic_props_schema});return class extends r.elementor.modules.elements.types.Widget{getType(){return e}getView(){return function({type:e,renderer:t,propsResolver:n,templateKey:r,baseStylesDictionary:o}){const i=_n();return class extends i{#n=null;getTemplateType(){return"twig"}renderOnChange(){this.render()}async _renderTemplate(){this.#r(),this.#n?.abort(),this.#n=new AbortController;const i=It(this.#n.signal).then(((e,t)=>{const r=this.model.get("settings").toJSON();return n({props:r,signal:t})})).then((n=>{const i={id:this.model.get("id"),type:e,settings:n,base_styles:o};return t.render(r,i)})).then((e=>this.$el.html(e)));await i.execute(),this.#o()}#r(){this.triggerMethod("before:render:template")}#o(){this.bindUIElements(),this.triggerMethod("render:template")}}}({type:e,renderer:t,propsResolver:o,baseStylesDictionary:n.base_styles_dictionary,templateKey:n.twig_main_template})}}}({type:e,renderer:n,element:r}):function(e){const t=window;return class extends t.elementor.modules.elements.types.Widget{getType(){return e}getView(){return _n()}}}(e);t.elementor.elementsManager.registerElementType(new o)}))}))}var Cn={href:"https://go.elementor.com/element-link-inside-link-infotip",target:"_blank",color:"inherit",variant:"text",sx:{marginInlineStart:"20px"},children:"Learn more"};function On(e){const{containers:t=[e.container],storageType:n}=e,r=t;if("localstorage"!==n)return!1;const o=window?.elementorCommon?.storage?.get();if(!o?.clipboard?.elements)return!1;const i=o.clipboard.elements,l={type:"default",message:(0,St.__)("To paste a link to this element, first remove the link from it's parent container.","elementor"),id:"paste-in-link-blocked",additionalActionProps:[Cn]},s=An(i,r);return s&&(0,Rt.notify)(l),s}function Ln(e){const{containers:t=[e.container],target:n}=e,r=t,o=n,i={type:"default",message:(0,St.__)("To drag a link to this element, first remove the link from it's parent container.","elementor"),id:"move-in-link-blocked",additionalActionProps:[Cn]},l=An(r,[o]);return l&&(0,Rt.notify)(i),l}function An(e,t){return!(!e?.length||!t?.length)&&(!!e.some((e=>!!e?.id&&((0,c.isElementAnchored)(e.id)||!!(0,c.getAnchoredDescendantId)(e.id))))&&t.some((e=>!!e?.id&&((0,c.isElementAnchored)(e.id)||!!(0,c.getAnchoredAncestorId)(e.id)))))}function Pn(e){const{containers:t=[e.container]}=e;return t.some($n)}function $n(e){return!!e&&Boolean(jn(e))}function Mn(e){const t=jn(e);if(!t)return null;const[n]=Object.entries(t).find((([,e])=>"plain"===e.kind&&e.key===yt.CLASSES_PROP_KEY))??[];return n??null}function jn(e){const t=e?.model.get("widgetType")||e?.model.get("elType"),n=(0,c.getWidgetsCache)(),r=n?.[t];return r?.atomic_props_schema??null}function In(e){return e.length>1?(0,St.__)("Elements","elementor"):(0,c.getElementLabel)(e[0].id)}var Dn=()=>(0,u.undoable)({do:({containers:e,newStyle:t})=>e.map((e=>{const n=e.id,r=Mn(e);if(!r)return null;const o=(0,c.getElementStyles)(e.id),[i,l]=Object.entries(o??{})[0]??[],s=Object.keys(l??{}).length?l:null,a={styleId:i,originalStyle:s};if(i)t.variants.forEach((({meta:e,props:t})=>{(0,c.updateElementStyle)({elementId:n,styleId:i,meta:e,props:t})}));else{const[e]=t.variants,o=t.variants.slice(1);a.styleId=(0,c.createElementStyle)({elementId:n,classesProp:r,label:ht.ELEMENTS_STYLES_RESERVED_LABEL,...e,additionalVariants:o})}return a})),undo:({containers:e},t)=>{e.forEach(((e,n)=>{const r=t[n];if(!r)return;if(!r.originalStyle)return void(0,c.deleteElementStyle)(e.id,r.styleId);const o=Mn(e);if(!o)return;const[i]=r.originalStyle.variants,l=r.originalStyle.variants.slice(1);(0,c.createElementStyle)({elementId:e.id,classesProp:o,label:ht.ELEMENTS_STYLES_RESERVED_LABEL,styleId:r.styleId,...i,additionalVariants:l})}))}},{title:({containers:e})=>In(e),subtitle:(0,St.__)("Style Pasted","elementor")});var Nn=()=>(0,u.undoable)({do:({containers:e})=>e.map((e=>{const t=e.model.get("id"),n=(0,c.getElementStyles)(t);return Object.keys(n??{}).forEach((e=>(0,c.deleteElementStyle)(t,e))),n})),undo:({containers:e},t)=>{e.forEach(((e,n)=>{const r=Mn(e);if(!r)return;const o=e.model.get("id"),i=t[n];Object.entries(i??{}).forEach((([e,t])=>{const[n]=t.variants,i=t.variants.slice(1);(0,c.createElementStyle)({elementId:o,classesProp:r,styleId:e,label:ht.ELEMENTS_STYLES_RESERVED_LABEL,...n,additionalVariants:i})}))}))}},{title:({containers:e})=>In(e),subtitle:(0,St.__)("Style Reset","elementor")});function Vn(){Wt.register("size",bn).register("shadow",wn).register("stroke",En).register("dimensions",gn(["block-start","block-end","inline-start","inline-end"],(({propKey:e,key:t})=>`${e}-${t}`))).register("filter",hn).register("box-shadow",pn(",")).register("background",dn).register("background-overlay",un).register("background-color-overlay",ln).register("background-image-overlay",an).register("background-gradient-overlay",sn).register("gradient-color-stop",pn(",")).register("color-stop",mn).register("background-image-position-offset",yn).register("background-image-size-scale",cn).register("image-src",nn).register("image",rn).register("object-position",yn).register("transform-move",xn).register("transform",Rn).register("layout-direction",gn(["row","column"],(({propKey:e,key:t})=>`${t}-${e}`))).register("border-width",gn(["block-start","block-end","inline-start","inline-end"],(({key:e})=>`border-${e}-width`))).register("border-radius",gn(["start-start","start-end","end-start","end-end"],(({key:e})=>`border-${e}-radius`))).registerFallback(on),function(){const e=Dn();(0,u.blockCommand)({command:"document/elements/paste-style",condition:Pn}),(0,u.__privateListenTo)((0,u.commandStartEvent)("document/elements/paste-style"),(t=>function(e,t){const{containers:n=[e.container],storageKey:r}=e,o=function(e="clipboard"){try{const t=JSON.parse(localStorage.getItem("elementor")??"{}");return t[e]?.elements}catch{return}}(r),[i]=o??[];if(!i)return;const l=i.styles,s=Object.values(l??{})[0];if(!s)return;const a=n.filter($n);a.length&&t({containers:a,newStyle:s})}(t.args,e)))}(),function(){const e=Nn();(0,u.blockCommand)({command:"document/elements/reset-style",condition:Pn}),(0,u.__privateListenTo)((0,u.commandStartEvent)("document/elements/reset-style"),(t=>function(e,t){const{containers:n=[e.container]}=e,r=n.filter($n);r.length&&t({containers:r})}(t.args,e)))}(),(0,u.blockCommand)({command:"document/elements/paste",condition:On}),(0,u.blockCommand)({command:"document/elements/move",condition:Ln}),Tn(),Zt.register("classes",function(){const e=new Map;return t=>t.map((t=>(e.has(t)||e.set(t,function(e){const t=ht.stylesRepository.getProviders().find((t=>t.actions.all().find((t=>t.id===e))));return t?t.actions.resolveCssName(e):e}(t)),e.get(t)))).filter(Boolean)}()).register("link",tn).register("image",rn).register("image-src",nn).registerFallback(on),(0,l.injectIntoTop)({id:"elements-overlays",component:Ct}),(0,l.injectIntoTop)({id:"canvas-style-render",component:Qt})}(window.elementorV2=window.elementorV2||{}).editorCanvas=i}(),window.elementorV2.editorCanvas?.init?.();