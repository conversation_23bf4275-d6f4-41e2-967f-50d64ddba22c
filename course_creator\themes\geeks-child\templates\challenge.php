<?php

function shortcode_enroll_challenge_form($atts) {
    $atts = shortcode_atts(array(
        'id' => '',
        'redirect_url' => ''
    ), $atts);
    
    $course_id = intval($atts['id']);
    $redirect_url = esc_url($atts['redirect_url']);
    
    if (!$redirect_url) {
        return;
    }

    if (!$course_id) {
        return '<p>Curso inválido.</p>';
    }

    $current_user_id = get_current_user_id();

    if (!$current_user_id) {
        return '<p>Por favor, faça login para se inscrever neste desafio.</p>';
    }

    $args = array(
        'post_type'   => 'tutor_enrolled',
        'post_parent' => $course_id,
        'post_status' => 'completed',
        'author'      => $current_user_id,
        'fields'      => 'ids'
    );

    $enrolled_query = new WP_Query($args);

    if ($enrolled_query->have_posts()) {
        ob_start();
        ?>
        <a href="<?php echo esc_url($redirect_url); ?>" class="btn btn-primary tutor-btn-lg tutor-btn-full w-100 tutor-enroll-course-button">
            Iniciar Desafio
        </a>
        <?php
        return ob_get_clean();
    }

    ob_start();
    ?>
    <form class="tutor-enrol-course-form" method="post">
        <?php wp_nonce_field(tutor()->nonce_action, tutor()->nonce); ?>
        <input type="hidden" name="tutor_course_id" value="<?php echo esc_attr($course_id); ?>">
        <input type="hidden" name="tutor_course_action" value="_tutor_course_enroll_now">
        <button type="submit" class="btn btn-primary tutor-btn-lg tutor-btn-full w-100 tutor-enroll-course-button">
            Cadastrar-se no Desafio
        </button>
    </form>
    <?php
    return ob_get_clean();
}

add_shortcode('enroll_challenge_form', 'shortcode_enroll_challenge_form');