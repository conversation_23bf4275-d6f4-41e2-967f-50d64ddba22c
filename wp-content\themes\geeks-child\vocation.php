<?php
function shortcode_selecao_caminho($atts) {
    global $wpdb;
    $user_id = get_current_user_id();
    if (!$user_id) return '<p>Você precisa estar logado para escolher um caminho.</p>';

    $atts = shortcode_atts(array(
        'preselected' => '',
        'quiz_id' => ''
    ), $atts);

    if (!empty($atts['quiz_id'])) {
        
        $quiz_id = $atts['quiz_id'];
        $latest_result = $wpdb->get_var($wpdb->prepare(
            "SELECT quiz_results FROM wp_mlw_results WHERE user = %d AND quiz_id = %d ORDER BY result_id DESC LIMIT 1",
            $user_id, $quiz_id
        ));
        

        if (!empty($latest_result)) {
            $results = unserialize($latest_result);
            $questions = $results[1];

            $vocacao_scores = ['designer' => 0, 'editor' => 0, 'programador' => 0, 'trafego' => 0, 'wordpress' => 0];
            $category_map = [52 => 'trafego', 53 => 'editor', 49 => 'programador', 50 => 'designer', 77 => 'wordpress'];
            
            foreach ($questions as $question) {
                // Verifica se há categorias múltiplas e se a categoria está mapeada
                if (isset($question['multicategories'][0]) && array_key_exists($question['multicategories'][0], $category_map)) {
                    // Obtem o primeiro elemento do array multicategories como a categoria
                    $category = $category_map[$question['multicategories'][0]];
                    // Soma os pontos à categoria correspondente
                    $vocacao_scores[$category] += intval($question['points']);
                }
            }
            $max_score = max($vocacao_scores);
            $highest_vocacoes = array_keys($vocacao_scores, $max_score);

            if (count($highest_vocacoes) > 1) {
                $highest_vocacao = "empate";
            } else {
                $highest_vocacao = $highest_vocacoes[0];
            }
            update_user_meta($user_id, 'vocacao_inicial', $highest_vocacao);
        }
    }

    $vocacao = get_user_meta($user_id, 'vocacao_inicial', true);
    $preselected = $atts['preselected'];
    
    $form = "<h2>Selecione o caminho que você deseja seguir</h2>";
   if (!empty($vocacao)) {
        // Textos introdutórios
		$intro_texts = array(
    'programador' => 'Você demonstrou grande interesse e habilidade em resolver problemas complexos e analisar informações em detalhes. Isso indica que você tem uma mente analítica privilegiada, essencial para a área de programação. Nesta formação você aprenderá a desenvolver soluções digitais poderosas e inovadoras através da lógica de programação.',
    'designer' => 'Ficou claro que você tem uma mente criativa e gosta de expressá-la através de layouts, imagens e explorando novas combinações visuais. Seu senso estético e inventividade brilham no universo do design. Nesta formação, você desenvolverá seu domínio sobre composições gráficas e criará projetos incríveis.',
    'trafego' => 'Você demonstrou ter facilidade em persuadir e vender ideias, além de se manter atualizado sobre tendências e entendimento do comportamento humano. Essas são valiosas habilidades para a área de marketing digital. Através desta formação, você dominará estratégias para conectar marcas a consumidores.',
    'editor' => 'Suas respostas mostraram que você adora usar sua criatividade para contar histórias visuais e tem muito interesse em editar e manipular vídeos. Essas características destacam seu potencial na área de edição de vídeo. Através desta formação, você poderá dominar ferramentas e técnicas para produzir vídeos impressionantes.',
    'wordpress' => 'Você demonstrou interesse em criar e gerenciar sites de forma prática e eficiente. A plataforma WordPress é uma das mais populares para o desenvolvimento de sites e blogs devido à sua flexibilidade e facilidade de uso. Nesta formação, você aprenderá a criar, customizar e otimizar sites WordPress para atender às necessidades de diferentes projetos.',
    'empate' => 'Parece que você ainda não encontrou uma área de maior interesse entre as opções apresentadas. Isso é perfeitamente normal, há uma infinidade de caminhos que você pode seguir!
                <br></br>As formações em Programação, Edição de Vídeo, Design e Marketing ensinam habilidades valiosas e procuradas no mercado de trabalho atual.<br></br>
                Portanto, recomendamos que escolha uma das formações abaixo para iniciar sua jornada conosco, e assim, possa desenvolver novo interesse e paixão!'
);


        // Verifica se a vocação tem um texto introdutório correspondente
        if (isset($intro_texts[$vocacao])) {
            $form .= '<p>' . $intro_texts[$vocacao] . '</p>';
        }
        
        $labels = array(
            'programador' => "Programador(a)",
            'designer' => "Designer",
            'trafego' => "Gestor(a) de trafego",
            'editor' => "Editor(a) de vídeo",
			'wordpress' => "Desenvolvedor(a) Wordpress"
            );
            
        if($vocacao != "empate")
            $form .= '<p>Nós identificamos que sua vocação é <b>' . $labels[$vocacao] . '</b>, porém, caso deseje seguir outro caminho, selecione-o abaixo e clique em Salvar escolha</p>';
    }
    
    $form .= '<form action="" method="POST">';
    $options = array(
        'programador' => 'Programação',
        'designer' => 'Designer',
        'editor' => 'Editor de Vídeos',
        'trafego' => 'Gestor de Tráfego Pago',
		'wordpress' => 'Desenvolvedor Wordpress'
    );

    foreach ($options as $value => $label) {
        $checked = ($preselected == $value || $vocacao == $value) ? 'checked' : '';
        $form .= sprintf('<input type="radio" name="caminho" class="input-radio" value="%s" %s required> %s<br>', $value, $checked, $label);
    }
	$form .= '<input type="submit" name="submit_caminho" class="submit-btn" value="Confirmar Formação">';
    $form .= '</form>';

    $roles_map = array(
        'programador' => 'aluno_programacao',
        'designer' => 'aluno_design',
        'wordpress' => 'aluno_wordpress',
        'trafego' => 'aluno_marketing',
        'editor' => 'aluno_edio_de_vdeo'
    );
    
    if (isset($_POST['submit_caminho'])) {
        $caminho = sanitize_text_field($_POST['caminho']);
        update_user_meta($user_id, 'caminho_selecionado', $caminho);
        
        // Add role
        $user = wp_get_current_user();
        $user->add_role($roles_map[$caminho]);
        
		wp_redirect( "/painel-inscricao/" );
		exit;	
    }

    return $form;
}
add_shortcode('career_path_selector', 'shortcode_selecao_caminho');