<?php

die("SUCCESS: The export_course_videos.php file IS being loaded!");

/**
 * Panapaná Tutor LMS Course Exporter
 *
 * This file contains all functionality for exporting course data and manages
 * the custom permissions required to do so.
 *
 * This file is included by functions.php.
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}


// =============================================================================
//  STEP 1: REGISTER THE CUSTOM CAPABILITY AND ADD IT TO THE 'GESTOR' ROLE
// =============================================================================

/**
 * Adds the custom capability 'export_course_data' to the 'gestor' role.
 *
 * This function runs once when WordPress loads. It ensures the permission exists
 * so we can check for it later. This is the correct, standard WordPress way.
 */
function panapana_add_custom_export_capability() {
	// Get the 'gestor' role object.
	$role = get_role( 'gestor' );

	// IMPORTANT: Check if the role actually exists before trying to modify it.
	if ( ! empty( $role ) ) {
		// Add our custom capability to the role.
		// WordPress is smart and will only add it once.
		$role->add_cap( 'export_course_data', true );
	}
}
add_action( 'init', 'panapana_add_custom_export_capability' );


// =============================================================================
//  STEP 2: USE THE NEW CAPABILITY IN YOUR SHORTCODE
// =============================================================================

/**
 * Creates a shortcode [tutor_course_exporter] to display a list of courses for export.
 *
 * The output is only visible to users with the 'export_course_data' capability.
 *
 * @return string The HTML for the course list, or an error message.
 */
function panapana_course_exporter_shortcode_ui() {
	// Check if the current user has our custom capability.
	// This works for Administrators (who have all caps) and for 'gestor' (who we gave it to).
	if ( ! current_user_can( 'export_course_data' ) ) {
		return '<p>Você não tem permissão para acessar esta funcionalidade.</p>';
	}

	// Get all published courses.
	$courses = get_posts(
		array(
			'post_type'      => 'courses',
			'post_status'    => 'publish',
			'posts_per_page' => -1,
			'orderby'        => 'title',
			'order'          => 'ASC',
		)
	);

	if ( empty( $courses ) ) {
		return '<p>Nenhum curso encontrado.</p>';
	}

	ob_start();
	?>
	<div class="tutor-course-exporter-wrapper">
		<h3>Exportador de Vídeos do Curso</h3>
		<p>Clique em um botão para exportar a lista de módulos e aulas de um curso.</p>
		<ul style="list-style-type: none; padding-left: 0;">
			<?php foreach ( $courses as $course ) : ?>
				<li style="padding: 10px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center;">
					<span><?php echo esc_html( $course->post_title ); ?></span>
					<a href="<?php echo esc_url( add_query_arg( 'export_tutor_course', $course->ID ) ); ?>" class="tutor-btn tutor-btn-primary tutor-btn-sm" target="_blank">
						Exportar Links das Aulas
					</a>
				</li>
			<?php endforeach; ?>
		</ul>
	</div>
	<?php
	return ob_get_clean();
}
add_shortcode( 'tutor_course_exporter', 'panapana_course_exporter_shortcode_ui' );


// =============================================================================
//  STEP 3: USE THE NEW CAPABILITY IN YOUR EXPORT TRIGGER
// =============================================================================

/**
 * Exports course data when triggered by a URL parameter.
 *
 * Only users with the 'export_course_data' capability can run this export.
 */
function panapana_export_course_videos_on_trigger() {
	if ( ! isset( $_GET['export_tutor_course'] ) ) {
		return;
	}

	// Security: Check for the custom capability again.
	if ( ! current_user_can( 'export_course_data' ) ) {
		wp_die( 'Você não tem permissão para acessar esta página.' );
	}

	$course_id = intval( $_GET['export_tutor_course'] );
	if ( ! $course_id ) {
		wp_die( 'ID de curso inválido.' );
	}

	$course = get_post( $course_id );
	if ( ! $course || 'courses' !== $course->post_type ) {
		wp_die( 'O ID fornecido não é um curso válido do Tutor LMS.' );
	}

	// --- The rest of your export logic ---
	header( 'Content-Type: text/plain; charset=utf-8' );
	echo "Exportando dados para o Curso: \"{$course->post_title}\" (ID: {$course_id})\n";
	echo "===================================================================\n\n";

	$topics = get_posts(
		array(
			'post_type'      => 'topics',
			'post_parent'    => $course_id,
			'posts_per_page' => -1,
			'orderby'        => 'menu_order',
			'order'          => 'ASC',
		)
	);

	if ( ! $topics ) {
		echo "Nenhum tópico (módulo) encontrado para este curso.\n";
		die();
	}

	foreach ( $topics as $topic ) {
		echo "Módulo: {$topic->post_title}\n";
		echo "----------------------------------------\n";
		$lessons_and_quizzes = get_posts(
			array(
				'post_type'      => array( 'lesson', 'tutor_quiz' ),
				'post_parent'    => $topic->ID,
				'posts_per_page' => -1,
				'orderby'        => 'menu_order',
				'order'          => 'ASC',
			)
		);
		if ( ! $lessons_and_quizzes ) {
			echo "  Nenhuma aula ou questionário encontrado neste módulo.\n\n";
			continue;
		}
		foreach ( $lessons_and_quizzes as $item ) {
			if ( 'lesson' === $item->post_type ) {
				$video_meta = get_post_meta( $item->ID, '_video', true );
				$video_url  = 'N/A';
				if ( is_array( $video_meta ) ) {
					if ( ! empty( $video_meta['source_shortcode'] ) && preg_match( '/src="([^"]+)"/', $video_meta['source_shortcode'], $matches ) ) {
						$video_url = $matches[1];
					} elseif ( ! empty( $video_meta['source_youtube'] ) ) {
						$video_url = $video_meta['source_youtube'];
					}
				}
				echo "  - Aula: {$item->post_title}\n";
				echo "    Link: {$video_url}\n\n";
			} elseif ( 'tutor_quiz' === $item->post_type ) {
				echo "  - Questionário: {$item->post_title}\n\n";
			}
		}
	}
	die();
}
add_action( 'init', 'panapana_export_course_videos_on_trigger' );

// =============================================================================
//  MASTER DEBUGGING FUNCTION
// =============================================================================

/**
 * Creates a shortcode [panapana_master_debug] to diagnose permission issues.
 *
 * This will show us exactly what roles and capabilities the current user has,
 * and if the 'gestor' role itself has the capability we are trying to add.
 */
function panapana_master_debug_shortcode_ui() {
	if ( ! is_user_logged_in() ) {
		return '<p style="color: red;"><strong>DEBUG: User is not logged in.</strong></p>';
	}

	$user = wp_get_current_user();
	$role_object = get_role( 'gestor' );

	ob_start();
	?>
	<div class="panapana-debug-box" style="border: 3px solid red; padding: 20px; margin: 20px 0; background: #fff;">
		<h3 style="margin-top:0;">Panapaná Permission Debug</h3>
		
		<h4>Current User Information</h4>
		<ul>
			<li><strong>User ID:</strong> <?php echo esc_html( $user->ID ); ?></li>
			<li><strong>Username:</strong> <?php echo esc_html( $user->user_login ); ?></li>
			<li><strong>User Roles:</strong> <?php echo esc_html( implode( ', ', (array) $user->roles ) ); ?></li>
		</ul>

		<h4>Capability Check for THIS User</h4>
		<ul>
			<li>Does user have capability '<strong>export_course_data</strong>'? 
				<strong style="color: <?php echo current_user_can( 'export_course_data' ) ? 'green' : 'red'; ?>;">
					<?php echo current_user_can( 'export_course_data' ) ? 'YES' : 'NO'; ?>
				</strong>
			</li>
             <li>Does user have capability '<strong>manage_options</strong>' (Admin)? 
				<strong style="color: <?php echo current_user_can( 'manage_options' ) ? 'green' : 'red'; ?>;">
					<?php echo current_user_can( 'manage_options' ) ? 'YES' : 'NO'; ?>
				</strong>
			</li>
		</ul>

		<h4>'gestor' Role Object Check</h4>
		<?php if ( empty( $role_object ) ) : ?>
			<p style="color: red;"><strong>CRITICAL ERROR: The role 'gestor' does not exist! Check the role name slug.</strong></p>
		<?php else : ?>
			<ul>
				<li>Does the '<strong>gestor</strong>' role object have the '<strong>export_course_data</strong>' capability?
					<strong style="color: <?php echo $role_object->has_cap( 'export_course_data' ) ? 'green' : 'red'; ?>;">
						<?php echo $role_object->has_cap( 'export_course_data' ) ? 'YES' : 'NO'; ?>
					</strong>
				</li>
			</ul>
			<p><em>(If this is NO, it means our `add_cap` function is not working or something is removing the capability after we add it.)</em></p>
		<?php endif; ?>
	</div>
	<?php
	return ob_get_clean();
}
add_shortcode( 'panapana_master_debug', 'panapana_master_debug_shortcode_ui' );