<?php
/**
 * Tutor LMS Course Automation - User Capabilities Management
 *
 * This file handles all user capability and permission management for the
 * course automation system.
 *
 * @package TutorLMS_CourseAutomation
 * @subpackage Core
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Adds the custom capability 'export_course_data' to the 'gestor' role.
 *
 * This function runs once when WordPress loads. It ensures the permission exists
 * so we can check for it later. This is the correct, standard WordPress way.
 */
function panapana_add_custom_export_capability() {
	// Get the 'gestor' role object.
	$role = get_role( 'gestor' );

	// IMPORTANT: Check if the role actually exists before trying to modify it.
	if ( ! empty( $role ) ) {
		// Add our custom capability to the role.
		// WordPress is smart and will only add it once.
		$role->add_cap( 'export_course_data', true );
	}
}
add_action( 'init', 'panapana_add_custom_export_capability' );

/**
 * Checks if current user has permission to access course automation features.
 *
 * @return bool True if user has permission, false otherwise.
 */
function panapana_user_can_access_automation() {
	return current_user_can( 'export_course_data' ) || current_user_can( 'manage_options' );
}

/**
 * Returns permission denied message for unauthorized users.
 *
 * @return string HTML error message.
 */
function panapana_get_permission_denied_message() {
	return '<p>Você não tem permissão para acessar esta funcionalidade.</p>';
}

/**
 * Checks permissions and returns error message if user lacks access.
 * Used as a helper for shortcodes and functions.
 *
 * @return string|false Error message if no permission, false if user has access.
 */
function panapana_check_user_permissions() {
	if ( ! panapana_user_can_access_automation() ) {
		return panapana_get_permission_denied_message();
	}
	return false;
}
