<?php 
$is_enrolled           = apply_filters( 'tutor_alter_enroll_status', tutor_utils()->is_enrolled() );
?>

<div class="p-lg-5 py-5">
	<div class="container">
		<?php if ( tutor_utils()->has_video_in_single() || has_post_thumbnail() ) { 
			?>
			<div class="row">
				<div class="col-lg-12 col-md-12 col-12 mb-5">
					<div class="single-enrolled-video rounded-3 position-relative w-100 d-block overflow-hidden p-0" style="height: 400px;">
						<?php 
						if ( tutor_utils()->has_video_in_single() ) {
							ob_start();
							tutor_course_video();							
							$video_html = ob_get_clean();
							$video_html = str_replace( '<iframe', '<iframe class="position-absolute top-0 end-0 start-0 end-0 bottom-0 h-100 w-100"', $video_html );
							echo wp_kses( $video_html, 'post-iframe' );
						} elseif ( has_post_thumbnail() ) { ?>
							<div class="h-100 d-flex justify-content-center position-relative rounded py-13 border-white border rounded-3 bg-cover" style="background-image: url(<?php echo esc_url( get_the_post_thumbnail_url() ); ?>);">
							</div>
						<?php } ?>
					</div>
				</div>
			</div>
		<?php
		}
		?>
		<!-- Content -->
		<div class="row desktop-card">
		    <!-- DESKTOP -->
			<div class="col-xl-8 col-lg-12 col-md-12 col-12 mb-4 mb-xl-0">
				<!-- Card -->
				<?php $course_data = geeks_tutor_get_single_course_data(); ?>

				<div class="card <?php if( !empty( $course_data['course_tabs'] ) ):?>rounded-0 rounded-top-md<?php endif;?>">
					<!-- Card body -->
					<div class="card-body">
						<div class="d-flex justify-content-between align-items-center">
							<h1 class="fw-semi-bold mb-2">
								<?php the_title();?> 
							</h1>
							<?php geeks_tutor_course_loop_bookmark();?>

						</div>
						<div class="d-flex mb-5">
							<span>
								<?php geeks_tutor_meta_course_rating(); ?>
							</span>
							<span class="ms-4 d-none d-md-block">
								<?php geeks_tutor_loop_meta_course_level(); ?>
							</span>
							<span class="ms-4 d-none d-md-block">
								<i class="mdi mdi-account-multiple-outline"></i>
								<span><?php esc_html_e( 'Enrolled', 'geeks' ); ?></span>
							</span>
						</div>
						<div class="d-flex justify-content-between">
							<div class="d-flex align-items-center">
								<?php echo wp_kses_post( get_avatar( get_the_author_meta( 'ID' ), 40, '', '', array( 'class' => 'rounded-circle avatar-md' ) ) ); ?>
								<div class="ms-2 lh-1">
									<h4 class="mb-1"><?php the_author(); ?></h4>
									<p class="fs-6 mb-0">@<?php the_author_meta( 'user_nicename' ); ?></p>
								</div>
							</div>
							<?php if ( isset( $profile_url ) ) : ?>
							<div>
								<a href="<?php echo esc_url( $profile_url ); ?>" class="btn btn-outline-white btn-sm"><?php echo esc_html__( 'View Profile', 'geeks' ); ?></a>
							</div>
							<?php endif; ?>
						</div>
					</div>
					<!-- Card -->
				</div>

				<?php geeks_get_template( 'single-course/tabs/tabs.php', $course_data ); ?>
			</div>
			<!---->
		
		    <!---->
			<div class="col-xl-4 col-lg-12 col-md-12 col-12">
				<?php $is_completed_course = tutor_utils()->is_completed_course(); 
				$completed_count           = tutor_utils()->get_course_completed_percent();
				$lesson_url                = tutor_utils()->get_course_first_lesson();
				$course_id                 = get_the_ID();
				$course_progress           = tutor_utils()->get_course_completed_percent( $course_id, 0, true );


				if( ! $is_completed_course ||  $completed_count > 0  || $lesson_url ) :?>

					<div class="card mb-4">
						<?php 
						if( $completed_count > 0 && tutor_utils()->get_option('enable_course_progress_bar', true, true ) ): 
						?>
						    <div class="tutor-course-enrolled-info">
								<div class="tutor-course-status">
									<div class="card-header">
								    	<h4 class="tutor-segment-title mb-0"><?php esc_html_e('Course Status', 'geeks'); ?></h4>
									</div>
								    <div class="tutor-progress-bar-wrap card-body pb-0">
								        <div class="tutor-progress-bar progress" style="height: 6px;">
		                					<div class="tutor-progress-filled progress-bar bg-success" role="progressbar" style="width: <?php echo esc_attr( $completed_count ); ?>%;" aria-valuenow="<?php echo esc_attr( $completed_count ); ?>" aria-valuemin="0" aria-valuemax="100"></div>
								        </div>
								        <small class="tutor-progress-percent d-block mt-1"><?php echo esc_html( $completed_count ); ?>% <?php esc_html_e(' Completed', 'geeks')?></small>
								    </div>
								</div>
						    </div>
						<?php endif; ?>

						<div class="tutor-lead-info-btn-group card-body">
							<?php
						    global $wp_query;
						    ?>
						    <div class="d-grid">
							    <?php

							        if ( $wp_query->query['post_type'] !== 'lesson') {
							            $lesson_url = tutor_utils()->get_course_first_lesson();
							            $completed_lessons = tutor_utils()->get_completed_lesson_count_by_course();
							            $completed_percent = tutor_utils()->get_course_completed_percent();
							            $is_completed_course = tutor_utils()->is_completed_course();
							            $retake_course = tutor_utils()->get_option('course_retake_feature', false) && ($is_completed_course || $completed_percent >= 100);
							            $start_content = '';

							            if ( $lesson_url ) { 
							                $button_class = 'btn btn-primary tutor-course-entry-button tutor-button tutor-button-primary' . ($retake_course ? ' tutor-course-retake-button' : '');
							                ob_start();
							                ?>
							                <a href="<?php echo esc_url( $lesson_url ); ?>" class="<?php echo esc_attr( $button_class ); ?>" <?php echo esc_attr( $retake_course ) ? 'disabled="disabled"' : ''; ?> data-course_id="<?php echo esc_attr( get_the_ID() ); ?>">
							                    <?php
							                        if( $retake_course ) {
							                            esc_html_e( 'Retake This Course', 'geeks' );
							                        } else if( $completed_percent <= 0 ){
							                            esc_html_e( 'Start Learning', 'geeks' );
							                        } else {
							                            esc_html_e( 'Continue Learning', 'geeks' );
							                        }
							                    ?>
							                </a>
							                <?php
											$start_content = ob_get_clean();
							                
							            }
							            echo apply_filters( 'tutor_course/single/start/button', $start_content, get_the_ID() );

							        }

						        /* --- Start Panapaná Comment Out --- 
						        // Original logic for showing Complete/Survey button based on nps_submited meta
						        // This is now handled by panapana_add_conditional_course_buttons() in functions.php
						        //
						        // Verificação da meta "nps_submited" do usuário
						        // if ( $is_completed_course ) {
						        // 	$current_user_id = get_current_user_id();
						        // 	$nps_submited = get_user_meta( $current_user_id, 'nps_submited', true );
						        // 
						        // 	if ( $nps_submited ) {
						        // 		// Se o NPS foi submetido, mostra o botão de completar curso normalmente
						        // 		ob_start();
						        // 		?>
						        // 		<div class="tutor-course-complete-form-wrap mt-2">
						        // 			<form method="post">
						        // 				<?php wp_nonce_field( tutor()->nonce_action, tutor()->nonce ); ?>
						        // 
						        // 				<input type="hidden" value="<?php echo esc_attr( get_the_ID() ); ?>" name="course_id"/>
						        // 				<input type="hidden" value="tutor_complete_course" name="tutor_action"/>
						        // 
						        // 				<button type="submit" class="course-complete-button btn btn-outline-primary btn-block w-100" name="complete_course_btn" value="complete_course">
						        // 					<?php esc_html_e( 'Complete Course', 'geeks' ); ?>
						        // 				</button>
						        // 			</form>
						        // 		</div>
						        // 		<?php
						        // 		echo apply_filters( 'tutor_course/single/complete_form', ob_get_clean() );
						        // 	} else {
						        // 		// Se o NPS não foi submetido, mostra um botão/link para o formulário NPS
						        // 		?>
						        // 		<div class="tutor-course-complete-form-wrap mt-2">
						        // 			<a href="/pesquisa-de-satisfacao" class="btn btn-outline-primary btn-block w-100">
						        // 				<?php esc_html_e( 'Responda à pesquisa antes de concluir o curso.', 'geeks' ); ?>
						        // 			</a>
						        // 		</div>
						        // 		<?php
						        // 	}
						        // }
						        --- End Panapaná Comment Out --- */

						        ?>
					    	</div>

					        <?php do_action('tutor_course/single/actions_btn_group/after'); ?>
					    </div>
					</div>
				<?php endif;?>
						    <?php do_action('tutor_course/single/actions_btn_group/before'); ?>

				<?php if( is_user_logged_in() ):
					if( $is_enrolled ) :?>
						<div class="tutor-single-course-segment  tutor-course-enrolled-wrap card mb-4">
						    <div class="card-body">
						        <p class="mb-0">
						            <i class="tutor-icon-purchase"></i>
						            <?php
					                echo sprintf( esc_html__('You enrolled this course on %s.', 'geeks'),  "<span class='text-bold-small tutor-color-success tutor-ml-3 tutor-enrolled-info-date'>". date_i18n(get_option('date_format'), strtotime($is_enrolled->post_date)
					                    )."</span>"  );
					                ?>
						        </p>

						        <?php do_action('tutor_enrolled_box_after') ?>
						    </div>

						</div>
				<?php endif;
			endif;?>

				<div class="card mb-4" id="courseAccordion">
					<div>
						<?php
							$topics = tutor_utils()->get_topics();
							if ( $topics->have_posts() ) {
								geeks_get_template( 'single-course/tabs/curriculum.php' ); 
							} else {
								?>
								<div class="card-body">
									<p class="mb-0"><?php echo esc_html( apply_filters( 'geeks_tutor_empty', esc_html__( 'No Topic for this Course', 'geeks' ) ) );?></p>
								</div>
								<?php
							}
						?>
					</div>
				</div>

				<?php tutor_course_requirements_html(); ?>
                <?php tutor_course_tags_html(); ?>
                <?php tutor_course_target_audience_html(); ?>
                <?php tutor_course_benefits_html(); ?>

			</div>
			<!---->
		</div>
	</div>
</div>

        <!-- Content -->
        <div class="row mobile-card">
			
		    <!-- MOBILE -->
			<div class="col-xl-8 col-lg-12 col-md-12 col-12 mb-4 mb-xl-0">
				<!-- Card -->
				<?php $course_data = geeks_tutor_get_single_course_data(); ?>

				<div class="card <?php if( !empty( $course_data['course_tabs'] ) ):?>rounded-0 rounded-top-md<?php endif;?>">
					<!-- Card body -->
					<div class="card-body">
						<div class="d-flex justify-content-between align-items-center">
							<h1 class="fw-semi-bold mb-2">
								<?php the_title();?> 
							</h1>
							<?php geeks_tutor_course_loop_bookmark();?>

						</div>
						<div class="d-flex mb-5">
							<span>
								<?php geeks_tutor_meta_course_rating(); ?>
							</span>
							<span class="ms-4 d-none d-md-block">
								<?php geeks_tutor_loop_meta_course_level(); ?>
							</span>
							<span class="ms-4 d-none d-md-block">
								<i class="mdi mdi-account-multiple-outline"></i>
								<span><?php esc_html_e( 'Enrolled', 'geeks' ); ?></span>
							</span>
						</div>
						<div class="d-flex justify-content-between">
							<div class="d-flex align-items-center">
								<?php echo wp_kses_post( get_avatar( get_the_author_meta( 'ID' ), 40, '', '', array( 'class' => 'rounded-circle avatar-md' ) ) ); ?>
								<div class="ms-2 lh-1">
									<h4 class="mb-1"><?php the_author(); ?></h4>
									<p class="fs-6 mb-0">@<?php the_author_meta( 'user_nicename' ); ?></p>
								</div>
							</div>
							<?php if ( isset( $profile_url ) ) : ?>
							<div>
								<a href="<?php echo esc_url( $profile_url ); ?>" class="btn btn-outline-white btn-sm"><?php echo esc_html__( 'View Profile', 'geeks' ); ?></a>
							</div>
							<?php endif; ?>
						</div>
					</div>
					<!-- Card -->
				</div>
			</div>
			<!---->
		
		    <!---->
			<div class="col-xl-4 col-lg-12 col-md-12 col-12">
				<?php $is_completed_course = tutor_utils()->is_completed_course(); 
				$completed_count           = tutor_utils()->get_course_completed_percent();
				$lesson_url                = tutor_utils()->get_course_first_lesson();
				$course_id                 = get_the_ID();
				$course_progress           = tutor_utils()->get_course_completed_percent( $course_id, 0, true );


				if( ! $is_completed_course ||  $completed_count > 0  || $lesson_url ) :?>

					<div class="card mb-4">
						<?php 
						if( $completed_count > 0 && tutor_utils()->get_option('enable_course_progress_bar', true, true ) ): 
						?>
						    <div class="tutor-course-enrolled-info">
								<div class="tutor-course-status">
									<div class="card-header">
								    	<h4 class="tutor-segment-title mb-0"><?php esc_html_e('Course Status', 'geeks'); ?></h4>
									</div>
								    <div class="tutor-progress-bar-wrap card-body pb-0">
								        <div class="tutor-progress-bar progress" style="height: 6px;">
		                					<div class="tutor-progress-filled progress-bar bg-success" role="progressbar" style="width: <?php echo esc_attr( $completed_count ); ?>%;" aria-valuenow="<?php echo esc_attr( $completed_count ); ?>" aria-valuemin="0" aria-valuemax="100"></div>
								        </div>
								        <small class="tutor-progress-percent d-block mt-1"><?php echo esc_html( $completed_count ); ?>% <?php esc_html_e(' Completed', 'geeks')?></small>
								    </div>
								</div>
						    </div>
						<?php endif; ?>

						<div class="tutor-lead-info-btn-group card-body">
						   
							<?php
						    global $wp_query;
						    ?>
						    <div class="d-grid">
							    <?php

							        if ( $wp_query->query['post_type'] !== 'lesson') {
							            $lesson_url = tutor_utils()->get_course_first_lesson();
							            $completed_lessons = tutor_utils()->get_completed_lesson_count_by_course();
							            $completed_percent = tutor_utils()->get_course_completed_percent();
							            $is_completed_course = tutor_utils()->is_completed_course();
							            $retake_course = tutor_utils()->get_option('course_retake_feature', false) && ($is_completed_course || $completed_percent >= 100);
							            $start_content = '';

							            if ( $lesson_url ) { 
							                $button_class = 'btn btn-primary tutor-course-entry-button tutor-button tutor-button-primary' . ($retake_course ? ' tutor-course-retake-button' : '');
							                ob_start();
							                ?>
							                <a href="<?php echo esc_url( $lesson_url ); ?>" class="<?php echo esc_attr( $button_class ); ?>" <?php echo esc_attr( $retake_course ) ? 'disabled="disabled"' : ''; ?> data-course_id="<?php echo esc_attr( get_the_ID() ); ?>">
							                    <?php
							                        if( $retake_course ) {
							                            esc_html_e( 'Retake This Course', 'geeks' );
							                        } else if( $completed_percent <= 0 ){
							                            esc_html_e( 'Start Learning', 'geeks' );
							                        } else {
							                            esc_html_e( 'Continue Learning', 'geeks' );
							                        }
							                    ?>
							                </a>
							                <?php
											$start_content = ob_get_clean();
							                
							            }
							            echo apply_filters( 'tutor_course/single/start/button', $start_content, get_the_ID() );

							        }

						        /* --- Start Panapaná Comment Out --- 
						        // Original logic for showing Complete/Survey button based on nps_submited meta
						        // This is now handled by panapana_add_conditional_course_buttons() in functions.php
						        //
						        // Verificação da meta "nps_submited" do usuário
						        // if ( $is_completed_course ) {
						        // 	$current_user_id = get_current_user_id();
						        // 	$nps_submited = get_user_meta( $current_user_id, 'nps_submited', true );
						        // 
						        // 	if ( $nps_submited ) {
						        // 		// Se o NPS foi submetido, mostra o botão de completar curso normalmente
						        // 		ob_start();
						        // 		?>
						        // 		<div class="tutor-course-complete-form-wrap mt-2">
						        // 			<form method="post">
						        // 				<?php wp_nonce_field( tutor()->nonce_action, tutor()->nonce ); ?>
						        // 
						        // 				<input type="hidden" value="<?php echo esc_attr( get_the_ID() ); ?>" name="course_id"/>
						        // 				<input type="hidden" value="tutor_complete_course" name="tutor_action"/>
						        // 
						        // 				<button type="submit" class="course-complete-button btn btn-outline-primary btn-block w-100" name="complete_course_btn" value="complete_course">
						        // 					<?php esc_html_e( 'Complete Course', 'geeks' ); ?>
						        // 				</button>
						        // 			</form>
						        // 		</div>
						        // 		<?php
						        // 		echo apply_filters( 'tutor_course/single/complete_form', ob_get_clean() );
						        // 	} else {
						        // 		// Se o NPS não foi submetido, mostra um botão/link para o formulário NPS
						        // 		?>
						        // 		<div class="tutor-course-complete-form-wrap mt-2">
						        // 			<a href="/pesquisa-de-satisfacao" class="btn btn-outline-primary btn-block w-100">
						        // 				<?php esc_html_e( 'Responda à pesquisa antes de concluir o curso.', 'geeks' ); ?>
						        // 			</a>
						        // 		</div>
						        // 		<?php
						        // 	}
						        // }
						        --- End Panapaná Comment Out --- */

						        ?>
					    	</div>

					        <?php do_action('tutor_course/single/actions_btn_group/after'); ?>
					    </div>
					</div>
				<?php endif;?>
			         <?php echo do_action('tutor_course/single/actions_btn_group/before'); ?>

				<?php if( is_user_logged_in() ):
					if( $is_enrolled ) :?>
						<div class="tutor-single-course-segment  tutor-course-enrolled-wrap card mb-4">
						    <div class="card-body">
						        <p class="mb-0">
						            <i class="tutor-icon-purchase"></i>
						            <?php
					                echo sprintf( esc_html__('You enrolled this course on %s.', 'geeks'),  "<span class='text-bold-small tutor-color-success tutor-ml-3 tutor-enrolled-info-date'>". date_i18n(get_option('date_format'), strtotime($is_enrolled->post_date)
					                    )."</span>"  );
					                ?>
						        </p>

						        <?php do_action('tutor_enrolled_box_after') ?>
						    </div>

						</div>
				<?php endif;
			endif;?>
				<?php geeks_get_template( 'single-course/tabs/tabs.php', $course_data ); ?>
				<div class="card mb-4" id="courseAccordion">
					<div>
						<?php
							$topics = tutor_utils()->get_topics();
							if ( $topics->have_posts() ) {
								geeks_get_template( 'single-course/tabs/curriculum.php' ); 
							} else {
								?>
								<div class="card-body">
									<p class="mb-0"><?php echo esc_html( apply_filters( 'geeks_tutor_empty', esc_html__( 'No Topic for this Course', 'geeks' ) ) );?></p>
								</div>
								<?php
							}
						?>
					</div>
				</div>

				<?php tutor_course_requirements_html(); ?>
                <?php tutor_course_tags_html(); ?>
                <?php tutor_course_target_audience_html(); ?>
                <?php tutor_course_benefits_html(); ?>

			</div>
			<!---->
		</div>
	</div>
</div>
