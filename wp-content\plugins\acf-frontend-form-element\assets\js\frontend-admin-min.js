!function(e){e.extend({bytesToMegaBytes:e=>e/1048576}),e(window).scroll((function(){var t=e(".load-more-results"),a=e(this);e.each(t,(function(){var t=e(this),i=t.offset().top,n=t.outerHeight(),s=e(window).height(),o=a.scrollTop();if(o>i+n-s&&i>o&&o+s>i+n){if(t.hasClass("loading"))return;t.addClass("loading").find("span").removeClass("acf-hidden");var r=parseInt(t.attr("data-page"))+1,l=parseInt(t.attr("data-count")),d=parseInt(t.attr("data-total")),c={action:"frontend_admin/forms/get_submissions",item_count:l,current_page:r,form_id:t.data("form"),load_more:1};e.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(c),type:"post",dataType:"html",cache:!1,success:function(e){t.before(e),t.attr("data-page",r).attr("data-count",t.siblings(".fea-list-item").length),t.removeClass("loading").find("span").addClass("acf-hidden"),r==d&&t.remove()}})}}))})),feaUpdateFileMeta=function(t,a){e.ajax({url:acf.get("ajaxurl"),data:{action:"acf/fields/upload_file/update_meta",attach_id:t,url:a,nonce:acf.data.nonce},type:"post"})};acf.models.TooltipConfirm.extend({events:{"click [data-block]":"onConfirm",'click [data-event="cancel"]':"onCancel"},render:function(){this.html(this.get("html")),this.$el.addClass("fea-edit-popup")}});e("body").on("click",".acf-field.has-prev-value .fea-view-changes",(function(t){var a=e(this).closest(".acf-field");a.children(".acf-input").hide(),a.children(".fea-prev-value").show(),a.children(".fea-edit-changes").show(),e(this).hide()})),e("body").on("click",".acf-field.has-prev-value .fea-edit-changes",(function(t){var a=e(this).closest(".acf-field");a.children(".acf-input").show(),a.children(".fea-prev-value").hide(),a.children(".fea-view-changes").show(),e(this).hide()})),e("body").on("click",".fea-inline-edit",(function(t){var a=e(this).closest(".fea-display-field");acf.showModal(a),currentModal.attr("data-source",a.data("source")),currentModal.attr("data-key",a.data("field")),currentModal.find(".fea-close").remove(),e(document).on("click",currentModal,(function(t){t.target==this&&e(this).hide()}));var i=currentModal.find("form");if(0==i.length){const e=a.children(".acf-field");e.removeClass("frontend-admin-hidden");const t=`\n\t\t\t\t\t<form class="fea-inline-form">\n\t\t\t\t\t\t<button type="button" class="fea-inline-save button">${acf.__("Save")}</button>\n\t\t\t\t\t\t<button type="button" class="fea-inline-cancel button">${acf.__("Cancel")}</button>\n\t\t\t\t\t</form>\n\t\t\t\t`;acf.showModalContent(t),(i=currentModal.find("form")).prepend(e)}})),e("body").on("submit",".fea-inline-form",(function(e){e.preventDefault()})),e("body").on("click",".fea-inline-save",(function(t){if(void 0!==acf.data){t.preventDefault();var a=e(this),i=a.siblings(".fea-loader");i.length>0?i.show():(i=e('<span class="fea-loader"></span>'),a.after(i));var n=a.closest(".fea-inline-form"),s=new FormData(n[0]);s.append("action","frontend_admin/forms/update_field"),s.append("nonce",acf.data.nonce),acf.lockForm(n),n.addClass("lock-form"),e.ajax({url:acf.get("ajaxurl"),type:"post",data:acf.prepareForAjax(s),cache:!1,processData:!1,contentType:!1,success:function(t){if(n.removeClass("lock-form"),n.find("button").removeAttr("disabled"),n.find("button").removeClass("disabled"),acf.unlockForm(n),t.success){if(t.data.errors)t.data.errors.map(acf.showErrors,n);else if(t.data.updates)a.parents(".fea-modal").hide(),t.data.updates.forEach((function(t){var i=a.parents(".fea-modal"),n=e(".fea-display-field[data-source="+i.attr("data-source")+"][data-field="+i.attr("data-key")+"]");n.length>0&&n.find(".fea-value").html(t.html)}));i.hide()}}})}})),e("body").on("click",".fea-inline-cancel",(function(t){var a=e(this).parents(".fea-modal");e(".fea-display-field[data-source="+a.attr("data-source")+"][data-field="+a.attr("data-key")+"]");a.hide()})),e("body").on("click","a[data-name=remove]",(function(t){null!=typeof imagePreview&&e(this).parents(".show-if-value").removeClass("show").siblings(".hide-if-value").removeClass("frontend-admin-hidden").find("input.image-preview").val("")})),e("body").on("click",".fea-new-form-window",(function(t){t.preventDefault();var a=e(this);acf.getForm(a,"admin_form")})),acf.addFilter("relationship_ajax_data",(function(e,t){return""!=t.$control().data("product_id")&&(e.product_id=t.$control().data("product_id")),e})),e(document).on("elementor/popup/show",((t,a,i)=>{acf.doAction("append",e("#elementor-popup-modal-"+a))})),e("body").on("click","span.close-msg",(function(t){e(this).parents(".frontend-admin-message").remove()})),e("body").on("input click",(function(t){e(".acf-success-message").remove()})),e("body").on("mouseenter",".choices a.edit-rel-post",(function(t){var a=e(this).parents(".acf-rel-item");a.hasClass("disabled")||a.addClass("disabled temporary")})),e("body").on("mouseleave",".choices a.edit-rel-post",(function(t){var a=e(this).parents(".acf-rel-item");a.hasClass("temporary")&&a.removeClass("disabled temporary")})),e("body").on("click",".render-form",(function(t){t.preventDefault();var a=e(this);acf.getForm(a)})),e(".post-slug-field input").on("input keyup",(function(){var t=this.selectionStart,a=e(this).val();e(this).val(a.replace(/[`~!@#$%^&*()|+=?;:..’“'"<>,€£¥•،٫؟»«\s\{\}\[\]\\\/] + /gi,"").toLowerCase()),this.setSelectionRange(t,t)})),e("body").on("click","button.edit-password",(function(){e(this).addClass("acf-hidden").parents(".acf-field-user-password").removeClass("edit_password").addClass("editing_password").siblings(".acf-field-user-password-confirm").removeClass("acf-hidden"),e(this).after('<input type="hidden" name="edit_user_password" value="1"/>'),e(this).siblings(".pass-strength-result").removeClass("acf-hidden")})),e("body").on("click","button.cancel-edit",(function(){e(this).siblings("button.edit-password").removeClass("acf-hidden").parents(".acf-field-user-password").addClass("edit_password").removeClass("editing_password").siblings(".acf-field-user-password-confirm").addClass("acf-hidden"),e(this).parents("acf-input-wrap").siblings("acf-notice"),e(this).siblings("input[name=edit_user_password]").remove(),e(this).siblings(".pass-strength-result").addClass("acf-hidden")})),e((function(){e(".acf-field-user-password-confirm").siblings(".acf-field-user-password").hasClass("edit_password")||e(".acf-field-user-password-confirm").removeClass("acf-hidden")}));var t=0,a=0,i=[];acf.showModal=function(a,i,n){var s;n=n||e("body"),i=i||600,s=a.data("modal_id")?a.data("modal_id"):acf.uniqid()+"-"+t;var o=9+t;return currentModal=e("#modal_"+s),currentModal.length?(currentModal.show(),!1):(currentModal=e('<div id="modal_'+s+'" class="fea-modal edit-modal" data-clear="1"><div class="fea-modal-content" style="margin:'+o+"% auto;width:"+parseInt(i)+'px"><div class="fea-modal-inner"><span class="acf-icon -cancel fea-close"></span><div class="content-container"><div class="loading"><span class="fea-loader"></span></div></div></div></div></div>'),n.append(currentModal),currentModal.show(),a.attr("data-modal_id",s),!0)},e("body").on("click",".fea-modal .fea-close",(function(i){var n=e(this).closest(".fea-modal");1==n.data("clear")&&(n.hide(),t--,a-=20)})),acf.validateFrontendForm=function(e){return acf.getFrontendValidator(e.form).validate(e)},acf.getFrontendValidator=function(e){var t=e.data("acf");return t||(t=new n(e)),t},acf.showErrors=function(e){var t=this.find('[name="'+e.input+'"]').first();if(t.length||(t=this.find('[name^="'+e.input+'"]').first()),t.length&&!t.closest(".acf-field").hasClass("fea-no-error")){var a=acf.getClosestField(t);return a.showError(e.message),a.$el}};var n=acf.Model.extend({id:"FrontendValidator",data:{errors:[],notice:null,status:""},events:{"changed:status":"onChangeStatus"},addErrors:function(e){e.map(this.addError,this)},addError:function(e){this.data.errors.push(e)},hasErrors:function(){return this.data.errors.length},clearErrors:function(){return this.data.errors=[]},getErrors:function(){return this.data.errors},getFieldErrors:function(){var e=[],t=[];return this.getErrors().map((function(a){if(a.input){var i=t.indexOf(a.input);i>-1?e[i]=a:(e.push(a),t.push(a.input))}})),e},getGlobalErrors:function(){return this.getErrors().filter((function(e){return!e.input}))},showErrors:function(){if(!this.hasErrors())return;let t=this.getFieldErrors(),a=this.getGlobalErrors(),i=!1,n=t.map(acf.showErrors,this.$el),s=n.length,o=this.$el.data("error-message"),r=acf.__("Validation failed");if(1==s?r+=". "+acf.__("1 field requires attention"):s>1&&(r+=". "+acf.__("%d fields require attention").replace("%d",s)),o&&(r=o),a&&a.map((function(e){r=". "+e.message})),this.has("notice"))this.get("notice").update({type:"error",text:r});else{let e=acf.newNotice({type:"error",text:r,target:this.$el,location:"after"});this.set("notice",e)}i||(i=n.length>0?n[0]:this.get("notice").$el),setTimeout((function(){e("html, body").animate({scrollTop:i.offset().top-e(window).height()/2},500)}),10)},onChangeStatus:function(e,t,a,i){this.$el.removeClass("is-"+i).addClass("is-"+a)},validate:function(t){if(t=acf.parseArgs(t,{event:!1,reset:!1,limit:!1,loading:function(){},complete:function(){},failure:function(){},success:function(e){}}),"valid"==this.get("status"))return!0;if("validating"==this.get("status"))return!1;if(!this.$(".acf-field").length)return!0;if(t.event){var a=e.Event(null,t.event);t.success=function(){acf.enableSubmit(e(a.target)).trigger(a)}}acf.doAction("validation_begin",this.$el),acf.lockForm(this.$el),this.$el.addClass("lock-form"),t.loading(this.$el,this),this.set("status","validating");if(t.limit)(i=acf.serialize(t.limit))._acf_form=this.$el.find("input[name=_acf_form]").val();else var i=acf.serialize(this.$el);return i.action="frontend_admin/validate_form_submit",e.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(i),type:"post",dataType:"json",context:this,success:function(e){if(acf.isAjaxSuccess(e)){var a=acf.applyFilters("validation_complete",e.data,this.$el,this);acf.unlockForm(this.$el),this.$el.find(".acf-notice").remove(),a.valid?(this.set("status","valid"),this.has("notice")&&this.get("notice").update({type:"success",text:acf.__("Validation successful"),timeout:1e3}),acf.doAction("validation_success",this.$el,this),acf.doAction("submit",this.$el),acf.lockForm(this.$el),t.reset&&this.reset(),this.clearErrors()):(this.set("status","invalid"),a.errors&&(this.addErrors(a.errors),acf.doAction("validation_failure",this.$el,this),this.showErrors()))}},complete:function(){t.complete(this.$el,this)}}),!1},setup:function(e){this.$el=e},reset:function(){this.set("errors",[]),this.set("notice",null),this.set("status",""),acf.unlockForm(this.$el),this.$el.removeClass("lock-form")}});function s(e){return!!e.success_message&&'<div class="frontend-admin-message"><div class="acf-notice -success acf-success-message"><p class="success-msg">'+e.success_message+'</p><span class="frontend-admin-dismiss close-msg acf-notice-dismiss acf-icon -cancel small"></span></div></div>'}acf.getForm=function(n,s){if("plans"==(s=s||n.data("name"))){var o="plan",r="add_item";(d=n).hasClass("edit-plan")&&(r=d.parents(".fea-single-plan").data("plan"))}else{var l=n.closest(".fea-list-item");if(l.length>0){var d=l;if("edit_item"==s)r=d.data("id");else r="add_item";o=d.data("item")}else{if("admin_form"==s)d=n,r="admin_form";else{d=n.parents(".acf-field");if("edit_item"==s)r=n.parents(".acf-rel-item").data("id");else r="add_item"}o="related_terms"==d.data("type")?"term":"post"}}var c;c=d.data("form-width")?d.data("form-width"):600,t++;var f=acf.showModal(n,c-a);if(i[t]=n.parents(".acf-field"),a+=20,f){var u={action:"frontend_admin/forms/add_form",field_key:d.data("key"),data_type:o,parent_form:d.parents("form").attr("id"),form_action:r};d.data("form")&&(u.form=d.data("form")),e.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(u),type:"post",dataType:"html",success:acf.showModalContent})}},acf.showModalContent=function(e){currentModal.find(".content-container").html(e),acf.doAction("append",currentModal);var t=new CustomEvent("renderModalContent");document.dispatchEvent(t)},e("body").on("change",".frontend-form input",(function(t){if(e(this).is(":-webkit-autofill"))return window.onbeforeunload=null,void e(window).off("beforeunload");var a=e(this).closest("form");a.find("input[name=_acf_changed]").val(1),a.data("allow_leave_page")||(window.onbeforeunload=function(){return!0})})),e("body").on("keydown",".frontend-form input",(function(t){if(13==t.keyCode){t.preventDefault();var a=e(this).closest("form").find("input,textarea,select,button"),i=a.index(this),n=a.eq(i+1);n.is("button")?n.click():(n.focus(),n.is("select")?n.select():(n[0].setSelectionRange(n.val().length,n.val().length),n.val(n.val())))}})),e("body").on("click",".frontend-form .fea-submit-button",(function(t){t.preventDefault();var a=e(this);if(!a.hasClass("disabled")){$form=e(this).closest("form"),e(".fea-submit-button",$form).removeClass("clicked-on"),a.addClass("disabled clicked-on");var i=a.siblings(".fea-loader");i.length>0?i.removeClass("acf-hidden"):(i=e('<span class="fea-loader"></span>'),a.after(i));var n=a.data("message");if(n&&$form.find("input[name=_acf_message]").val(n),$form=acf.applyFilters("frontend_admin/submit_form",$form),"undefined"==typeof $form||!$form)return i.removeClass("acf-hidden"),void e(this).removeClass("disabled clicked-on");if(a.data("success")&&$form.find("input[name=_acf_message]").val(a.data("success")),"save"==a.data("state"))return $form.find("input[name=_acf_status]").val("save"),void acf.submitFrontendForm($form);$form.find("input[name=_acf_status]").val(""),acf.disableFileInputs($form),acf.lockForm($form),$form.addClass("lock-form"),args={form:$form,reset:!1,complete:acf.submitFrontendForm},acf.validateFrontendForm(args)}})),acf.disableFileInputs=function(t){e('input[type="file"]:not([disabled])',t).each((function(t,a){e(a).attr("disabled",!0),e(a).addClass("temp-disabled")}))},acf.enableFileInputs=function(t){e("input.temp-disabled",t).each((function(t,a){e(a).attr("disabled",!1),e(a).removeClass("temp-disabled")}))},acf.submitFrontendForm=function(t,a){if(t="string"==typeof t?e(t):t,(a=a||!1)&&a.hasErrors()){e(".fea-loader",t).addClass("acf-hidden"),e(".fea-submit-button",t).removeClass("disabled");var i=t.closest("div.edit-modal");return void 0!==i&&e(i).animate({scrollTop:t.offset().top-50},"slow"),a.reset(),acf.enableFileInputs(t),t.removeClass("lock-form"),void acf.unlockForm(t)}var n=new FormData(t[0]);n.append("action","frontend_admin/form_submit");let s=t.find(".clicked-on");s.data("redirect")&&n.append("redirect",s.data("redirect")),n=acf.applyFilters("frontend_admin/form_submit/form_data",n,t,s),e.ajax({url:acf.get("ajaxurl"),type:"post",data:acf.prepareForAjax(n),cache:!1,processData:!1,contentType:!1,success:function(e){acf.frontendFormSuccess(e,t)},error:function(e){acf.frontendFormSuccess(e,t)}})},acf.frontendFormSuccess=function(i,n=null){if(n=n||e("form[data-id="+i?.data?.form_element+"]"),i.success&&i.data?.form_element){window.onbeforeunload=null,e(window).off("beforeunload");var o=i.data;if(!o)return;if(o?.redirect){e(window).off("beforeunload");var r=o?.redirect.replace(/&amp;/g,"&");let t=n.find("button.clicked-on").attr("data-reload");return t&&t.split(",").forEach((e=>{let[t,a]=e.split("="),i=new URLSearchParams(window.location.search);i.set(t,a),r=window.location.pathname+"?"+i.toString()})),void(window.location=decodeURIComponent(r))}if(!n.length)return void window.location.reload();if(acf.unlockForm(n),n.removeClass("lock-form"),o.modal)t--,a-=20,n.parents(".fea-modal").remove(),e(".fea-loader").addClass("acf-hidden");else{if(o.submission&&(acf.updateSubmission(o),n.parents(".fea-modal").hide()),o.reload_form){var l=e(o.reload_form);n.replaceWith(l),acf.doAction("append",l);let t=s(o);t&&l.prepend(t);var d=l.closest("div.edit-modal");d.length>0?(d.scrollTop(0),o.close_modal&&d.hide()):e("body, html").animate({scrollTop:l.offset().top-50},"slow")}else{let e=s(o);e&&n.prepend(e),n.find("input[name=_acf_message]").val("")}o.objects&&n.find("input[name=_acf_objects]").val(o.objects),acf.doAction("frontend_form_success",o,n)}}else{n.find(".acf-notice").remove();let e=i?.data?.message||acf.__("An error occurred. Please try again later.");n.append('<div class="frontend-admin-message"><div class="acf-notice -error acf-error-message"><p class="error-msg">'+e+'</p><span class="frontend-admin-dismiss close-msg acf-notice-dismiss acf-icon -cancel small"></span></div></div>')}acf.unlockForm(n),n.removeClass("lock-form"),window.onbeforeunload=null,e(window).off("beforeunload"),acf.getFrontendValidator(n).reset(),e(".fea-loader").addClass("acf-hidden"),e(".fea-submit-button").removeClass("disabled")},acf.updateSubmission=function(t){var a=e(".fea-list-item[data-id="+t.submission+"]");if(a){let e=s(t);a.find(".item-title").html(t.submission_title),e&&a.prepend(e)}},acf.addAction("frontend_form_success",(function(a){if(a.data){var n=a.data.post_info;if(n)if("edit"==n.action)e(".acf-field div.values").find("span[data-id="+n.id+"]").html(n.text+'<a href="#" class="acf-icon -minus small dark" data-name="remove_item"></a>'),e(".acf-field div.choices").find("span[data-id="+n.id+"]").html(n.text);else{var s=i[t],o=acf.getField(s);if("relationship"==n.field_type&&(s.find("div.values ul").append('<li><input type="hidden" name="'+s.find("div.selection").siblings("input").attr("name")+'[]" value="'+n.id+'" /><span data-id="'+n.id+'" class="acf-rel-item">'+n.text+'<a href="#" class="acf-icon -minus small dark" data-name="remove_item"></a></span></li>'),s.find("div.choices ul").prepend('<li><span class="acf-rel-item disabled" data-id="'+n.id+'">'+n.text+"</span></li>")),"post_object"==n.field_type)(o=acf.getField(s)).select2.addOption({id:n.id,text:n.text}),o.select2.selectOption(n.id)}}}));var o=acf.Field.extend({type:"upload_files",events:{"click .fea-uploads-add":"onClickAdd","click .fea-uploads-edit":"onClickEdit","click .fea-uploads-remove":"onClickRemove",'click .fea-uploads-attachment:not([data-id="0"])':"onClickSelect","click .fea-uploads-close":"onClickClose","change .fea-uploads-sort":"onChangeSort","click .fea-uploads-update":"onUpdate",mouseover:"onHover",showField:"render","input .images-preview":"filePreviews"},actions:{resize:"onResize"},$control:function(){return this.$(".fea-uploads")},$uploader:function(){return this.$control().data("uploader")},$collection:function(){return this.$(".fea-uploads-attachments")},$attachments:function(){return this.$(".fea-uploads-attachment:not(.not-valid)")},$clone:function(){return this.$(".image-preview-clone")},$attachment:function(e){return this.$('.fea-uploads-attachment[data-id="'+e+'"]')},$active:function(){return this.$(".fea-uploads-attachment.active")},$inValid:function(){return this.$(".fea-uploads-attachment.not-valid")},$main:function(){return this.$(".fea-uploads-main")},$side:function(){return this.$(".fea-uploads-side")},$sideData:function(){return this.$(".fea-uploads-side-data")},isFull:function(){var e=parseInt(this.get("max")),t=this.$attachments().length;return e&&t>=e},getValue:function(){var t=[];return this.$attachments().each((function(){t.push(e(this).data("id"))})),!!t.length&&t},addUnscopedEvents:function(e){},reorderFiles:function(){var t=this;t.$attachments().each((function(a,i){var n=e(this).data("index");a++;let s=new RegExp(`file - ${n}`,"gi");var o=e(this).html().replace(s,"file-"+a);if(e(this).data("index",a),e(this).html(o),e(i).hasClass("active")){var r=t.$side();o=r.html().replace(s,"file-"+a);r.html(o)}}))},addSortable:function(e){this.$collection().sortable({items:'.fea-uploads-attachment:not([data-id="0"])',forceHelperSize:!0,forcePlaceholderSize:!0,scroll:!0,start:function(e,t){t.placeholder.html(t.item.html()),t.placeholder.removeAttr("style")},update:function(t,a){e.$input().trigger("change")}})},initialize:function(){this.files=[],this.addUnscopedEvents(this),this.render()},render:function(){var e=this.$(".fea-uploads-sort"),t=this.$(".fea-uploads-add"),a=this.$attachments().length;this.isFull()?t.addClass("disabled"):t.removeClass("disabled"),a?e.removeClass("disabled"):e.addClass("disabled"),this.resize()},resize:function(){var e=this.$control().width(),t=Math.round(e/150);t=Math.min(t,8),this.$control().attr("data-columns",t)},onResize:function(){this.resize()},openSidebar:function(){this.$control().addClass("-open");var e=this.$control().width()/3;e=parseInt(e),e=Math.max(e,350),this.$(".fea-uploads-side-inner").css({width:e-1}),this.$side().animate({width:e-1},250),this.$main().animate({right:e},250)},closeSidebar:function(){var e=this.$(".fea-uploads-side-data");this.$control().removeClass("-open"),this.$active().append(e.find(".file-meta-data")),this.$active().removeClass("active"),acf.disable(this.$side()),this.$main().animate({right:0},250),this.$side().animate({width:0},250,(function(){e.html("")}))},onClickAdd:function(t,a){if("basic"!=this.get("uploader"))if(this.isFull())this.showNotice({text:acf.__("Maximum selection reached"),type:"warning"});else acf.newMediaPopup({mode:"select",title:acf.__("Add Image to Gallery"),field:this.get("key"),multiple:"add",library:this.get("library"),allowedTypes:this.get("mime_types"),selected:this.val(),select:e.proxy((function(e,t){this.appendAttachment(e,t)}),this)});else this.onClickUpload(t,a)},addAttachment:function(e){e.appendTo(this.$collection()),e.data("index",acf.uniqid("file_"))},filePreviews:function(t,a){var i=this,n=this.$control();n.removeClass("acf-hidden");var s=this.$attachments().length,o=this.$control().data("max");const r=t.currentTarget.files;Object.keys(r).forEach((t=>{if(o>0&&s>=o)return!1;const a=r[t];var l=this.$clone().clone();let d=n.data("max_size");var c=e.bytesToMegaBytes(a.size);if(d&&c>d||c>acf.data.server_upload_size||c>acf.data.server_post_size)return i.showNotice({text:acf.__("File size exceeds the maximum allowed"),type:"warning"}),l.addClass("not-valid").attr("data-id",0),l.find(".actions").remove(),void l.find(".uploads-progress").remove();l.removeClass("acf-hidden image-preview-clone").addClass("fea-uploads-attachment"),i.addAttachment(l);const f=new FileReader;f.onload=e=>{"application/pdf"==a.type&&l.find(".margin").append('<span class="gi-file-name">'+a.name+"</span>");var t=l.find("img");"image/png"!=a.type&&"image/jpg"!=a.type&&"image/jpeg"!=a.type?(t.attr("src",t.data("default")),i.validateFile(a,l)):(t.attr("src",f.result),feaResizeFile(a,f.result,i,l)||(l.addClass("not-valid").attr("data-id",0),l.find(".actions").remove(),l.find(".uploads-progress").remove()))},s++,f.readAsDataURL(a)})),i.$(".images-preview").val(""),s>=o&&o>0&&this.$("input.images-preview").prop("disabled",!0)},validateFile:function(t,a){var i=this,n=a.find(".uploads-progress .percent"),s=a.find(".uploads-progress .bar"),o=this.get("key"),r=new FormData;r.append("action","acf/fields/upload_file/add_attachment"),r.append("file",t),r.append("field_key",o),r.append("nonce",acf.data.nonce),e.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(r),type:"post",processData:!1,contentType:!1,cache:!1,xhr:function(){var t=e.ajaxSettings.xhr();return t.upload.onprogress=function(e){if(e.lengthComputable){var t=Math.round(e.loaded/e.total*100);t<100&&(n.text(t+"%"),s.css("width",t+"%"))}},t},error:function(e){i.showNotice({text:acf.__("Error Uploading Files"),type:"warning"}),a.find(".uploads-progress").remove(),a.addClass("not-valid").append('<p class="errors">'+acf.__("Could not upload file")+"</p>")}}).done((function(t){if(t.success){var o=t.data.id,r=t.data.url;feaUpdateFileMeta(o,r);var l=e("<input>").attr({type:"hidden",name:i.$input().attr("name")+"["+a.data("index")+"]",value:o});a.prepend(l).removeClass("acf-uploading"),n.text("100%"),s.css("width","100%"),i.$input().trigger("change"),setTimeout((function(){a.find(".uploads-progress").remove()}),1e3)}else i.showNotice({text:acf.__("Error Uploading Files"),type:"warning"}),a.find(".uploads-progress").remove(),a.addClass("not-valid").append('<p class="errors">'+t.data+"</p>").removeClass("has-value")}))},onClickUpload:function(e,t){this.$(".not-valid").remove(),this.$(".acf-notice").remove(),this.isFull()?this.showNotice({text:acf.__("Maximum selection reached: "+this.$control().data("max")),type:"warning"}):(this.$inValid()&&(this.$inValid().remove(),this.$("input.images-preview").prop("disabled",!1)),this.$(".images-preview").click())},appendAttachment:function(t,a){if(t=this.validateAttachment(t),!this.isFull()&&!this.$attachment(t.id).length){this.$control().removeClass("acf-hidden");var i=['<div class="fea-uploads-attachment" data-id="'+t.id+'">','<input type="hidden" value="'+t.id+'" name="'+this.getInputName()+'[]">','<div class="thumbnail">','<img src="" alt="">',"</div>",'<div class="filename"></div>','<div class="actions">','<a href="#" class="acf-icon small -cancel dark fea-uploads-remove" data-id="'+t.id+'"></a>',"</div>","</div>"].join(""),n=e(i);if(this.$collection().append(n),"prepend"===this.get("insert")){var s=this.$attachments().eq(a);s.length&&s.before(n)}this.renderAttachment(t),this.render(),this.$input().trigger("change")}},validateAttachment:function(e){if((e=acf.parseArgs(e,{id:"",url:"",alt:"",title:"",filename:"",type:"image"})).attributes){e=e.attributes;var t=acf.isget(e,"sizes",this.get("preview_size"),"url");null!==t&&(e.url=t)}return e},renderAttachment:function(e){e=this.validateAttachment(e);var t=this.$attachment(e.id);if("image"==e.type)t.find(".filename").remove();else{var a=acf.isget(e,"image","src");null!==a&&(e.url=a),t.find(".filename").text(e.filename)}e.url||(e.url=acf.get("mimeTypeIcon"),t.addClass("-icon")),t.find("img").attr({src:e.url,alt:e.alt,title:e.title}),acf.val(t.find("input"),e.id)},editAttachment:function(t){acf.newMediaPopup({mode:"edit",title:acf.__("Edit Image"),button:acf.__("Update Image"),attachment:t,field:this.get("key"),select:e.proxy((function(e,t){this.renderAttachment(e)}),this)})},onClickEdit:function(e,t){var a=t.data("id");a&&this.editAttachment(a)},removeAttachment:function(e){this.closeSidebar(),this.$attachment(e).remove(),this.render(),this.$input().trigger("change")},onClickRemove:function(e,t){e.preventDefault(),e.stopPropagation();var a=t.data("id");a?this.removeAttachment(a):t.parents(".fea-uploads-attachment").remove();this.$attachments().length<this.$control().data("max")&&this.$("input.images-preview").prop("disabled",!1)},selectAttachment:function(e,t){this.get("click_event");if(e.attr("data-lightbox"))return;if(e.attr("data-download")){const t=e.attr("data-href");var a=document.createElement("a");return a.setAttribute("download",""),a.href=t,document.body.appendChild(a),a.click(),void a.remove()}if(!e.hasClass("active")){var i;if(e.find(".file-meta-data").length>0)i=e.find(".file-meta-data");else{var n=(i=this.$(".file-meta-data.clone").clone()).html().replace(/{file-index}/g,e.data("index"));i.html(n),i.removeClass("clone")}i.find(".fea-file-meta").removeAttr("disabled");var s=this.$side(),o=this.$sideData();this.$control().hasClass("-open")&&s.find(".file-meta-data").hide().appendTo(this.$active()),this.$side().find(":focus").trigger("blur"),o.append(i),i.show(),this.$active().removeClass("active"),e.addClass("active"),this.openSidebar()}},onClickSelect:function(e,t){this.selectAttachment(t)},onClickClose:function(e,t){this.$side().find(".file-meta-data").hide().appendTo(this.$active()),this.closeSidebar()},onChangeSort:function(t,a){if(!a.hasClass("disabled")){var i=a.val();if(i){var n=[];this.$attachments().each((function(){n.push(e(this).data("id"))}));var s=this.proxy((function(){var t={action:"acf/fields/gallery/get_sort_order",field_key:this.get("key"),ids:n,sort:i};e.ajax({url:acf.get("ajaxurl"),dataType:"json",type:"post",cache:!1,data:acf.prepareForAjax(t),success:o})})),o=this.proxy((function(e){acf.isAjaxSuccess(e)&&(e.data.reverse(),e.data.map((function(e){this.$collection().prepend(this.$attachment(e))}),this))}));s()}}},onUpdate:function(e,t){this.$side().find(".file-meta-data").hide().appendTo(this.$active()),this.closeSidebar()},onHover:function(){this.addSortable(this),this.off("mouseover")}});acf.registerFieldType(o);var r=acf.Field.extend({type:"recaptcha",wait:"load",actions:{validation_failure:"validationFailure"},$control:function(){return this.$(".frontend-admin-recaptcha")},$input:function(){return this.$('input[type="hidden"]')},$selector:function(){return this.$control().find("> div")},selector:function(){return this.$selector()[0]},onLoad:function(){"v2"===this.get("version")&&this.renderV2(this)},initialize:function(){"v3"===this.get("version")&&this.renderV3()},renderV2:function(e){if(!this.recaptcha&&"undefined"!=typeof grecaptcha){var t=this.selector(),a=this.$input(),i=this.get("siteKey"),n=this.get("theme"),s=this.get("size");this.recaptcha=grecaptcha.render(t,{sitekey:i,theme:n,size:s,callback:function(t){acf.val(a,t,!0),e.removeError()},"error-callback":function(){acf.val(a,"",!0),e.showError("An error has occured")},"expired-callback":function(){acf.val(a,"",!0),e.showError("reCaptcha has expired")}})}},renderV3:function(){var e=this.$input(),t=this.get("siteKey");grecaptcha.ready((function(){grecaptcha.execute(t,{action:"homepage"}).then((function(t){acf.val(e,t,!0)}))}))},validationFailure:function(e){"v2"===this.get("version")&&grecaptcha.reset(this.recaptcha)}});acf.registerFieldType(r);o=acf.Field.extend({type:"custom_terms",select2:!1,wait:"load",events:{removeField:"onRemove",duplicateField:"onDuplicate"},$input:function(){return this.$("select")},initialize:function(){var e=this.$input();if(this.inherit(e),this.get("ui")){var t=this.get("ajax_action");t||(t="acf/fields/"+this.get("type")+"/query"),this.select2=acf.newSelect2(e,{field:this,ajax:this.get("ajax"),multiple:this.get("multiple"),placeholder:this.get("placeholder"),allowNull:this.get("allow_null"),ajaxAction:t})}},onRemove:function(){this.select2&&this.select2.destroy()},onDuplicate:function(e,t,a){this.select2&&(a.find(".select2-container").remove(),a.find("select").removeClass("select2-hidden-accessible"))}});function l(e,t,a,i,n,s){var o=function(e){e.naturalWidth;var t=e.naturalHeight,a=document.createElement("canvas");a.width=1,a.height=t;var i=a.getContext("2d");i.drawImage(e,0,0);for(var n=i.getImageData(0,0,1,t).data,s=0,o=t,r=t;r>s;)0===n[4*(r-1)+3]?o=r:s=r,r=o+s>>1;var l=r/t;return 0===l?1:l}(t);e.drawImage(t,a*o,i*o,n*o,s*o)}acf.registerFieldType(o),feaResizeFile=function(e,t,a,i){i=i||"";let n=a.$control(),s=n.data("resize")||!1,o=n.data("max_width"),r=n.data("max_height");if(!s||"image/png"!=e.type&&"image/jpg"!=e.type&&"image/jpeg"!=e.type)return o&&t.width>o?void a.showNotice({text:acf.__("Image width is too wide"),type:"warning"}):r&&t.height>r?void a.showNotice({text:acf.__("Image height is too tall"),type:"warning"}):(a.validateFile(e,i),!0);let d=document.createElement("img"),c=document.createElement("canvas");return d.onload=function(){const t=navigator.userAgent;if((t.match(/iPad/i)||t.match(/iPhone/i))&&"undefined"!=typeof MegaPixImage){let t=new MegaPixImage(d);window.setTimeout((function(){t.render(c,{maxWidth:o,maxHeight:r,quality:.8}),c.toBlob((function(t){var n=new File([t],"resized_"+e.name,{type:"image/jpeg"});a.validateFile(n,i)}),"image/jpeg",.8)}),3e3)}else{const t=Math.min(o/d.width,r/d.height),n=Math.round(d.width*t),s=Math.round(d.height*t);c.width=n,c.height=s,l(c.getContext("2d"),d,0,0,n,s),c.toBlob((function(t){let n=e.name.replace(/[^a-z0-9.]/gi,"_");var s=new File([t],n,e);a.validateFile(s,i)}),"image/jpeg",true)}},d.src=t,!0},acf.registerConditionForFieldType("hasValue","upload_files"),acf.registerConditionForFieldType("hasNoValue","upload_files"),acf.registerConditionForFieldType("selectionLessThan","upload_files"),acf.registerConditionForFieldType("selectionGreaterThan","upload_files");o=acf.models.RelationshipField.extend({type:"product_grouped"});acf.registerFieldType(o);o=acf.models.RelationshipField.extend({type:"product_cross_sells"});acf.registerFieldType(o);o=acf.models.RelationshipField.extend({type:"product_upsells"});acf.registerFieldType(o);o=acf.Field.extend({type:"upload_image",$control:function(){return this.$(".acf-image-uploader")},$uploader:function(){return this.$control().data("uploader")},$img:function(){return this.$(".image-wrap > img")},$id:function(){return this.$('input[data-name="id"]')},events:{'click a[data-name="add"]':"onClickAdd",'click a[data-name="edit"]':"onClickEdit",'click a[data-name="remove"]':"onClickRemove",'click a[data-name="upload-file"]':"onClickUploadButton","input .image-preview":"imagePreview",'click a[data-name="edit-preview"]':"onClickEditPreview","click button.close-edit":"closeEdit","click .update-meta":"onChangeMeta"},initialize:function(){this.files=[],"basic"===this.get("uploader")&&this.$el.closest("form").attr("enctype","multipart/form-data")},onClickUploadButton:function(e,t){this.$('input[type="file"]').trigger("click")},validateAttachment:function(e){e&&e.attributes&&(e=e.attributes),e=acf.parseArgs(e,{id:0,url:"",alt:"",title:"",caption:"",description:"",width:0,height:0});var t=acf.isget(e,"sizes",this.get("preview_size"));return t&&(e.url=t.url,e.width=t.width,e.height=t.height),e},render:function(e){if(e=this.validateAttachment(e),this.get("destination")){var t=this.$el.parents("form"),a=this.$el.parent(".acf-row");void 0!==a&&(t=a),t.find('[data-key="'+this.get("destination")+'"').find(".acf-url").addClass("-valid").find("input").val(e.url)}else this.updatePreview(e),e.id?(this.val(e.id),this.trigger("change"),this.$control().addClass("has-value")):(this.val(""),this.$control().removeClass("has-value"),this.$(".fea-file-meta").val(""),this.$(".edit-modal").find("img").remove())},updatePreview:function(t){const{url:a,alt:i}=t;this.$img()?.attr({src:a,alt:i});var n=this.$control();if(n.data("preview_element")){var s=e("body").find(n.data("preview_element"));"img"==(n.data("preview_type")||"img")?s.is("img")?s.attr("src",a).attr("srcset",a):(s.find("img").attr("src",a).attr("srcset",a),s.find("source").attr("src",a).attr("srcset",a)):s.style.backgroundImage="url("+attachment.url+")"}},append:function(e,t){var a=function(e,t){for(var a=acf.getFields({key:e.get("key"),parent:t.$el}),i=0;i<a.length;i++)if(!a[i].val())return a[i];return!1},i=a(this,t);i||(t.$(".acf-button:last").trigger("click"),i=a(this,t)),i&&i.render(e)},selectAttachment:function(){var t=this.parent(),a=t&&"repeater"===t.get("type");acf.newMediaPopup({mode:"select",type:"image",title:acf.__("Select Image"),field:this.get("key"),multiple:a,library:this.get("library"),allowedTypes:this.get("mime_types"),select:e.proxy((function(e,a){a>0?this.append(e,t):this.render(e)}),this)})},editAttachment:function(){var t=this.val();if(t)acf.newMediaPopup({mode:"edit",title:acf.__("Edit Image"),button:acf.__("Update Image"),attachment:t,field:this.get("key"),select:e.proxy((function(e,t){this.render(e)}),this)})},removeAttachment:function(){this.render(!1)},onClickAdd:function(e,t){"basic"===this.$uploader()?this.$('input[type="file"]').trigger("click"):this.selectAttachment()},onClickEdit:function(e,t){this.editAttachment()},onChangeMeta:function(e,t){t.closest(".edit-modal").hide()},onClickEditPreview:function(e,t){acf.showModal(t,600,this.$el);var a=this.$(".file-meta-data");"0"==this.$(".edit-modal").find(".file-meta-data").length&&this.$(".edit-modal").find(".content-container").html(a),"0"==this.$(".edit-modal").find("img").length&&a.prepend(this.$img().clone()).show(),this.$(".fea-file-meta").removeAttr("disabled")},closeEdit:function(){this.$(".edit-modal").hide()},onClickRemove:function(){this.files.pop(),this.render(!1)},getRelatedType:function(){return this.get("field_type")},getRelatedPrototype:function(){return acf.getFieldType(this.getRelatedType()).prototype},imagePreview:function(t,a){this.removeError();let i=t.target.files[0],n=new FileReader,s=this.$control(),o=this;s.find("p.errors").remove();let r=s.data("max_size");var l=e.bytesToMegaBytes(i.size);r&&l>r||l>acf.data.server_upload_size||l>acf.data.server_post_size?o.showNotice({text:acf.__("File size is too large"),type:"warning"}):(s.addClass("has-value"),s.addClass("not-valid").find(".uploads-progress").removeClass("frontend-admin-hidden"),n.onload=function(){const e={url:n.result,alt:i.name,title:i.name};o.updatePreview(e),o.$(".file-meta-data .fea-file-meta.title").val(i.name),o.$(".file-meta-data .fea-file-meta.alt").val(i.name),feaResizeFile(i,n.result,o),o.$(".image-preview").val("")},imagePreview=!0,n.readAsDataURL(i))},validateFile:function(t){let a=this,i=this.$control(),n=i.parents("form"),s=i.find(".uploads-progress");s.removeClass("frontend-admin-hidden");let o=s.find(".percent"),r=s.find(".bar"),l=this.get("key"),d=new FormData;d.append("action","acf/fields/upload_file/add_attachment"),d.append("file",t),d.append("field_key",l),d.append("nonce",acf.data.nonce),n.find(".fea-submit-button").addClass("disabled"),i.find(".acf-actions").hide(),e.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(d),type:"post",processData:!1,contentType:!1,cache:!1,xhr:function(){var t=e.ajaxSettings.xhr();return t.upload.onprogress=function(e){if(e.lengthComputable){var t=Math.round(e.loaded/e.total*100);t<100&&(o.text(t+"%"),r.css("width",t+"%"))}},t}}).done((function(e){if(e.success){var l=e.data.id,d=e.data.url;feaUpdateFileMeta(l,d),acf.doAction("feaUploadFile",t,l,d,a.$el),a.$id().val(l),n.find(".fea-submit-button").removeClass("disabled"),o.text("100%"),r.css("width","100%"),setTimeout((function(){i.find(".uploads-progress").addClass("frontend-admin-hidden"),o.text("0%"),r.css("width","0"),i.find(".acf-actions").show()}),1e3),i.removeClass("not-valid")}else a.$(".show-if-value").find("img").attr("src",""),o.text("0%"),r.css("width","0"),i.find(".acf-actions").hide(),s.addClass("frontend-admin-hidden"),i.removeClass("has-value"),a.showNotice({text:e.data,type:"warning"})}))}});acf.registerFieldType(o);o=acf.Field.extend({type:"text_editor",wait:"load",events:{"mousedown .acf-editor-wrap.delay":"onMousedown",unmountField:"disableEditor",remountField:"enableEditor",removeField:"disableEditor"},$control:function(){return this.$(".acf-editor-wrap")},$input:function(){return this.$("textarea")},getMode:function(){return this.$control().hasClass("tmce-active")?"visual":"text"},initialize:function(){this.$control().hasClass("delay")||this.initializeEditor()},initializeEditor:function(){var e=this.$control(),t=this.$input(),a={tinymce:!0,quicktags:!0,toolbar:this.get("toolbar"),mode:this.getMode(),field:this},i=t.attr("id"),n=acf.uniqueId("acf-editor-"),s=t.data(),o=t.val();acf.rename({target:e,search:i,replace:n,destructive:!0}),this.set("id",n,!0),this.$input().data(s).val(o),acf.tinymce.initialize(n,a)},onMousedown:function(e){e.preventDefault();var t=this.$control();t.removeClass("delay"),t.find(".acf-editor-toolbar").remove(),this.initializeEditor()},enableEditor:function(){"visual"==this.getMode()&&acf.tinymce.enable(this.get("id"))},disableEditor:function(){acf.tinymce.destroy(this.get("id"))}});acf.registerFieldType(o);o=acf.models.UploadImageField.extend({type:"upload_file",$control:function(){return this.$(".acf-file-uploader")},$uploader:function(){return this.$control().data("uploader")},$img:function(){return this.$(".file-icon > img")},$id:function(){return this.$('input[data-name="id"]')},events:{'click a[data-name="add"]':"onClickAdd",'click a[data-name="edit"]':"onClickEdit",'click a[data-name="remove"]':"onClickRemove",'click a[data-name="upload-file"]':"onClickUploadButton",'click a[data-name="edit-preview"]':"onClickEditPreview","input .file-preview":"filePreview","click .update-meta":"onChangeMeta"},onClickEditPreview:function(e,t){acf.showModal(t,600,this.$el);var a=this.$(".file-meta-data");"0"==this.$(".edit-modal").find(".file-meta-data").length&&this.$(".edit-modal").find(".content-container").html(a),"0"==this.$(".edit-modal").find("img").length&&a.prepend(this.$img().clone()).show(),this.$(".file-meta-data").removeAttr("disabled")},getRelatedType:function(){return this.get("field_type")},getRelatedPrototype:function(){return acf.getFieldType(this.getRelatedType()).prototype},filePreview:function(t,a){var i=this;i.removeError();var n=new FileReader,s=this.$control();s.find("p.errors").remove();const o=t.target.files[0];let r=s.data("max_size");var l=e.bytesToMegaBytes(o.size);r&&l>r||l>acf.data.server_upload_size||l>acf.data.server_post_size?i.showNotice({text:acf.__("File size is too large"),type:"warning"}):(s.addClass("has-value"),s.addClass("not-valid"),n.onload=function(){s.addClass("has-value");var e=i.$img();if(e&&("image/png"!=o.type&&"image/jpg"!=o.type&&"image/jpeg"!=o.type?(e.attr("src",e.data("default")),i.validateFile(o)):(e.attr("src",n.result),feaResizeFile(o,n.result,i))),i.$(".file-preview").val(""),s.find("[data-name=filename]").html(o.name).attr("href","#"),o.size<1e6)var t=Math.floor(o.size/1e3)+"KB";else t=Math.floor(o.size/1e6)+"MB";s.find("[data-name=filesize]").html(t)},imagePreview=!0,n.readAsDataURL(o))},validateAttachment:function(e){return void 0!==(e=e||{}).id&&(e=e.attributes),e=acf.parseArgs(e,{url:"",alt:"",title:"",filename:"",filesizeHumanReadable:"",icon:"/wp-includes/images/media/default.png"})},render:function(e){e=this.validateAttachment(e),this.$("img").attr({src:e.icon,alt:e.alt,title:e.title}),this.$('[data-name="title"]').text(e.title),this.$('[data-name="filename"]').text(e.filename).attr("href",e.url),this.$('[data-name="filesize"]').text(e.filesizeHumanReadable);var t=e.id||"";acf.val(this.$input(),t),t?this.$control().addClass("has-value"):this.$control().removeClass("has-value")},selectAttachment:function(){var t=this.parent(),a=t&&"repeater"===t.get("type");acf.newMediaPopup({mode:"select",title:acf.__("Select File"),field:this.get("key"),multiple:a,library:this.get("library"),allowedTypes:this.get("mime_types"),select:e.proxy((function(e,a){a>0?this.append(e,t):this.render(e)}),this)})},editAttachment:function(){var t=this.val();if(!t)return!1;acf.newMediaPopup({mode:"edit",title:acf.__("Edit File"),button:acf.__("Update File"),attachment:t,field:this.get("key"),select:e.proxy((function(e,t){this.render(e)}),this)})}});acf.registerFieldType(o);e.each(["post_content","product_description","text_editor","featured_image","main_image","site_logo","site_favicon","upload_image"],(function(e,t){if("upload_image"!=t){var a=acf.models.UploadImageField.extend({type:t});acf.registerFieldType(a)}acf.registerConditionForFieldType("hasValue",t),acf.registerConditionForFieldType("hasNoValue",t)}))}(jQuery),function(e,t){var a=acf.Field.extend({type:"related_terms",data:{ftype:"select"},select2:!1,wait:"load",events:{'click a[data-name="add"]':"onClickAdd",'click input[type="radio"]':"onClickRadio",'click input[type="checkbox"]':"onClickCheckbox","click .tax-btn":"onClickButton"},$control:function(){return this.$(".acf-related-terms-field")},$input:function(){return this.getRelatedPrototype().$input.apply(this,arguments)},getRelatedType:function(){var e=this.get("ftype");return"multi_select"==e&&(e="select"),e},getRelatedPrototype:function(){return acf.getFieldType(this.getRelatedType()).prototype},getValue:function(){return this.getRelatedPrototype().getValue.apply(this,arguments)},setValue:function(){return this.getRelatedPrototype().setValue.apply(this,arguments)},initialize:function(){var e=this.$input();this.inherit(e),this.get("ui")&&(ajaxAction="acf/fields/related_terms/query",this.select2=acf.newSelect2(e,{field:this,ajax:this.get("ajax"),multiple:this.get("multiple"),placeholder:this.get("placeholder"),allowNull:this.get("allow_null"),ajaxAction:ajaxAction}))},onRemove:function(){this.select2&&this.select2.destroy()},onClickAdd:function(t,a){var i=this,n=!1,s=!1,o=!1,r=!1,l=!1,d=!1,c=function(e){n.loading(!1),n.content(e),s=n.$("form"),o=n.$('input[name="term_name"]'),r=n.$('select[name="term_parent"]'),l=n.$(".acf-submit-button"),o.focus(),n.on("submit","form",f)},f=function(t,a){if(t.preventDefault(),t.stopImmediatePropagation(),""===o.val())return o.focus(),!1;acf.startButtonLoading(l);var n={action:"acf/fields/related_terms/add_term",field_key:i.get("key"),taxonomy:i.get("taxonomy"),term_name:o.val(),term_parent:r.length?r.val():0};e.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(n),type:"post",dataType:"json",success:u})},u=function(e){acf.stopButtonLoading(l),d&&d.remove(),acf.isAjaxSuccess(e)?(o.val(""),p(e.data),d=acf.newNotice({type:"success",text:acf.getAjaxMessage(e),target:s,timeout:2e3,dismiss:!1})):d=acf.newNotice({type:"error",text:acf.getAjaxError(e),target:s,timeout:2e3,dismiss:!1}),o.focus()},p=function(t){var a=e('<option value="'+t.term_id+'">'+t.term_label+"</option>");t.term_parent?r.children('option[value="'+t.term_parent+'"]').after(a):r.append(a),acf.getFields({type:"related_terms"}).map((function(e){e.get("taxonomy")==i.get("taxonomy")&&e.appendTerm(t)})),i.selectTerm(t.term_id)};!function(){n=acf.newPopup({title:a.attr("title"),loading:!0,width:"300px"});var t={action:"acf/fields/related_terms/add_term",field_key:i.get("key"),taxonomy:i.get("taxonomy")};e.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(t),type:"post",dataType:"html",success:c})}()},appendTerm:function(e){"select"==this.getRelatedType()?this.appendTermSelect(e):this.appendTermCheckbox(e)},appendTermSelect:function(e){this.select2.addOption({id:e.term_id,text:e.term_label})},appendTermCheckbox:function(t){var a=this.$("[name]:first").attr("name"),i=this.$("ul:first");"checkbox"==this.getRelatedType()&&(a+="[]");var n=e(['<li data-id="'+t.term_id+'">',"<label>",'<input type="'+this.get("ftype")+'" value="'+t.term_id+'" name="'+a+'" /> ',"<span>"+t.term_name+"</span>","</label>","</li>"].join(""));if(t.term_parent){var s=i.find('li[data-id="'+t.term_parent+'"]');(i=s.children("ul")).exists()||(i=e('<ul class="children acf-bl"></ul>'),s.append(i))}i.append(n)},selectTerm:function(e){"select"==this.getRelatedType()?this.select2.selectOption(e):this.$('input[value="'+e+'"]').prop("checked",!0).trigger("change")},onClickRadio:function(e,t){var a=t.parent("label"),i=a.hasClass("selected");this.$(".selected").removeClass("selected"),a.addClass("selected"),this.get("allow_null")&&i&&(a.removeClass("selected"),t.prop("checked",!1).trigger("change"))},onClickCheckbox:function(e,t){t.parent("label").toggleClass("selected")},onClickButton:function(e,t){t.closest("label").find("input").trigger("click")}});acf.registerFieldType(a),acf.registerConditionForFieldType("hasValue","related_terms"),acf.registerConditionForFieldType("hasNoValue","related_terms"),acf.registerConditionForFieldType("equalTo","related_terms"),acf.registerConditionForFieldType("notEqualTo","related_terms"),acf.registerConditionForFieldType("patternMatch","related_terms"),acf.registerConditionForFieldType("contains","related_terms"),acf.registerConditionForFieldType("selectionLessThan","related_terms"),acf.registerConditionForFieldType("selectionGreaterThan","related_terms")}(jQuery),acf.add_filter("select2_ajax_data",(function(e,t,a,i,n){return 0!=i&&($field_taxonomy=i.find(".acf-related-terms-field").data("taxonomy"),e.taxonomy=$field_taxonomy),e})),function(e,t){var a=acf.Field.extend({type:"display_name",select2:!1,wait:"load",events:{removeField:"onRemove",duplicateField:"onDuplicate"},$input:function(){return this.$("select")},initialize:function(){var e=this.$input();if(this.inherit(e),this.get("ui")){var t=this.get("ajax_action");t||(t="acf/fields/"+this.get("type")+"/query"),this.select2=acf.newSelect2(e,{field:this,ajax:this.get("ajax"),multiple:this.get("multiple"),placeholder:this.get("placeholder"),allowNull:this.get("allow_null"),ajaxAction:t})}},onRemove:function(){this.select2&&this.select2.destroy()},onDuplicate:function(e,t,a){this.select2&&(a.find(".select2-container").remove(),a.find("select").removeClass("select2-hidden-accessible"))}});acf.registerFieldType(a);e.each(["allow_comments"],(function(e,t){var a=acf.models.TrueFalseField.extend({type:t});acf.registerFieldType(a),acf.registerConditionForFieldType("equalTo",t),acf.registerConditionForFieldType("notEqualTo",t)}));var i=new acf.Model({name:"this.collapsedRows",key:function(e,t){var a=this.get(e+t)||0;return a++,this.set(e+t,a,!0),a>1&&(e+="-"+a),e},load:function(e){e=this.key(e,"load");var t=acf.getPreference(this.name);return!(!t||!t[e])&&t[e]},save:function(t,a){t=this.key(t,"save");var i=acf.getPreference(this.name)||{};null===a?delete i[t]:i[t]=a,e.isEmptyObject(i)&&(i=null),acf.setPreference(this.name,i)}});a=acf.Field.extend({type:"list_items",wait:"",events:{'click a[data-event="add-row"]':"onClickAdd",'click a[data-event="duplicate-row"]':"onClickDuplicate",'click a[data-event="remove-row"]':"onClickRemove",'click [data-event="collapse-row"]':"onClickCollapse",showField:"onShow",unloadField:"onUnload",mouseover:"onHover"},$control:function(){return this.$(".acf-list-items:first")},$table:function(){return this.$("table:first")},$tbody:function(){return this.$("tbody:first")},$rows:function(){return this.$("tbody:first > tr").not(".acf-clone")},$row:function(e){return this.$("tbody:first > tr:eq("+e+")")},$clone:function(){return this.$("tbody:first > tr.acf-clone")},$actions:function(){return this.$(".acf-actions:last")},$button:function(){return this.$(".acf-actions:last .button")},getValue:function(){return this.$rows().length},allowRemove:function(){var e=parseInt(this.get("min"));return!e||e<this.val()},allowAdd:function(){var e=parseInt(this.get("max"));return!e||e>this.val()},addSortable:function(e){1!=this.get("max")&&this.$tbody().sortable({items:"> tr",handle:"> td.order",forceHelperSize:!0,forcePlaceholderSize:!0,scroll:!0,stop:function(t,a){e.render()},update:function(t,a){e.$input().trigger("change")}})},addCollapsed:function(){var t=i.load(this.get("key"));if(!t)return!1;this.$rows().each((function(a){t.indexOf(a)>-1&&e(this).addClass("-collapsed")}))},addUnscopedEvents:function(t){this.on("invalidField",".acf-row",(function(a){var i=e(this);t.isCollapsed(i)&&t.expand(i)}))},initialize:function(){this.addUnscopedEvents(this),acf.disable(this.$clone(),this.cid),this.render()},render:function(){this.$rows().each((function(t){e(this).find("> .order:not(.ids) > span").html(t+1)}));var t=this.$control(),a=this.$button();0==this.val()?t.addClass("-empty"):t.removeClass("-empty"),this.allowAdd()?(t.removeClass("-max"),a.removeClass("disabled")):(t.addClass("-max"),a.addClass("disabled"))},validateAdd:function(){if(this.allowAdd())return!0;var e=this.get("max"),t=acf.__("Maximum rows reached ({max} rows)");return t=t.replace("{max}",e),this.showNotice({text:t,type:"warning"}),!1},onClickAdd:function(e,t){if(!this.validateAdd())return!1;t.hasClass("acf-icon")?this.add({before:t.closest(".acf-row")}):this.add()},add:function(t){if(!this.allowAdd())return!1;t=acf.parseArgs(t,{before:!1});var a=acf.duplicate({target:this.$clone(),append:this.proxy((function(a,i){t.before?t.before.before(i):a.before(i),i.removeClass("acf-clone"),acf.enable(i,this.cid),this.render(),e("html, body").animate({scrollTop:e(i).offset().top-75})}))});return this.$input().trigger("change"),a},onClickDuplicate:function(e,t){if(!this.validateAdd())return!1;var a=t.closest(".acf-row");this.duplicateRow(a)},duplicateRow:function(e){if(!this.allowAdd())return!1;var t=this.get("key"),a=acf.duplicate({target:e,rename:function(e,a,i,n){return"data-id"===e?a.replace(t+"-"+i,t+"-"+n):a.replace(t+"]["+i,t+"]["+n)},before:function(e){acf.doAction("unmount",e)},after:function(e,t){acf.doAction("remount",e)}});return this.$input().trigger("change"),this.render(),acf.focusAttention(a),a},validateRemove:function(){if(this.allowRemove())return!0;var e=this.get("min"),t=acf.__("Minimum rows reached ({min} rows)");return t=t.replace("{min}",e),this.showNotice({text:t,type:"warning"}),!1},onClickRemove:function(e,t){var a=t.closest(".acf-row");if(e.shiftKey)return this.remove(a);a.addClass("-hover");acf.newTooltip({confirmRemove:!0,target:t,context:this,confirm:function(){this.remove(a)},cancel:function(){a.removeClass("-hover")}})},remove:function(e){var t=this;acf.remove({target:e,endHeight:0,complete:function(){t.$input().trigger("change"),t.render()}})},isCollapsed:function(e){return e.hasClass("-collapsed")},collapse:function(e){e.addClass("-collapsed"),acf.doAction("hide",e,"collapse")},expand:function(t){t.removeClass("-collapsed"),acf.doAction("show",t,"collapse"),e("html, body").animate({scrollTop:e(t).closest(".acf-row").offset().top-75})},onClickCollapse:function(e,t){var a=t.closest(".acf-row"),i=this.isCollapsed(a);e.shiftKey&&(a=this.$rows()),i?this.expand(a):this.collapse(a)},onShow:function(e,t,a){var i=acf.getFields({is:":visible",parent:this.$el});acf.doAction("show_fields",i)},onUnload:function(){var t=[];this.$rows().each((function(a){e(this).hasClass("-collapsed")&&t.push(a)})),t=t.length?t:null,void 0!==i&&i.save(this.get("key"),t)},onHover:function(){this.addSortable(this),this.off("mouseover")}});acf.registerFieldType(a),acf.registerConditionForFieldType("hasValue","list_items"),acf.registerConditionForFieldType("hasNoValue","list_items"),acf.registerConditionForFieldType("lessThan","list_items"),acf.registerConditionForFieldType("greaterThan","list_items")}(jQuery),function(e,t){var a=acf.models.PostObjectField.extend({type:"post_to_edit",events:{"change .acf-input > select":"onChangePost"},getType:function(){return"post"},onChangePost:function(t,a){if(a.hasClass("disabled"))return;if(!a.val())return;let i=a.parents(".frontend-form");i.addClass("disabled");let n=this;a.after('<span class="fea-loader"></span>');let s=i.find("input[name=_acf_form]").val(),o=i.find("input[name=_acf_current_url]").val(),r={action:"frontend_admin/forms/change_form",item_id:a.val(),type:this.getType(),form_data:s,current_url:o};e.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(r),type:"post",dataType:"json",cache:!1,success:function(t){if(t.success&&t.data.reload_form){i.removeClass("disabled"),n.$(".fea-loader").remove();let s=e(t.data.reload_form);i.replaceWith(s),acf.doAction("append",s);const o=new URL(window.location.href),r=n.$el.data("url_query")||"post_id";o.searchParams.set(r,a.val()),window.history.pushState({post_id:a.val()},"",o)}}})}});acf.registerFieldType(a);a=acf.models.PostToEditField.extend({type:"product_to_edit",events:{"change .acf-input > select":"onChangePost"},getType:function(){return"product"}});acf.registerFieldType(a);a=acf.models.UserField.extend({type:"user_to_edit",events:{"change .acf-input > select":"onChangeUser"},getType:function(){return"user"},onChangeUser:function(t,a){if(a.hasClass("disabled"))return;if(!a.val())return;let i=a.parents(".frontend-form");i.addClass("disabled");let n=this;a.after('<span class="fea-loader"></span>');let s=i.find("input[name=_acf_form]").val(),o=i.find("input[name=_acf_current_url]").val(),r={action:"frontend_admin/forms/change_form",item_id:a.val(),type:this.getType(),form_data:s,current_url:o};e.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(r),type:"post",dataType:"json",cache:!1,success:function(t){if(t.success&&t.data.reload_form){i.removeClass("disabled"),n.$(".fea-loader").remove();let s=e(t.data.reload_form);i.replaceWith(s),acf.doAction("append",s);const o=new URL(window.location.href),r=n.$el.data("url_query")||"user_id";o.searchParams.set(r,a.val()),window.history.pushState({user_id:a.val()},"",o)}}})}});acf.registerFieldType(a);a=acf.models.ImageField.extend({type:"url_upload",$control:function(){return this.$(".acf-file-uploader")},$input:function(){return this.$('input[type="hidden"]')},validateAttachment:function(e){return undefined!==(e=e||{}).id&&(e=e.attributes),e=acf.parseArgs(e,{url:"",alt:"",title:"",filename:"",filesizeHumanReadable:"",icon:"/wp-includes/images/media/default.png"})},render:function(e){e=this.validateAttachment(e);var t=this.$el.parents("form"),a=this.$el.parent(".acf-row");void 0!==a&&(t=a),t.find('[data-key="'+this.get("destination")+'"').find(".acf-url").addClass("-valid").find("input").val(e.url)},selectAttachment:function(){var t=this.parent(),a=t&&"repeater"===t.get("type");acf.newMediaPopup({mode:"select",title:acf.__("Select File"),field:this.get("key"),multiple:a,library:this.get("library"),allowedTypes:this.get("mime_types"),select:e.proxy((function(e,a){a>0?this.append(e,t):this.render(e)}),this)})},editAttachment:function(){var t=this.val();if(!t)return!1;acf.newMediaPopup({mode:"edit",title:acf.__("Edit File"),button:acf.__("Update File"),attachment:t,field:this.get("key"),select:e.proxy((function(e,t){this.render(e)}),this)})}});acf.registerFieldType(a);var i=["hasValue","hasNoValue","equalTo","notEqualTo","patternMatch","contains"];e.each(["post_title","product_title","site_title","site_tagline","term_name","username","first_name","last_name","nickname"],(function(t,a){e.each(i,(function(e,t){acf.registerConditionForFieldType(t,a)}))}));a=acf.models.SelectField.extend({type:"post_author"});acf.registerFieldType(a);a=acf.models.SelectField.extend({type:"product_author"});acf.registerFieldType(a),acf.addFilter("select2_ajax_data/name=mailchimp_lists",(function(e,t,a,i,n){if(!i)return e;var s=i.$el.siblings(".acf-field[data-key=api_key]").find("input[type=text]").val();return s&&(e.api_key=s),e})),acf.addFilter("select2_ajax_data/name=fields_exclude",(function(t,a,i,n,s){if(!n)return t;var o=n.$el,r=o.siblings(".acf-field[data-key=fields_select]").find("select.fields-and-groups"),l=o.find(".field-group-fields");if(r){var d=r.select2("val"),c=[];if(d&&e.each(d,(function(e,t){t.indexOf("group")>=0&&c.push(t)})),c.length<1)return l.empty().trigger("change"),t;t.groups=c}return t})),new acf.Model({filters:{select2_args:"select2Args",select2_ajax_data:"select2Ajax"},select2Args:function(t,a,i,n,s){return n?(n.get("closeOnSelect")&&(t.closeOnSelect=!1),n.get("allowCustom")&&(t.tags=!0,t.createTag=function(t){var a=e.trim(t.term);if(""===a)return null;var i=!1;return this.$element.find("option").each((function(){if(this.value.toLowerCase()===a.toLowerCase())return i=!0,!1})),i?null:{id:a,text:a}},t.insertTag=function(t,a){var i=!1;e.each(t,(function(){if(e.trim(a.text).toUpperCase()===e.trim(this.text).toUpperCase())return i=!0,!1})),i||t.unshift(a)}),t=acf.applyFilters("select2_args/type="+n.get("type"),t,a,i,n,s),t=acf.applyFilters("select2_args/name="+n.get("name"),t,a,i,n,s),t=acf.applyFilters("select2_args/key="+n.get("key"),t,a,i,n,s)):t},select2Ajax:function(e,t,a,i,n){return e=acf.applyFilters("select2_ajax_data/type="+i.get("type"),e,t,a,i,n),e=acf.applyFilters("select2_ajax_data/name="+i.get("name"),e,t,a,i,n),(e=acf.applyFilters("select2_ajax_data/key="+i.get("key"),e,t,a,i,n)).action&&(e=acf.applyFilters("select2_ajax_data/action="+e.action,e,t,a,i,n)),e}});a=acf.Field.extend({type:"fea_plans",select2:!1,wait:"load",events:{"click .add-plan":"addEditPlan","click .edit-plan":"addEditPlan","click .delete-plan":"deletePlan"},actions:{frontend_form_success:"showPlan"},$input:function(){return this.$("select")},initialize:function(){var e=this.$input();this.inherit(e)},addEditPlan:function(e,t){acf.getForm(t,"plans")},deleteObject:function(t){var a=t.parents(".fea-single-plan");a.append('<span class="fea-loader"></span>');var i={action:"frontend_admin/plans/delete",plan:a.data("plan")};e.ajax({url:acf.get("ajaxurl"),type:"post",data:acf.prepareForAjax(i),cache:!1,success:function(e){e.success?a.remove():(a.find(".fea-loader").remove(),t.removeClass("disabled"))}})},showPlan:function(e){if(e.success){var t=e.data,a=t.plan||!1;if(!a)return;if(t.new)(i=this.$(".fea-single-plan.clone").clone().removeClass("acf-hidden clone").data("plan",a.id).attr("data-plan",a.id)).find("input").val(a.id).removeAttr("disabled"),this.$(".fea-plans").append(i);else var i=this.$("[data-plan="+a.id+"]");i.find(".fea-plan-title").text(a.title+" - "+a.pricing+" "+a.currency)}},deletePlan:function(e,t){if(!t.hasClass("disabled")){t.addClass("disabled");var a=this;acf.newTooltip({confirm:!0,text:t.data("confirm"),target:t,context:t.parents("acf-field"),confirm:function(){a.deleteObject(t)},cancel:function(){t.removeClass("disabled")}})}}});acf.registerFieldType(a)}(jQuery),document.addEventListener("click",(function(e){if(e.target.classList.contains("fea-password-toggle")){let t=e.target.closest(".acf-field").querySelector("input.fea-password");"password"===t.type?(t.type="text",e.target.classList.remove("dashicons-visibility"),e.target.classList.add("dashicons-hidden")):(t.type="password",e.target.classList.remove("dashicons-hidden"),e.target.classList.add("dashicons-visibility"))}})),document.addEventListener("DOMContentLoaded",(function(){function e(e){const t=e.getAttribute("data-conditions");if(!t)return!0;const a=JSON.parse(t);let i=!1;const n=e.closest(".acf-row");return a.forEach((e=>{let t=!0;!e||e.length<1||(e.forEach((e=>{let a;if(a=n&&document.querySelector(`[data-key="${e.field}"]`).closest(".acf-row")?n.querySelectorAll(`[data-key="${e.field}"] input, \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  [data-key="${e.field}"] select, \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  [data-key="${e.field}"] textarea`):document.querySelectorAll(`[data-key="${e.field}"] input, \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t [data-key="${e.field}"] select, \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t [data-key="${e.field}"] textarea`),!a.length)return;let i=!1;a.forEach((t=>{const a=function(e){if(!e)return null;if("radio"===e.type||"checkbox"===e.type){const t=document.querySelector(`[name="${e.name}"]:checked`);return t?t.value:null}return e.value||null}(t);switch(e.operator){case"!=empty":a&&(i=!0);break;case"==empty":a||(i=!0);break;case"==":a==e.value&&(i=!0);break;case"!=":a!=e.value&&(i=!0);break}})),i||(t=!1)})),t&&(i=!0))})),i}function t(){document.querySelectorAll("[data-conditions]").forEach((t=>{!function(e,t){let a=e.parent().closest("[data-conditions]");for(;a;){const i=window.getComputedStyle(a);if("none"===i.display||"hidden"===i.visibility){t=!1;break}a=e.parent().closest("[data-conditions]")}e.style.display=t?"block":"none",e.querySelectorAll("input, select, textarea").forEach((e=>{t?e.removeAttribute("disabled"):e.setAttribute("disabled","disabled")}))}(t,e(t))}))}document.addEventListener("change",(function(e){e.target.matches("input, select, textarea")&&t()})),t()}));