<?php
function inscricao_formulario_completo_email() {
    $user = wp_get_current_user();
    $user_id = $user->ID;
    $email_sent = get_user_meta($user_id, 'inscricao_formulario_email_sent', true);
    
    // Verifica se o e-mail já foi enviado ou se o status_candidatura está vazio
    if ($email_sent ) {
        return; // Se já foi enviado ou status_candidatura está vazio, encerra a função
    }
    
    update_user_meta($user_id, 'inscricao', 'completa');

    $to = $user->user_email;
    $first_name = $user->first_name;
    $subject = "Parabéns! Você completou a primeira etapa do Alagoas Tech";
    $headers = [
        'Content-Type: text/html; charset=UTF-8',
        'From: Instituto Panapaná <<EMAIL>>'
    ];
    
    $message = '<html><body>';
    $message .= '            <img style="max-width: 150px; height: auto;" src="https://cursos.institutopanapana.org.br/wp-content/uploads/2023/06/LOGO-insituto-panapana-org-hr.png" alt="Instituto Panapaná Logo" data-mce-src="https://cursos.institutopanapana.org.br/ugyrgems/2023/06/LOGO-insituto-panapana-org-hr.png" data-mce-style="max-width: 150px; height: auto;" data-mce-selected="1">';
    $message .= '<p>Olá ' . $first_name . '!</p>';
    $message .= '<p>Que ótimo, você concluiu o Formulário de Inscrição para o Alagoas Tech! Você está um passo mais próximo de iniciar sua jornada incrível no mundo da tecnologia.</p>';
    $message .= '<p>Agora é hora de descobrir suas habilidades digitais! O próximo passo é realizar o Teste Vocacional. Ele vai te ajudar a identificar suas aptidões em áreas como Programação, WordPress, Design Gráfico, Edição de Vídeo e Tráfego Pago.</p>';
    $message .= '<p>Acesse a plataforma e continue sua inscrição. É rapidinho!</p>';
    $message .= '<p><a href="https://cursos.institutopanapana.org.br/login/" style="background-color: #4CAF50; border: none; color: white; padding: 15px 32px; text-align: center; text-decoration: none; display: inline-block; font-size: 16px; margin: 4px 2px; cursor: pointer;">Continuar Inscrição</a></p>';
    $message .= '<p>Estamos ansiosos para conhecer seus talentos.</p>';
    $message .= '<p>Até breve,<br>Equipe Instituto Panapaná</p>';
    $message .= '</body></html>';
    
    $email_sent = wp_mail($to, $subject, $message, $headers);

    // Se o e-mail for enviado com sucesso, marca como enviado no meta do usuário
    if ($email_sent) {
        update_user_meta($user_id, 'inscricao_formulario_email_sent', true);
    }
}
add_shortcode('inscricao_formulario_completo', 'inscricao_formulario_completo_email');