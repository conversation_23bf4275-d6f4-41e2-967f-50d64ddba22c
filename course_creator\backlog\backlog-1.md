# Backlog Session 1 - <PERSON><PERSON>MS Automation Project

**Date:** 2025-07-04  
**Session Duration:** Full Phase 1 Implementation  
**Status:** Phase 1 Complete ✅

## Project Overview

Automating course creation for WordPress + Tutor LMS platform to replace manual process of creating courses with YouTube videos, VTT subtitles, e-books, and quizzes.

### Current Manual Process
- Create course structure manually in WordPress admin
- Add YouTube embed links with custom shortcode: `[custom_video_with_vtt]`
- Upload PDFs and create iframe embeds for e-books
- Create quizzes with questions and answers
- Use yt-dlp to download subtitles: `yt-dlp --write-auto-subs --sub-langs pt --sub-format vtt --skip-download [URL]`

### Automation Goal
4-phase approach to create web-based interface for bulk course creation from CSV input with automated subtitle handling.

## Phase 1: Database Investigation & Proof-of-Concept ✅

### Task 1.1: Analyze Course Metadata Structure ✅
**Completed:** Database structure analysis using PowerShell Select-String to examine large SQL files

**Key Findings:**
- **Post Types Hierarchy:**
  - `courses` - Main course posts
  - `topics` - Course modules/sections (children of courses)
  - `lesson` - Individual lessons (children of topics)
  - `tutor_quiz` - Quiz posts (children of topics)

- **Database Tables:**
  - `wp_posts` - All content with hierarchical relationships via `post_parent`
  - `wp_postmeta` - Metadata using `meta_key`/`meta_value` pairs
  - `wp_tutor_quiz_questions` - Quiz questions structure
  - `wp_tutor_quiz_question_answers` - Quiz answer options

- **Relationship Pattern:**
  ```
  Course (ID: 2913) 
  └── Topic (post_parent: 2913, menu_order: 1)
      ├── Lesson (post_parent: topic_id, menu_order: 1)
      ├── Lesson (post_parent: topic_id, menu_order: 2)
      └── Quiz (post_parent: topic_id, menu_order: 3)
  ```

### Task 1.2: Analyze Video Lesson Storage ✅
**Completed:** Examined `_video` meta key structure for video lessons

**Key Findings:**
- Video configuration stored as serialized PHP array in `_video` meta key
- **Structure:**
  ```php
  array(
      'source' => 'shortcode',  // shortcode, embedded, youtube, vimeo
      'source_shortcode' => '[custom_video_with_vtt src="YOUTUBE_URL" width="640" height="360" vtt="VTT_PATH"]',
      'source_youtube' => 'https://www.youtube.com/watch?v=VIDEO_ID',
      'runtime' => array('hours' => '00', 'minutes' => '10', 'seconds' => '27'),
      'duration_sec' => '627',
      'playtime' => '10:27'
  )
  ```

- **VTT File Pattern:** `https://cursos.institutopanapana.org.br/wp-content/uploads/legendas/VIDEO_ID.pt.vtt`
- **Custom Shortcode Format:** `[custom_video_with_vtt src="YOUTUBE_URL" width="640" height="360" vtt="VTT_FILE_PATH"]`

### Task 1.3: Analyze E-book Lesson Storage ✅
**Completed:** Examined how PDF e-books are embedded in lessons

**Key Findings:**
- E-book content stored directly in `post_content` field (no special meta keys)
- **Pattern:** Google Docs viewer iframe
  ```html
  <iframe style="width: 100%;height: 700px" 
          src="https://docs.google.com/gview?url=PDF_URL&embedded=true" 
          frameborder="0"></iframe>
  ```
- **PDF Storage:** `wp-content/uploads/YEAR/MONTH/FILENAME.pdf`

### Task 1.4: Analyze Quiz Structure ✅
**Completed:** Examined quiz database tables and relationships

**Key Findings:**
- **wp_tutor_quiz_questions Table:**
  ```sql
  question_id, quiz_id, question_title, question_description, 
  question_type ('single_choice', 'true_false'), question_mark, 
  question_settings (serialized), question_order
  ```

- **wp_tutor_quiz_question_answers Table:**
  ```sql
  answer_id, belongs_question_id, answer_title, 
  is_correct (1/0), answer_order
  ```

- **Question Types:** single_choice, true_false, multiple_choice
- **Settings:** answer_required, randomize_question, show_question_mark

### Task 1.5: Create 'Hello World' Course Script ✅
**Completed:** Built functional proof-of-concept course creation script

**Implementation:**
- **File:** `public/wp-content/themes/geeks-child/tutor/export_course_videos.php`
- **New Shortcode:** `[panapana_hello_world_course]`
- **Security:** Uses existing `export_course_data` capability for `gestor` role
- **Features:**
  - Creates complete course hierarchy
  - Video lesson with custom_video_with_vtt shortcode
  - E-book lesson with iframe PDF viewer
  - Quiz post structure
  - Proper WordPress metadata

**Code Structure:**
```php
function panapana_create_hello_world_course() {
    // 1. Create main course with wp_insert_post()
    // 2. Set course metadata (_tutor_course_price_type, _tutor_course_settings)
    // 3. Create topic with post_parent = course_id
    // 4. Create video lesson with _video meta
    // 5. Create e-book lesson with iframe content
    // 6. Create quiz post
    return $course_id;
}
```

## Technical Discoveries

### Database Analysis Method
- Used PowerShell Select-String with regex patterns to search large SQL files (103MB wp_posts, 139MB wp_postmeta)
- Avoided context window issues by searching specific patterns
- Commands used:
  ```powershell
  Select-String -Path "file.sql" -Pattern "'courses'" -Context 0,1 | Select-Object -First 10
  Select-String -Path "file.sql" -Pattern "custom_video_with_vtt" -Context 1,1
  ```

### WordPress Integration Patterns
- Proper use of `wp_insert_post()` for content creation
- `update_post_meta()` for metadata storage
- Security with custom capabilities and nonces
- Hierarchical relationships using `post_parent` field
- Menu ordering with `menu_order` field

### Content Type Patterns
1. **Video Lessons:** Metadata in `_video` key + shortcode in content
2. **E-book Lessons:** Direct iframe HTML in `post_content`
3. **Quizzes:** Separate tables for questions and answers
4. **Courses:** Free/paid settings in `_tutor_course_price_type`

## Files Modified
- `public/wp-content/themes/geeks-child/tutor/export_course_videos.php` - Added hello world course creator

## Next Phase Preview
**Phase 2: Develop Core Automation Script**
- Design unified CSV input format
- Build CSV parser for bulk course creation
- Implement quiz creation logic
- Handle multiple courses from single CSV file

## Testing Instructions
1. Add shortcode `[panapana_hello_world_course]` to any page
2. Access as user with `gestor` role
3. Click "Criar Curso de Teste" button
4. Verify course creation with all content types

## Key Learnings
- Tutor LMS uses standard WordPress post types with custom metadata
- Video configuration is complex but follows consistent patterns
- E-book implementation is simpler than expected (just iframes)
- Quiz system requires direct database manipulation for full automation
- Security model already established and working well
