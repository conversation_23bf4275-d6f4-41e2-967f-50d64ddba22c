<?php
/**
 * Template for course entry box
 *
 * @package Tu<PERSON>\Templates
 * @subpackage Single\Course
 * <AUTHOR> <<EMAIL>>
 * @link https://themeum.com
 * @since 1.0.0
 */

// Utility data.
global $is_enrolled;

$is_enrolled          = apply_filters( 'tutor_alter_enroll_status', $is_enrolled );
$lesson_url            = tutor_utils()->get_course_first_lesson();
//$is_administrator      = tutor_utils()->has_user_role( 'administrator' );
//$is_instructor         = tutor_utils()->is_instructor_of_this_course();
//$course_content_access = (bool) get_tutor_option( 'course_content_access_for_ia' );
$is_privileged_user    = tutor_utils()->has_user_course_content_access();
$tutor_course_sell_by  = apply_filters( 'tutor_course_sell_by', null );
$is_public             = get_post_meta( get_the_ID(), '_tutor_is_public_course', true ) == 'yes';

// Monetization info.
$monetize_by              = tutor_utils()->get_option( 'monetize_by' );
$enable_guest_course_cart = tutor_utils()->get_option( 'enable_guest_course_cart' );
$is_purchasable           = tutor_utils()->is_course_purchasable();

// Get login url if.
$is_tutor_login_disabled = ! tutor_utils()->get_option( 'enable_tutor_native_login', null, true, true );
$auth_url                = $is_tutor_login_disabled ? isset( $_SERVER['REQUEST_SCHEME'] ) ? wp_login_url( $_SERVER['REQUEST_SCHEME'] . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'] ) : '' : '';


$default_meta = array(
	array(
		'icon_class' => 'ttr-student-line-1',
		'label'      => esc_html__( 'Total Enrolled', 'geeks' ),
		'value'      => tutor_utils()->get_option( 'enable_course_total_enrolled' ) ? tutor_utils()->count_enrolled_users_by_course() : null,
	),
	array(
		'icon_class' => 'ttr-clock-filled',
		'label'      => esc_html__( 'Duration', 'geeks' ),
		'value'      => get_tutor_option( 'enable_course_duration' ) ? get_tutor_course_duration_context() : null,
	),
	array(
		'icon_class' => 'ttr-refresh-l',
		'label'      => esc_html__( 'Last Updated', 'geeks' ),
		'value'      => get_tutor_option( 'enable_course_update_date' ) ? tutor_get_formated_date( get_option( 'date_format' ), get_the_modified_date() ) : null,
	),
);

// Add level if enabled.
if(tutor_utils()->get_option('enable_course_level', true, true)) {
	array_unshift($default_meta, array(
		'icon_class' => 'ttr-level-line',
		'label'      => esc_html__( 'Level', 'geeks' ),
		'value'      => get_tutor_course_level( get_the_ID() ),
	));
}

// Right sidebar meta data.
$sidebar_meta = apply_filters('tutor/course/single/sidebar/metadata', $default_meta, get_the_ID());

$login_class = ! is_user_logged_in() ? 'tutor-course-entry-box-login' : '';
$login_url   = tutor_utils()->get_option( 'enable_tutor_native_login', null, true, true ) ? '' : wp_login_url( tutor()->current_url );
$savedPrerequisitesIDS = maybe_unserialize(get_post_meta(get_the_ID(), '_tutor_course_prerequisites_ids', true));
$user_id = get_current_user_id();
$all_completed = true;
if($savedPrerequisitesIDS){
	foreach($savedPrerequisitesIDS as $course_id){
		if(!tutor_utils()->is_completed_course( $course_id)){
			$all_completed = false;
		}
	}
}
?>

<!-- Course Entry -->
<div class="tutor-course-sidebar-card-body">
	<?php
		ob_start();
		?>
		<div class="tutor-course-sidebar-card-pricing">
			<div class="mb-3">
				<div class="text-dark fw-bold h2 mb-0"><?php esc_html_e( 'Free', 'geeks' ); ?></div>
			</div>
		</div>
		<!-- Edited -->
		<?php if(!is_user_logged_in()): ?>
				<span class="btn btn-primary tutor-btn-lg tutor-btn-full w-100 tutor-enroll-course-button tutor-course-entry-box-login" style="padding: 0;display: flex;align-items:center;">
					<a href="javascript:;" class="cart-required-login" style="width: 100%;color: white;text-align: center;align-self: end;padding: 0.7rem;">
						Faça Login!
					</a>
				</span>
		<?php else: ?>
			<?php if ($all_completed): ?>
				<div class="tutor-course-single-btn-group <?php echo is_user_logged_in() ? '' : 'tutor-course-entry-box-login'; ?>" data-login_url="<?php echo esc_url( $login_url ); ?>">
					<form class="tutor-enrol-course-form" method="post">
					<?php wp_nonce_field( tutor()->nonce_action, tutor()->nonce ); ?>
						<input type="hidden" name="tutor_course_id" value="<?php echo esc_attr( get_the_ID() ); ?>">
						<input type="hidden" name="tutor_course_action" value="_tutor_course_enroll_now">


							<button type="submit" class="btn btn-primary tutor-btn-lg tutor-btn-full w-100 tutor-enroll-course-button">
							<?php esc_html_e( apply_filters( 'geeks_enroll_course_text', 'Matricular-se', 'geeks' ) ); ?>
							</button>
					</form>

				</div>
			<?php else: ?>
				<button type="submit" title="Você não cumpre os pré-requisitos deste curso!" 
						class="btn btn-secondary tutor-btn-lg tutor-btn-full w-100 tutor-enroll-course-button" 
						onclick="window.location.href='#tutor_prereq'">
					Não habilitado!
				</button>
			<?php endif; ?>
		<?php endif; ?>
        <!-- End of Edittion -->

		<div class="text-regular-caption tutor-color-text-hints tutor-mt-12 tutor-text-center d-none">
			<?php esc_html_e( 'Free acess this course', 'geeks' ); ?>
		</div>
		<?php
		echo apply_filters( 'tutor/course/single/entry-box/free', ob_get_clean(), get_the_ID() );
		
		
	?>
</div>

<!-- Course Info -->
<div class="tutor-course-sidebar-card-footer d-none">
	<ul class="tutor-ul">
			<?php foreach ( $sidebar_meta as $key => $meta ) : ?>
				<?php
				if ( ! $meta['value'] ) {
					continue;
				}
				?>
				<li class="tutor-d-flex<?php echo $key > 0 ? ' tutor-mt-12' : ''; ?>">
					<span class="<?php echo esc_attr( $meta['icon_class'] ); ?> tutor-color-black tutor-mt-4 tutor-mr-12" aria-labelledby="<?php echo esc_html( $meta['label'] ); ?>"></span>
					<span class="tutor-fs-6 tutor-color-secondary">
						<?php echo wp_kses_post( $meta['value'] ); ?>
					</span>
				</li>
			<?php endforeach; ?>
		</ul>
</div>

<?php geeks_tutor_single_course_bookmark(); ?>

<?php
if ( ! is_user_logged_in() ) {
    tutor_load_template( 'global.login' ); 
}
?>
