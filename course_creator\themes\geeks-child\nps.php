<?php
if (!defined('PANAPANA_MIN_PROGRESS_FOR_COMPLETION')) {
    define('PANAPANA_MIN_PROGRESS_FOR_COMPLETION', 0); // Minimum percentage (e.g., 3% for testing)
}
if (!defined('PANAPANA_COMPLETION_BUTTON_ROLE')) {
    define('PANAPANA_COMPLETION_BUTTON_ROLE', 'administrator'); // Role that can see/use the button
}
if (!defined('PANAPANA_TARGET_PROCESSING_ROLE')) {
    define('PANAPANA_TARGET_PROCESSING_ROLE', 'aluno_programacao'); // 'all' or specific role name like 'test_role'
}

function panapana_nps_form_shortcode() {
    if ( ! is_user_logged_in() ) {
        return '<p>Por favor, faça login para responder o questionário.</p>';
    }

    global $wpdb;
    $table_name = $wpdb->prefix . 'nps_responses'; // Reusing the table, adjust columns if needed or add new ones
    $current_user_id = get_current_user_id();

    // --- Get Course ID and Return URL ---
    // Get course ID from URL param if present (passed from functions.php)
    $course_id_from_url = isset($_GET['course_id']) ? intval($_GET['course_id']) : 0;
    // Get return URL from URL param if present, otherwise use referer or fallback
    $course_page_url = '/minha-formacao/'; // Default fallback
    if (isset($_GET['return_url'])) {
        $course_page_url = esc_url_raw(urldecode($_GET['return_url']));
    } elseif (isset($_SERVER['HTTP_REFERER'])) {
        $course_page_url = esc_url($_SERVER['HTTP_REFERER']);
    }
    // Simple validation: ensure it's a relative path starting with / or an absolute URL for the same site
    if (!preg_match('/^\//', $course_page_url) && !str_starts_with($course_page_url, home_url())) {
         $course_page_url = '/minha-formacao/'; // Reset if it looks invalid
    }
    // --- End Get Course ID and Return URL ---

    // Check if already submitted
    if ( get_user_meta( $current_user_id, 'certificate_survey_completed', true ) ) {
        // --- Updated Success Message Block ---
        $output = '<div style="text-align: center; margin-top: 50px;">';
        $output .= '<p style="font-size: 18px;">Você já respondeu este questionário. Seu certificado deve estar disponível.</p>';

        // Button 1: Voltar para o Curso
        $output .= '<a href="' . esc_url($course_page_url) . '" class="button" style="background-color: #FF9900; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none; font-size: 16px; display: inline-block; margin-top: 20px; margin-right: 10px;">Voltar para o Curso</a>';

        // Button 2: Ver Certificado
        $certificate_display_url = '';
        // Try to get the course ID associated with this submission if possible (might need to store it with submission)
        // Assuming we have $course_id_from_url from the form, or retrieve it if stored with the submission
        $submitted_course_id = $course_id_from_url; // Use the one from URL for now

        if ($submitted_course_id && function_exists('tutor_utils')) { // Check if Tutor function likely exists
             $certificate_display_url = tutor_utils()->get_certificate_view_url($submitted_course_id);
        }
        // Fallback link if specific URL isn't found
        if (!$certificate_display_url && function_exists('tutor_utils')) {
             $certificate_display_url = tutor_utils()->get_tutor_dashboard_page_url('my-certificates');
        }

        if ($certificate_display_url) {
             $output .= '<a href="' . esc_url($certificate_display_url) . '" class="button tutor-btn-view-certificate" style="background-color: #FF6600; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none; font-size: 16px; display: inline-block; margin-top: 20px;">Ver Certificado</a>';
        }

        $output .= '</div>';
        return $output;
        // --- End Updated Success Message Block ---
    }


    if ( isset( $_POST['panapana_certificate_survey_submit'] ) && isset( $_POST['survey_nonce'] ) && wp_verify_nonce( $_POST['survey_nonce'], 'panapana_certificate_survey_action' ) ) {
        error_log('[Panapana Survey Submit Debug] --- Submission handler started ---'); // DEBUG
        
        // Verify Nonce explicitly
        $nonce_verified = wp_verify_nonce( $_POST['survey_nonce'], 'panapana_certificate_survey_action' );
        error_log('[Panapana Survey Submit Debug] Nonce verified: ' . ($nonce_verified ? 'Yes' : 'No')); // DEBUG
        if (!$nonce_verified) {
             error_log('[Panapana Survey Submit Debug] Nonce verification failed!');
             return '<p style="color: red;">Erro de segurança. Por favor, tente novamente.</p>' . panapana_get_survey_form_html();
        }

        // Get Course ID from URL param (already retrieved earlier in the function)
        // We need to use the $course_id_from_url variable defined near the top of the function
        $submitted_course_id = isset($_GET['course_id']) ? intval($_GET['course_id']) : 0;
        error_log('[Panapana Survey Submit Debug] Retrieved Course ID: ' . $submitted_course_id); // DEBUG

        // Sanitiza os dados de $_POST
        $recomendacao        = isset($_POST['survey_recomendacao']) ? intval($_POST['survey_recomendacao']) : 10;
        $aprendizado_aplicar = isset($_POST['survey_aprendizado_aplicar']) ? sanitize_text_field($_POST['survey_aprendizado_aplicar']) : '';
        $qualidade_geral     = isset($_POST['survey_qualidade_geral']) ? sanitize_text_field($_POST['survey_qualidade_geral']) : '';
        $declaracao_status   = 'accepted_by_button_click'; // New status to save
        error_log('[Panapana Survey Submit Debug] Data sanitized. Recomendacao: ' . $recomendacao . ', Aprendizado: ' . $aprendizado_aplicar . ', Qualidade: ' . $qualidade_geral); // DEBUG

       // Captura o IP
        $ip_address = $_SERVER['REMOTE_ADDR'];

        // Captura a data e hora atual
        $current_time = current_time('mysql');


        // Prepare data for insertion, including course_id
        $insert_data = array(
            'user_id'               => $current_user_id,
            'course_id'             => $submitted_course_id ?: null, // Store course_id, use null if 0
            'recommended'           => $recomendacao,        // Reusing 'recommended' column
            'qualidade_conteudo'    => $qualidade_geral,     // Reusing 'qualidade_conteudo' for overall quality
            'utilidade_aulas'       => $aprendizado_aplicar, // Reusing 'utilidade_aulas' for applicability
            'sugestoes'             => $declaracao_status, // SAVING NEW STATUS HERE
            'ip_address'            => $ip_address,
            'submission_date'       => $current_time,
        );

        // Prepare formats for insertion, including course_id
        $insert_formats = array(
            '%d', // user_id
            '%d', // course_id (or null if not provided/invalid)
            '%d', // recommended
            '%s', // qualidade_geral
            '%s', // aprendizado_aplicar
            '%s', // declaracao
            '%s', // ip_address
            '%s'  // submission_date
        );

        // Remove course_id if it's not valid (e.g., 0) to allow NULL insertion
        if (empty($submitted_course_id)) {
            unset($insert_data['course_id']);
            // Find the index of course_id's format and remove it
            $course_id_format_index = array_search('%d', array_keys($insert_data), true) === 1 ? 1 : -1; // Assuming course_id is second
             if ($course_id_format_index !== -1) {
                  unset($insert_formats[$course_id_format_index]);
                  $insert_formats = array_values($insert_formats); // Re-index array
             }
        }


        // Insere no banco
        $insert_result = $wpdb->insert(
            $table_name,
            $insert_data,
            $insert_formats
        );

         if (false === $insert_result) {
              error_log('[Panapana Survey Submit Debug] Database insert failed. Error: ' . $wpdb->last_error); // DEBUG
              // Optionally return an error message to the user
              return '<p style="color: red;">Ocorreu um erro ao salvar sua resposta. Por favor, tente novamente.</p>' . panapana_get_survey_form_html();
         } else {
              error_log('[Panapana Survey Submit Debug] Database insert successful. Rows affected: ' . $insert_result); // DEBUG
         }


        // Atualiza o meta indicando que o usuário submeteu o questionário para o certificado
        update_user_meta( $current_user_id, 'certificate_survey_completed', true );

        // *** Trigger Tutor LMS Course Completion ***
        // This attempts to mark the course as complete in Tutor and trigger certificate generation
        if ($submitted_course_id && $current_user_id) {
             error_log("[Panapana Survey Submit Debug] Triggering tutor_course_completed action for Course ID: {$submitted_course_id}, User ID: {$current_user_id}");
             do_action('tutor_course_completed', $submitted_course_id, $current_user_id);
             error_log("[Panapana Survey Submit Debug] tutor_course_completed action triggered.");
        }

        // *** Potential Hook Point for Certificate Generation ***
        // If the Certificate Builder plugin has an action/function to trigger generation:
        // do_action('trigger_certificate_generation_maybe', $current_user_id, $course_id_from_url);
        // or
        // some_certificate_builder_function($current_user_id, $course_id_from_url);

        // Redirect back to the course page using the captured URL
        wp_redirect( $course_page_url );
        exit; // Important after redirect

    }

    // Display the form
    return panapana_get_survey_form_html();

}
add_shortcode('nps_form', 'panapana_nps_form_shortcode'); // Keep the shortcode name for now unless page is updated

// Helper function to generate form HTML
function panapana_get_survey_form_html() {
     global $post; // Get global post object to potentially get course ID if not passed in URL
     $current_course_id = isset($_GET['course_id']) ? intval($_GET['course_id']) : ($post ? $post->ID : 0);
     $return_url = isset($_GET['return_url']) ? esc_url(urldecode($_GET['return_url'])) : (isset($_SERVER['HTTP_REFERER']) ? esc_url($_SERVER['HTTP_REFERER']) : home_url());

     ob_start();
    ?>
    <style>
    /* --- Temporarily Commenting Out Form Input CSS ---
        /* Override theme's hiding rule specifically within our form */
        .panapana-survey-form input[type="checkbox"],
        .panapana-survey-form input[type="radio"] {
            position: static !important; /* Override absolute positioning */
            left: auto !important;
            opacity: 1 !important; /* Make it visible */
            pointer-events: auto !important; /* Allow clicks */
            /* Re-apply our custom styles */
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            border: 2px solid #ccc;
            border-radius: 0; /* Square */
            margin-right: 10px;
            vertical-align: middle;
            cursor: pointer;
            /* Adjust position slightly */
            position: relative;
            top: -2px;
        }
        .panapana-survey-form input[type="radio"] {
             width: 20px; /* Size */
             height: 20px; /* Size */
        }
         .panapana-survey-form input[type="checkbox"] {
             width: 18px;
             height: 18px;
        }

        .panapana-survey-form input[type="radio"]:checked,
        .panapana-survey-form input[type="checkbox"]:checked {
            background-color: #ff7300; /* Orange */
            border-color: #ff7300;
        }
        /* Checkmark */
        .panapana-survey-form input[type="radio"]:checked::after,
        .panapana-survey-form input[type="checkbox"]:checked::after {
            content: '\2713'; /* Checkmark character */
            color: white;
            text-align: center;
            display: block;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
         .panapana-survey-form input[type="radio"]:checked::after {
             font-size: 14px;
             line-height: 18px;
        }
         .panapana-survey-form input[type="checkbox"]:checked::after {
            font-size: 12px;
            line-height: 16px;
        }

        /* Label alignment */
         .panapana-survey-form label {
             display: block;
             margin-bottom: 10px;
             cursor: pointer;
        }
         .panapana-survey-form label[for="declaracao_aluno"] {
             display: flex; /* Align checkbox and text */
             align-items: center;
        }
         .panapana-survey-form label[for="declaracao_aluno"] span {
             flex: 1;
             margin-left: 5px; /* Add some space after checkbox */
        }
    --- End Temp Comment --- */
    </style>
    <form method="post" class="panapana-survey-form">
        <?php wp_nonce_field( 'panapana_certificate_survey_action', 'survey_nonce' ); ?>
        <!-- Add hidden fields -->
        <input type="hidden" name="course_id" value="<?php echo esc_attr($current_course_id); ?>">
        <input type="hidden" name="return_url" value="<?php echo esc_attr($return_url); ?>">

        <h3>Questionário de Avaliação - Instituto Panapaná</h3>
        <p>Olá! Sua opinião é muito importante para nós. Responda a este breve questionário para nos ajudar a melhorar ainda mais nossos cursos.</p>
        <p><strong>Importante:</strong> O envio deste formulário é obrigatório para a emissão do seu certificado de conclusão.</p>
        <hr>

        <label for="survey_recomendacao"><strong>1. De 0 a 10, qual a probabilidade de você recomendar este curso para outras pessoas?</strong></label><br>
        <select name="survey_recomendacao" id="survey_recomendacao" required style="min-width: 100px;">
            <?php for ($i = 0; $i <= 10; $i++): ?>
                <option value="<?php echo $i; ?>" <?php echo ($i == 10) ? 'selected' : ''; ?>><?php echo $i; ?></option>
            <?php endfor; ?>
        </select><br><br>

        <label for="survey_aprendizado_aplicar"><strong>2. As aulas ajudaram no seu aprendizado e você se sente preparado para aplicar o que foi ensinado?</strong></label><br>
        <select name="survey_aprendizado_aplicar" id="survey_aprendizado_aplicar" required>
            <option value="" disabled selected>Selecione...</option>
            <option value="Sim">Sim</option>
            <option value="Parcialmente">Parcialmente</option>
            <option value="Não">Não</option>
        </select>
        <br><br>

        <label for="survey_qualidade_geral"><strong>3. A qualidade geral do curso (conteúdo, professores e plataforma) atendeu ou superou suas expectativas?</strong></label><br>
        <select name="survey_qualidade_geral" id="survey_qualidade_geral" required>
            <option value="" disabled selected>Selecione...</option>
            <option value="Sim, superou">Sim, superou</option>
            <option value="Sim, atendeu">Sim, atendeu</option>
            <option value="Não, abaixo">Não, ficou abaixo do esperado</option>
        </select><br><br>

        <hr>

        <p style="margin-top: 20px; margin-bottom: 15px; font-style: italic;">
            Ao clicar no botão abaixo, declara que realizou integralmente o curso, e está apto(a) a aplicar, de forma ética e responsável, os conhecimentos adquiridos.
        </p>

        <input type="submit" name="panapana_certificate_survey_submit" value="Declaro que concluí todo o curso" class="button submit-button">
    </form>
    <?php
    return ob_get_clean();
}

// Adicionar página no painel administrativo
add_action('admin_menu', function() {
    add_menu_page(
        'Respostas Pré-Certificado', // Título da página
        'Respostas Certificado',     // Nome no menu
        'manage_options',            // Permissão
        'respostas-nps',             // Slug (keep slug for now to avoid breaking links, or change and update references)
        'mostrar_respostas_nps',     // Função callback (updated below)
        'dashicons-feedback',        // Ícone
        6                            // Posição no menu
    );
});

// Função para listar as respostas (Updated for new fields)

function mostrar_respostas_nps() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'nps_responses';

    $results = $wpdb->get_results("SELECT * FROM $table_name ORDER BY submission_date DESC");

    echo '<div class="wrap"><h1>Respostas do Questionário Pré-Certificado</h1>';
    
    // Export CSV Button
    echo '<a href="' . admin_url('admin-post.php?action=export_certificate_survey_csv') . '" class="button button-primary">Exportar CSV</a>';

    // New "Mark as Complete" Button
    if (current_user_can(PANAPANA_COMPLETION_BUTTON_ROLE)) {
        $mark_complete_url = wp_nonce_url(admin_url('admin-post.php?action=panapana_mark_eligible_courses_complete'), 'panapana_mark_eligible_action_nonce', 'panapana_mark_nonce');
        echo ' <a href="' . esc_url($mark_complete_url) . '" class="button button-secondary" style="margin-left: 10px;" onclick="return confirm(\'Tem certeza que deseja marcar todos os cursos com progresso >= ' . PANAPANA_MIN_PROGRESS_FOR_COMPLETION . '% como concluídos para os alunos elegíveis? Esta ação não pode ser desfeita e irá gerar os certificados.\');">Marcar Cursos >' . PANAPANA_MIN_PROGRESS_FOR_COMPLETION . '% Concluídos</a>';
    }
    
    echo '<br><br>'; // Existing line break

    if (!empty($results)) {
        echo '<table class="wp-list-table widefat fixed striped">';
        // Updated table headers to include "Telefone"
        echo '<thead><tr><th>User ID</th><th>Nome</th><th>Telefone</th><th>Nome do Curso</th><th>IP Address</th><th>Data Envio</th><th>Recomendação (0-10)</th><th>Qualidade Geral</th><th>Aprendizado Aplicável</th><th>Declaração Aceita</th><th>Certificado</th></tr></thead>';
        echo '<tbody>';
        foreach ($results as $row) {
            $user_info = get_userdata($row->user_id);
            $user_name = $user_info ? $user_info->display_name : 'Usuário Desconhecido';
            $user_id   = $row->user_id; // Keep user_id for other logic like certificate link

            // Fetch user phone number (telefone)
            $user_telefone = 'N/A'; // Default value
            if ($user_info) { // Check if $user_info is valid
                $telefone_meta = get_user_meta($row->user_id, 'telefone', true);
                if (!empty($telefone_meta)) {
                    $user_telefone = $telefone_meta;
                }
            }

            $course_id = isset($row->course_id) ? (int) $row->course_id : 0;
            error_log("[Admin Table Debug] User ID: {$user_id}, Course ID: {$course_id}");

            // Get Course Name
            $course_name = ($course_id > 0) ? get_the_title($course_id) : 'N/A';
            if (!$course_name) { $course_name = 'N/A'; } // Ensure it's not empty/false

                        // --- Generate Certificate Link ---
            $certificate_link_html = 'N/A'; // Default

            if ($course_id && $user_id && function_exists('tutor_utils')) {
                // Scenario 1: The NPS row has a specific course_id. Try to get that certificate.
                error_log("[Admin Table Debug V2] NPS row has Course ID: {$course_id} for User ID: {$user_id}. Attempting specific certificate link.");
                $certificate_specific_url = tutor_utils()->get_certificate_view_url($course_id, $user_id);

                if ($certificate_specific_url) {
                    // Check if it's just a link to the generic cert page or an actual hash link
                    if (strpos($certificate_specific_url, 'cert_hash=') !== false || strpos($certificate_specific_url, '/tutor-certificates/') !== false ) { // Added /tutor-certificates/ for direct link from some builders
                        $certificate_link_html = '<a href="' . esc_url($certificate_specific_url) . '" target="_blank">Ver Certificado (Curso Específico)</a>';
                         error_log("[Admin Table Debug V2] Found specific certificate URL: {$certificate_specific_url}");
                    } else {
                        // It might have returned a general dashboard link if no specific cert for *this* course,
                        // so we'll fall through to the general check below.
                        error_log("[Admin Table Debug V2] tutor_utils()->get_certificate_view_url did not return a direct hash link for Course ID {$course_id}. Will try general check.");
                        $certificate_link_html = '<em>(Cert. p/ este curso N/A)</em>'; // Placeholder before general check
                    }
                } else {
                     // Fallback: Try to get hash directly from comments if get_certificate_view_url fails for specific course
                    $certificate_hash = $wpdb->get_var($wpdb->prepare(
                        "SELECT comment_content FROM {$wpdb->comments} WHERE comment_post_ID = %d AND user_id = %d AND comment_type = %s ORDER BY comment_date_gmt DESC LIMIT 1",
                        $course_id, $user_id, 'course_completed'
                    ));
                    if ($certificate_hash) {
                        $certificate_url = home_url('/tutor-certificados/?cert_hash=') . $certificate_hash;
                        $certificate_link_html = '<a href="' . esc_url($certificate_url) . '" target="_blank">Ver Certificado (Curso Específico - Hash)</a>';
                        error_log("[Admin Table Debug V2] Found specific certificate URL via hash direct query: {$certificate_url}");
                    } else {
                        error_log("[Admin Table Debug V2] No specific certificate URL or hash found for Course ID {$course_id}, User ID {$user_id}.");
                        $certificate_link_html = '<em>(Cert. p/ este curso N/A)</em>';
                    }
                }
            }

            // Scenario 2: If specific link wasn't fully resolved OR if the NPS row had no course_id,
            // try to link to the general "My Certificates" dashboard page if the user has *any* certificate.
            // We check if the link is still 'N/A' or contains "N/A" from the previous block.
            if ( $certificate_link_html === 'N/A' || strpos($certificate_link_html, 'N/A') !== false ) {
                if ($user_id && function_exists('tutor_utils')) {
                    error_log("[Admin Table Debug V2] NPS row has no Course ID OR specific cert not found for User ID: {$user_id}. Checking for any certificates and linking to dashboard.");
                    
                    // Check if user has ANY certificate generated (by looking for any 'course_completed' comment with a hash)
                    $any_certificate_hash_exists = $wpdb->get_var($wpdb->prepare(
                        "SELECT COUNT(comment_ID) FROM {$wpdb->comments} WHERE user_id = %d AND comment_type = %s AND comment_content != '' AND comment_content IS NOT NULL",
                        $user_id,
                        'course_completed'
                    ));

                    if ($any_certificate_hash_exists && $any_certificate_hash_exists > 0) {
                        $dashboard_certificates_url = tutor_utils()->get_tutor_dashboard_page_url('my-certificates');
                        if ($dashboard_certificates_url) {
                            $certificate_link_html = '<a href="' . esc_url($dashboard_certificates_url) . '" target="_blank" title="Este usuário possui certificados. Verifique o painel.">Painel de Certificados</a>';
                             error_log("[Admin Table Debug V2] User {$user_id} has certificates. Linking to dashboard: {$dashboard_certificates_url}");
                        } else {
                            $certificate_link_html = '<em>(Painel de certs. indisponível)</em>';
                             error_log("[Admin Table Debug V2] User {$user_id} has certificates, but dashboard URL for 'my-certificates' not found.");
                        }
                    } else {
                        $certificate_link_html = '<em>(Nenhum certificado gerado)</em>';
                        error_log("[Admin Table Debug V2] User {$user_id} has no 'course_completed' comments with a hash.");
                    }
                } else {
                    // Fallback if user_id is somehow missing or tutor_utils not available at this stage
                    $certificate_link_html = '<em>(Dados insuficientes)</em>';
                }
            }
            // Append note if the original NPS row had no course_id
            if (!$course_id && strpos($certificate_link_html, 'NPS sem curso reg.') === false) { // Avoid duplicate message
                 $certificate_link_html .= '<br><small style="display:block; margin-top:3px; color:#777;"><em>(NPS sem curso reg.)</em></small>';
            }
            // --- End Generate Certificate Link ---

            // Get and format Declaration Status
            $declaracao = $row->sugestoes;
            $display_declaracao = ($declaracao === 'accepted_by_button_click') ? 'Aceita' : esc_html($declaracao);

            echo '<tr>';
            echo '<td>' . esc_html($user_id) . '</td>';
            echo '<td>' . esc_html($user_name) . '</td>';
            echo '<td>' . esc_html($user_telefone) . '</td>'; // Added telefone cell
            echo '<td>' . esc_html($course_name) . '</td>';
            echo '<td>' . esc_html($row->ip_address) . '</td>';
            echo '<td>' . esc_html($row->submission_date) . '</td>';
            echo '<td>' . esc_html($row->recommended) . '</td>';
            echo '<td>' . esc_html($row->qualidade_conteudo) . '</td>';
            echo '<td>' . esc_html($row->utilidade_aulas) . '</td>';
            echo '<td>' . $display_declaracao . '</td>';
            echo '<td>' . $certificate_link_html . '</td>';
            echo '</tr>';
        }
        echo '</tbody>';
        echo '</table>';
    } else {
        echo '<p>Nenhuma resposta encontrada.</p>';
    }
    echo '</div>';
}

add_action('admin_post_export_certificate_survey_csv', 'exportar_certificate_survey_csv');

function exportar_certificate_survey_csv() {
    if (!current_user_can('manage_options')) {
        wp_die('Você não tem permissão para fazer isso.');
    }

    global $wpdb;
    $table_name = $wpdb->prefix . 'nps_responses';
    // Make sure to select course_id if you want it in the export
    $results = $wpdb->get_results("SELECT user_id, course_id, ip_address, submission_date, recommended, qualidade_conteudo, utilidade_aulas, sugestoes FROM $table_name ORDER BY submission_date DESC", ARRAY_A);

    $filename = "respostas-pre-certificado-" . date("Y-m-d") . ".csv";

    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Pragma: no-cache');
    header('Expires: 0');

    $output = fopen('php://output', 'w');

    // Add BOM for UTF-8 Excel compatibility
    fputs($output, "\xEF\xBB\xBF");

    // Define CSV headers, including "Telefone"
    $headers = array(
        'User ID',
        'User Name',
        'Telefone', // Added Telefone column
        'Course ID',
        'Course Name',
        'IP Address',
        'Data Envio',
        'Recomendação (0-10)',
        'Qualidade Geral',
        'Aprendizado Aplicável',
        'Declaração Aceita',
        'Certificado'
    );
    fputcsv($output, $headers);


    if (!empty($results)) {
        foreach ($results as $row) {
            // Fetch user name for each row
            $user_info = get_userdata($row['user_id']);
            $user_name = $user_info ? $user_info->display_name : 'Usuário Desconhecido';
            
            // Fetch user phone number (telefone)
            $user_telefone = 'N/A'; // Default value
            if ($user_info) {
                $telefone_meta = get_user_meta($row['user_id'], 'telefone', true);
                if (!empty($telefone_meta)) {
                    $user_telefone = $telefone_meta;
                }
            }

            // Fetch Course ID and Name
            $course_id = isset($row['course_id']) ? (int)$row['course_id'] : 0;
            $course_name = 'N/A'; // Default course name
            if ($course_id > 0) {
                $course_title = get_the_title($course_id);
                if ($course_title) {
                    $course_name = $course_title;
                }
            }

            // --- Generate Certificate Link --- 
            $certificate_url = 'N/A'; // Default value
            $certificate_hash = null;

            if ($course_id && $row['user_id']) {
                // Query wp_comments table for the certificate hash
                $certificate_hash = $wpdb->get_var($wpdb->prepare(
                    "SELECT comment_content FROM {$wpdb->comments} WHERE comment_post_ID = %d AND user_id = %d AND comment_type = %s ORDER BY comment_date_gmt DESC LIMIT 1",
                    $course_id,
                    $row['user_id'],
                    'course_completed' // The comment type used by Tutor for certificate hash
                ));
            }

            if ($certificate_hash) {
                // Construct the URL manually if hash found
                $certificate_url = home_url('/tutor-certificados/?cert_hash=') . $certificate_hash;
            } elseif (!$course_id) {
                 $certificate_url = 'Curso não registrado';
            } elseif (!$certificate_hash) {
                 $certificate_url = 'Não gerado/encontrado';
            }
            // --- End Generate Certificate Link ---

            // Get and format Declaration Status for CSV
            $declaracao = $row['sugestoes'];
            $display_declaracao_csv = ($declaracao === 'accepted_by_button_click') ? 'Aceita' : $declaracao;

            // Prepare row data for CSV in the correct order, including the user's phone number
            $csv_row = array(
                $row['user_id'],
                $user_name,
                $user_telefone, // Added user phone number
                $course_id ? $course_id : '', // Course ID
                $course_name, // Course Name
                $row['ip_address'],
                $row['submission_date'],
                $row['recommended'],          // Reused column
                $row['qualidade_conteudo'],   // Reused column
                $row['utilidade_aulas'],      // Reused column
                $display_declaracao_csv,     // Use formatted declaration status for CSV
                $certificate_url             // Add the generated certificate URL
            );
            fputcsv($output, $csv_row);
        }
    }
    fclose($output);
    exit;
}

// Action hook and function for the new "Mark as Complete" button
add_action('admin_post_panapana_mark_eligible_courses_complete', 'panapana_admin_mark_eligible_courses_complete');


// REPLACE THE ENTIRE panapana_admin_mark_eligible_courses_complete FUNCTION WITH THIS V4.1:

function panapana_admin_mark_eligible_courses_complete() {
    global $wpdb;
    error_log('[PANA_MARK_DEBUG_V4.1_EXPLICIT_BATCH] === Function panapana_admin_mark_eligible_courses_complete triggered (V4.1 EXPLICIT BATCH) ===');

    // 1. Security Checks (same as before)
    if (!isset($_GET['panapana_mark_nonce']) || !wp_verify_nonce(sanitize_key($_GET['panapana_mark_nonce']), 'panapana_mark_eligible_action_nonce')) {
        error_log('[PANA_MARK_DEBUG_V4.1_EXPLICIT_BATCH] Nonce verification failed.');
        wp_die('Erro de segurança (nonce inválido).');
    }
    // ... (other initial checks for user role, tutor_utils, CourseModel remain the same) ...
     if (!current_user_can(PANAPANA_COMPLETION_BUTTON_ROLE)) {
        error_log('[PANA_MARK_DEBUG_V4.1_EXPLICIT_BATCH] User does not have PANAPANA_COMPLETION_BUTTON_ROLE ('.PANAPANA_COMPLETION_BUTTON_ROLE.').');
        wp_die('Você não tem permissão para executar esta ação.');
    }
    if (!function_exists('tutor_utils')) {
        error_log('[PANA_MARK_DEBUG_V4.1_EXPLICIT_BATCH] tutor_utils function does not exist.');
        wp_die('Tutor LMS não parece estar ativo ou as funções essenciais não estão disponíveis.');
    }
    if (!class_exists('\Tutor\Models\CourseModel')) {
        error_log('[PANA_MARK_DEBUG_V4.1_EXPLICIT_BATCH] Critical Error: \Tutor\Models\CourseModel class not found.');
        // ... redirect ...
        exit;
    }
    error_log('[PANA_MARK_DEBUG_V4.1_EXPLICIT_BATCH] Initial checks passed.');


    $min_progress_to_complete = PANAPANA_MIN_PROGRESS_FOR_COMPLETION;
    $current_operation_target_role_for_course_map = PANAPANA_TARGET_PROCESSING_ROLE; 
    error_log('[PANA_MARK_DEBUG_V4.1_EXPLICIT_BATCH] Min Progress: ' . $min_progress_to_complete . '%. Role for Course Map: ' . $current_operation_target_role_for_course_map);

    $role_to_target_course_id_map = array(
        'aluno_wordpress'       => 6946,
        'aluno_design'          => 3145,
        'aluno_programacao'     => 2999,
        'aluno_edio_de_vdeo'    => 3962,
        'aluno_marketing'       => 5339,
        'test_role'             => 0, 
    );

    $specific_course_to_process_for_this_operation = 0; // General target if user has no specific role in map
    if ( array_key_exists( $current_operation_target_role_for_course_map, $role_to_target_course_id_map ) ) {
        $specific_course_to_process_for_this_operation = (int) $role_to_target_course_id_map[$current_operation_target_role_for_course_map];
        // Log messages about this...
    }
    // ... (logging for course map role) ...

    
    // --- EXPLICIT LIST OF USER IDS TO PROCESS ---
    $user_ids_string = "343,346,347,349,355,357,359,363,366,371,372,375,376,379,380,383,386,387,389,390,391,394,395,396,397,398,399,400,401,402,403,404,405,406,409,410,412,414,415,418,421,423,426,429,432,433,435,436,439,442,443,445,446,448,453,456,458,459,460,463,465,468,470,473,474,476,477,478,481,483,492,494,495,496,499,500,506,507,511,513,514,515,516,517,520,521,522,524,525,526,527,528,529,530,531,532,533,534,535,537,539,540,541,543,545,548,550,551,552,553,554,556,557,558,559,561,564,571,578,581,597,165,178,179,189,200,203,206,208,212,213,216,217,218,221,222,228,236,237,240,243,246,247,248,249,250,254,256,258,259,260,263,266,267,272,274,275,276,279,280,282,285,286,288,289,293,301,302,304,313,314,321,322,323,324,328,329,334,652,650,649,647,648,641,645,644,643,642,639";
    $all_user_ids_in_list = array_map('intval', explode(',', $user_ids_string));
    $all_user_ids_in_list = array_unique(array_filter($all_user_ids_in_list));
    sort($all_user_ids_in_list);

    if (empty($all_user_ids_in_list)) {
        error_log('[PANA_MARK_DEBUG_V4.1_EXPLICIT_BATCH] The explicit list of student IDs is empty.');
        // ... redirect with notice ...
        exit;
    }

    $processed_courses_count_this_run = 0; // Courses actioned in this HTTP request
    $students_actioned_this_run = 0;       // Unique students for whom at least one course was actioned in this HTTP request
    
    $max_students_to_action_per_run = 10; // <<<< ----- ADJUST THIS NUMBER FOR BATCH SIZE -----
    $students_newly_actioned_in_this_request = 0;

    $transient_key = 'panapana_v4.1_completed_batch_students';
    $already_fully_processed_students_in_past = get_transient($transient_key) ?: array();
    
    error_log('[PANA_MARK_DEBUG_V4.1_EXPLICIT_BATCH] Total users in explicit list: ' . count($all_user_ids_in_list) . '. Already fully processed in past batches: ' . count($already_fully_processed_students_in_past));

    foreach ($all_user_ids_in_list as $student_id) {
        if (in_array($student_id, $already_fully_processed_students_in_past)) {
            // This student was marked as fully processed in a previous run, skip them.
            continue;
        }

        // If we've hit our limit for *newly actioned students* in this specific HTTP request, stop.
        if ($students_newly_actioned_in_this_request >= $max_students_to_action_per_run) {
            error_log("[PANA_MARK_DEBUG_V4.1_EXPLICIT_BATCH] Actioned {$students_newly_actioned_in_this_request} new students. Ending this batch run.");
            break; 
        }
        
        error_log("[PANA_MARK_DEBUG_V4.1_EXPLICIT_BATCH] Checking Student ID from explicit list: {$student_id}");
        $user_data = get_userdata($student_id);
        if (!$user_data) {
            error_log("[PANA_MARK_DEBUG_V4.1_EXPLICIT_BATCH] Student ID {$student_id} from list not found. Marking as 'processed' to skip in future.");
            $already_fully_processed_students_in_past[] = $student_id; // Add to processed list to skip next time
            continue;
        }

        $course_id_for_this_user = $specific_course_to_process_for_this_operation;
        $user_actual_roles = (array) $user_data->roles;
        $found_role_for_course_map = false;

        foreach ($user_actual_roles as $user_role_slug) {
            if (array_key_exists($user_role_slug, $role_to_target_course_id_map) && $role_to_target_course_id_map[$user_role_slug] > 0) {
                $course_id_for_this_user = (int) $role_to_target_course_id_map[$user_role_slug];
                // error_log("[PANA_MARK_DEBUG_V4.1_EXPLICIT_BATCH] User ID {$student_id} has role '{$user_role_slug}'. Target course for this user is specifically: {$course_id_for_this_user}");
                $found_role_for_course_map = true;
                break; 
            }
        }
        
        if (!$found_role_for_course_map && $course_id_for_this_user == 0) {
             error_log("[PANA_MARK_DEBUG_V4.1_EXPLICIT_BATCH] User ID {$student_id}. No specific course mapped via their roles or operation role. Will check all enrolled courses.");
        } else if ($course_id_for_this_user > 0) {
             error_log("[PANA_MARK_DEBUG_V4.1_EXPLICIT_BATCH] User ID {$student_id}. Target course determined as: {$course_id_for_this_user}.");
        }


        $enrolled_courses = tutor_utils()->get_enrolled_courses_ids_by_user($student_id);
        if (empty($enrolled_courses)) {
            error_log("[PANA_MARK_DEBUG_V4.1_EXPLICIT_BATCH] Student ID: {$student_id} has no enrolled courses. Marking as 'processed'.");
            $already_fully_processed_students_in_past[] = $student_id;
            $students_newly_actioned_in_this_request++; // Consumes a slot even if no courses
            continue;
        }

        $student_had_at_least_one_course_actioned = false;

        foreach ($enrolled_courses as $enrolled_course_id_obj) { // This is an object
            $course_id = $enrolled_course_id_obj->ID; // Get the ID from the object

            if (empty($course_id) || !is_numeric($course_id)) continue;

            // If a specific course ID was determined for this user, only process that one.
            if ($course_id_for_this_user > 0 && (int) $course_id !== $course_id_for_this_user) {
                continue; // Not the target course for this user
            }
            // If $course_id_for_this_user is 0, process this $course_id (the enrolled one)

            error_log("[PANA_MARK_DEBUG_V4.1_EXPLICIT_BATCH] Student ID: {$student_id}, Examining Course ID: {$course_id}");

            // Check if this specific course for this student is already fully processed & certificate generated
            $cert_generated_meta_key = 'tutor_certificate_generated_for_' . $course_id;
            if (get_user_meta($student_id, $cert_generated_meta_key, true)) {
                // error_log("[PANA_MARK_DEBUG_V4.1_EXPLICIT_BATCH] Student ID: {$student_id}, Course ID: {$course_id} already has specific cert generated meta. Skipping course action.");
                if ($course_id_for_this_user > 0) { // If this was the specific target, student is done for this target
                    $student_had_at_least_one_course_actioned = true; // Considered "actioned" as it was checked
                    break; 
                }
                continue;
            }


            if (tutor_utils()->is_completed_course($course_id, $student_id)) {
                 error_log("[PANA_MARK_DEBUG_V4.1_EXPLICIT_BATCH] Student ID: {$student_id}, Course ID: {$course_id} is ALREADY completed. Re-firing completion hook.");
                 do_action('tutor_course_completed', $course_id, $student_id);
                 update_user_meta($student_id, $cert_generated_meta_key, time()); // Mark this specific course as processed by this script
                 $processed_courses_count_this_run++;
                 $student_had_at_least_one_course_actioned = true;
                 if ($course_id_for_this_user > 0) break; else continue;
            }

            $progress = tutor_utils()->get_course_completed_percent($course_id, $student_id);
            // error_log("[PANA_MARK_DEBUG_V4.1_EXPLICIT_BATCH] Student ID: {$student_id}, Course ID: {$course_id}, Progress: {$progress}%");

            if ($progress >= $min_progress_to_complete) {
                error_log("[PANA_MARK_DEBUG_V4.1_EXPLICIT_BATCH] Student ID: {$student_id}, Course ID: {$course_id} meets min progress. Attempting simplified completion.");
                \Tutor\Models\CourseModel::mark_course_as_completed($course_id, $student_id);

                $course_contents = tutor_utils()->get_course_contents_by_id($course_id);
                if (!empty($course_contents) && is_array($course_contents)) {
                    foreach ($course_contents as $content) { /* ... lesson/quiz completion ... */ }
                }
                error_log("[PANA_MARK_DEBUG_V4.1_EXPLICIT_BATCH] Firing tutor_course_completed for Course ID: {$course_id}, Student ID: {$student_id}");
                do_action('tutor_course_completed', $course_id, $student_id);
                update_user_meta($student_id, $cert_generated_meta_key, time()); // Mark this specific course as processed by this script
                if (function_exists('wp_cache_delete')) { /* ... cache clearing ... */ }
                $processed_courses_count_this_run++;
                $student_had_at_least_one_course_actioned = true;
            } else {
                // error_log("[PANA_MARK_DEBUG_V4.1_EXPLICIT_BATCH] Student ID: {$student_id}, Course ID: {$course_id} does NOT meet min progress. Skipping.");
            }
             // If we were targeting a specific course for this user and we just processed it (or skipped due to progress), break from their course loop
            if ($course_id_for_this_user > 0 && (int) $course_id === $course_id_for_this_user) {
                break;
            }
        } // End foreach $enrolled_courses
        
        if ($student_had_at_least_one_course_actioned) {
            $students_actioned_this_run++;
        }
        // Whether courses were actioned or not, if we iterated through this student's relevant courses,
        // mark them in the transient so we don't pick them again from the top of the explicit list.
        $already_fully_processed_students_in_past[] = $student_id; 
        $students_newly_actioned_in_this_request++; // Increment student slot for this HTTP request

    } // End foreach $all_user_ids_in_list

    set_transient($transient_key, array_unique($already_fully_processed_students_in_past), DAY_IN_SECONDS);
    $all_users_attempted = (count(array_unique($already_fully_processed_students_in_past)) >= count($all_user_ids_in_list));
    if ($all_users_attempted) {
        delete_transient($transient_key);
        error_log("[PANA_MARK_DEBUG_V4.1_EXPLICIT_BATCH] All users from explicit list have been attempted. Transient cleared.");
    }

    // Feedback and Redirect (adjust message slightly)
    $feedback_message = '';
    $feedback_type = 'info';
    
    if ($processed_courses_count_this_run > 0) {
        $feedback_message = "Lote processado: {$processed_courses_count_this_run} curso(s) para {$students_actioned_this_run} aluno(s) da lista explícita foram processados/re-triggered. Certificados devem ter sido gerados/re-gerados.";
        $feedback_type = 'success';
    } else {
        $feedback_message = "Nenhum curso novo processado neste lote da lista explícita. Usuários podem ter sido já processados, não ter o curso alvo, ou não atender critérios.";
    }
    
    if (!$all_users_attempted) {
        $remaining_to_process = count($all_user_ids_in_list) - count(array_unique($already_fully_processed_students_in_past));
        $feedback_message .= " Ainda restam aproximadamente {$remaining_to_process} alunos da lista explícita. Clique no botão novamente para processar o próximo lote.";
    } else {
        $feedback_message .= " Todos os alunos da lista explícita foram tentados.";
    }

    error_log('[PANA_MARK_DEBUG_V4.1_EXPLICIT_BATCH] Feedback Message: ' . $feedback_message);
    add_settings_error('panapana_notices', 'courses_marked_complete_status', $feedback_message, $feedback_type);
    
    $admin_page_url = admin_url('admin.php?page=respostas-nps');
    // ... (add_query_arg logic for redirect URL as before) ...
    wp_safe_redirect(add_query_arg(array('pana_batch_v4_status' => $feedback_type), $admin_page_url));
    exit;
}


?>
