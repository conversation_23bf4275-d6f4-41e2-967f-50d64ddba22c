# Course Creator Environment Setup Guide

## Overview
This guide explains how to set up the Course Creator development environment on a new PC and maintain the workflow between the GitHub repository and WordPress installation.

## Project Structure
```
plataforma_panapana/                    # WordPress root directory
├── wp-content/themes/geeks-child/      # Active WordPress theme (working directory)
├── course_creator/                     # GitHub repository
│   ├── themes/geeks-child/            # Repository version of theme
│   ├── scripts/                       # Automation scripts
│   │   ├── setup-new-environment.ps1  # Initial setup for new PC
│   │   ├── deploy-theme.ps1           # Deploy from repo to WordPress
│   │   └── sync-from-wordpress.ps1    # Sync from WordPress to repo
│   └── backlog/sample-course.csv      # Test data
```

## Initial Setup on New PC

### Prerequisites
- WordPress installation with XAMPP/WAMP
- Git installed
- PowerShell execution policy allows scripts
- Geeks parent theme installed
- Tutor LMS plugin installed

### Step 1: Clone Repository
```bash
cd /path/to/wordpress/root
git clone https://github.com/dbs-web/course_creator.git
cd course_creator
```

### Step 2: Run Automated Setup
```powershell
# If WordPress is at root level (like XAMPP htdocs)
.\scripts\setup-new-environment.ps1 -WordPressPath ".."

# If WordPress is in a 'public' folder
.\scripts\setup-new-environment.ps1 -WordPressPath "public"
```

**What this script does:**
- Verifies repository structure
- Checks WordPress installation
- Deploys theme files to WordPress
- Verifies Tutor LMS plugin presence
- Checks for Geeks parent theme

### Step 3: Manual WordPress Configuration
1. Go to **WordPress Admin → Appearance → Themes**
2. Activate **"Geeks Child"** theme
3. Go to **WordPress Admin → Plugins**
4. Ensure **"Tutor LMS"** plugin is active
5. Create a test page with shortcode: `[panapana_master_debug]`
6. Verify all functions show as "EXISTS"

### Step 4: Test the System
1. Create a test page with: `[panapana_csv_course_creator]`
2. Upload `backlog/sample-course.csv`
3. Check if course "Git - Iniciante ao Avancado" is created

## Daily Development Workflow

### Option 1: Edit WordPress Files Directly (Recommended)
```powershell
# 1. Edit files directly in WordPress
code ../wp-content/themes/geeks-child/tutor/core/course-creator.php

# 2. Test changes in WordPress browser

# 3. Sync changes back to repository
.\scripts\sync-from-wordpress.ps1 -WordPressPath ".."

# 4. Review and commit changes
git diff
git add .
git commit -m "Description of changes"
git push origin main
```

### Option 2: Edit Repository Files First
```powershell
# 1. Edit files in repository
code themes/geeks-child/tutor/core/course-creator.php

# 2. Deploy to WordPress
.\scripts\deploy-theme.ps1 -WordPressPath ".."

# 3. Test in WordPress

# 4. Commit changes
git add .
git commit -m "Description of changes"
git push origin main
```

## Syncing Between Multiple PCs

### From Work PC (after making changes)
```powershell
# Sync changes to repository
.\scripts\sync-from-wordpress.ps1 -WordPressPath ".."

# Commit and push
git add .
git commit -m "Work PC changes"
git push origin main
```

### On Home PC (to get latest changes)
```powershell
# Pull latest changes
git pull origin main

# Deploy to local WordPress
.\scripts\deploy-theme.ps1 -WordPressPath ".."
```

## Database Synchronization (Optional)

### Important: What Requires Database Sync
- **Created courses and content** (not synced via Git)
- **Test pages with shortcodes** (need to be recreated or database synced)
- **User accounts and permissions** (admin/gestor users)
- **WordPress settings and plugin configurations**

### Database Backup/Restore Commands
```powershell
# Export database (from source PC)
mysqldump -u root -p plataforma_panapana > database_backup.sql

# Import database (on target PC)
mysql -u root -p plataforma_panapana < database_backup.sql
```

### When to Use Database Sync
- ✅ **When you have important test courses** you want to preserve
- ✅ **When testing complex course structures** across environments
- ✅ **When you have configured users/permissions** you want to keep
- ❌ **For regular code development** (Git is sufficient)

### Alternative: Recreate Test Content
Instead of database sync, you can:
1. Create test pages with shortcodes on each PC
2. Use the sample CSV file to recreate test courses
3. Set up admin/gestor users manually

## Script Details

### setup-new-environment.ps1
- **Purpose**: Complete setup for new PC/environment
- **Parameters**: `-WordPressPath` (default: "public")
- **Actions**: Verifies setup, deploys theme, checks dependencies

### deploy-theme.ps1
- **Purpose**: Copy theme files from repository to WordPress
- **Parameters**: `-WordPressPath` (default: "public")
- **Direction**: `course_creator/themes/geeks-child` → `wp-content/themes/geeks-child`

### sync-from-wordpress.ps1
- **Purpose**: Copy theme files from WordPress to repository
- **Parameters**: `-WordPressPath` (default: "public")
- **Direction**: `wp-content/themes/geeks-child` → `course_creator/themes/geeks-child`

## Key Files and Locations

### Core Course Creator Files
- `wp-content/themes/geeks-child/functions.php` - Main theme functions
- `wp-content/themes/geeks-child/tutor/course-automation-loader.php` - Main loader
- `wp-content/themes/geeks-child/tutor/core/course-creator.php` - Course creation logic
- `wp-content/themes/geeks-child/tutor/core/csv-parser.php` - CSV processing
- `wp-content/themes/geeks-child/tutor/shortcodes/course-shortcodes.php` - Shortcodes

### Test Files
- `backlog/sample-course.csv` - Sample course data for testing
- `tests/debug-shortcodes.php` - Debug utilities

## Troubleshooting

### Common Issues
1. **PowerShell Execution Policy Error**
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

2. **WordPress Path Not Found**
   - Verify the `-WordPressPath` parameter
   - Use `".."` if WordPress is at parent directory
   - Use `"public"` if WordPress is in public folder

3. **Theme Not Activating**
   - Ensure Geeks parent theme is installed
   - Check file permissions
   - Verify all core files are present

4. **Shortcodes Not Working**
   - Verify Tutor LMS plugin is active
   - Check functions.php includes course-automation-loader.php
   - Test with `[panapana_master_debug]` first

### Verification Commands
```powershell
# Check if files are in sync
git diff --no-index themes/geeks-child ../wp-content/themes/geeks-child

# Check git status
git status

# Verify WordPress structure
ls ../wp-content/themes/geeks-child/tutor/core/
```

## Important Notes
- Always test changes in WordPress before committing
- Use descriptive commit messages
- Keep repository and WordPress in sync
- The workflow supports multiple developers working on different PCs
- Scripts handle file copying automatically - no manual file management needed

## Quick Reference Commands
```powershell
# Setup new environment
.\scripts\setup-new-environment.ps1 -WordPressPath ".."

# Deploy repo → WordPress
.\scripts\deploy-theme.ps1 -WordPressPath ".."

# Sync WordPress → repo
.\scripts\sync-from-wordpress.ps1 -WordPressPath ".."

# Standard git workflow
git pull origin main
git add .
git commit -m "Description"
git push origin main
```

## VTT Subtitle Configuration

### Automatic Path Detection
The system automatically detects your environment and uses the appropriate VTT path:

- **Local Development** (`localhost`): `http://localhost/wp-content/uploads/legendas/`
- **Production** (live site): `https://cursos.institutopanapana.org.br/wp-content/uploads/legendas/`

### CSV VTT Path Options
In your CSV files, you can specify VTT paths in two ways:

1. **Filename only** (recommended): `4n4I0EqvBk0.pt.vtt`
   - System automatically prepends the correct base URL
   - Works across different environments

2. **Full URL**: `https://cursos.institutopanapana.org.br/wp-content/uploads/legendas/4n4I0EqvBk0.pt.vtt`
   - Uses the exact URL provided
   - May not work across different environments

### VTT File Management
- **Local Development**: Place VTT files in `wp-content/uploads/legendas/`
- **Production**: Upload VTT files to the same path on live server
- **File Naming**: Use YouTube video ID + `.pt.vtt` (e.g., `4n4I0EqvBk0.pt.vtt`)
