<?php
/**
 * Template Name: Certificate Viewer (for Hash URLs) - Updated V2
 * This template is used by the page 'visualizar-certificado'
 * and is triggered by the rewrite rule for cert_hash=...
 */

// Get the certificate hash from the URL query variable (set by rewrite rule)
$certificate_hash = get_query_var('certificate_hash') ? sanitize_text_field(get_query_var('certificate_hash')) : null;

// Initialize variables
$student_name = 'NOME DO ALUNO(A) EXEMPLO LONGO PARA TESTE DE QUEBRA DE LINHA'; // Default for preview if no hash
$course_title = 'TÍTULO DO CURSO EXEMPLO COM VÁRIAS PALAVRAS PARA VERIFICAR O LAYOUT'; // Default for preview if no hash
$verification_id = $certificate_hash ? htmlspecialchars($certificate_hash) : 'ID EXEMPLO LONGO ff15c91381df46f472f434a077fc3faa8'; // Use the hash as verification ID
$data_found = false;

if ($certificate_hash) {
    // Path to your CSV file within your child theme
    $csv_file_path = get_stylesheet_directory() . '/csv/LISTA_ALUNAS_FINAL_CERTIFICATE_SYSTEM.csv';

    if (file_exists($csv_file_path) && is_readable($csv_file_path)) {
        if (($handle = fopen($csv_file_path, 'r')) !== FALSE) {
            // Skip the header row of the CSV
            fgetcsv($handle); 

            while (($row_data = fgetcsv($handle, 1000, ',')) !== FALSE) {
                if (isset($row_data[2]) && trim($row_data[2]) == trim($certificate_hash)) {
                    $student_name = isset($row_data[0]) ? htmlspecialchars(trim($row_data[0])) : 'N/A';
                    $course_title = isset($row_data[1]) ? htmlspecialchars(trim($row_data[1])) : 'N/A';
                    $data_found = true;
                    break; 
                }
            }
            fclose($handle);
        }
    }
}

if (!$data_found && $certificate_hash) {
    $student_name = 'Certificado não encontrado';
    $course_title = 'Verifique o ID fornecido';
    $verification_id = htmlspecialchars($certificate_hash); // Keep the provided hash as ID
} elseif (!$certificate_hash && !$data_found) { // If no hash and no data (e.g. direct load for styling)
     $verification_id = 'ID EXEMPLO LONGO ff15c91381df46f472f434a077fc3faa8'; // keep example for styling
}


?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificado - <?php echo $student_name; ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lexend:wght@400;700&family=Montserrat:ital,wght@0,400;0,700;1,400&family=Manrope:wght@400;700&family=Montserrat+Alternates:wght@700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; background-color: #f0f4f8; display: flex; justify-content: center; align-items: center; min-height: 100vh; padding: 20px; box-sizing: border-box; margin: 0; }
        .certificate-container { width: 1139px; height: 880px; background-color: white; position: relative; box-shadow: 0 0 20px rgba(0,0,0,0.1); overflow: hidden; }
        .font-lexend { font-family: 'Lexend', sans-serif; }
        .font-montserrat { font-family: 'Montserrat', sans-serif; }
        .font-montserrat-alternates { font-family: 'Montserrat Alternates', sans-serif; }
        .font-manrope { font-family: 'Manrope', sans-serif; }
        .certificate-element { position: absolute; box-sizing: border-box; line-height: 1.4; }
        .certificate-image { position: absolute; object-fit: contain; }
        img.error-placeholder { border: 1px dashed #ccc; background-color: #f9f9f9; display: flex; align-items: center; justify-content: center; text-align: center; font-size: 0.8rem; color: #777; }
    </style>
</head>
<body>
    <div class="certificate-container">
        <img src="https://cursos.institutopanapana.org.br/wp-content/uploads/2024/09/<EMAIL>"
             alt="Banner Lateral do Certificado"
             class="certificate-image"
             style="width: 382px; height: 881px; top: -0.666657px; left: -1.11111px;"
             onerror="this.classList.add('error-placeholder'); this.alt='Erro ao carregar banner'; this.src='https://placehold.co/382x881/FF6347/FFFFFF?text=Banner+Erro';"
        >

        <img src="https://cursos.institutopanapana.org.br/wp-content/uploads/2023/06/LOGO-insituto-panapana-org-hr.png"
             alt="Logo Instituto Panapaná"
             class="certificate-image"
             style="width: 270px; height: 89px; top: 91.5555px; left: 481.222px;"
             onerror="this.classList.add('error-placeholder'); this.alt='Erro ao carregar logo Panapaná'; this.src='https://placehold.co/270x89/CCCCCC/000000?text=Logo+Panapaná';"
        >

        <img src="https://cursos.institutopanapana.org.br/wp-content/uploads/2024/09/<EMAIL>"
             alt="Selo do Certificado"
             class="certificate-image"
             style="width: 120px; height: 121px; top: 78.235px; left: 915.179px;"
             onerror="this.classList.add('error-placeholder'); this.alt='Erro ao carregar selo'; this.src='https://placehold.co/120x121/CCCCCC/000000?text=Selo';"
        >

        <!-- TEXT SECTION - Align Left -->
        <div class="certificate-element font-montserrat"
             style="top: 230px; left: 474px; width: 570px; height: auto; font-size: 18px; color: rgb(0, 0, 0); text-align: left;">
            <p>Certificamos que</p>
        </div>

        <div class="certificate-element font-montserrat-alternates placeholder-text"
             style="top: 265px; left: 474px; width: 580px; height: auto; font-size: 28px; font-weight: 700; color: rgb(20, 19, 37); text-align: left; line-height: 1.3;">
            <?php echo $student_name; ?>
        </div>
        
        <div class="certificate-element font-montserrat"
             style="top: 335px; left: 474px; width: 570px; height: auto; font-size: 18px; color: rgb(0, 0, 0); text-align: left;">
            <p>concluiu com êxito a formação</p>
        </div>

        <div class="certificate-element font-montserrat placeholder-text"
             style="top: 370px; left: 474px; width: 580px; height: auto; font-size: 22px; font-weight: 700; color: rgb(0, 0, 0); text-align: left; line-height: 1.3;">
            <?php echo $course_title; ?>
        </div>
        
        <div class="certificate-element font-montserrat"
             style="top: 425px; left: 474px; width: 570px; height: auto; font-size: 18px; color: rgb(0, 0, 0); text-align: left;">
            <p>promovida pelo Instituto Panapaná.</p>
        </div>

        <div class="certificate-element font-montserrat"
             style="top: 490px; left: 474px; width: 570px; height: auto; font-size: 16px; color: rgb(49, 38, 39); text-align: left; line-height: 1.6;">
            <p>Parabenizamos por essa conquista. Que este certificado simbolize seu esforço e a confiança no seu potencial para transformar o futuro.</p>
        </div>

        <div class="certificate-element font-montserrat"
             style="top: 565px; left: 474px; width: 570px; height: auto; font-size: 16px; color: rgb(49, 38, 39); text-align: left; line-height: 1.6;"> <!-- Adjusted top slightly for spacing -->
            <p>Com orgulho, reconhecemos sua trajetória e celebramos sua evolução.</p>
        </div>
        <!-- END TEXT SECTION -->


        <img src="https://cursos.institutopanapana.org.br/wp-content/uploads/2023/05/assinatura-camila.jpg"
             alt="Assinatura Camila Siqueira"
             class="certificate-image"
             style="width: 199px; height: 78px; top: 640px; left: 475.75px;"
             onerror="this.classList.add('error-placeholder'); this.alt='Erro ao carregar assinatura'; this.src='https://placehold.co/199x78/CCCCCC/000000?text=Assinatura';"
        >

        <div class="certificate-element font-lexend"
             style="top: 722px; left: 481.5px; width: auto; height: auto; font-size: 17px; color: rgb(0, 0, 0);">
            <p>Camila Siqueira</p>
        </div>

        <div class="certificate-element font-manrope"
             style="top: 746px; left: 481.75px; width: auto; height: auto; font-size: 13px; color: rgb(49, 38, 39);">
            <p>Presidente do Instituto</p>
        </div>

        <div class="certificate-element font-manrope"
             style="top: 812px; left: 474px; width: auto; height: auto; font-size: 13px; color: rgb(49, 38, 39);">
            <p>Certificado Id: </p>
        </div>

        <div class="certificate-element font-lexend placeholder-text"
             style="top: 812px; left: 568px; width: 380px; height: auto; font-size: 13px; color: rgb(0, 0, 0); text-align: left;"> <!-- Adjusted left, added width and text-align for potentially long ID -->
            <?php echo $verification_id; ?>
        </div>

        <img src="https://cursos.institutopanapana.org.br/wp-content/uploads/2024/12/Instituto_Federal_de_Alagoas_-_Marca_Vertical_2015.svg.png"
             alt="Logo Instituto Federal de Alagoas"
             class="certificate-image"
             style="width: 98px; height: 86px; top: 665.375px; left: 989.278px;"
             onerror="this.classList.add('error-placeholder'); this.alt='Erro ao carregar logo IFAL'; this.src='https://placehold.co/98x86/CCCCCC/000000?text=Logo+IFAL';"
        >

        <img src="https://cursos.institutopanapana.org.br/wp-content/uploads/2024/12/GOVERNO-FEDERAL-SECRETARIA-GERAL-LOGO.png"
             alt="Logo Governo Federal"
             class="certificate-image"
             style="width: 260px; height: 57px; top: 778.374px; left: 828.389px;"
             onerror="this.classList.add('error-placeholder'); this.alt='Erro ao carregar logo Gov Federal'; this.src='https://placehold.co/260x57/CCCCCC/000000?text=Logo+Gov';"
        >

    </div>
</body>
</html>
<?php
exit;
?>