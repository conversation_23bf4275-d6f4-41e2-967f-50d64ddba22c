# Course Creator Repository Structure

This document outlines the recommended structure for the course_creator GitHub repository.

## 📁 Repository Structure

```
course_creator/
├── README.md                           # Main project documentation
├── .gitignore                         # Git ignore rules for WordPress
├── SETUP.md                           # Setup instructions for new environments
├── 
├── themes/
│   └── geeks-child/                   # WordPress child theme
│       ├── functions.php              # Theme functions
│       ├── style.css                  # Child theme styles
│       └── tutor/                     # Course automation system
│           ├── course-automation-loader.php
│           ├── core/
│           │   ├── capabilities.php
│           │   ├── csv-parser.php
│           │   ├── course-creator.php
│           │   ├── course-exporter.php
│           │   └── quiz-creator.php
│           └── shortcodes/
│               └── course-shortcodes.php
│
├── backlog/                           # Sample files and test data
│   ├── sample-course.csv              # Example CSV for testing
│   └── test-files/                    # Additional test resources
│
├── database/                          # Database management
│   ├── schema/                        # Database structure docs
│   ├── migrations/                    # Database update scripts
│   └── sample-data/                   # Sample database exports
│
├── docs/                              # Project documentation
│   ├── ARCHITECTURE.md                # System architecture overview
│   ├── CSV-FORMAT.md                  # CSV file format specification
│   ├── DEPLOYMENT.md                  # Deployment instructions
│   └── TROUBLESHOOTING.md             # Common issues and solutions
│
├── scripts/                           # Utility scripts
│   ├── deploy.sh                      # Deployment script
│   ├── backup-db.sh                   # Database backup script
│   └── sync-files.sh                  # File synchronization script
│
└── tests/                             # Testing files
    ├── test-csv-automation.php        # Automation testing script
    └── debug-shortcodes.php           # Shortcode debugging tool
```

## 🎯 What Gets Committed

### ✅ Include in Git:
- Custom theme files (geeks-child)
- Course automation system
- Documentation
- Sample/test files
- Configuration templates
- Utility scripts

### ❌ Exclude from Git:
- WordPress core files
- Third-party plugins (except custom ones)
- wp-config.php (sensitive data)
- Uploads folder
- Cache files
- Database files (except samples)
- Environment-specific configs

## 🔄 Workflow

1. **Work PC**: Make changes, commit, push to GitHub
2. **Home PC**: Pull changes, sync database if needed
3. **Both**: Use same WordPress setup with this child theme

## 📋 Next Steps

1. Create .gitignore file
2. Copy automation system files
3. Create documentation
4. Initialize Git repository
5. Push to GitHub
