<?php
/**
 * Tutor LMS Course Automation - Quiz Creator
 *
 * This file handles quiz, question, and answer creation functionality
 * for the course automation system.
 *
 * @package TutorLMS_CourseAutomation
 * @subpackage Core
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Creates a quiz with questions and answers from CSV data.
 *
 * @param int   $topic_id   Topic ID where quiz belongs.
 * @param array $quiz_data  Quiz data including questions and answers.
 * @return int|WP_Error Quiz ID on success, error object on failure.
 */
function panapana_create_quiz_from_data( $topic_id, $quiz_data ) {
	$quiz_info = $quiz_data['quiz_info'];
	$quiz_title = sanitize_text_field( $quiz_info['item_title'] );

	// Create the quiz post
	$quiz_post_data = array(
		'post_title'   => $quiz_title,
		'post_content' => wp_kses_post( $quiz_info['quiz_description'] ?? '' ),
		'post_status'  => 'publish',
		'post_type'    => 'tutor_quiz',
		'post_parent'  => $topic_id,
		'post_author'  => get_current_user_id(),
	);

	$quiz_id = wp_insert_post( $quiz_post_data );
	if ( is_wp_error( $quiz_id ) ) {
		return new WP_Error( 'quiz_creation_failed', 'Falha ao criar quiz: ' . $quiz_id->get_error_message() );
	}

	// Set quiz metadata
	$course_id = get_post_meta( $topic_id, '_tutor_topic_course_id', true );
	update_post_meta( $quiz_id, '_tutor_quiz_course_id', $course_id );
	update_post_meta( $quiz_id, '_tutor_quiz_topic_id', $topic_id );

	// Parse and set quiz settings
	$quiz_settings = panapana_parse_quiz_settings( $quiz_info['quiz_settings'] ?? '' );
	update_post_meta( $quiz_id, 'tutor_quiz_option', $quiz_settings );

	// Create questions and answers
	$question_order = 1;
	foreach ( $quiz_data['questions'] as $question_data ) {
		$question_result = panapana_create_quiz_question( $quiz_id, $question_data, $question_order );
		if ( is_wp_error( $question_result ) ) {
			return new WP_Error( 'question_creation_failed',
				"Falha ao criar pergunta {$question_order}: " . $question_result->get_error_message() );
		}
		$question_order++;
	}

	return $quiz_id;
}

/**
 * Parses quiz settings from CSV format to WordPress meta format.
 *
 * @param string $settings_string Settings in format "key1:value1,key2:value2".
 * @return array Parsed settings array.
 */
function panapana_parse_quiz_settings( $settings_string ) {
	$default_settings = array(
		'time_limit' => array(
			'time_limit_duration' => '0',
			'time_limit_duration_type' => 'minutes',
		),
		'passing_score' => '70',
		'max_attempts' => '0',
		'feedback_mode' => 'default',
		'hide_quiz_time_display' => '0',
		'quiz_auto_start' => '0',
		'question_layout_view' => 'single_question',
		'questions_order' => 'rand',
		'hide_question_number_overview' => '0',
		'short_answer_characters_limit' => '200',
	);

	if ( empty( $settings_string ) ) {
		return $default_settings;
	}

	// Parse settings string
	$settings_pairs = explode( ',', $settings_string );
	foreach ( $settings_pairs as $pair ) {
		$parts = explode( ':', trim( $pair ) );
		if ( count( $parts ) === 2 ) {
			$key = trim( $parts[0] );
			$value = trim( $parts[1] );

			switch ( $key ) {
				case 'time_limit':
					if ( intval( $value ) > 0 ) {
						$default_settings['time_limit'] = array(
							'time_limit_duration' => intval( $value ),
							'time_limit_duration_type' => 'minutes',
						);
					}
					break;

				case 'passing_score':
					$default_settings['passing_score'] = intval( $value );
					break;

				case 'max_attempts':
					$default_settings['max_attempts'] = intval( $value );
					break;
			}
		}
	}

	return $default_settings;
}

/**
 * Creates a quiz question in the database.
 *
 * @param int   $quiz_id        Quiz ID.
 * @param array $question_data  Question data including answers.
 * @param int   $question_order Question order number.
 * @return int|WP_Error Question ID on success, error object on failure.
 */
function panapana_create_quiz_question( $quiz_id, $question_data, $question_order ) {
	global $wpdb;

	$question_info = $question_data['question_info'];
	$question_text = sanitize_text_field( $question_info['content'] );
	$question_type = sanitize_text_field( $question_info['question_type'] ?? 'single_choice' );

	// Validate question type
	$valid_types = array( 'single_choice', 'true_false' );
	if ( ! in_array( $question_type, $valid_types ) ) {
		return new WP_Error( 'invalid_question_type', "Tipo de pergunta inválido: {$question_type}" );
	}

	// Insert question into database
	$question_data_db = array(
		'quiz_id'              => $quiz_id,
		'question_title'       => $question_text,
		'question_description' => sanitize_textarea_field( $question_info['question_description'] ?? '' ),
		'question_type'        => $question_type,
		'question_mark'        => intval( $question_info['question_points'] ?? 1 ),
		'question_settings'    => maybe_serialize( array() ),
		'question_order'       => $question_order,
	);

	$result = $wpdb->insert(
		$wpdb->prefix . 'tutor_quiz_questions',
		$question_data_db,
		array( '%d', '%s', '%s', '%s', '%d', '%s', '%d' )
	);

	if ( $result === false ) {
		return new WP_Error( 'question_insert_failed', 'Falha ao inserir pergunta no banco de dados.' );
	}

	$question_id = $wpdb->insert_id;

	// Create answers for the question
	$answer_order = 1;
	foreach ( $question_data['answers'] as $answer_data ) {
		$answer_result = panapana_create_quiz_answer( $question_id, $answer_data, $question_type );
		if ( is_wp_error( $answer_result ) ) {
			return new WP_Error( 'answer_creation_failed',
				"Falha ao criar resposta {$answer_order}: " . $answer_result->get_error_message() );
		}
		$answer_order++;
	}

	return $question_id;
}

/**
 * Creates a quiz answer in the database.
 *
 * @param int    $question_id   Question ID.
 * @param array  $answer_data   Answer data from CSV.
 * @param string $question_type Type of question.
 * @return int|WP_Error Answer ID on success, error object on failure.
 */
function panapana_create_quiz_answer( $question_id, $answer_data, $question_type ) {
	global $wpdb;

	$answer_text = sanitize_text_field( $answer_data['answer_text'] );
	$is_correct = intval( $answer_data['is_correct'] ?? 0 );

	// Prepare answer data based on question type
	$answer_data_db = array(
		'belongs_question_id'   => $question_id,
		'belongs_question_type' => $question_type,
		'answer_title'          => $answer_text,
		'is_correct'            => $is_correct,
		'answer_order'          => 0, // Will be updated after insertion
		'answer_view_format'    => 'text',
	);

	// Handle special cases for true_false questions
	if ( $question_type === 'true_false' ) {
		$answer_data_db['answer_two_gap_match'] = $is_correct ? 'true' : 'false';
	}

	$result = $wpdb->insert(
		$wpdb->prefix . 'tutor_quiz_question_answers',
		$answer_data_db,
		array( '%d', '%s', '%s', '%d', '%d', '%s' )
	);

	if ( $result === false ) {
		return new WP_Error( 'answer_insert_failed', 'Falha ao inserir resposta no banco de dados.' );
	}

	$answer_id = $wpdb->insert_id;

	// Update answer order
	$wpdb->update(
		$wpdb->prefix . 'tutor_quiz_question_answers',
		array( 'answer_order' => $answer_id ),
		array( 'answer_id' => $answer_id ),
		array( '%d' ),
		array( '%d' )
	);

	return $answer_id;
}
