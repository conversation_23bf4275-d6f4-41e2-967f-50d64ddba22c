# <!=Copyright (C) 2024 MadrasThemes
# This file is distributed under the GNU General Public License v3 or later.=!>
msgid ""
msgstr ""
"Project-Id-Version: Geeks 1.2.22\n"
"Report-Msgid-Bugs-To: https://madrasthemes.freshdesk.com/\n"
"POT-Creation-Date: 2024-05-10 07:57:43+00:00\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"PO-Revision-Date: 2025-05-05 00:08+0000\n"
"Last-Translator: \n"
"Language-Team: Português do Brasil\n"
"X-Generator: Loco https://localise.biz/\n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Loco-Version: 2.6.11; wp-6.6.1"

#: tutor/single/course/enrolled/completing-progress.php:26
msgid " Complete"
msgstr ""

#: learnpress/single-course/loop-section.php:132
#: templates/courses/single-course-enrolled.php:102
msgid " Completed"
msgstr ""

#: tutor/dashboard/my-courses.php:156
msgid " Free"
msgstr ""

#: inc/template-functions/header.php:570 inc/template-functions/header.php:587
msgid " Sign In"
msgstr "Entrar"

#: woocommerce/single-product-reviews.php:101
msgid " Your review must be at least 50 characters."
msgstr ""

#: tutor/dashboard/notifications/profile-completion.php:33
msgid "% Complete"
msgstr "% Concluído"

#: learnpress/single-course/sidebar/user-progress.php:34
msgid "%1$d/%2$d"
msgstr "%1$d / %2$d"

#. translators: 1: credit card type 2: last 4 digits
#: woocommerce/myaccount/payment-methods.php:57
msgid "%1$s ending in %2$s"
msgstr "%1$s terminando em %2$s"

#. translators: 1: formatted order total 2: total order items
#: woocommerce/myaccount/orders.php:78
msgid "%1$s for %2$s item"
msgid_plural "%1$s for %2$s items"
msgstr[0] "%1$s para o item %2$s"
msgstr[1] "%1$s para itens %2$s"

#: learnpress/single-course/content-item/popup-header.php:36
msgid "%1$s of %2$d items"
msgstr "%1$s de %2$d itens"

#: tutor/dashboard/index.php:80
msgid "%d Ratings"
msgstr " %d  Ratings"

#: inc/learnpress/template-functions/single-item.php:31
msgid "%dq"
msgid_plural "%dq"
msgstr[0] "%dq"
msgstr[1] "%dq"

#: tutor/single/quiz/top.php:90
msgid "%s"
msgstr "%s"

#: tutor/dashboard/index.php:92
msgid "%s Add A New Course"
msgstr " %s  adicionar um novo curso"

#. translators: %s is comment date
#: inc/woocommerce/template-functions/single-product.php:401
#: learnpress/addons/course-review/loop-review.php:23
#: tutor/dashboard/question-answer/answers.php:46
#: tutor/dashboard/question-answer/answers.php:87
#: tutor/dashboard/reviews/given-reviews.php:79 tutor/dashboard/reviews.php:71
#: tutor/single/course/reviews-loop.php:10
msgid "%s ago"
msgstr "%s ago"

#: tutor/dashboard/index.php:99
msgid "%s Become an instructor"
msgstr "%s torne-se um instrutor"

#. translators: %s: course category title
#: inc/tutor/geeks-tutor-template-functions.php:573
msgid "%s Courses"
msgstr " %s  Cursos"

#: woocommerce/single-product/rating.php:43
msgid "%s customer review"
msgid_plural "%s customer reviews"
msgstr[0] " %s  avaliação do cliente"
msgstr[1] " %s  avaliações de clientes"

#. translators: %s template
#: inc/geeks-functions.php:545
msgid "%s does not exist."
msgstr "%s não existe."

#: inc/learnpress/template-functions/loop.php:253
#: inc/template-functions/lms.php:44
msgid "%s Enrolled"
msgstr "%s inscritos"

#: tutor/dashboard/assignments/submitted.php:97
msgid "%s Fail %s"
msgstr " %s  falha %s"

#. translators: 1: plugin name.
#: inc/classes/class-tgm-plugin-activation.php:3634
msgid "%s installed and activated successfully."
msgstr " %s  instalado e ativado com sucesso."

#. translators: 1: plugin name.
#: inc/classes/class-tgm-plugin-activation.php:3642
msgid "%s installed successfully."
msgstr " %s  instalado com sucesso."

#. translators: %s - Minutes
#: inc/template-functions/posts.php:195
#: inc/template-functions/single-post.php:29
msgid "%s min read"
msgstr " %s  min read"

#: learnpress/addons/course-review/rating-stars.php:16
msgid "%s out of 5 stars"
msgstr "%s de 5 estrelas"

#: tutor/dashboard/assignments/submitted.php:97
msgid "%s Pass %s"
msgstr " %s  Pass %s"

#. translators: %s: Quantity.
#: woocommerce/global/quantity-input.php:24
msgid "%s quantity"
msgstr " %s  quantidade"

#. translators: %s: span wrap start, %s: number of courses, %s: span wrap close
#: archive-course_path.php:59
msgid "%s%s%s Courses"
msgstr " %s   %s   %s  Cursos"

#. translators: %s: span wrap start, %s: number of hours, %s: span wrap close
#: archive-course_path.php:67
msgid "%s%s%s Hours"
msgstr "%s %s   %s  horas"

#. translators: %s is the decimal value of number of students expressed in
#. thousands.
#: inc/acf/functions/path.php:18
msgid "%sK"
msgstr "%sK"

#: inc/geeks-functions.php:150
msgid "&laquo; Prev"
msgstr "\"Prev"

#: comments.php:59
msgid "&larr; Older Comments"
msgstr "⑤ Comentários Mais Antigos"

#. translators: %s: page number
#: inc/tutor/geeks-tutor-template-functions.php:569
msgid "&nbsp;&ndash; Page %s"
msgstr " - Página %s"

#. translators: %s location.
#: woocommerce/cart/cart-totals.php:73
msgid "(estimated for %s)"
msgstr "(estimativa para %s)"

#: learnpress/checkout/order-received.php:102
msgid "(No item)"
msgstr "(Nenhum item)"

#: job_manager/account-signin.php:65 job_manager/job-submit.php:43
#: job_manager/job-submit.php:62
msgid "(optional)"
msgstr "(opcional)"

#: inc/template-functions/posts.php:112
msgid ", "
msgstr ""

#: inc/ocdi/class-geeks-ocdi.php:296
msgid "1 course including 1 page & 14 images"
msgstr "1 curso incluindo 1 página e 14 imagens"

#: inc/ocdi/class-geeks-ocdi.php:407 inc/ocdi/class-geeks-ocdi.php:447
msgid "1 page"
msgstr "1 página"

#: inc/ocdi/class-geeks-ocdi.php:427 inc/ocdi/class-geeks-ocdi.php:437
msgid "1 page & 1 image"
msgstr "1 página & 1 imagem"

#: inc/ocdi/class-geeks-ocdi.php:316
msgid "1 page & 15 images"
msgstr "1 Página & 15 imagens"

#: inc/ocdi/class-geeks-ocdi.php:457
msgid "1 page & 17 image"
msgstr "1 Página & 17 imagem"

#: inc/ocdi/class-geeks-ocdi.php:386
msgid "1 page & 17 images"
msgstr "1 Página & 17 imagens"

#: inc/ocdi/class-geeks-ocdi.php:306
msgid "1 page & 8 images"
msgstr "1 Página & 8 imagens"

#: inc/ocdi/class-geeks-ocdi.php:397
msgid "1 page & 9 images"
msgstr "1 Página & 9 imagens"

#: inc/ocdi/class-geeks-ocdi.php:417
msgid "1 table including 1 page & 2 images"
msgstr "1 tabela incluindo 1 página e 2 imagens"

#: inc/ocdi/class-geeks-ocdi.php:350
msgid "10 images, 31 courses, 600 lessons & 19 paths"
msgstr "10 imagens, 31 cursos, 600 aulas e 19 caminhos"

#: inc/ocdi/class-geeks-ocdi.php:339
msgid "11 blog posts, 1 page & 6 images"
msgstr "11 posts, 1 página e 6 imagens"

#: inc/ocdi/class-geeks-ocdi.php:328
msgid "11 job listings, 2 pages & 16 images"
msgstr "11 anúncios de emprego, 2 páginas e 16 imagens"

#: inc/ocdi/class-geeks-ocdi.php:286
msgid "12 courses including 1 page & 21 images"
msgstr "12 cursos, incluindo 1 página e 21 imagens"

#: inc/ocdi/class-geeks-ocdi.php:328 inc/ocdi/class-geeks-ocdi.php:339
#: inc/ocdi/class-geeks-ocdi.php:350 inc/ocdi/class-geeks-ocdi.php:362
msgid "2-3 minutes"
msgstr "2-3 minutos"

#: inc/customizer/class-geeks-customizer.php:694
msgid "404"
msgstr "404"

#: inc/customizer/class-geeks-customizer.php:733
msgid "404 Image Upload"
msgstr "404 Upload De Imagem"

#: inc/customizer/class-geeks-customizer.php:757
msgid "404 Title"
msgstr "404 título"

#: inc/ocdi/class-geeks-ocdi.php:276
msgid "8 courses including 1 page & 9 images"
msgstr "8 cursos, incluindo 1 página e 9 imagens"

#: inc/ocdi/class-geeks-ocdi.php:362
msgid "80 items"
msgstr "80 itens"

#: tutor/dashboard/assignments/review.php:94
msgid ": 2MB"
msgstr ": 2MB"

#: tutor/dashboard/my-profile.php:52
msgid "________"
msgstr "________"

#: inc/learnpress/geeks-learnpress-functions.php:12
msgid "A &rarr; Z"
msgstr "A ⑦ Z"

#: templates/create-course/general-info-content.php:50
msgid "A brief summary of your courses."
msgstr "Um breve resumo de seus cursos."

#: woocommerce/myaccount/lost-password-confirmation.php:28
msgid ""
"A password reset email has been sent to the email address on file for your "
"account, but may take several minutes to show up in your inbox. Please wait "
"at least 10 minutes before attempting another reset."
msgstr ""
"Um e-mail de redefinição de senha foi enviado para o endereço de E-mail "
"registrado em sua conta, mas pode levar vários minutos para aparecer em sua "
"caixa de entrada. Aguarde pelo menos 10 minutos antes de tentar outra "
"redefinição."

#: inc/woocommerce/template-functions/my-account.php:139
#: woocommerce/myaccount/form-login.php:120
msgid "A password will be sent to your email address."
msgstr "Uma senha será enviada para o seu endereço de E-mail."

#: inc/gke/geeks-gke-template-functions.php:115
msgid "About"
msgstr "Sobre"

#: tutor/profile/bio.php:24
msgid "About me"
msgstr "Sobre mim"

#: inc/gke/geeks-gke-template-functions.php:304
msgid "About Path"
msgstr "Sobre O Caminho"

#: inc/template-functions/global.php:234
msgid ""
"Account created successfully. Please check your email to create your account "
"password"
msgstr ""
"Conta criada com sucesso. Verifique seu e-mail para criar a senha da sua "
"conta"

#: inc/learnpress/functions/profile.php:125 inc/tutor/class-geeks-tutor.php:65
msgid "Account Settings"
msgstr "Configurações Da Conta"

#: inc/geeks-functions.php:562
msgid "action_args should not be overwritten when calling geeks_get_template."
msgstr "action_args não deve ser sobrescrito ao chamar geeks_get_template."

#: edd_templates/checkout_cart.php:13
#: learnpress/profile/tabs/orders/list.php:37
msgid "Actions"
msgstr "Ações"

#: inc/classes/class-tgm-plugin-activation.php:2847
msgid "Activate"
msgstr "Ativar"

#. translators: %2$s: plugin name in screen reader markup
#: inc/classes/class-tgm-plugin-activation.php:2735
msgid "Activate %2$s"
msgstr "Ativar %2$s"

#: inc/classes/class-tgm-plugin-activation.php:2442
msgid "Active"
msgstr "Ativo"

#: learnpress/profile/tabs/courses/general-statistic.php:38
#: tutor/dashboard/dashboard.php:180 tutor/dashboard/enrolled-courses.php:23
msgid "Active Courses"
msgstr "Cursos Ativos"

#: woocommerce/myaccount/my-address.php:68
msgid "Add"
msgstr "Adicionar"

#: inc/gke/class-geeks-gke-customizer.php:73
msgid "Add a description for your path"
msgstr "Adicione uma descrição para o seu caminho"

#: inc/classes/class-wp-bootstrap-navwalker.php:440
msgid "Add a menu"
msgstr "Adicionar um menu"

#: inc/tutor/geeks-tutor-functions.php:84
#: inc/tutor/geeks-tutor-functions.php:90
msgid "Add A New Course"
msgstr "Adicionar Um Novo Curso"

#: inc/gke/class-geeks-gke-customizer.php:57
msgid "Add a Title for your path"
msgstr "Adicione um título para o seu caminho"

#: tutor/dashboard/announcements.php:88
msgid "Add New Announcement"
msgstr "Adicionar Novo Anúncio"

#: tutor/dashboard/create-course.php:51
msgid "Add New Course"
msgstr "Adicionar Novo Curso"

#: woocommerce/myaccount/payment-methods.php:123
msgid "Add Payment Method"
msgstr "Adicionar Forma De Pagamento"

#: woocommerce/myaccount/form-add-payment-method.php:56
msgid "Add payment method"
msgstr "Adicionar forma de pagamento"

#: learnpress/addons/course-review/review-form.php:71
msgid "Add review"
msgstr "Adicionar revisão"

#: inc/tutor/geeks-tutor-template-functions.php:357
msgid "Add to Bookmark"
msgstr "Adicionar aos favoritos"

#: learnpress/addons/wishlist/button.php:34
msgid "Add to Wishlist"
msgstr "Adicionar à Wishlist"

#: tutor/dashboard/create-course.php:190
msgid ""
"Add Topics in the Course Builder section to create lessons, quizzes, and "
"assignments."
msgstr ""
"Adicione tópicos na seção Criador de cursos para criar lições, questionários "
"e tarefas."

#: tutor/dashboard/settings/profile.php:179
msgid "Add your social profile links in below social accounts."
msgstr "Adicione os links do seu perfil social nas contas sociais abaixo."

#: woocommerce/wishlist-view-mobile.php:139
msgid "Added on:"
msgstr "Adicionado em:"

#. translators: date added label: 1 date added.
#: woocommerce/wishlist-view.php:257
msgid "Added on: %s"
msgstr "Adicionado em: %s"

#: inc/tutor/geeks-tutor-template-functions.php:355
msgid "Added to Bookmark"
msgstr "Adicionado ao marcador"

#: inc/learnpress/class-geeks-learnpress.php:142
msgid "Additional Info"
msgstr "Informações Adicionais"

#: learnpress/checkout/order-comment.php:15
msgid "Additional Information"
msgstr "Informações Adicionais"

#: woocommerce/checkout/form-shipping.php:59
#: woocommerce/single-product/tabs/additional-information.php:22
msgid "Additional information"
msgstr "Informações adicionais"

#: learnpress/profile/tabs/courses.php:29
#: learnpress/profile/tabs/courses.php:37 tutor/dashboard/announcements.php:108
#: tutor/dashboard/assignments.php:43
msgid "All"
msgstr "Todos"

#: inc/classes/class-tgm-plugin-activation.php:3635
msgid "All installations and activations have been completed."
msgstr "Todas as instalações e ativações foram concluídas."

#: inc/classes/class-tgm-plugin-activation.php:3643
msgid "All installations have been completed."
msgstr "Todas as instalações foram concluídas."

#: inc/template-functions/lms.php:70
msgid "All Levels"
msgstr "Todos Os Níveis"

#: tutor/dashboard/earning.php:85
msgid "All of withdraw type excluding rejected."
msgstr "Todos do tipo retirada excluindo rejeitados."

#. translators: 1: dashboard link.
#: inc/classes/class-tgm-plugin-activation.php:401
msgid "All plugins installed and activated successfully. %1$s"
msgstr "Todos os plugins instalados e ativados com sucesso. %1$s"

#: inc/customizer/class-geeks-customizer.php:900
#: inc/template-functions/footer.php:204
msgid "All Rights Reserved"
msgstr "Todos Os Direitos Reservados"

#: tutor/dashboard/earning.php:67
msgid "All Time"
msgstr "Todos Os Tempos"

#: tutor/dashboard/earning/report-date_range.php:35
#: tutor/dashboard/earning/report-last_month.php:34
#: tutor/dashboard/earning/report-last_week.php:39
#: tutor/dashboard/earning/report-last_year.php:35
#: tutor/dashboard/earning/report-this_month.php:36
#: tutor/dashboard/earning/report-this_week.php:37
#: tutor/dashboard/earning/report-this_year.php:33
msgid "All time"
msgstr "Todos os tempos"

#: tutor/dashboard/earning/report-date_range.php:46
#: tutor/dashboard/earning/report-last_month.php:45
#: tutor/dashboard/earning/report-last_week.php:50
#: tutor/dashboard/earning/report-last_year.php:46
#: tutor/dashboard/earning/report-this_month.php:47
#: tutor/dashboard/earning/report-this_week.php:48
#: tutor/dashboard/earning/report-this_year.php:44
#: tutor/dashboard/earning.php:78
msgid "All time sales"
msgstr "All time sales"

#: tutor/dashboard/earning.php:87
msgid "All time withdrawals"
msgstr "Todas as retiradas de tempo"

#: learnpress/checkout/account-register.php:50
msgid "Already had an account?"
msgstr "Já tinha uma conta?"

#: inc/learnpress/template-functions/profile.php:47
#: inc/woocommerce/template-functions/my-account.php:109
#: tutor/dashboard/instructor/registration.php:65
#: tutor/dashboard/registration.php:65 woocommerce/myaccount/form-login.php:89
msgid "Already have an account?"
msgstr "Já tem uma conta?"

#: inc/ocdi/class-geeks-ocdi.php:267
msgid ""
"Alternatively, you can import this template directly into your page via Edit "
"with Elementor."
msgstr ""
"Como alternativa, você pode importar este modelo diretamente para sua página "
"via editar com Elementor."

#: edd_templates/history-purchases.php:22 tutor/dashboard/withdraw.php:157
#: tutor/dashboard/withdraw.php:209
msgid "Amount"
msgstr "Montante"

#. translators: 1: plugin name, 2: error message.
#: inc/classes/class-tgm-plugin-activation.php:3626
msgid "An error occurred while installing %1$s: %2$s."
msgstr "Ocorreu um erro durante a instalação do %1$s: %2$s."

#: learnpress/checkout/guest-checkout.php:40
msgid ""
"An order key to activate the course will be sent to your email after the "
"payment proceeded successfully."
msgstr ""
"Uma chave de pedido para ativar o curso será enviada para o seu e-mail após "
"o pagamento prosseguir com sucesso."

#: inc/tutor/class-geeks-tutor.php:145
msgid "Analytics"
msgstr "Analytics"

#: tutor/dashboard/announcements.php:65
msgid "Announcement"
msgstr "Edital"

#: tutor/dashboard/announcements/create.php:48
#: tutor/dashboard/announcements/update.php:48
msgid "Announcement Title"
msgstr "Título Do Anúncio"

#: tutor/dashboard/announcements/create.php:50
#: tutor/dashboard/announcements/update.php:50
msgid "Announcement title"
msgstr "Título do anúncio"

#: inc/tutor/class-geeks-tutor.php:120
#: inc/tutor/geeks-tutor-template-functions.php:153
#: inc/woocommerce/integrations.php:31
msgid "Announcements"
msgstr "Anúncios"

#: tutor/dashboard/question-answer/answers.php:22
msgid "Answer"
msgstr "Resposta"

#: job_manager/job-filters.php:65
msgid "Any category"
msgstr "Qualquer categoria"

#: tutor/single/assignment/content.php:341
msgid ""
"Any standard Image, Document, Presentation, Sheet, PDF or Text file is "
"allowed"
msgstr ""
"Qualquer imagem padrão, documento, apresentação, folha, PDF ou arquivo de "
"texto é permitido"

#: inc/wpjm/class-geeks-wpjm-customizer.php:101
msgid "Application Form"
msgstr "Formulário De Inscrição"

#: woocommerce/cart/cart.php:168 woocommerce/checkout/form-coupon.php:38
msgid "Apply coupon"
msgstr "Aplicar cupom"

#: inc/wpjm/geeks-wpjm-template-functions.php:68
msgid "Apply for this Job"
msgstr "Candidate-se a esta vaga"

#: tutor/dashboard/instructor/apply_for_instructor.php:51
msgid "Apply Now"
msgstr "Inscreva-Se Agora"

#: tutor/dashboard/my-courses.php:194
msgid ""
"Are you sure you want to delete this course permanently from the site? "
"Please confirm your choice."
msgstr ""
"Tem certeza que deseja excluir este curso permanentemente do site? Por favor,"
" confirme sua escolha."

#: tutor/dashboard/reviews/given-reviews.php:145
msgid ""
"Are you sure you want to delete this review permanently from the site? "
"Please confirm your choice."
msgstr ""
"Tem certeza de que deseja excluir esta resenha permanentemente do site? Por "
"favor, confirme sua escolha."

#: tutor/single/quiz/top.php:145
msgid "Are you sure you want to skip this quiz? Please confirm your choice."
msgstr "Tem certeza que quer pular esse quiz? Por favor, confirme sua escolha."

#: woocommerce/wishlist-view.php:137
msgid "Arrange"
msgstr "Organizar"

#: inc/acf/fields/carousel-control-field.php:64
msgid "Arrows ?"
msgstr "Flechas ?"

#: learnpress/checkout/guest-checkout.php:28
msgid "As Guest"
msgstr "Como Convidado"

#: inc/classes/class-tgm-plugin-activation.php:2603
msgctxt "as in: \"version nr unknown\""
msgid "unknown"
msgstr "desconhecido"

#: tutor/dashboard/earning/statement.php:48
#: tutor/dashboard/earning/statements.php:186
msgid "As per"
msgstr "Conforme"

#: inc/acf/fields/query-control-field.php:199
#: tutor/dashboard/announcements.php:125 tutor/dashboard/assignments.php:59
msgid "ASC"
msgstr "ASC"

#: tutor/single/assignment/content.php:293
msgid "Assignment answer form"
msgstr "Formulário de resposta de atribuição"

#: tutor/single/assignment/content.php:320
msgctxt "Assignment attachment"
msgid " file)"
msgstr ""

#: tutor/single/assignment/content.php:319
msgctxt "Assignment attachment"
msgid "Attach assignment files (Max: "
msgstr ""

#: tutor/dashboard/assignments/review.php:69
msgid "Assignment Description:"
msgstr "Descrição Da Tarefa:"

#: tutor/dashboard/assignments/review.php:117
msgid "Assignment evaluated"
msgstr "Atribuição avaliada"

#: tutor/dashboard/assignments.php:78
msgid "Assignment Name"
msgstr "Nome Da Atribuição"

#: tutor/single/assignment/content.php:290
msgid "Assignment Submission"
msgstr "Envio De Tarefa"

#: inc/tutor/class-geeks-tutor.php:135
msgid "Assignments"
msgstr "Atribuições"

#: tutor/dashboard/assignments/review.php:154
msgid "Assignments submission not found or not completed"
msgstr "Envio de tarefas não encontrado ou não concluído"

#: tutor/dashboard/assignments/review.php:78
msgid "Attach assignment file(s)"
msgstr "Anexar arquivo(s)de atribuição"

#: tutor/global/attachments.php:24 tutor/single/assignment/content.php:244
msgid "Attachments"
msgstr "Anexos"

#: tutor/single/quiz/parts/meta.php:33
msgid "Attempts Allowed"
msgstr "Tentativas Permitidas"

#: tutor/single/course/course-target-audience.php:26
msgid "Audience"
msgstr "Audiência"

#: inc/acf/fields/query-control-field.php:210
#: inc/acf/fields/query-control-field.php:243
msgid "Author"
msgstr "Autor"

#: inc/template-functions/single-post.php:310
msgid "author"
msgstr "autor"

#: inc/acf/fields/carousel-control-field.php:76
msgid "Autoheight ?"
msgstr "Autoheight ?"

#: inc/acf/fields/carousel-control-field.php:52
msgid "Autoplay ?"
msgstr "Autoplay ?"

#: inc/acf/fields/carousel-control-field.php:70
msgid "Autowidth ?"
msgstr "Autowidth ?"

#: woocommerce/cart/cart.php:90
msgid "Available on backorder"
msgstr "Disponível em backorder"

#: inc/classes/class-tgm-plugin-activation.php:2631
msgid "Available version:"
msgstr "Versão disponível:"

#: learnpress/profile/tabs/settings/avatar.php:22
msgid "Avatar"
msgstr "Avatar"

#: woocommerce/single-product-reviews.php:88
msgid "Average"
msgstr "Média"

#: inc/woocommerce/template-functions/single-product.php:562
msgid "Awaiting product image"
msgstr "Aguardando imagem do produto"

#: tutor/dashboard/assignments/review.php:38
#: tutor/dashboard/assignments/submitted.php:29
#: tutor/single/quiz/parts/question.php:136
msgid "Back"
msgstr "Voltar"

#: tutor/dashboard/create-course.php:79
msgid "Back to Course"
msgstr "Voltar ao curso"

#: 404.php:15 inc/customizer/class-geeks-customizer.php:802
msgid "Back to Safety"
msgstr "Back to Safety"

#: tutor/dashboard/earning/report-date_range.php:44
#: tutor/dashboard/earning/report-last_month.php:43
#: tutor/dashboard/earning/report-last_week.php:48
#: tutor/dashboard/earning/report-last_year.php:44
#: tutor/dashboard/earning/report-this_month.php:45
#: tutor/dashboard/earning/report-this_week.php:46
#: tutor/dashboard/earning/report-this_year.php:42
#: tutor/dashboard/earning.php:76
msgid "Based on course price"
msgstr "Com base no preço do curso"

#: learnpress/profile/tabs/settings/basic-information.php:24
msgid "Basic Information"
msgstr "Informações Básicas"

#: inc/tutor/geeks-tutor-functions.php:74
msgid "Become an instructor"
msgstr "Torne-se um instrutor"

#: inc/classes/class-tgm-plugin-activation.php:387
msgid "Begin activating plugin"
msgid_plural "Begin activating plugins"
msgstr[0] "Começar a ativar o plugin"
msgstr[1] "Começar a ativar plugins"

#. translators: 1: plugin name(s).
#: inc/classes/class-tgm-plugin-activation.php:377
msgid "Begin installing plugin"
msgid_plural "Begin installing plugins"
msgstr[0] "Comece a instalar o plugin"
msgstr[1] "Comece a instalar plugins"

#: inc/classes/class-tgm-plugin-activation.php:382
msgid "Begin updating plugin"
msgid_plural "Begin updating plugins"
msgstr[0] "Begin updating plugin"
msgstr[1] "Comece a atualizar plugins"

#: tutor/course-filter/filters.php:14
msgid "Beginner"
msgstr "Iniciante"

#: woocommerce/checkout/form-billing.php:25
msgid "Billing &amp; Shipping"
msgstr "Faturamento E Envio"

#: woocommerce/myaccount/form-edit-address.php:20
#: woocommerce/myaccount/my-address.php:26
#: woocommerce/myaccount/my-address.php:35
#: woocommerce/order/order-details-customer.php:25
msgid "Billing address"
msgstr "Endereço de cobrança"

#: woocommerce/checkout/form-billing.php:29
msgid "Billing details"
msgstr "Detalhes de cobrança"

#: tutor/dashboard/my-profile.php:82 tutor/dashboard/settings/profile.php:153
msgid "Bio"
msgstr "Bio"

#: tutor/profile/bio.php:30
msgid "Bio data is empty"
msgstr "Bio data está vazio"

#: learnpress/profile/tabs/settings/basic-information.php:63
msgid "Biographical Info"
msgstr "Informações Biográficas"

#: inc/acf/fields/fields.php:375
msgid "Block"
msgstr "Bloco"

#. translators: title of section in Customizer
#: inc/customizer/class-geeks-customizer.php:1042
#: inc/template-functions/posts.php:14
msgid "Blog"
msgstr "Blog"

#: inc/class-geeks.php:226
msgid "Blog Categories"
msgstr "Categorias Do Blog"

#. translators: label field of control in Customizer
#: inc/customizer/class-geeks-customizer.php:1097
msgid "Blog Layout"
msgstr "Layout Do Blog"

#: inc/class-geeks.php:390
msgid "Blog Sidebar"
msgstr "Blog Sidebar"

#: archive-course_path.php:8
msgid "Browse Paths"
msgstr "Procurar Caminhos"

#: woocommerce/myaccount/downloads.php:40 woocommerce/myaccount/orders.php:150
msgid "Browse products"
msgstr "Procurar produtos"

#: inc/customizer/class-geeks-customizer.php:845
msgctxt "button"
msgid "Danger"
msgstr "Perigo"

#: inc/customizer/class-geeks-customizer.php:848
msgctxt "button"
msgid "Dark"
msgstr "Escuro"

#: inc/customizer/class-geeks-customizer.php:849
msgctxt "button"
msgid "Gradient"
msgstr "Gradiente"

#: inc/customizer/class-geeks-customizer.php:847
msgctxt "button"
msgid "Info"
msgstr "Info"

#: inc/customizer/class-geeks-customizer.php:850
msgctxt "button"
msgid "Link"
msgstr "Link"

#: inc/customizer/class-geeks-customizer.php:843
msgctxt "button"
msgid "Primary"
msgstr "Primário"

#: inc/customizer/class-geeks-customizer.php:844
msgctxt "button"
msgid "Success"
msgstr "Sucesso"

#: inc/customizer/class-geeks-customizer.php:846
msgctxt "button"
msgid "Warning"
msgstr "Aviso"

#: inc/customizer/class-geeks-customizer.php:640
msgid "Button Background"
msgstr "Fundo Do Botão"

#: inc/customizer/class-geeks-customizer.php:841
msgid "Button Color"
msgstr "Cor Do Botão"

#: inc/customizer/class-geeks-customizer.php:581
msgid "Button CSS Class"
msgstr "Button CSS Class"

#: inc/customizer/class-geeks-customizer.php:473
msgid "Button Link"
msgstr "Botão Link"

#: inc/customizer/class-geeks-customizer.php:614
msgid "Button Primary Color"
msgstr "Cor Primária Do Botão"

#: inc/customizer/class-geeks-customizer.php:541
msgid "Button Size"
msgstr "Tamanho Do Botão"

#: inc/customizer/class-geeks-customizer.php:507
#: inc/customizer/class-geeks-customizer.php:813
msgid "Button Text"
msgstr "Texto Do Botão"

#: inc/customizer/class-geeks-customizer.php:869
msgid "Button Url"
msgstr "URL Do Botão"

#: inc/customizer/class-geeks-customizer.php:436
msgid "Button Variant"
msgstr "Variante Botão"

#: inc/customizer/class-geeks-customizer.php:496
#: inc/template-functions/header.php:633
#: learnpress/single-course/buttons/purchase.php:33
msgid "Buy Now"
msgstr "Compre Agora"

#. translators: %s - Author name
#: woocommerce/content-widget-reviews.php:38
msgid "by %s"
msgstr "por %s"

#: learnpress/checkout/term-conditions.php:32
msgid "By completing your purchase you agree to those %s."
msgstr "Ao concluir sua compra, você concorda com esses %s."

#: tutor/dashboard/instructor/registration.php:167
#: tutor/dashboard/registration.php:169
msgid "By signing up, I agree with the website's"
msgstr "Ao me inscrever, concordo com o site"

#: woocommerce/cart/shipping-calculator.php:24
msgid "Calculate shipping"
msgstr "Calcular frete"

#: learnpress/addons/course-review/review-form.php:70
#: tutor/dashboard/announcements/create.php:68
#: tutor/dashboard/announcements/details.php:45
#: tutor/dashboard/announcements/update.php:64
#: tutor/dashboard/my-courses.php:199
#: tutor/dashboard/reviews/given-reviews.php:125
#: tutor/dashboard/withdraw.php:176
msgid "Cancel"
msgstr "Cancelar"

#: tutor/dashboard/purchase_history.php:154
msgid "Cancelled"
msgstr "Cancelado"

#: learnpress/addons/stripe-payment/form.php:54
msgid "Card Code %s"
msgstr "Código do cartão %s"

#: learnpress/addons/stripe-payment/form.php:23
msgid "Card Number %s"
msgstr "Número do cartão %s"

#: inc/acf/fields/carousel-control-field.php:182
msgid "Carousel"
msgstr "Carrossel"

#: inc/acf/fields/carousel-control-field.php:23
msgid "Carousel Control Options"
msgstr "Opções De Controle De Carrossel"

#: woocommerce/cart/cart.php:32
msgid "Cart Summary"
msgstr "Resumo Do Carrinho"

#: woocommerce/cart/cart-totals.php:25
msgid "Cart totals"
msgstr "Totais do carrinho"

#: job_manager/job-filters.php:61 tutor/course-filter/filters.php:77
msgid "Category"
msgstr "Categoria"

#: woocommerce/single-product/meta.php:45
msgid "Category: "
msgid_plural "Categories: "
msgstr[0] ""
msgstr[1] ""

#: woocommerce/cart/cart-shipping.php:54
msgid "Change address"
msgstr "Alterar endereço"

#: inc/customizer/class-geeks-customizer.php:81
msgid "Change logo"
msgstr "Alterar logo"

#: learnpress/profile/tabs/settings/change-password.php:22
msgid "Change Password"
msgstr "Alterar Senha"

#: tutor/single/quiz/parts/open-ended.php:11
#: tutor/single/quiz/parts/short-answer.php:11
msgid "characters remaining"
msgstr "personagens restantes"

#: woocommerce/cart/mini-cart.php:129
msgid "Checkout"
msgstr "Checkout"

#: learnpress/checkout/guest-checkout-link.php:20
msgctxt "checkout guest link"
msgid "Guest"
msgstr "Convidado"

#: learnpress/checkout/guest-checkout.php:47
msgctxt "checkout sign in link"
msgid "Sign in"
msgstr "Entrar"

#: learnpress/checkout/account-login.php:56
#: learnpress/checkout/guest-checkout.php:55
msgctxt "checkout sign up link"
msgid "Sign up"
msgstr "Cadastre-se"

#: inc/acf/settings/blog-tax.php:44
msgid ""
"Choose a context class for this term. It can reflects in the color of the "
"link generated."
msgstr ""
"Escolha uma classe de contexto para este termo. Pode refletir na cor do link "
"gerado."

#: inc/acf/settings/blog-tax.php:18
msgid ""
"Choose a layout for the archive page of this term. Enable sidebar from "
"Appearance > Customize > Blog > General"
msgstr ""

#: inc/acf/settings/single-post.php:18
msgid ""
"Choose a main category to be displayed in posts loop. If nothing is chosen, "
"all selected categories will be displayed."
msgstr ""
"Escolha uma categoria principal para ser exibida no loop posts. Se nada for "
"escolhido, todas as categorias selecionadas serão exibidas."

#: tutor/single/assignment/content.php:330
msgid "Choose file"
msgstr "Escolher arquivo"

#: inc/customizer/class-geeks-customizer.php:86
msgid "Choose logo"
msgstr "Escolha logo"

#: inc/customizer/class-geeks-customizer.php:1098
msgid "Choose the layout of your blog."
msgstr "Escolha o layout do seu blog."

#: inc/acf/fields/query-control-field.php:128
msgid "Choose the post types you want to query"
msgstr "Escolha os tipos de postagem que deseja consultar"

#: woocommerce/cart/shipping-calculator.php:76
msgid "City"
msgstr "Cidade"

#: tutor/course-filter/filters.php:40
#: woocommerce/single-product/add-to-cart/variable.php:54
msgid "Clear"
msgstr "Claro"

#: tutor/dashboard/dashboard.php:126
msgid "Click Here"
msgstr "Clique Aqui"

#: woocommerce/checkout/form-coupon.php:26
msgid "Click here to enter your code"
msgstr "Clique aqui para inserir seu código"

#: woocommerce/checkout/form-login.php:26
msgid "Click here to login"
msgstr "Clique aqui para fazer login"

#: tutor/dashboard.php:70
msgid "Close"
msgstr "Fechar"

#: tutor/dashboard/my-courses.php:113
msgid "Co-author"
msgstr "Co-autor"

#: comments.php:92
msgid "Comment"
msgstr "Comentário"

#: comments.php:57
msgid "Comment navigation"
msgstr "Navegação de comentários"

#: comments.php:56
msgid "Comment Navigation Below"
msgstr "Navegação De Comentários Abaixo"

#: comments.php:68
msgid "Comments are closed."
msgstr "Os comentários estão encerrados."

#. translators: 1: number of comments, 2: post title
#: comments.php:31
msgctxt "comments title"
msgid "%1$s Comment"
msgid_plural "%1$s Comments"
msgstr[0] "%1$s comentário"
msgstr[1] "%1$s Comentários"

#: tutor/dashboard/earning/statement.php:52
#: tutor/dashboard/earning/statements.php:149
msgid "Commission"
msgstr "Comissão"

#: job_manager/job-submit.php:56
msgid "Company Details"
msgstr "Detalhes Da Empresa"

#: learnpress/content-lesson/button-complete.php:56
#: tutor/single/lesson/complete_form.php:30
msgid "Complete"
msgstr "Completo"

#: templates/courses/single-course-enrolled.php:158
#: tutor/single/course/complete_form.php:31
msgid "Complete Course"
msgstr "Completar Curso"

#: learnpress/content-lesson/button-complete.php:43
msgid "Complete lesson"
msgstr "Aula completa"

#: edd_templates/history-purchases.php:40
msgid "Complete Purchase"
msgstr "Compra Completa"

#: tutor/dashboard/dashboard.php:37
#: tutor/dashboard/notifications/profile-completion.php:21
msgid "Complete Your Profile"
msgstr "Complete Seu Perfil"

#: tutor/dashboard/notifications/profile-completion.php:23
msgid "Complete your profile so people can know more about you! Go to Profile"
msgstr ""
"Complete seu perfil para que as pessoas possam saber mais sobre você! Ir "
"para o perfil"

#: learnpress/content-lesson/button-complete.php:37
#: tutor/dashboard/purchase_history.php:138
msgid "Completed"
msgstr "Concluído"

#: learnpress/profile/tabs/courses/general-statistic.php:46
#: tutor/dashboard/dashboard.php:188 tutor/dashboard/enrolled-courses.php:24
msgid "Completed Courses"
msgstr "Cursos Concluídos"

#: learnpress/profile/tabs/settings/change-password.php:47
#: woocommerce/myaccount/form-edit-account.php:66
msgid "Confirm new password"
msgstr "Confirmar nova senha"

#: learnpress/checkout/account-register.php:39
#: learnpress/global/form-register.php:42
#: tutor/template-part/form-retrieve-password.php:37
msgid "Confirm Password"
msgstr "Confirmar Senha"

#: 404.php:16
msgid "Contact us"
msgstr "Entre em contato conosco"

#: learnpress/addons/course-review/review-form.php:51
msgid "Content"
msgstr "Conteúdo"

#: inc/acf/settings/blog-tax.php:41
msgid "Context Class"
msgstr "Classe Context"

#: learnpress/single-course/buttons/continue.php:19
msgid "Continue"
msgstr "Continuar"

#: templates/courses/single-course-enrolled.php:134
msgid "Continue Learning"
msgstr "Continue Aprendendo"

#: tutor/single/assignment/content.php:672
msgid "Continue Lesson"
msgstr "Continuar Lição"

#: job_manager/job-dashboard.php:78
msgid "Continue Submission"
msgstr "Continuar Submissão"

#. translators: label field for setting in Customizer
#: inc/customizer/class-geeks-customizer.php:1015
msgid "Copyright"
msgstr "Copyright"

#: woocommerce/cart/cart.php:167 woocommerce/checkout/form-coupon.php:34
msgid "Coupon code"
msgstr "Código do cupom"

#: inc/template-functions/lms.php:24 inc/template-functions/lms.php:34
#: learnpress/order/order-details.php:28
#: tutor/dashboard/announcements/details.php:35
#: tutor/dashboard/assignments/review.php:49
#: tutor/dashboard/assignments/submitted.php:35
#: tutor/dashboard/earning/statement.php:13 tutor/public-profile.php:142
#: tutor/single/quiz/top.php:56 tutor/student-public-profile.php:134
msgid "Course"
msgstr "Curso"

#: inc/tutor/geeks-tutor-template-functions.php:156
msgid "Course Announcements"
msgstr "Comunicados Do Curso"

#: tutor/dashboard/create-course.php:189
msgid "Course Builder is where you create & organize a course."
msgstr "Course Builder é onde você cria e organiza um curso."

#: tutor/public-profile.php:189 tutor/student-public-profile.php:181
msgid "Course Completed"
msgstr "Curso Concluído"

#: templates/create-course/general-info-content.php:39
#: templates/single-course/tabs/description.php:2
msgid "Course Description"
msgstr "Descrição Do Curso"

#: inc/tutor/geeks-tutor-template-functions.php:122
msgid "Course description"
msgstr "Descrição do curso"

#: learnpress/checkout/order-received.php:85
msgid "Course does not exist"
msgstr "Curso não existe"

#: tutor/public-profile.php:179 tutor/student-public-profile.php:171
msgid "Course Enrolled"
msgstr "Curso Matriculado"

#: templates/create-course/general-info-content.php:12
#: tutor/dashboard/create-course.php:108
#: tutor/dashboard/earning/statements.php:147
msgid "Course Info"
msgstr "Informações Do Curso"

#: tutor/dashboard/dashboard.php:234 tutor/dashboard/purchase_history.php:103
msgid "Course Name"
msgstr "Nome Do Curso"

#: inc/learnpress/class-geeks-learnpress.php:129
msgid "Course Overview"
msgstr "Visão Geral Do Curso"

#: templates/create-course/general-info-content.php:65
msgid "Course Price"
msgstr "Preço Do Curso"

#: learnpress/single-course/sidebar/user-progress.php:41
msgid "Course progress:"
msgstr "Progresso do curso:"

#: inc/tutor/geeks-tutor-template-functions.php:146
msgid "Course Question & Answer"
msgstr "Pergunta E Resposta Do Curso"

#: templates/courses/single-course-enrolled.php:96
#: tutor/single/course/enrolled/completing-progress.php:21
msgid "Course Status"
msgstr "Status Do Curso"

#: templates/create-course/general-info-content.php:90
msgid "Course Thumbnail"
msgstr "Miniatura Do Curso"

#: templates/create-course/general-info-content.php:18
msgid "Course Title"
msgstr "Título Do Curso"

#: tutor/course-filter/course-archive-filter-bar.php:32
msgid "Course Title (a-z)"
msgstr "Título do curso (a-z)"

#: tutor/course-filter/course-archive-filter-bar.php:35
msgid "Course Title (z-a)"
msgstr "Título do curso (z-a)"

#: tutor/dashboard/create-course.php:181
msgid "Course Upload Tips"
msgstr "Dicas De Upload Do Curso"

#: learnpress/single-course/sidebar/user-time.php:31
msgid "Course will end:"
msgstr "Curso vai acabar:"

#: learnpress/single-course/content-item/nav.php:40
msgctxt "course-item-navigation"
msgid "Next"
msgstr "Proximo"

#: learnpress/single-course/content-item/nav.php:31
msgctxt "course-item-navigation"
msgid "Prev"
msgstr "Prev"

#: tutor/dashboard/assignments.php:101
msgid "Course: "
msgstr ""

#: inc/gke/geeks-gke-template-functions.php:80
#: inc/gke/geeks-gke-template-functions.php:111
#: inc/learnpress/functions/instructor.php:111
#: inc/learnpress/functions/instructor.php:118
#: inc/template-functions/lms.php:24 inc/template-functions/lms.php:34
#: inc/tutor/functions/single-course.php:72
#: inc/tutor/geeks-tutor-functions.php:49
#: inc/tutor/geeks-tutor-template-functions.php:578
#: tutor/archive-course-init.php:142 tutor/dashboard/announcements.php:104
#: tutor/dashboard/assignments.php:39 tutor/public-profile.php:142
#: tutor/student-public-profile.php:134
msgid "Courses"
msgstr "Cursos"

#: templates/create-course/general-info-content.php:23
msgid "Courses category"
msgstr "Categoria Cursos"

#: tutor/public-profile.php:189 tutor/student-public-profile.php:181
msgid "Courses Completed"
msgstr "Cursos Concluídos"

#: tutor/public-profile.php:179 tutor/student-public-profile.php:171
msgid "Courses Enrolled"
msgstr "Cursos Matriculados"

#: inc/learnpress/class-geeks-learnpress-customizer.php:78
msgid "Courses Single v1"
msgstr "Cursos Single v1"

#: inc/learnpress/class-geeks-learnpress-customizer.php:79
msgid "Courses Single v2"
msgstr "Cursos Single v2"

#: templates/create-course/general-info-content.php:31
msgid "Courses tag"
msgstr "Cursos tag"

#: job_manager/job-submit.php:24
msgid "Create A New Job"
msgstr "Criar Um Novo Emprego"

#: woocommerce/checkout/form-billing.php:57
msgid "Create an account?"
msgstr "Criar uma conta?"

#: tutor/dashboard/announcements.php:78
msgid "Create Announcement"
msgstr "Criar Anúncio"

#: inc/tutor/class-geeks-tutor.php:110
msgid "Create Course"
msgstr "Criar Curso"

#: tutor/dashboard/assignments.php:64
msgid "Create Date"
msgstr "Data De Criação"

#: tutor/dashboard/announcements/create.php:19
msgid "Create New Announcement"
msgstr "Criar Novo Anúncio"

#: learnpress/profile/tabs/courses.php:58
msgid "Created"
msgstr "Criado"

#: tutor/dashboard/withdraw.php:74
msgid "Current Balance"
msgstr "Saldo Atual"

#: tutor/dashboard/settings/reset-password.php:28
msgid "Current Password"
msgstr "Senha Atual"

#: learnpress/profile/tabs/settings/change-password.php:35
msgid "Current password"
msgstr "Senha atual"

#: woocommerce/myaccount/form-edit-account.php:56
msgid "Current password (leave blank to leave unchanged)"
msgstr "Senha atual (deixe em branco para deixar inalterada)"

#: inc/tutor/geeks-tutor-template-functions.php:110
msgid "Curriculum"
msgstr "Currículo"

#: learnpress/single-course/tabs/curriculum.php:57
msgid "Curriculum is empty"
msgstr "Currículo está vazio"

#: inc/customizer/class-geeks-customizer.php:618
msgid "Custom"
msgstr "Personalizado"

#: inc/acf/settings/footer.php:15
msgid "Custom Footer"
msgstr "Rodapé Personalizado"

#: inc/customizer/class-geeks-customizer.php:885
msgid "Customize the theme footer."
msgstr "Personalize o rodapé do tema."

#: inc/customizer/class-geeks-customizer.php:112
msgid "Customize the theme header."
msgstr "Personalize o cabeçalho do tema."

#: inc/tutor/class-geeks-tutor.php:85
#: inc/tutor/geeks-tutor-template-functions.php:427
#: inc/tutor/geeks-tutor-template-functions.php:715
#: inc/woocommerce/integrations.php:13 inc/woocommerce/integrations.php:14
#: woocommerce/checkout/thankyou.php:54
msgid "Dashboard"
msgstr "Dashboard"

#: edd_templates/history-purchases.php:21
#: edd_templates/shortcode-receipt.php:59
#: inc/acf/fields/query-control-field.php:186
#: learnpress/checkout/order-received.php:108
#: learnpress/profile/tabs/orders/list.php:36
#: tutor/dashboard/announcements.php:131
#: tutor/dashboard/assignments/submitted.php:76
#: tutor/dashboard/purchase_history.php:108
#: tutor/single/assignment/content.php:457
msgid "Date"
msgstr "Data"

#: tutor/dashboard/earning/statement.php:37
#: tutor/dashboard/earning/statements.php:162
#: woocommerce/checkout/order-receipt.php:29
#: woocommerce/checkout/thankyou.php:66
msgid "Date:"
msgstr "Data:"

#: tutor/single/quiz/top.php:83
msgid "Day"
msgstr "Dia"

#: tutor/single/quiz/top.php:83
msgid "Days"
msgstr "Dias"

#: tutor/single/assignment/content.php:185
#: tutor/single/assignment/content.php:191
msgid "Deadline : "
msgstr ""

#: tutor/dashboard/earning/statement.php:15
#: tutor/dashboard/earning/statements.php:150
msgid "Deduct"
msgstr "Deduzir"

#: tutor/dashboard/earning/statement.php:56
msgid "Deducted"
msgstr "Deduzido"

#: tutor/dashboard/earning/report-date_range.php:55
#: tutor/dashboard/earning/report-last_month.php:54
#: tutor/dashboard/earning/report-last_week.php:59
#: tutor/dashboard/earning/report-last_year.php:55
#: tutor/dashboard/earning/report-this_month.php:56
#: tutor/dashboard/earning/report-this_week.php:57
#: tutor/dashboard/earning/report-this_year.php:53
#: tutor/dashboard/earning.php:96
msgid "Deducted Commissions"
msgstr "Comissões Deduzidas"

#: tutor/dashboard/earning/report-date_range.php:64
#: tutor/dashboard/earning/report-date_range.php:66
#: tutor/dashboard/earning/report-last_month.php:63
#: tutor/dashboard/earning/report-last_month.php:65
#: tutor/dashboard/earning/report-last_week.php:68
#: tutor/dashboard/earning/report-last_week.php:70
#: tutor/dashboard/earning/report-last_year.php:64
#: tutor/dashboard/earning/report-last_year.php:66
#: tutor/dashboard/earning/report-this_month.php:65
#: tutor/dashboard/earning/report-this_month.php:67
#: tutor/dashboard/earning/report-this_week.php:66
#: tutor/dashboard/earning/report-this_week.php:68
#: tutor/dashboard/earning/report-this_year.php:62
#: tutor/dashboard/earning/report-this_year.php:64
msgid "Deducted Fees"
msgstr "Taxas Deduzidas"

#: tutor/dashboard/earning.php:106
msgid "Deducted Fees."
msgstr "Taxas Deduzidas."

#: inc/customizer/class-geeks-customizer.php:83
#: inc/customizer/class-geeks-customizer.php:439
#: inc/customizer/class-geeks-customizer.php:544
#: inc/customizer/class-geeks-customizer.php:617
msgid "Default"
msgstr "Inadimplência"

#: job_manager/job-dashboard.php:82
#: tutor/dashboard/announcements/details.php:48
#: tutor/dashboard/reviews/given-reviews.php:88
#: tutor/dashboard/settings/profile.php:94
msgid "Delete"
msgstr "Apagar"

#: tutor/dashboard/my-courses.php:191
msgid "Delete This Course?"
msgstr "Excluir Este Curso?"

#: inc/acf/fields/query-control-field.php:200
#: tutor/dashboard/announcements.php:126 tutor/dashboard/assignments.php:60
msgid "DESC"
msgstr "DESC"

#: tutor/single/lesson/sidebar_question_and_answer.php:48
msgid "Describe what you're trying to achieve and where you're getting stuck"
msgstr "Descreva o que você está tentando alcançar e onde está ficando preso"

#: inc/customizer/class-geeks-customizer.php:785
#: inc/tutor/geeks-tutor-template-functions.php:119
#: tutor/single/assignment/content.php:233
#: tutor/single/assignment/content.php:398
#: tutor/single/assignment/content.php:640
#: woocommerce/single-product/tabs/description.php:22
msgid "Description"
msgstr "Descrição"

#: edd_templates/history-purchases.php:23
#: tutor/dashboard/assignments/submitted.php:98
#: tutor/dashboard/assignments.php:108
msgid "Details"
msgstr "Detalhes"

#: inc/acf/fields/carousel-control-field.php:101
msgid "Disable"
msgstr "Desativar"

#: edd_templates/shortcode-receipt.php:83
msgid "Discount(s)"
msgstr "Desconto(s)"

#: inc/classes/class-tgm-plugin-activation.php:402
msgid "Dismiss this notice"
msgstr "Indeferir este edital"

#: learnpress/profile/tabs/settings/basic-information.php:50
#: woocommerce/myaccount/form-edit-account.php:40
msgid "Display name"
msgstr "Nome de exibição"

#: tutor/dashboard/settings/profile.php:162
msgid "Display name publicly as"
msgstr "Exibir nome publicamente como"

#: learnpress/content-lesson/button-complete.php:22
msgid "Do you want to complete lesson"
msgstr "Quer completar a aula"

#: tutor/dashboard/reviews/given-reviews.php:144
msgid "Do You Want to Delete This Review?"
msgstr "Deseja excluir esta resenha?"

#: learnpress/single-course/buttons/retry.php:22
msgid "Do you want to retake course"
msgstr "Quer retomar curso"

#: tutor/single/quiz/top.php:144
msgid "Do You Want to Skip This Quiz?"
msgstr "Quer pular esse Quiz?"

#: tutor/dashboard/instructor/apply_for_instructor.php:40
msgid "Do you want to start your career as an instructor?"
msgstr "Quer começar sua carreira como instrutor?"

#: inc/wpjm/class-geeks-wpjm-customizer.php:82
msgid "Do you want to use a custom application form for the jobs?"
msgstr ""
"Deseja usar um formulário de inscrição personalizado para os trabalhos?"

#: inc/learnpress/template-functions/profile.php:33
#: inc/tutor/geeks-tutor-template-functions.php:403
#: learnpress/checkout/account-login.php:54
#: woocommerce/global/form-login.php:39 woocommerce/myaccount/form-login.php:39
msgid "Don't have an account?"
msgstr "Não tem uma conta?"

#: inc/acf/fields/carousel-control-field.php:58
msgid "Dots ?"
msgstr "Dots ?"

#: inc/tutor/geeks-tutor-template-functions.php:717
msgid "Downloads"
msgstr "Downloads"

#: tutor/dashboard/my-courses.php:68
msgid "Draft"
msgstr "Rascunho"

#: inc/acf/fields/carousel-control-field.php:82
msgid "Drag ?"
msgstr "Drag ?"

#: tutor/single/quiz/parts/image-matching.php:31
#: tutor/single/quiz/parts/matching.php:57
msgid "Drag your answer"
msgstr "Arraste sua resposta"

#: job_manager/job-dashboard.php:63
msgid "Duplicate"
msgstr "Duplicado"

#: tutor/single/course/course-entry-box.php:42
msgid "Duration"
msgstr "Duração"

#: inc/learnpress/template-functions/single-item.php:13
msgctxt "duration"
msgid "%sd"
msgstr " %s d"

#: inc/learnpress/template-functions/single-item.php:14
msgctxt "duration"
msgid "%sh"
msgstr "%sh"

#: inc/learnpress/template-functions/single-item.php:15
msgctxt "duration"
msgid "%sm"
msgstr "%sm"

#: inc/learnpress/template-functions/single-item.php:16
msgctxt "duration"
msgid "%ss"
msgstr " %s s"

#: learnpress/single-course/sidebar/user-time.php:36
msgid "Duration:"
msgstr "Duração:"

#: tutor/single/assignment/content.php:472
msgid "Earned Marks"
msgstr "Marcas Conquistadas"

#: tutor/dashboard/earning/report.php:33
#: tutor/dashboard/earning/statement.php:14
#: tutor/dashboard/earning/statements.php:31
#: tutor/dashboard/earning/statements.php:148 tutor/dashboard/earning.php:141
msgid "Earning"
msgstr "Ganhando"

#: tutor/dashboard/earning/report-last_month.php:75
#: tutor/dashboard/earning/report-this_month.php:78
msgid "Earning Data for the month of %s"
msgstr "Dados de ganho para o mês de %s"

#: tutor/dashboard/earning/report-last_year.php:77
#: tutor/dashboard/earning/report-this_year.php:75
msgid "Earning Data for the year of %s"
msgstr "Dados de ganhos para o ano de %s"

#: tutor/dashboard/earning.php:40
msgid "Earnings"
msgstr "Ganhos"

#: tutor/dashboard/earning.php:116
msgid "Earnings Chart for this month"
msgstr "Gráfico de ganhos deste mês"

#: inc/template-functions/single-post.php:334 job_manager/job-dashboard.php:55
#: job_manager/job-dashboard.php:73 learnpress/content-lesson/content.php:20
#: tutor/dashboard/announcements/details.php:49
#: tutor/dashboard/reviews/given-reviews.php:84
#: tutor/single/assignment/content.php:590
#: woocommerce/myaccount/my-address.php:66
msgid "Edit"
msgstr "Editar"

#: inc/tutor/geeks-tutor-template-functions.php:719
msgid "Edit Account"
msgstr "Editar Conta"

#: inc/tutor/geeks-tutor-template-functions.php:718
msgid "Edit Address"
msgstr "Editar Endereço"

#: job_manager/job-preview.php:46
msgid "Edit listing"
msgstr "Editar listagem"

#: inc/tutor/class-geeks-tutor.php:68 inc/woocommerce/integrations.php:47
msgid "Edit Profile"
msgstr "Editar Perfil"

#: templates/single-course/tabs/review.php:109
#: tutor/single/course/review-form.php:17
msgid "Edit review"
msgstr "Editar revisão"

#: woocommerce/wishlist-view-header.php:51
msgid "Edit title"
msgstr "Editar título"

#: tutor/dashboard/settings/reset-password.php:18
msgid "Edit your account settings and change your password here."
msgstr "Edite as configurações da sua conta e altere sua senha aqui."

#: learnpress/profile/tabs/settings/avatar.php:23
msgid "Edit your avatar."
msgstr "Edite seu avatar."

#: learnpress/profile/tabs/settings/basic-information.php:25
#: tutor/dashboard/settings/profile.php:105
msgid "Edit your personal information."
msgstr "Edite suas informações pessoais."

#: comments.php:82 inc/woocommerce/template-functions/my-account.php:127
#: learnpress/checkout/account-register.php:28
#: learnpress/global/form-register.php:31
#: tutor/dashboard/instructor/registration.php:111
#: tutor/dashboard/instructor/registration.php:114
#: tutor/dashboard/my-profile.php:65 tutor/dashboard/registration.php:111
#: tutor/dashboard/registration.php:114
#: woocommerce/myaccount/form-login.php:108
msgid "Email"
msgstr "E-mail"

#: inc/woocommerce/template-functions/my-account.php:126
#: learnpress/checkout/account-register.php:27
#: learnpress/global/form-register.php:30
#: learnpress/profile/tabs/settings/basic-information.php:56
#: woocommerce/myaccount/form-edit-account.php:46
#: woocommerce/myaccount/form-login.php:107
msgid "Email address"
msgstr "Endereço de E-mail"

#: inc/template-functions/global.php:186
msgid "Email already registered"
msgstr "Email já cadastrado"

#: learnpress/checkout/account-login.php:30 learnpress/global/form-login.php:29
#: woocommerce/global/form-login.php:61 woocommerce/myaccount/form-login.php:49
msgid "Email or username"
msgstr "E-mail ou nome de usuário"

#: woocommerce/checkout/thankyou.php:72
msgid "Email:"
msgstr "E-mail:"

#: inc/acf/fields/carousel-control-field.php:100
msgid "Enable"
msgstr "Ativar"

#: inc/customizer/class-geeks-customizer.php:1156
msgid "Enable Blog Related Posts"
msgstr "Habilitar Posts Relacionados Ao Blog"

#: inc/customizer/class-geeks-customizer.php:403
msgid "Enable Button?"
msgstr "Botão Ativar?"

#: inc/customizer/class-geeks-customizer.php:235
msgid "Enable Cart?"
msgstr "Habilitar Carrinho?"

#: inc/customizer/class-geeks-customizer.php:1466
msgid "Enable Custom Color?"
msgstr "Habilitar Cor Personalizada?"

#: inc/acf/settings/footer.php:26
msgid "Enable Custom Footer"
msgstr "Habilitar Rodapé Personalizado"

#: inc/customizer/class-geeks-customizer.php:301
msgid "Enable Dark Header?"
msgstr "Habilitar Cabeçalho Escuro?"

#: inc/acf/settings/footer.php:125
msgid "Enable Footer White Background"
msgstr "Ativar Rodapé Fundo Branco"

#: inc/customizer/class-geeks-customizer.php:979
msgid "Enable Footer White Background "
msgstr ""

#: inc/acf/settings/footer.php:93
msgid "Enable Footer Widgets"
msgstr "Ativar Widgets De Rodapé"

#: inc/customizer/class-geeks-customizer.php:944
msgid "Enable Footer Widgets "
msgstr ""

#: inc/customizer/class-geeks-customizer.php:1130
msgid "Enable Full-width Sticky Post ?"
msgstr "Habilitar Post fixo de largura total ?"

#: inc/customizer/class-geeks-customizer.php:1208
msgid "Enable Newsletter Form"
msgstr "Ativar Formulário De Newsletter"

#: inc/customizer/class-geeks-customizer.php:203
msgid "Enable Search?"
msgstr "Ativar Pesquisa?"

#: inc/customizer/class-geeks-customizer.php:336
msgid "Enable Shadow?"
msgstr "Ativar Sombra?"

#: inc/customizer/class-geeks-customizer.php:1071
msgid "Enable Sidebar Widget Area?"
msgstr "Habilitar Área De Widget Da Barra Lateral?"

#: inc/customizer/class-geeks-customizer.php:1181
msgid "Enable Social Share in Single Post"
msgstr "Ativar compartilhamento Social em postagem única"

#: inc/customizer/class-geeks-customizer.php:667
msgid "Enable Sticky Header ?"
msgstr "Ativar Cabeçalho Fixo ?"

#: inc/acf/settings/footer.php:18
msgid "Enable to add custom footer to this page"
msgstr "Ative para adicionar rodapé personalizado a esta página"

#: inc/customizer/class-geeks-customizer.php:369
msgid "Enable Transparent Header?"
msgstr "Habilitar Cabeçalho Transparente?"

#: inc/customizer/class-geeks-customizer.php:268
msgid "Enable User Profile Info?"
msgstr "Habilitar Informações Do Perfil Do Usuário?"

#: tutor/single/course/add-to-cart.php:86
msgid "Enroll Now"
msgstr "Inscreva-Se Agora"

#: learnpress/profile/tabs/courses.php:55
#: templates/courses/single-course-enrolled.php:54
#: tutor/dashboard/dashboard.php:235
msgid "Enrolled"
msgstr "Inscritos"

#: inc/tutor/class-geeks-tutor.php:87
msgid "Enrolled  Courses"
msgstr "Cursos Inscritos"

#: inc/woocommerce/integrations.php:16
#: learnpress/profile/tabs/courses/general-statistic.php:30
#: tutor/dashboard/dashboard.php:172 tutor/dashboard/enrolled-courses.php:22
msgid "Enrolled Courses"
msgstr "Cursos Inscritos"

#: woocommerce/cart/cart-shipping.php:87
msgid "Enter a different address"
msgstr "Digite um endereço diferente"

#: woocommerce/myaccount/form-reset-password.php:26
msgid "Enter a new password below."
msgstr "Digite uma nova senha abaixo."

#: inc/wpforms/integration.php:69
msgid ""
"Enter CSS class names for the form tag. Multiple class names should be "
"separated with spaces."
msgstr ""
"Insira nomes de classe CSS para a tag de formulário. Os nomes de várias "
"classes devem ser separados por espaços."

#: inc/customizer/class-geeks-customizer.php:1288
msgid "Enter Newsletter Form Shortcode"
msgstr "Enter Newsletter Form Shortcode"

#: tutor/template-part/form-retrieve-password.php:28
msgid "Enter Password and Confirm Password to reset your password"
msgstr "Digite a senha e confirme a senha para redefinir sua senha"

#: woocommerce/cart/cart-shipping.php:66
msgid "Enter your address to view shipping options."
msgstr "Digite seu endereço para ver as opções de envio."

#: learnpress/checkout/guest-checkout.php:35
msgid "Enter your email..."
msgstr "Digite seu e-mail..."

#: tutor/dashboard/assignments/submitted.php:94
msgid "Evaluate"
msgstr "Avaliar"

#: tutor/dashboard/assignments/review.php:127
msgid "Evaluate this assignment out of %s"
msgstr "Avalie esta atribuição de %s"

#: tutor/dashboard/assignments/review.php:142
msgid "Evaluate this submission"
msgstr "Avalie esta submissão"

#: tutor/dashboard/assignments/review.php:116
msgid "Evaluation"
msgstr "Avaliação"

#: templates/create-course/general-info-content.php:19
msgid "ex. Learn photoshop CS6 from scratch"
msgstr "ex. Aprenda photoshop CS6 do zero"

#: tutor/course-filter/filters.php:16
msgid "Expert"
msgstr "Especialista"

#: tutor/single/assignment/content.php:186
msgid "Expired"
msgstr "Expirado"

#: woocommerce/myaccount/payment-methods.php:73
msgid "Expires in "
msgstr ""

#: learnpress/addons/stripe-payment/form.php:28
msgid "Expiry Month %s"
msgstr "Mês de vencimento %s"

#: learnpress/addons/stripe-payment/form.php:46
msgid "Expiry Year %s"
msgstr "Ano de validade %s"

#: inc/classes/class-tgm-plugin-activation.php:2416
msgid "External Source"
msgstr "Fonte Externa"

#: inc/customizer/class-geeks-customizer.php:545
msgid "Extra Small"
msgstr "Extra Pequeno"

#: inc/tutor/class-geeks-tutor.php:171 inc/tutor/class-geeks-tutor.php:197
msgid "Facebook"
msgstr "Facebook"

#: learnpress/profile/tabs/courses.php:33
#: tutor/single/assignment/content.php:518
msgid "Failed"
msgstr "Falhou"

#: learnpress/single-course/sidebar/user-progress.php:34
msgid "Failed %1$d, Passed %2$d"
msgstr "Falhou %1$d, passou %2$d"

#: inc/template-functions/posts.php:137
msgid "Featured"
msgstr "Destaque"

#: job_manager/job-dashboard.php:47
msgid "Featured Job"
msgstr "Trabalho Em Destaque"

#: learnpress/single-course/featured-review.php:19
msgid "Featured Review"
msgstr "Resenha Em Destaque"

#: edd_templates/shortcode-receipt.php:66
msgid "Fees"
msgstr "Taxas"

#: tutor/single/assignment/content.php:339
msgid "File Support: "
msgstr ""

#: tutor/course-filter/filters.php:37
#: woocommerce/content-widget-price-filter.php:34
msgid "Filter"
msgstr "Filtro"

#: tutor/single/quiz/body.php:69
msgid "Finish"
msgstr "Acabamento"

#: learnpress/single-course/buttons/finish.php:15
#: learnpress/single-course/buttons/finish.php:16
msgid "Finish course"
msgstr "Concluir curso"

#: learnpress/profile/tabs/courses.php:31
msgid "Finished"
msgstr "Finalizado"

#: tutor/dashboard/instructor/registration.php:87
#: tutor/dashboard/instructor/registration.php:90
#: tutor/dashboard/my-profile.php:41 tutor/dashboard/registration.php:87
#: tutor/dashboard/registration.php:90 tutor/dashboard/settings/profile.php:127
#: tutor/dashboard/settings/profile.php:128
msgid "First Name"
msgstr "Prénom"

#: learnpress/profile/tabs/settings/basic-information.php:38
#: woocommerce/myaccount/form-edit-account.php:28
msgid "First name"
msgstr "Prénom"

#: inc/customizer/class-geeks-customizer.php:884
msgid "Footer"
msgstr "Rodapé"

#. translators: 1: column number
#: inc/class-geeks.php:414
msgid "Footer Column %1$d"
msgstr "Coluna de rodapé %1$d"

#: inc/acf/settings/footer.php:133
msgid "Footer Copyright"
msgstr "Rodapé Copyright"

#. translators: 1: row number, 2: column number
#: inc/class-geeks.php:420
msgid "Footer Row %1$d - Column %2$d"
msgstr "Rodapé linha %1$d-coluna %2$d"

#: inc/acf/settings/footer.php:56 inc/customizer/class-geeks-customizer.php:919
msgid "Footer Speciality"
msgstr "Especialidade Do Rodapé"

#: inc/acf/settings/footer.php:54 inc/customizer/class-geeks-customizer.php:917
msgid "Footer v1"
msgstr "Rodapé v1"

#: inc/acf/settings/footer.php:55 inc/customizer/class-geeks-customizer.php:918
msgid "Footer v2"
msgstr "Rodapé v2"

#: inc/acf/settings/footer.php:34
msgid "Footer Variant"
msgstr "Variante Do Rodapé"

#: inc/customizer/class-geeks-customizer.php:914
msgid "Footer Version"
msgstr "Versão Rodapé"

#: inc/acf/settings/footer.php:101
msgid "Footer White Background"
msgstr "Rodapé Fundo Branco"

#: inc/acf/settings/footer.php:69
msgid "Footer Widgets"
msgstr "Widgets De Rodapé"

#: tutor/dashboard/reviews/given-reviews.php:95 tutor/dashboard/reviews.php:76
msgid "for"
msgstr "para"

#: tutor/template-part/retrieve-password.php:30
msgid "Forgot Password"
msgstr "Esqueci A Senha"

#: tutor/global/login.php:6 tutor/login-form.php:91
msgid "Forgot Password?"
msgstr "Esqueceu A Senha?"

#: learnpress/global/form-lost-password.php:17
#: woocommerce/myaccount/form-lost-password.php:30
msgctxt "forgot-password-heading"
msgid "Forgot Password"
msgstr "Esqueci A Senha"

#: inc/wpforms/integration.php:67
msgid "Form Tag Class"
msgstr "Form Tag Class"

#: templates/courses/curriculum-module.php:27
#: templates/courses/curriculum-module.php:62
#: templates/create-course/general-info-content.php:82
#: tutor/course-filter/filters.php:19
#: tutor/single/course/course-entry-box.php:112
#: tutor/single/course/wc-price-html.php:21
msgid "Free"
msgstr "Livre"

#: tutor/single/course/course-entry-box.php:127
msgid "Free acess this course"
msgstr "Acesso gratuito a este curso"

#: inc/learnpress/class-geeks-learnpress.php:133
msgid "Frequently Asked Questions"
msgstr "Perguntas Frequentes"

#. translators: 1: Orders URL 2: Address URL 3: Account URL.
#: woocommerce/myaccount/dashboard.php:50
msgid ""
"From your account dashboard you can view your <a href=\"%1$s\">recent "
"orders</a>, manage your <a href=\"%2$s\">billing address</a>, and <a "
"href=\"%3$s\">edit your password and account details</a>."
msgstr ""

#. translators: 1: Orders URL 2: Addresses URL 3: Account URL.
#: woocommerce/myaccount/dashboard.php:53
msgid ""
"From your account dashboard you can view your <a href=\"%1$s\">recent "
"orders</a>, manage your <a href=\"%2$s\">shipping and billing addresses</a>, "
"and <a href=\"%3$s\">edit your password and account details</a>."
msgstr ""

#: 404.php:23 inc/customizer/class-geeks-customizer.php:746
msgctxt "front-end"
msgid "404"
msgstr "404"

#. translators: navigation through comments
#: inc/woocommerce/template-functions/single-product.php:462
msgctxt "front-end"
msgid "Comment navigation"
msgstr "Navegação de comentários"

#. translators: comment author e-mail
#: inc/template-functions/single-post.php:63
msgctxt "front-end"
msgid "Email"
msgstr "E-mail"

#: 404.php:45
msgctxt "front-end"
msgid "Error 404"
msgstr "Erro 404"

#: inc/customizer/class-geeks-customizer.php:1251
msgctxt "front-end"
msgid ""
"Join our newsletter and get resources, curated content, and design "
"inspiration delivered straight to your inbox."
msgstr ""
"Participe da nossa newsletter e receba recursos, conteúdo selecionado e "
"inspiração de design diretamente na sua caixa de entrada."

#: inc/woocommerce/template-functions/my-account.php:14
msgctxt "front-end"
msgid "My Account"
msgstr "Minha Conta"

#. translators: comment author name
#: inc/template-functions/single-post.php:51
msgctxt "front-end"
msgid "Name"
msgstr "Nome"

#. translators: label for link to the next comments page
#: inc/woocommerce/template-functions/single-product.php:454
msgctxt "front-end"
msgid "Newer comments"
msgstr "Comentários mais recentes"

#. translators: label for link to the previous comments page
#: inc/woocommerce/template-functions/single-product.php:450
msgctxt "front-end"
msgid "Older comments"
msgstr "Comentários mais antigos"

#: inc/customizer/class-geeks-customizer.php:774
msgctxt "front-end"
msgid ""
"Oops! Sorry, we couldn’t find the page you were looking for. If you think "
"this is a problem with us, please Contact us"
msgstr ""
"Opa! Desculpe, não encontramos a página que você estava procurando. Se você "
"acha que isso é um problema Conosco, Entre Em Contato conosco"

#: inc/woocommerce/template-functions/my-account.php:19
msgctxt "front-end"
msgid "Orders"
msgstr "Pedidos"

#. translators: related to comment form; phrase follows by red mark
#: woocommerce/single-product-reviews.php:121
msgctxt "front-end"
msgid "Required fields are marked"
msgstr "Os campos obrigatórios estão marcados"

#: inc/template-functions/single-post.php:76
msgctxt "front-end"
msgid "Save my name and email in this browser for the next time I comment."
msgstr ""
"Salve meu nome e email neste navegador para a próxima vez que eu comentar."

#: inc/customizer/class-geeks-customizer.php:1223
msgctxt "front-end"
msgid "Sign up for our Newsletter"
msgstr "Assine nossa Newsletter"

#: woocommerce/single-product-reviews.php:108
msgctxt "front-end"
msgid "Submit Review"
msgstr "Enviar Revisão"

#: woocommerce/cart/mini-cart.php:124
msgctxt "front-end"
msgid "View Cart"
msgstr "Ver Carrinho"

#: woocommerce/single-product-reviews.php:109
msgctxt "front-end"
msgid "Write a review"
msgstr "Escreva um comentário"

#: woocommerce/single-product-reviews.php:119
msgctxt "front-end"
msgid "Your email address will not be published."
msgstr "Seu endereço de E-mail não será publicado."

#. Template Name of the plugin/theme
msgid "Full Width"
msgstr "Largura Total"

#: inc/customizer/class-geeks-customizer.php:1100
msgid "Full width"
msgstr "Largura total"

#: inc/acf/fields/carousel-control-field.php:183
msgid "Gallery"
msgstr "Galeria"

#. Theme Name of the plugin/theme
msgid "Geeks"
msgstr "Geeks"

#. Description of the plugin/theme
msgid ""
"Geeks is an online Learning and Teaching Marketplace WordPress Theme built "
"using Tutor LMS. You can build the most advanced online Learning and "
"Teaching Marketplace using WordPress easily."
msgstr ""
"Geeks é um mercado de aprendizagem e ensino online WordPress tema construído "
"usando Tutor LMS. Você pode construir o mercado de ensino e aprendizagem "
"online mais avançado usando o WordPress facilmente."

#: woocommerce/myaccount/view-order.php:27
msgid "Geeks Orders"
msgstr "Geeks Pedidos"

#: archive-course_path.php:9
msgid "Get started by learning from a path below"
msgstr "Comece aprendendo com um caminho abaixo"

#: content-none.php:13
msgid "Get started here"
msgstr "Comece aqui"

#: inc/tutor/class-geeks-tutor.php:166
msgid "Github"
msgstr "Github"

#: tutor/dashboard/reviews/given-reviews.php:44 tutor/dashboard/reviews.php:49
msgid "Given"
msgstr "Dado"

#: woocommerce/checkout/thankyou.php:54
msgid "Go to "
msgstr ""

#. translators: %1$s: dashboard link open, %2$s: dashboard link close
#: tutor/dashboard/logged-in.php:22
msgid "Go to %1$sDashboard%2$s"
msgstr "Ir para %1$sDashboard%2$s"

#: tutor/single/lesson/content.php:125
msgid "Go to Course Home"
msgstr "Go to Course Home"

#: tutor/dashboard/instructor/registration.php:30
#: tutor/dashboard/registration.php:30
msgid "Go to Home"
msgstr "Ir para Home"

#: inc/learnpress/template-functions/checkout.php:46
msgid "Go to my account"
msgstr "Acesse Minha Conta"

#: inc/learnpress/template-functions/checkout.php:49
msgid "Go to the course"
msgstr "Ir para o curso"

#: woocommerce/single-product-reviews.php:87
msgid "Good"
msgstr "Bom"

#: woocommerce/myaccount/orders.php:68
msgctxt "hash before order number"
msgid "#"
msgstr ""

#: woocommerce/checkout/form-coupon.php:26
msgid "Have a coupon?"
msgstr "Tem um cupom?"

#: job_manager/account-signin.php:40
msgid "Have an account?"
msgstr "Tem uma conta?"

#: inc/customizer/class-geeks-customizer.php:111
msgid "Header"
msgstr "Cabeçalho"

#: inc/customizer/class-geeks-customizer.php:144
msgid "Header Speciality"
msgstr "Especialidade De Cabeçalho"

#: inc/customizer/class-geeks-customizer.php:143
msgid "Header v1"
msgstr "Cabeçalho v1"

#: inc/customizer/class-geeks-customizer.php:140
msgid "Header Version"
msgstr "Versão Do Cabeçalho"

#. translators: 1: user display name 2: logout url
#: woocommerce/myaccount/dashboard.php:40
msgid "Hello %1$s (not %1$s? <a href=\"%2$s\">Log out</a>)"
msgstr ""

#: templates/create-course/general-info-content.php:27
msgid ""
"Help people find your courses by choosing categories that represent your "
"course."
msgstr ""
"Ajude as pessoas a encontrar seus cursos escolhendo categorias que "
"representem seu curso."

#: templates/create-course/general-info-content.php:35
msgid ""
"Help people find your courses by choosing tag that represent your course."
msgstr ""
"Ajude as pessoas a encontrarem seus cursos escolhendo tags que representem "
"seu curso."

#: inc/woocommerce/template-functions/product-archive.php:140
msgid "High Price"
msgstr "Preço Alto"

#: inc/woocommerce/template-functions/product-archive.php:138
msgid "Highest Rated"
msgstr "Maior Pontuação"

#: inc/learnpress/geeks-learnpress-functions.php:16
msgid "Highest Rating"
msgstr "Classificação Mais Alta"

#: inc/acf/fields/carousel-control-field.php:207
msgid "Horizontal"
msgstr "Horizontal"

#: tutor/single/quiz/top.php:82
msgid "Hour"
msgstr "Hora"

#: inc/gke/geeks-gke-template-functions.php:84 tutor/single/quiz/top.php:82
msgid "Hours"
msgstr "Horas"

#: inc/woocommerce/template-functions/single-product.php:216
msgid "How customers rated this product"
msgstr "Como os clientes avaliaram este produto"

#: inc/learnpress/class-geeks-learnpress.php:137
#: inc/tutor/geeks-tutor-template-functions.php:185
#: templates/single-course/tabs/review.php:30
msgid "How students rated this course"
msgstr "Como os alunos avaliaram este curso"

#: tutor/dashboard/reviews/given-reviews.php:110
msgid "How would you rate this course?"
msgstr "Como você classificaria esse curso?"

#: tutor/dashboard/index.php:70
msgid "Howdys,"
msgstr "Howdys,"

#. translators: description field for "Copyright" setting in Customizer
#: inc/customizer/class-geeks-customizer.php:1017
msgid "HTML is allowed in this field."
msgstr "HTML é permitido neste campo."

#. Theme URI of the plugin/theme
msgid "https://geeks.madrasthemes.com/"
msgstr ""

#. Author URI of the plugin/theme
msgid "https://themeforest.net/user/madrasthemes/"
msgstr ""

#: inc/class-geeks.php:352
msgid "Huge"
msgstr "Enorme"

#: edd_templates/history-purchases.php:20
msgid "ID"
msgstr "ID"

#. translators: Placeholder %s is the optionally text.
#: job_manager/account-signin.php:47
msgid ""
"If you don't have an account you can %screate one below by entering your "
"email address/username."
msgstr ""
"Se você não tem uma conta você pode %screate um abaixo, digitando o seu "
"endereço de E-mail / nome de usuário."

#: woocommerce/checkout/form-coupon.php:31
msgid "If you have a coupon code, please apply it below."
msgstr "Se você tiver um código de cupom, aplique-o abaixo."

#: learnpress/profile/tabs/orders/recover-order.php:18
msgid "If you have a valid order key you can recover it here."
msgstr "Se você tiver uma chave de pedido válida, poderá recuperá-la aqui."

#: woocommerce/checkout/form-login.php:32
msgid ""
"If you have shopped with us before, please enter your details below. If you "
"are a new customer, please proceed to the Billing section."
msgstr ""
"Se você já comprou conosco antes, insira seus dados abaixo. Se você é um "
"novo cliente, por favor, vá para a seção de faturamento."

#: woocommerce/wishlist-view.php:86
msgid "Image"
msgstr "Imagem"

#: woocommerce/wishlist-view-mobile.php:189 woocommerce/wishlist-view.php:243
msgid "In Stock"
msgstr "Em Estoque"

#: templates/courses/curriculum-module.php:79
msgid "In this module you’ll learn:"
msgstr "Neste módulo você aprenderá:"

#: learnpress/profile/tabs/courses.php:30
msgid "In-Progress"
msgstr "Em Andamento"

#: inc/acf/fields/query-control-field.php:207
msgid "Include By"
msgstr "Incluir Por"

#: learnpress/order/order-details.php:22
msgid "Information about the selected order"
msgstr "Informações sobre o pedido selecionado"

#: tutor/dashboard/create-course.php:192
msgid ""
"Information from the Additional Data section shows up on the course single "
"page."
msgstr ""
"As informações da seção Dados Adicionais são exibidas na página única do "
"curso."

#: inc/classes/class-tgm-plugin-activation.php:2838
msgid "Install"
msgstr "Instalar"

#. translators: %2$s: plugin name in screen reader markup
#: inc/classes/class-tgm-plugin-activation.php:2724
msgid "Install %2$s"
msgstr "Instalar %2$s"

#: inc/admin/class-geeks-plugin-install.php:103
#: inc/classes/class-tgm-plugin-activation.php:335
msgid "Install Plugins"
msgstr "Instalar Plugins"

#: inc/classes/class-tgm-plugin-activation.php:334
msgid "Install Required Plugins"
msgstr "Instalar Plugins Necessários"

#. translators: 1: install status, 2: update status
#: inc/classes/class-tgm-plugin-activation.php:2463
msgctxt "Install/Update Status"
msgid "%1$s, %2$s"
msgstr "%1$s, %2$s"

#: inc/classes/class-tgm-plugin-activation.php:2440
msgid "Installed But Not Activated"
msgstr "Instalado, Mas Não Ativado"

#: inc/classes/class-tgm-plugin-activation.php:2611
msgid "Installed version:"
msgstr "Versão instalada:"

#. translators: 1: plugin name, 2: action number 3: total number of actions.
#: inc/classes/class-tgm-plugin-activation.php:3637
msgid "Installing and Activating Plugin %1$s (%2$d/%3$d)"
msgstr "Instalando e ativando o Plugin %1$s (%2$d/%3$d)"

#. translators: 1: plugin name, 2: action number 3: total number of actions.
#: inc/classes/class-tgm-plugin-activation.php:3645
msgid "Installing Plugin %1$s (%2$d/%3$d)"
msgstr "Instalando o Plugin %1$s (%2$d/%3$d)"

#. translators: %s: plugin name.
#: inc/classes/class-tgm-plugin-activation.php:337
msgid "Installing Plugin: %s"
msgstr "Instalando Plugin: %s"

#: inc/tutor/class-geeks-tutor.php:105 inc/woocommerce/integrations.php:29
#: learnpress/profile/profile-header.php:52
#: tutor/dashboard/question-answer.php:48 tutor/public-profile.php:43
#: tutor/student-public-profile.php:41
msgid "Instructor"
msgstr "Instrutor"

#: tutor/dashboard/instructor/apply_for_instructor.php:33
msgid "Instructor Application"
msgstr "Aplicação Instrutor"

#: tutor/single/assignment/content.php:544
msgid "Instructor Note"
msgstr "Nota Do Instrutor"

#: inc/learnpress/functions/instructor.php:107
#: inc/learnpress/functions/instructor.php:130
#: inc/tutor/geeks-tutor-functions.php:45 templates/instructor/list.php:31
#: templates/single-course/instructor.php:24
msgid "Instructor Rating"
msgstr "Avaliação Do Instrutor"

#: inc/gke/geeks-gke-template-functions.php:119
msgid "Instructors"
msgstr "Instrutores"

#: tutor/course-filter/filters.php:15
msgid "Intermediate"
msgstr "Intermediário"

#: inc/template-functions/global.php:181
msgid "Invalid email"
msgstr "E-mail inválido"

#: learnpress/order/order-details.php:15
msgid "Invalid order"
msgstr "Pedido inválido"

#: learnpress/checkout/order-received.php:29
msgid "Invalid order."
msgstr "Pedido inválido."

#: inc/template-functions/global.php:171
msgid "Invalid username"
msgstr "Nome de usuário inválido"

#: inc/ocdi/class-geeks-ocdi.php:373 inc/ocdi/class-geeks-ocdi.php:471
msgid "It imports the entire demo. It may take upto 5 minutes"
msgstr "Importa toda a demo. Pode levar até 5 minutos"

#: content-none.php:20
msgid ""
"It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps "
"searching can help."
msgstr ""
"Parece que não conseguimos encontrar o que você está procurando. Talvez a "
"busca possa ajudar."

#: learnpress/checkout/order-received.php:74
msgid "Item"
msgstr "Item"

#: edd_templates/checkout_cart.php:11
msgid "Item Name"
msgstr "Nome Do Item"

#: edd_templates/checkout_cart.php:12
msgid "Item Price"
msgstr "Preço Do Item"

#: inc/acf/fields/carousel-control-field.php:116
msgid "Items Default"
msgstr "Itens Padrão"

#: inc/acf/fields/carousel-control-field.php:140
msgid "Items Large Screen"
msgstr "Itens Tela Grande"

#: inc/acf/fields/carousel-control-field.php:134
msgid "Items Medium Screen"
msgstr "Itens Tela Média"

#: inc/acf/fields/carousel-control-field.php:128
msgid "Items Small Screen"
msgstr "Itens Tela Pequena"

#: inc/acf/fields/carousel-control-field.php:146
msgid "Items Xtra Large Screen"
msgstr "Itens Xtra Tela Grande"

#: inc/acf/fields/carousel-control-field.php:122
msgid "Items Xtra Small Screen"
msgstr "Itens Xtra Tela Pequena"

#: inc/wpjm/class-geeks-wpjm-customizer.php:40
msgid "Jobs"
msgstr "Empregos"

#: inc/template-functions/single-post.php:469
msgid ""
"Join our newsletter and get resources, curated content, and design "
"inspiration delivered straight to your inbox."
msgstr ""
"Participe da nossa newsletter e receba recursos, conteúdo selecionado e "
"inspiração de design diretamente na sua caixa de entrada."

#: tutor/dashboard/create-course.php:52
msgid "Just fill the form and create your courses."
msgstr "Basta preencher o formulário e criar seus cursos."

#: job_manager/job-filters.php:42 job_manager/job-filters.php:43
msgid "Keywords"
msgstr "Palavras-chave"

#: woocommerce/myaccount/view-order.php:44
msgid "l jS \\o\\f F Y, h:ia"
msgstr "l jS \\o\\f F Y, h: ia"

#: inc/template-functions/posts.php:62 searchform.php:6
msgctxt "label"
msgid "Search for:"
msgstr "Pesquisar por:"

#: inc/class-geeks.php:347
msgid "Large"
msgstr "Grande"

#: inc/customizer/class-geeks-customizer.php:547
msgid "Large "
msgstr ""

#: tutor/dashboard/earning/earning-report-top-menu.php:15
msgid "Last Month"
msgstr "Mês Passado"

#: tutor/dashboard/instructor/registration.php:95
#: tutor/dashboard/instructor/registration.php:98
#: tutor/dashboard/my-profile.php:49 tutor/dashboard/registration.php:95
#: tutor/dashboard/registration.php:98 tutor/dashboard/settings/profile.php:132
#: tutor/dashboard/settings/profile.php:133
msgid "Last Name"
msgstr "Sobrenome"

#: learnpress/profile/tabs/settings/basic-information.php:44
#: woocommerce/myaccount/form-edit-account.php:32
msgid "Last name"
msgstr "Sobrenome"

#: tutor/single/course/course-entry-box.php:47
msgid "Last Updated"
msgstr "Última Atualização"

#: tutor/dashboard/earning/earning-report-top-menu.php:17
msgid "Last Week"
msgstr "Semana Passada"

#: tutor/dashboard/earning/earning-report-top-menu.php:13
msgid "Last Year"
msgstr "Ano Passado"

#: tutor/dashboard/assignments/submitted.php:66
msgid "Latest"
msgstr "Latest"

#: inc/acf/fields/fields.php:369 inc/acf/settings/blog-tax.php:15
msgid "Layout"
msgstr "Layout"

#: inc/learnpress/class-geeks-learnpress-customizer.php:46
msgid "Learnpress"
msgstr "Learnpress"

#: woocommerce/single-product-reviews.php:110
msgid "Leave a Reply"
msgstr "Deixe uma resposta"

#: inc/customizer/class-geeks-customizer.php:1101
msgid "Left Sidebar"
msgstr "Barra Lateral Esquerda"

#: inc/template-functions/single-post.php:31
msgid "Less than a minute to read"
msgstr "Menos de um minuto para ler"

#: learnpress/content-lesson/content.php:17
msgid "Lesson content is empty."
msgstr "O conteúdo da aula está vazio."

#: learnpress/single-course/sidebar/user-progress.php:25
msgid "Lessons completed:"
msgstr "Aulas concluídas:"

#: tutor/course-filter/filters.php:104
#: tutor/single/course/course-entry-box.php:56
msgid "Level"
msgstr "Nível"

#: learnpress/single-course/sidebar/user-time.php:37
msgid "Lifetime"
msgstr "Lifetime"

#: inc/tutor/class-geeks-tutor.php:199
msgid "Linked In"
msgstr "Linked In"

#: inc/tutor/class-geeks-tutor.php:181
msgid "Linkedin"
msgstr "Linkedin"

#: learnpress/addons/course-review/course-review.php:43
#: templates/single-course/tabs/review.php:123
msgid "Load More"
msgstr "Carregar Mais"

#: learnpress/addons/course-review/course-review.php:38
msgid "Loading..."
msgstr "Carregando..."

#: job_manager/job-filters.php:49 job_manager/job-filters.php:50
msgid "Location"
msgstr "Localização"

#: inc/woocommerce/integrations.php:50
msgid "Log Out"
msgstr "Sair"

#: learnpress/checkout/account-logged-in.php:27
msgid "Log out &raquo;"
msgstr "Sair \""

#: learnpress/checkout/account-logged-in.php:26
msgid "Log out of this account"
msgstr "Sair desta conta"

#: learnpress/checkout/account-logged-in.php:24
msgid "Logged in as %s."
msgstr "Logado como  %s ."

#: learnpress/global/form-login.php:18 woocommerce/global/form-login.php:37
#: woocommerce/myaccount/form-login.php:37
msgctxt "login-heading"
msgid "Sign in"
msgstr "Entrar"

#: inc/customizer/class-geeks-customizer.php:72
msgid "Logo Inverse"
msgstr "Logo Inverso"

#: inc/tutor/class-geeks-tutor.php:75
msgid "Logout"
msgstr "Logout"

#: learnpress/checkout/account-login.php:48
msgid "Lost password?"
msgstr "Senha perdida?"

#: learnpress/global/form-login.php:44 woocommerce/global/form-login.php:77
#: woocommerce/myaccount/form-login.php:65
msgid "Lost your password?"
msgstr "Perdeu sua senha?"

#: learnpress/global/form-lost-password.php:18
#: tutor/template-part/retrieve-password.php:31
#: woocommerce/myaccount/form-lost-password.php:31
msgid ""
"Lost your password? Please enter your username or email address. You will "
"receive a link to create a new password via email."
msgstr ""
"Perdeu sua senha? Digite seu nome de usuário ou endereço de E-mail. Você "
"receberá um link para criar uma nova senha por e-mail."

#: inc/woocommerce/template-functions/product-archive.php:139
msgid "Low Price"
msgstr "Preço Baixo"

#. Author of the plugin/theme
msgid "MadrasThemes"
msgstr "Madrastemas"

#: inc/acf/settings/single-post.php:15
msgid "Main Category"
msgstr "Categoria Principal"

#: inc/customizer/class-geeks-customizer.php:168
msgid "Make header full-width?"
msgstr "Fazer cabeçalho largura total?"

#: tutor/dashboard/my-courses.php:51
msgid "Manage your courses and its updates."
msgstr "Gerencie seus cursos e suas atualizações."

#: inc/acf/fields/query-control-field.php:163
msgid "Manual Selection"
msgstr "Seleção Manual"

#: tutor/single/lesson/complete_form.php:29
msgid "Mark as "
msgstr ""

#: job_manager/job-dashboard.php:60
msgid "Mark filled"
msgstr "Marca preenchida"

#: job_manager/job-dashboard.php:58
msgid "Mark not filled"
msgstr "Marca não preenchida"

#: tutor/single/quiz/parts/question.php:71
msgid "Marks : "
msgstr ""

#: woocommerce/content-widget-price-filter.php:28
msgid "Max price"
msgstr "Preço Max"

#: job_manager/form-fields/file-field.php:62
msgid "Maximum file size: %s."
msgstr "Tamanho máximo do arquivo: %s."

#: tutor/single/assignment/content.php:348
msgid "MB"
msgstr "MB"

#: inc/class-geeks.php:342
msgid "Medium"
msgstr "Médio"

#: inc/tutor/geeks-tutor-template-functions.php:419
#: woocommerce/myaccount/my-account.php:33
msgid "Menu"
msgstr "Menu"

#: inc/acf/fields/query-control-field.php:188
msgid "Menu Order"
msgstr "Ordem Do Menu"

#: inc/ocdi/class-geeks-ocdi.php:266
msgid ""
"Menus, Widgets and Settings will not be imported. Content imported already "
"will not be imported."
msgstr ""
"Menus, Widgets e configurações não serão importados. O conteúdo importado já "
"não será importado."

#: woocommerce/content-widget-price-filter.php:27
msgid "Min price"
msgstr "Preço mínimo"

#: tutor/dashboard/settings/withdraw-settings.php:54
msgid "Min withdraw"
msgstr "Min retirar"

#: tutor/single/assignment/content.php:206
msgid "Minimum Pass Points : "
msgstr ""

#: inc/classes/class-tgm-plugin-activation.php:2619
msgid "Minimum required version:"
msgstr "Versão mínima exigida:"

#: tutor/dashboard/withdraw.php:165
msgid "Minimum withdraw amount is"
msgstr "O valor mínimo de Saque é"

#: tutor/single/quiz/top.php:81
msgid "Minute"
msgstr "Minuto"

#: tutor/single/quiz/top.php:81
msgid "Minutes"
msgstr "Ata"

#: tutor/dashboard/purchase_history.php:56
msgid "Monthly"
msgstr "Mensal"

#: inc/learnpress/geeks-learnpress-functions.php:15
#: inc/woocommerce/template-functions/product-archive.php:137
msgid "Most Popular"
msgstr "Mais Populares"

#: tutor/dashboard/dashboard.php:228
msgid "Most Popular Courses"
msgstr "Cursos Mais Populares"

#: woocommerce/wishlist-view-mobile.php:213 woocommerce/wishlist-view.php:276
msgid "Move"
msgstr "Move"

#: woocommerce/wishlist-view-mobile.php:234 woocommerce/wishlist-view.php:297
msgid "Move to another list &rsaquo;"
msgstr "Mover para outra lista \""

#: woocommerce/myaccount/dashboard.php:32
msgid "My Account"
msgstr "Minha Conta"

#: woocommerce/checkout/thankyou.php:42
msgid "My account"
msgstr "Minha conta"

#: tutor/dashboard/earning.php:60
msgid "My Balance"
msgstr "Meu Saldo"

#: inc/tutor/class-geeks-tutor.php:115 inc/woocommerce/integrations.php:30
#: tutor/dashboard/my-courses.php:50 tutor/public-profile.php:209
#: tutor/student-public-profile.php:201
msgid "My Courses"
msgstr "Meus Cursos"

#: tutor/dashboard/earning/report-date_range.php:37
#: tutor/dashboard/earning/report-last_month.php:36
#: tutor/dashboard/earning/report-last_week.php:41
#: tutor/dashboard/earning/report-last_year.php:37
#: tutor/dashboard/earning/report-this_month.php:38
#: tutor/dashboard/earning/report-this_week.php:39
#: tutor/dashboard/earning/report-this_year.php:35
msgid "My Earning"
msgstr "Meu Ganho"

#: tutor/dashboard/earning.php:69
msgid "My Earnings"
msgstr "Meus Ganhos"

#: learnpress/profile/tabs/orders/list.php:21
msgid "My Orders"
msgstr "Meus Pedidos"

#: inc/tutor/class-geeks-tutor.php:86 inc/woocommerce/integrations.php:15
#: tutor/dashboard/my-profile.php:27
msgid "My Profile"
msgstr "Meu Perfil"

#: inc/tutor/class-geeks-tutor.php:90 inc/woocommerce/integrations.php:19
#: tutor/dashboard/my-quiz-attempts.php:36
msgid "My Quiz Attempts"
msgstr "Minhas Tentativas De Quiz"

#: woocommerce/order/order-details-customer.php:52
#: woocommerce/order/order-details-customer.php:98
#: woocommerce/single-product/meta.php:26
msgid "N/A"
msgstr "N / A"

#: learnpress/content-instructor-grid-view.php:39
msgid "n/a"
msgstr "n / a"

#: tutor/single/assignment/content.php:197
msgid "N\\A"
msgstr "N\\A"

#: comments.php:80 edd_templates/shortcode-receipt.php:126
msgid "Name"
msgstr "Nome"

#: inc/class-geeks.php:225
msgid "Nav Footer"
msgstr "Nav Rodapé"

#: inc/class-geeks.php:223
msgid "Navbar Nav Primary"
msgstr "Navbar Nav Primary"

#: tutor/dashboard/settings/reset-password.php:36
msgid "New Password"
msgstr "Nova Senha"

#: learnpress/profile/tabs/settings/change-password.php:41
#: woocommerce/myaccount/form-reset-password.php:29
msgid "New password"
msgstr "Nova senha"

#: woocommerce/myaccount/form-edit-account.php:61
msgid "New password (leave blank to leave unchanged)"
msgstr "Nova senha (deixe em branco para deixar inalterada)"

#: learnpress/profile/tabs/settings/change-password.php:50
msgid "New password does not match!"
msgstr "Nova senha não corresponde!"

#: woocommerce/myaccount/form-add-payment-method.php:62
msgid ""
"New payment methods can only be added during checkout. Please contact us if "
"you require assistance."
msgstr ""
"Novas formas de pagamento só podem ser adicionadas durante o checkout. Entre "
"em contato conosco se precisar de assistência."

#: comments.php:60
msgid "Newer Comments &rarr;"
msgstr "Newer Comments Loubet"

#: inc/learnpress/geeks-learnpress-functions.php:11
#: inc/tutor/geeks-tutor-functions.php:21
#: inc/woocommerce/template-functions/product-archive.php:136
msgid "Newest"
msgstr "Mais novo"

#: inc/customizer/class-geeks-customizer.php:1262
msgid "Newsletter Form Description"
msgstr "Descrição Do Formulário De Newsletter"

#: inc/customizer/class-geeks-customizer.php:1234
msgid "Newsletter Form Title"
msgstr "Título Do Formulário De Newsletter"

#: inc/woocommerce/template-functions/single-product.php:593
#: tutor/single/next-previous-pagination.php:28
#: woocommerce/myaccount/orders.php:142
msgid "Next"
msgstr "Proximo"

#: inc/geeks-functions.php:151
msgid "Next &raquo;"
msgstr "Próximo \""

#: inc/customizer/class-geeks-customizer.php:1473
#: inc/wpjm/class-geeks-wpjm-customizer.php:86
#: learnpress/global/lp-modal-overlay.php:23
msgid "No"
msgstr "Não"

#. translators: 1: plugin name.
#: inc/classes/class-tgm-plugin-activation.php:397
msgid "No action taken. Plugin %1$s was already active."
msgstr "Nenhuma ação tomada. O Plugin %1$s já estava ativo."

#: tutor/single/course/enrolled/announcements.php:49
msgid "No announcements posted yet."
msgstr "Nenhum anúncio publicado ainda."

#: tutor/global/attachments.php:53
msgid "No Attchment Found"
msgstr "No Attchment Found"

#: tutor/dashboard/announcements/create.php:41
#: tutor/dashboard/announcements/update.php:42
#: tutor/dashboard/announcements.php:117 tutor/dashboard/assignments.php:52
msgid "No course found"
msgstr "Nenhum curso encontrado"

#: tutor/profile/courses_taken.php:119
msgid "No course yet."
msgstr "Ainda não tem curso."

#: inc/templates/class-geeks-template-course.php:32
msgid "No courses available to display."
msgstr "Não há cursos disponíveis para exibição."

#: tutor/course-none.php:12
msgid "No courses available."
msgstr "Não há cursos disponíveis."

#: inc/learnpress/template-functions/global.php:33
#: learnpress/archive-course.php:96
msgid "No courses found."
msgstr "Nenhum curso encontrado."

#: inc/gke/geeks-gke-template-functions.php:289
msgid "No courses in this path."
msgstr "Não há cursos nesse caminho."

#: tutor/single/lesson/sidebar_question_and_answer.php:43
msgid "No Data Available in this Section"
msgstr "Não há dados disponíveis nesta seção"

#: edd_templates/shortcode-receipt.php:201
msgid "No downloadable files found for this bundled item."
msgstr "Nenhum arquivo para download encontrado para este item empacotado."

#: edd_templates/shortcode-receipt.php:210
msgid "No downloadable files found."
msgstr "Nenhum arquivo para download encontrado."

#: woocommerce/myaccount/downloads.php:42
msgid "No downloads available yet."
msgstr "Ainda não há downloads disponíveis."

#: tutor/dashboard/earning/report-date_range.php:28
#: tutor/dashboard/earning/report-last_month.php:27
#: tutor/dashboard/earning/report-last_week.php:32
#: tutor/dashboard/earning/report-last_year.php:27
#: tutor/dashboard/earning/report-this_month.php:28
#: tutor/dashboard/earning/report-this_week.php:29
#: tutor/dashboard/earning/report-this_year.php:27
#: tutor/dashboard/earning.php:28
msgid "No Earning info available"
msgstr "Nenhuma informação de ganhos disponível"

#: inc/learnpress/template-functions/instructor.php:37
msgid "No Instructors found."
msgstr "Nenhum instrutor encontrado."

#: inc/gke/geeks-gke-template-functions.php:401
msgid "No instructors in this path."
msgstr "Nenhum instrutor nesse caminho."

#: learnpress/single-course/loop-section.php:120
msgid "No items in this section"
msgstr "Nenhum item nesta seção"

#: tutor/dashboard/assignments/submitted.php:24
msgid "No Limit"
msgstr "Sem Limite"

#: tutor/single/assignment/content.php:173 tutor/single/quiz/parts/meta.php:29
msgid "No limit"
msgstr "Sem limite"

#: inc/customizer/class-geeks-customizer.php:84
msgid "No logo selected"
msgstr "Nenhum logotipo selecionado"

#: woocommerce/myaccount/orders.php:151
msgid "No order has been made yet."
msgstr "Nenhum pedido foi feito ainda."

#: learnpress/profile/tabs/orders/list.php:26
msgid "No orders!"
msgstr "Sem ordens!"

#: inc/classes/class-tgm-plugin-activation.php:3055
msgid "No plugins are available to be activated at this time."
msgstr "Não há plugins disponíveis para serem ativados no momento."

#: inc/classes/class-tgm-plugin-activation.php:2921
msgid "No plugins are available to be installed at this time."
msgstr "Não há plugins disponíveis para serem instalados no momento."

#: inc/classes/class-tgm-plugin-activation.php:2923
msgid "No plugins are available to be updated at this time."
msgstr "Não há plugins disponíveis para atualização no momento."

#: inc/classes/class-tgm-plugin-activation.php:2654
msgid "No plugins to install, update or activate."
msgstr "Sem plugins para instalar, atualizar ou ativar."

#: inc/classes/class-tgm-plugin-activation.php:3029
msgid "No plugins were selected to be activated. No action taken."
msgstr "Nenhum plugin foi selecionado para ser ativado. Nenhuma ação tomada."

#: inc/classes/class-tgm-plugin-activation.php:2878
msgid "No plugins were selected to be installed. No action taken."
msgstr "Nenhum plugin foi selecionado para ser instalado. Nenhuma ação tomada."

#: inc/classes/class-tgm-plugin-activation.php:2880
msgid "No plugins were selected to be updated. No action taken."
msgstr ""
"Nenhum plugin foi selecionado para ser atualizado. Nenhuma ação tomada."

#: woocommerce/wishlist-view-mobile.php:256 woocommerce/wishlist-view.php:327
msgid "No products added to the wishlist"
msgstr "Nenhum produto adicionado à wishlist"

#: woocommerce/cart/mini-cart.php:143
msgid "No products in the cart."
msgstr "Nenhum produto no carrinho."

#: woocommerce/loop/no-products-found.php:21
msgid "No products were found matching your selection."
msgstr "Não foram encontrados produtos correspondentes à sua seleção."

#: tutor/single/lesson/sidebar_question_and_answer.php:45
msgid "No questions yet"
msgstr "Sem perguntas ainda"

#: inc/template-functions/lms.php:125
#: inc/woocommerce/template-functions/product-archive.php:410
msgid "No ratings yet"
msgstr "Ainda não há avaliações"

#: inc/woocommerce/template-functions/product-archive.php:436
msgid "No ratings yet."
msgstr "Ainda não há avaliações."

#: job_manager/form-fields/multiselect-field.php:20
msgid "No results match"
msgstr "No results match"

#: templates/single-course/tabs/review.php:46
msgid "No Review Yet"
msgstr "No Review Yet"

#: woocommerce/myaccount/payment-methods.php:116
msgid "No saved methods found."
msgstr "Nenhum método salvo encontrado."

#. Translators: $s shipping destination.
#: woocommerce/cart/cart-shipping.php:83
msgid "No shipping options were found for %s."
msgstr "Não foram encontradas opções de envio para %s."

#: templates/courses/single-course-enrolled.php:203
msgid "No Topic for this Course"
msgstr "Nenhum tópico para este curso"

#: inc/class-geeks.php:337
msgid "Normal"
msgstr "Normal"

#: inc/classes/class-tgm-plugin-activation.php:2436
msgid "Not Installed"
msgstr "Não Instalado"

#: woocommerce/single-product-reviews.php:89
msgid "Not that bad"
msgstr "Não é tão ruim assim"

#: inc/learnpress/template-functions/loop.php:235
#: inc/learnpress/template-functions/single.php:57
#: inc/learnpress/template-functions/single.php:407
msgid "Not yet rated"
msgstr "Ainda não classificado"

#: learnpress/checkout/order-comment.php:16
msgid "Note to administrator"
msgstr "Nota ao administrador"

#: woocommerce/order/order-details.php:97
msgid "Note:"
msgstr "Nota:"

#: content-none.php:24
msgid "Nothing Found"
msgstr "Nada Encontrado"

#: tutor/dashboard/announcements.php:81
msgid "Notify all students of your course"
msgstr "Notifique todos os alunos do seu curso"

#: inc/tutor/functions/instructor.php:15
msgctxt "number of courses"
msgid "Course"
msgid_plural "Courses"
msgstr[0] "Curso"
msgstr[1] "Cursos"

#: inc/gke/class-geeks-gke-customizer.php:89
msgid "Number of Paths to show"
msgstr "Número de caminhos para mostrar"

#. translators: %s number of articles to read.
#: inc/woocommerce/template-functions/single-product.php:227
#: templates/single-course/tabs/review.php:55
msgctxt "number of reviews"
msgid "Based on %s review"
msgid_plural "Based on %s reviews"
msgstr[0] "Baseado em  %s  review"
msgstr[1] "Baseado em  %s  reviews"

#: inc/tutor/functions/instructor.php:16
msgctxt "number of students"
msgid "Student"
msgid_plural "Students"
msgstr[0] "Estudante"
msgstr[1] "Estudantes"

#: tutor/single/assignment/content.php:88 tutor/single/lesson/content.php:81
#: tutor/single-quiz.php:54
msgid "of "
msgstr ""

#: inc/tutor/geeks-tutor-functions.php:22
#: tutor/dashboard/assignments/submitted.php:67
msgid "Oldest"
msgstr "Mais velho"

#: tutor/dashboard/purchase_history.php:146
msgid "On Hold"
msgstr "Em Espera"

#: inc/ocdi/class-geeks-ocdi.php:268
msgid ""
"Only courses gets imported with homepages, for courses with lessons and "
"quizes import One Click courses.xml."
msgstr ""
"Somente cursos é importado com homepages, para cursos com aulas e quizes "
"importar cursos de um clique.xml."

#: woocommerce/single-product-reviews.php:137
msgid ""
"Only logged in customers who have purchased this product may leave a review."
msgstr ""
"Somente clientes logados que compraram este produto podem deixar um "
"comentário."

#: tutor/dashboard/instructor/registration.php:27
#: tutor/dashboard/registration.php:27
msgid "Oooh! Access Denied"
msgstr "Oooh! Acesso Negado"

#: 404.php:28
msgid ""
"Oops! Sorry, we couldn’t find the page you were looking for. If you think "
"this is a problem with us, please %s"
msgstr ""
"Opa! Desculpe, não encontramos a página que você estava procurando. Se você "
"acha que isso é um problema conosco, por favor %s"

#: job_manager/account-signin.php:47
msgid "optionally"
msgstr "opcionalmente"

#: learnpress/checkout/guest-checkout-link.php:16
msgid "Or quick checkout as"
msgstr "Ou checkout rápido como"

#: learnpress/checkout/guest-checkout.php:60
msgid "Or you can %1$s%2$s %3$s now."
msgstr "Ou você pode %1$s %2$s %3$s agora."

#: inc/acf/fields/query-control-field.php:196
#: learnpress/profile/tabs/orders/list.php:33
msgid "Order"
msgstr "Ordem"

#: woocommerce/order/order-again.php:22
msgid "Order again"
msgstr "Peça novamente"

#: inc/acf/fields/query-control-field.php:183
msgid "Order By"
msgstr "Ordem Por"

#: tutor/dashboard/settings/withdraw-settings.php:26
msgid "Order Dashboard is a quick overview of all current orders."
msgstr ""
"O painel de Pedidos é uma visão geral rápida de todos os pedidos atuais."

#: woocommerce/order/order-details-customer.php:114
msgid "Order Date"
msgstr "Data Do Pedido"

#: learnpress/order/order-details.php:21
msgid "Order Details"
msgstr "Detalhes Do Pedido"

#: inc/tutor/class-geeks-tutor.php:91 tutor/dashboard/purchase_history.php:36
msgid "Order History"
msgstr "Histórico De Pedidos"

#: woocommerce/order/order-details-customer.php:109
msgid "ORDER ID"
msgstr "ID DO PEDIDO"

#: tutor/dashboard/earning/statement.php:35
#: tutor/dashboard/earning/statements.php:177
#: tutor/dashboard/purchase_history.php:98
msgid "Order ID"
msgstr "ID DO pedido"

#. translators: %s - order number
#: woocommerce/myaccount/view-order.php:31
msgid "ORDER ID: #%s"
msgstr ""

#: learnpress/order/recover-form.php:18
msgid "Order key"
msgstr "Chave do pedido"

#: learnpress/order/order-details.php:98
msgid "Order key:"
msgstr "Chave do pedido:"

#: learnpress/checkout/order-received.php:68
msgid "Order Number"
msgstr "Número Do Pedido"

#: woocommerce/checkout/order-receipt.php:25
#: woocommerce/checkout/thankyou.php:61
msgid "Order number:"
msgstr "Número do pedido:"

#: learnpress/checkout/order-received.php:48
msgid "Order Received"
msgstr "Pedido Recebido"

#: learnpress/order/order-details.php:103
msgid "Order status:"
msgstr "Status do pedido:"

#: woocommerce/myaccount/view-order.php:38
msgid "Order updates"
msgstr "Atualizações de Pedidos"

#: inc/tutor/geeks-tutor-template-functions.php:716
msgid "Orders"
msgstr "Pedidos"

#: woocommerce/wishlist-view-mobile.php:189 woocommerce/wishlist-view.php:243
msgid "Out of stock"
msgstr "Fora de estoque"

#: inc/customizer/class-geeks-customizer.php:440
msgid "Outline"
msgstr "Esboço"

#: inc/geeks-functions.php:163
msgid "Page navigation"
msgstr "Navegação na página"

#: inc/template-functions/page.php:79 inc/template-functions/page.php:110
#: inc/template-functions/single-post.php:221
msgid "Pages:"
msgstr "Páginas:"

#: tutor/course-filter/filters.php:20
msgid "Paid"
msgstr "Pago"

#: tutor/single/assignment/content.php:467
msgid "Pass Marks"
msgstr "Passar Marcas"

#: tutor/dashboard/assignments/submitted.php:53
msgid "Pass Points"
msgstr "Passar Pontos"

#: learnpress/profile/tabs/courses.php:32
#: tutor/single/assignment/content.php:512
msgid "Passed"
msgstr "Passou"

#: learnpress/single-course/sidebar/user-progress.php:53
msgid "Passing condition: %s%%"
msgstr "Condição de aprovação: %s%%"

#: tutor/single/quiz/top.php:109
msgid "Passing Grade"
msgstr "Nota De Aprovação"

#: inc/woocommerce/template-functions/my-account.php:133
#: inc/woocommerce/template-functions/my-account.php:134
#: learnpress/checkout/account-login.php:33
#: learnpress/checkout/account-login.php:34
#: learnpress/checkout/account-register.php:35
#: learnpress/checkout/account-register.php:36
#: learnpress/checkout/account-register.php:40
#: learnpress/global/form-login.php:32 learnpress/global/form-login.php:33
#: learnpress/global/form-register.php:38
#: learnpress/global/form-register.php:39
#: learnpress/global/form-register.php:43
#: tutor/dashboard/instructor/registration.php:122
#: tutor/dashboard/instructor/registration.php:125
#: tutor/dashboard/registration.php:122 tutor/dashboard/registration.php:125
#: tutor/global/login.php:69 tutor/global/login.php:70 tutor/login-form.php:65
#: tutor/login-form.php:66 tutor/template-part/form-retrieve-password.php:32
#: woocommerce/global/form-login.php:65 woocommerce/global/form-login.php:66
#: woocommerce/myaccount/form-login.php:53
#: woocommerce/myaccount/form-login.php:54
#: woocommerce/myaccount/form-login.php:114
#: woocommerce/myaccount/form-login.php:115
msgid "Password"
msgstr "Senha"

#: woocommerce/myaccount/form-edit-account.php:52
msgid "Password change"
msgstr "Alteração de senha"

#: tutor/dashboard/instructor/registration.php:146
#: tutor/dashboard/registration.php:148
msgid "Password Confirmation"
msgstr "Confirmação De Senha"

#: tutor/dashboard/instructor/registration.php:141
#: tutor/dashboard/registration.php:142
msgid "Password confirmation"
msgstr "Confirmação de senha"

#: woocommerce/myaccount/lost-password-confirmation.php:21
msgid "Password reset email has been sent."
msgstr "O e-mail de redefinição de senha foi enviado."

#: inc/template-functions/global.php:199
msgid "Password you entered is mismatched"
msgstr "A senha que você digitou é incompatível"

#: inc/template-functions/single-post.php:450
msgid "Password:"
msgstr "Senha:"

#: inc/wpjm/class-geeks-wpjm-customizer.php:102
msgid "Paste your application form HTML or shortcode here"
msgstr "Cole seu formulário de inscrição HTML ou shortcode aqui"

#: inc/customizer/class-geeks-customizer.php:1289
msgid "Paste your newsletter signup form or shortcode"
msgstr "Cole seu formulário de inscrição no boletim informativo ou shortcode"

#: archive-course_path.php:19
msgid "Path"
msgstr "Caminho"

#: archive-course_path.php:19 inc/gke/class-geeks-gke-customizer.php:41
msgid "Paths"
msgstr "Caminhos"

#: woocommerce/checkout/thankyou.php:40
msgid "Pay"
msgstr "Pagar"

#: edd_templates/shortcode-receipt.php:31 learnpress/checkout/payment.php:25
msgid "Payment"
msgstr "Pagamento"

#: edd_templates/shortcode-receipt.php:46
msgid "Payment Key"
msgstr "Chave De Pagamento"

#: edd_templates/shortcode-receipt.php:53
#: learnpress/checkout/order-received.php:131
msgid "Payment Method"
msgstr "Forma De Pagamento"

#: learnpress/checkout/payment.php:29
msgctxt "payment method"
msgid "Secure Connection"
msgstr "Conexão Segura"

#: woocommerce/checkout/order-receipt.php:38
#: woocommerce/checkout/thankyou.php:84
msgid "Payment method:"
msgstr "Forma de pagamento:"

#: inc/tutor/geeks-tutor-template-functions.php:720
#: woocommerce/checkout/payment.php:25
msgid "Payment Methods"
msgstr "Formas De Pagamento"

#: edd_templates/shortcode-receipt.php:40
msgid "Payment Status"
msgstr "Status Do Pagamento"

#: learnpress/profile/tabs/courses.php:39 tutor/dashboard/my-courses.php:63
#: tutor/dashboard/purchase_history.php:158
#: tutor/single/assignment/content.php:528
msgid "Pending"
msgstr "Pendente"

#: woocommerce/single-product-reviews.php:86
msgid "Perfect"
msgstr "Perfeito"

#: tutor/dashboard/create-course.php:34 tutor/permission-denied.php:26
msgid "Permission Denied"
msgstr "Permissão Negada"

#: tutor/dashboard/settings/profile.php:104
msgid "Personal Details"
msgstr "Dados Pessoais"

#: tutor/dashboard/my-profile.php:73 tutor/dashboard/settings/profile.php:142
#: tutor/dashboard/settings/profile.php:143
msgid "Phone Number"
msgstr "Telefone"

#: learnpress/checkout/payment.php:75
msgid "Place order"
msgstr "Fazer pedido"

#: inc/template-functions/posts.php:60 searchform.php:4
msgctxt "placeholder"
msgid "Search …"
msgstr "Pesquisar …"

#: tutor/dashboard/withdraw.php:133
msgid ""
"Please check your transaction notification on your connected withdrawal "
"method"
msgstr ""
"Verifique sua notificação de transação em seu método de Saque conectado"

#: inc/classes/class-tgm-plugin-activation.php:404
msgid "Please contact the administrator of this site for help."
msgstr "Entre em contato com o administrador deste site para obter ajuda."

#: inc/classes/class-tgm-plugin-activation.php:1022
#: inc/classes/class-tgm-plugin-activation.php:1032
msgid ""
"Please contact the plugin provider and ask them to package their plugin "
"according to the WordPress guidelines."
msgstr ""
"Entre em contato com o Provedor do plug-in e peça que ele empacote o plug-in "
"de acordo com as diretrizes do WordPress."

#: tutor/dashboard/withdraw.php:39
msgid "Please contact the site administrator for more information."
msgstr ""
"Entre em contato com o administrador do site para obter mais informações."

#: inc/template-functions/global.php:176
msgid "Please enter a username"
msgstr "Por favor, insira um nome de usuário"

#: woocommerce/checkout/payment.php:35
msgid "Please fill in your details above to see available payment methods."
msgstr "Preencha seus dados acima para ver as formas de pagamento disponíveis."

#: learnpress/checkout/form.php:21
msgid "Please login to enroll the course!"
msgstr "Faça o login para se inscrever no curso!"

#: tutor/single/course/add-to-cart-edd.php:24
msgid "Please make sure that your EDD product exists and valid for this course"
msgstr "Certifique-se de que seu produto EDD existe e é válido para este curso"

#: tutor/single/course/add-to-cart-woocommerce.php:78
msgid "Please make sure that your product exists and valid for this course"
msgstr "Certifique - se de que seu produto existe e é válido para este curso"

#: tutor/dashboard/create-course.php:36
msgid "Please make sure you are logged in to correct account"
msgstr "Por favor, verifique se você está logado na Conta correta"

#: tutor/permission-denied.php:30
msgid ""
"Please make sure you are logged in to correct account if the content needs "
"authorization 35345."
msgstr ""
"Por favor, verifique se você está logado na Conta correta se o conteúdo "
"precisa de autorização 35345."

#: inc/classes/class-tgm-plugin-activation.php:2668
msgid "Plugin"
msgstr "Plugin"

#: inc/classes/class-tgm-plugin-activation.php:1221
#: inc/classes/class-tgm-plugin-activation.php:3069
msgctxt "plugin A *and* plugin B"
msgid "and"
msgstr "e"

#: inc/classes/class-tgm-plugin-activation.php:394
#: inc/classes/class-tgm-plugin-activation.php:3280
msgid "Plugin activated successfully."
msgstr "Plugin ativado com sucesso."

#: inc/classes/class-tgm-plugin-activation.php:3279
msgid "Plugin activation failed."
msgstr "Falha na ativação do Plugin."

#. translators: 1: plugin name.
#: inc/classes/class-tgm-plugin-activation.php:399
msgid ""
"Plugin not activated. A higher version of %s is needed for this theme. "
"Please update the plugin."
msgstr ""
"Plugin não ativado. Uma versão superior de %s é necessária para este tema. "
"Por favor, atualize o plugin."

#. translators: 1: number of plugins.
#: inc/classes/class-tgm-plugin-activation.php:2509
msgctxt "plugins"
msgid "All <span class=\"count\">(%s)</span>"
msgid_plural "All <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: tutor/dashboard/settings/profile.php:82
msgid "PNG or JPG no bigger than 800px wide and tall."
msgstr "PNG ou JPG não maior que 800px de largura e altura."

#: comments.php:21
msgid "Post Comments"
msgstr "Postar Comentários"

#: inc/acf/fields/query-control-field.php:290
msgid "Post Ids"
msgstr "Post Ids"

#: inc/class-geeks.php:726
msgid "Post Navigation"
msgstr "Pós Navegação"

#: inc/acf/fields/query-control-field.php:127
msgid "Post Types"
msgstr "Tipos De Post"

#: woocommerce/cart/shipping-calculator.php:82
msgid "Postcode / ZIP"
msgstr "CEP / CEP"

#: tutor/single/course/enrolled/announcements.php:33
msgid "Posted by %1$s, at %2$s ago"
msgstr "Postado por %1$s, em %2$s atrás"

#: inc/acf/fields/query-control-field.php:314
msgid "Posts Per Page"
msgstr "Posts Por Página"

#: inc/classes/class-tgm-plugin-activation.php:2419
msgid "Pre-Packaged"
msgstr "Pré-Embalados"

#: tutor/dashboard/create-course.php:191
msgid ""
"Prerequisites refers to the fundamental courses to complete before taking "
"this particular course."
msgstr ""
"Pré-requisitos refere-se aos cursos fundamentais a serem concluídos antes de "
"fazer este curso específico."

#: tutor/single/next-previous-pagination.php:22
msgid "Prev"
msgstr "Prev"

#: job_manager/job-preview.php:31 tutor/dashboard/create-course.php:200
msgid "Preview"
msgstr "Antevisão"

#: inc/woocommerce/template-functions/single-product.php:582
#: woocommerce/myaccount/orders.php:138
msgid "Previous"
msgstr "Anterior"

#: edd_templates/shortcode-receipt.php:133 tutor/course-filter/filters.php:128
#: tutor/dashboard/earning/statement.php:30
#: tutor/dashboard/purchase_history.php:113 woocommerce/cart/cart.php:40
#: woocommerce/cart/cart.php:97
msgid "Price"
msgstr "Preço"

#: inc/learnpress/geeks-learnpress-functions.php:14
msgid "Price &darr;"
msgstr "Preço ↓"

#: inc/learnpress/geeks-learnpress-functions.php:13
msgid "Price &uarr;"
msgstr "Preço ↑"

#. translators: Filter: verb "to filter"
#: tutor/dashboard/my-courses.php:150
#: woocommerce/content-widget-price-filter.php:32
#: woocommerce/wishlist-view-mobile.php:150
msgid "Price:"
msgstr "Preço:"

#: tutor/dashboard/earning/statements.php:173
msgid "Price: "
msgstr ""

#: inc/customizer/class-geeks-customizer.php:1493
msgid "Primary color"
msgstr "Cor primária"

#: woocommerce/cart/proceed-to-checkout-button.php:26
msgid "Proceed to checkout"
msgstr "Proceder ao checkout"

#: tutor/dashboard/purchase_history.php:142
#: tutor/dashboard/purchase_history.php:150
msgid "Processing"
msgstr "Processamento"

#: learnpress/addons/wishlist/button.php:33
msgid "Processing..."
msgstr "Processing..."

#: woocommerce/cart/cart.php:39 woocommerce/cart/cart.php:61
#: woocommerce/checkout/form-pay.php:26
#: woocommerce/checkout/review-order.php:32
#: woocommerce/order/order-details.php:55
msgid "Product"
msgstr "Produto"

#: inc/woocommerce/class-geeks-woocommerce.php:184
msgid "Product Filters"
msgstr "Filtros De Produtos"

#: woocommerce/wishlist-view.php:93
msgid "Product name"
msgstr "Nome do produto"

#: woocommerce/global/quantity-input.php:49
msgctxt "Product quantity input tooltip"
msgid "Qty"
msgstr "Qtd"

#: edd_templates/shortcode-receipt.php:120
msgid "Products"
msgstr "Produtos"

#: inc/template-functions/header.php:493
#: tutor/dashboard/settings/nav-bar.php:20
msgid "Profile"
msgstr "Perfil"

#: tutor/dashboard/settings/profile.php:63
msgid "Profile Details"
msgstr "Detalhes Do Perfil"

#: inc/learnpress/functions/profile.php:131
#: learnpress/profile/tabs/settings/privacy.php:19
msgid "Profile Privacy"
msgstr "Privacidade Do Perfil"

#: learnpress/profile/tabs/courses.php:38
#: tutor/dashboard/announcements/create.php:67
#: tutor/dashboard/my-courses.php:58
msgid "Publish"
msgstr "Publicar"

#: tutor/dashboard/create-course.php:202
msgid "Publish Course"
msgstr "Publicar Curso"

#: tutor/dashboard/announcements/details.php:39
msgid "Publish Date"
msgstr "Data De Publicação"

#: tutor/dashboard/dashboard.php:244
msgid "Published"
msgstr "Publicado"

#: inc/woocommerce/integrations.php:20
msgid "Purchase History"
msgstr "Histórico De Compras"

#: inc/tutor/geeks-tutor-template-functions.php:143
msgid "Q&A"
msgstr "Q & A"

#: woocommerce/checkout/form-pay.php:27
msgid "Qty"
msgstr "Qtd"

#: edd_templates/shortcode-receipt.php:131 woocommerce/cart/cart.php:41
#: woocommerce/cart/cart.php:103 woocommerce/global/quantity-input.php:24
#: woocommerce/wishlist-view.php:110
msgid "Quantity"
msgstr "Quantidade"

#: woocommerce/wishlist-view-mobile.php:171
msgid "Quantity:"
msgstr "Quantidade:"

#: inc/acf/fields/query-control-field.php:25
msgid "Query Control Options"
msgstr "Opções De Controle De Consulta"

#: inc/tutor/class-geeks-tutor.php:92 inc/woocommerce/integrations.php:34
#: tutor/dashboard/question-answer.php:40
msgid "Question & Answer"
msgstr "Pergunta E Resposta"

#: tutor/single/quiz/top.php:70
msgid "Questions"
msgstr "Perguntas"

#: tutor/single/quiz/parts/meta.php:10
msgid "Questions No"
msgstr "Perguntas Nº"

#: tutor/single/quiz/top.php:32
msgid "Quiz"
msgstr "Quiz"

#: inc/tutor/class-geeks-tutor.php:130 inc/woocommerce/integrations.php:33
#: tutor/dashboard/quiz-attempts.php:32
msgid "Quiz Attempts"
msgstr "Tentativas De Quiz"

#: learnpress/single-course/sidebar/user-progress.php:32
msgid "Quizzes finished:"
msgstr "Quizzes finalizados:"

#: inc/acf/fields/query-control-field.php:189
msgid "Random"
msgstr "Aleatório"

#: tutor/dashboard/earning/statement.php:53
#: tutor/dashboard/earning/statements.php:191
msgid "Rate"
msgstr "Taxa"

#: woocommerce/single-product-reviews.php:85
msgid "Rate&hellip;"
msgstr "Taxa…"

#: learnpress/addons/course-review/review-form.php:38
msgid "Rating"
msgstr "Rating"

#: woocommerce/myaccount/form-reset-password.php:33
msgid "Re-enter new password"
msgstr "Digite novamente a nova senha"

#: tutor/dashboard/settings/reset-password.php:63
msgid "Re-type New Password"
msgstr "Digite novamente a nova senha"

#. translators: 1: URL
#: content-none.php:15
msgid "Ready to publish your first post? %s."
msgstr "Pronto para publicar seu primeiro post?  %s ."

#: tutor/single/quiz/parts/meta.php:93
msgid "Reattempt"
msgstr "Tentar novamente"

#: tutor/dashboard/reviews/given-reviews.php:38 tutor/dashboard/reviews.php:42
msgid "Received"
msgstr "Recebido"

#: inc/classes/class-tgm-plugin-activation.php:2397
msgid "Recommended"
msgstr "Recomendado"

#: learnpress/order/recover-form.php:21
msgid "Recover"
msgstr "Recuperar"

#: learnpress/profile/tabs/orders/recover-order.php:17
msgid "Recover Order"
msgstr "Recuperar Pedido"

#: inc/woocommerce/template-functions/my-account.php:147
#: learnpress/global/form-register.php:54 tutor/dashboard/registration.php:174
#: woocommerce/myaccount/form-login.php:128
msgid "Register"
msgstr "Cadastre-se"

#: tutor/dashboard/instructor/registration.php:172
msgid "Register as instructor"
msgstr "Cadastre-se como instrutor"

#: inc/woocommerce/template-functions/my-account.php:108
#: learnpress/global/form-register.php:19
#: woocommerce/myaccount/form-login.php:88
msgctxt "register-heading"
msgid "Register"
msgstr "Cadastre-se"

#: tutor/dashboard/my-profile.php:33
msgid "Registration Date"
msgstr "Data De Inscrição"

#: inc/learnpress/template-functions/single.php:347
msgid "Related Courses"
msgstr "Cursos Relacionados"

#: inc/template-functions/single-post.php:414
msgid "Related Posts"
msgstr "Posts Relacionados"

#: woocommerce/single-product/related.php:27
msgid "Related products"
msgstr "Produtos relacionados"

#: tutor/course-filter/course-archive-filter-bar.php:26
msgid "Release Date (newest first)"
msgstr "Data de lançamento (mais recente primeiro)"

#: tutor/course-filter/course-archive-filter-bar.php:29
msgid "Release Date (oldest first)"
msgstr "Data de lançamento (Mais antigo primeiro)"

#: job_manager/job-dashboard.php:67
msgid "Relist"
msgstr "Relist"

#: tutor/global/login.php:88 tutor/login-form.php:85
msgid "Remember Me"
msgstr "Remember Me"

#: learnpress/checkout/account-login.php:44 learnpress/global/form-login.php:42
#: woocommerce/global/form-login.php:74 woocommerce/myaccount/form-login.php:62
msgid "Remember me"
msgstr "Remember me"

#: edd_templates/checkout_cart.php:57 edd_templates/checkout_cart.php:75
#: inc/customizer/class-geeks-customizer.php:82
#: learnpress/profile/tabs/settings/avatar.php:54
#: woocommerce/wishlist-view.php:307
msgid "Remove"
msgstr "Remover"

#: job_manager/form-fields/uploaded-file-html.php:28
#: job_manager/form-fields/uploaded-file-html.php:30
msgid "remove"
msgstr "remover"

#: learnpress/addons/wishlist/button.php:34
msgid "Remove from Wishlist"
msgstr "Remover da Wishlist"

#: woocommerce/cart/cart.php:141 woocommerce/cart/mini-cart.php:85
msgid "Remove this item"
msgstr "Remover este item"

#: woocommerce/wishlist-view-mobile.php:242 woocommerce/wishlist-view.php:171
#: woocommerce/wishlist-view.php:307
msgid "Remove this product"
msgstr "Remover este produto"

#: tutor/dashboard/question-answer/answers.php:118
msgid "Reply"
msgstr "Responder"

#: tutor/dashboard/earning/report.php:39
#: tutor/dashboard/earning/statements.php:37
msgid "Report"
msgstr "Relatório"

#: tutor/dashboard/earning.php:45
msgid "Reports"
msgstr "Relatórios"

#: tutor/dashboard/withdraw.php:208
msgid "Requested On"
msgstr "Solicitado Em"

#: inc/classes/class-tgm-plugin-activation.php:2394
msgid "Required"
msgstr "Obrigatório"

#: inc/classes/class-tgm-plugin-activation.php:2448
msgid "Required Update not Available"
msgstr "Atualização necessária não disponível"

#: tutor/single/course/course-requirements.php:25
msgid "Requirements"
msgstr "Requisitos"

#: inc/classes/class-tgm-plugin-activation.php:2451
msgid "Requires Update"
msgstr "Requer Atualização"

#: inc/wpjm/geeks-wpjm-functions.php:28
msgid "Reset"
msgstr "Reset"

#: tutor/dashboard/settings/nav-bar.php:25
#: tutor/dashboard/settings/reset-password.php:17
#: tutor/dashboard/settings/reset-password.php:81
msgid "Reset Password"
msgstr "Redefinir Senha"

#: learnpress/global/form-lost-password.php:34
#: tutor/template-part/form-retrieve-password.php:44
#: tutor/template-part/retrieve-password.php:48
#: woocommerce/myaccount/form-lost-password.php:47
msgid "Reset password"
msgstr "Redefinir senha"

#: inc/tutor/geeks-tutor-template-functions.php:164
#: inc/tutor/geeks-tutor-template-functions.php:167
msgid "Resources"
msgstr "Recursos"

#: tutor/dashboard/assignments/submitted.php:79
#: tutor/single/assignment/content.php:477
msgid "Result"
msgstr "Resultado"

#: learnpress/single-course/buttons/retry.php:37
msgid "Retake course"
msgstr "Retomar curso"

#: templates/courses/single-course-enrolled.php:130
msgid "Retake This Course"
msgstr "Refaça Este Curso"

#: learnpress/global/form-lost-password.php:39
msgid "Return to %ssign in%s"
msgstr "Return to %ssign in%s"

#: woocommerce/checkout/cart-errors.php:25
msgid "Return to cart"
msgstr "Voltar ao carrinho"

#: inc/classes/class-tgm-plugin-activation.php:392
msgid "Return to Required Plugins Installer"
msgstr "Voltar ao Instalador de Plugins necessário"

#: woocommerce/cart/cart-empty.php:39
msgid "Return to shop"
msgstr "Voltar à loja"

#: inc/classes/class-tgm-plugin-activation.php:393
#: inc/classes/class-tgm-plugin-activation.php:913
#: inc/classes/class-tgm-plugin-activation.php:2654
#: inc/classes/class-tgm-plugin-activation.php:3705
msgid "Return to the Dashboard"
msgstr "Voltar ao Painel"

#: woocommerce/checkout/form-login.php:26
msgid "Returning customer?"
msgstr "Cliente recorrente?"

#: woocommerce/single-product-reviews.php:103
msgid "Review Content"
msgstr "Revisar Conteúdo"

#: inc/learnpress/functions/instructor.php:103
#: inc/learnpress/functions/instructor.php:126
#: inc/template-functions/header.php:497 inc/tutor/class-geeks-tutor.php:89
#: inc/tutor/functions/single-course.php:76
#: inc/tutor/geeks-tutor-template-functions.php:182
#: inc/woocommerce/integrations.php:18
#: learnpress/addons/course-review/course-review.php:24
#: templates/single-course/tabs/review.php:92
#: tutor/dashboard/reviews/given-reviews.php:27 tutor/dashboard/reviews.php:30
#: tutor/public-profile.php:162 tutor/student-public-profile.php:154
#: woocommerce/single-product-reviews.php:35
msgid "Reviews"
msgstr "Reviews"

#: inc/customizer/class-geeks-customizer.php:1102
msgid "Right Sidebar"
msgstr "Barra Lateral Direita"

#: inc/acf/fields/fields.php:377
msgid "Row"
msgstr "Linha"

#: inc/wpjm/geeks-wpjm-functions.php:32
msgid "RSS"
msgstr "RSS"

#: tutor/dashboard/earning/report-date_range.php:89
#: tutor/dashboard/earning/report-last_month.php:87
#: tutor/dashboard/earning/report-last_week.php:93
#: tutor/dashboard/earning/report-last_year.php:89
#: tutor/dashboard/earning/report-this_month.php:90
#: tutor/dashboard/earning/report-this_week.php:90
#: tutor/dashboard/earning/report-this_year.php:87
msgid "Sales statements for this period"
msgstr "Demonstrativos de vendas para este período"

#: tutor/dashboard/create-course.php:84
#: woocommerce/myaccount/form-reset-password.php:46
msgid "Save"
msgstr "Salvar"

#: woocommerce/myaccount/form-edit-address.php:46
msgid "Save address"
msgstr "Salvar endereço"

#: learnpress/profile/tabs/settings/basic-information.php:140
#: learnpress/profile/tabs/settings/change-password.php:64
#: learnpress/profile/tabs/settings/privacy.php:63
#: woocommerce/myaccount/form-edit-account.php:76
msgid "Save changes"
msgstr "Salvar alterações"

#: job_manager/job-submit.php:80
msgid "Save Draft"
msgstr "Salvar Rascunho"

#: comments.php:86
msgid "Save my name and email in this browser for the next time I comment."
msgstr ""
"Salve meu nome e email neste navegador para a próxima vez que eu comentar."

#: tutor/dashboard/settings/withdraw-settings.php:138
msgid "Save Withdrawal Account"
msgstr "Salvar Conta De Saque"

#: inc/template-functions/header.php:244
msgid "Search Courses"
msgstr "Pesquisar Cursos"

#: inc/template-functions/header.php:251 woocommerce/product-searchform.php:26
msgid "Search for:"
msgstr "Pesquisar por:"

#: job_manager/job-filters.php:85
msgid "Search Jobs"
msgstr "Pesquisar Vagas"

#: woocommerce/product-searchform.php:27
msgid "Search products&hellip;"
msgstr "Pesquisar produtos…"

#: inc/wpjm/geeks-wpjm-template-functions.php:86
msgid "Search Results"
msgstr "Resultados Da Pesquisa"

#: inc/class-geeks.php:81
msgid "Search Results for \"%s\""
msgstr "Resultados da busca por \"%s\""

#. translators: %s: search query
#: inc/tutor/geeks-tutor-template-functions.php:565
msgid "Search Results for: &ldquo;%s&rdquo;"
msgstr "Resultados da busca por: \"%s\""

#: inc/template-functions/header.php:240
msgid "Search …"
msgstr "Pesquisar …"

#: tutor/course-filter/filters.php:52
msgid "Search..."
msgstr "Search..."

#: tutor/single/quiz/top.php:80
msgid "Second"
msgstr "Segundo"

#: tutor/single/quiz/top.php:80
msgid "Seconds"
msgstr "Segundos"

#: learnpress/single-course/loop-section.php:128
msgid "Section progress %s%%"
msgstr "Progresso da seção %s%%"

#: inc/tutor/class-geeks-tutor.php:69 inc/woocommerce/integrations.php:48
msgid "Security"
msgstr "Segurança"

#: woocommerce/cart/shipping-calculator.php:31
msgid "Select a country / region&hellip;"
msgstr "Selecione um país / região…"

#: tutor/dashboard/settings/withdraw-settings.php:25
msgid "Select a withdraw method"
msgstr "Selecione um método de Saque"

#: woocommerce/cart/shipping-calculator.php:56
msgid "Select an option&hellip;"
msgstr "Selecione uma opção…"

#: tutor/dashboard/announcements/create.php:31
#: tutor/dashboard/announcements/update.php:32
msgid "Select Course"
msgstr "Selecione O Curso"

#: inc/customizer/class-geeks-customizer.php:80
#: inc/customizer/class-geeks-customizer.php:85
msgid "Select logo"
msgstr "Selecione logo"

#: tutor/dashboard/reviews/given-reviews.php:111
msgid "Select Rating"
msgstr "Select Rating"

#: job_manager/form-fields/multiselect-field.php:20
msgid "Select Some Options"
msgstr "Selecione Algumas Opções"

#: tutor/dashboard/withdraw.php:142
msgid "Selected Payment Method"
msgstr "Método De Pagamento Selecionado"

#: learnpress/global/form-lost-password.php:34
#: woocommerce/myaccount/form-lost-password.php:47
msgid "Send Reset Link"
msgstr "Enviar Link De Reset"

#: templates/create-course/general-info-content.php:73
msgid "Set course price"
msgstr "Definir preço do curso"

#: tutor/dashboard/create-course.php:186
msgid "Set the Course Price option or make it free."
msgstr "Defina a opção de preço do curso ou torne-o gratuito."

#: tutor/dashboard/notifications/profile-completion.php:27
msgid "Set Your"
msgstr "Defina Seu"

#: inc/template-functions/header.php:501
#: tutor/dashboard/notifications/profile-completion.php:23
msgid "Settings"
msgstr "Configurações"

#: inc/gke/geeks-gke-template-functions.php:42
#: inc/template-functions/single-post.php:118
msgid "Share"
msgstr "Compartilhar"

#: inc/template-functions/single-post.php:164
msgid "Share : "
msgstr ""

#: learnpress/profile/tabs/settings/basic-information.php:66
msgid ""
"Share a little biographical information to fill out your profile. This may "
"be shown publicly."
msgstr ""
"Compartilhe algumas informações biográficas para preencher seu perfil. Isso "
"pode ser mostrado publicamente."

#: woocommerce/checkout/form-shipping.php:27
msgid "Ship to a different address?"
msgstr "Enviar para um endereço diferente?"

#: woocommerce/cart/cart-totals.php:53 woocommerce/cart/cart-totals.php:54
msgid "Shipping"
msgstr "Envio"

#: woocommerce/myaccount/form-edit-address.php:20
#: woocommerce/myaccount/my-address.php:27
#: woocommerce/order/order-details-customer.php:71
msgid "Shipping address"
msgstr "Endereço de entrega"

#: woocommerce/cart/cart-shipping.php:64
msgid "Shipping costs are calculated during checkout."
msgstr "Os custos de envio são calculados durante o checkout."

#: woocommerce/cart/cart-shipping.php:56
msgid "Shipping options will be updated during checkout."
msgstr "As opções de envio serão atualizadas durante o checkout."

#. Translators: $s shipping destination.
#: woocommerce/cart/cart-shipping.php:53
msgid "Shipping to %s."
msgstr "Envio para  %s ."

#: woocommerce/loop/orderby.php:24
msgid "Shop order"
msgstr "Ordem da loja"

#: inc/woocommerce/class-geeks-woocommerce.php:174
msgid "Shop Sidebar"
msgstr "Barra Lateral Da Loja"

#: templates/single-course/tabs/description.php:24
#: tutor/single/assignment/content.php:419
#: tutor/single/assignment/content.php:661
msgid "Show More"
msgstr "Mostrar Mais"

#. translators: %d: total courses
#: inc/template-functions/posts.php:273
msgid "Showing all %1$d %2$s"
msgid_plural "Showing all %1$d %2$s"
msgstr[0] "Mostrando todos %1$d %2$s"
msgstr[1] "Mostrando todos %1$d %2$s"

#. translators: %d: total results
#: woocommerce/loop/result-count.php:31
msgid "Showing all %d product"
msgid_plural "Showing all %d products"
msgstr[0] "Mostrando todos %d produto"
msgstr[1] "Mostrando todos os produtos %d"

#: tutor/dashboard/earning/report-date_range.php:77
#: tutor/dashboard/earning/report-last_week.php:81
#: tutor/dashboard/earning/report-this_week.php:78
msgid "Showing Result from %1$s to %2$s"
msgstr "Mostrando resultado de %1$s a %2$s"

#: tutor/dashboard/earning/statements.php:139
msgid "Showing results %1$d to %2$d of %3$d"
msgstr "Mostrando resultados %1$d A %2$d de %3$d"

#. translators: %s: single courses
#: inc/template-functions/posts.php:270
msgid "Showing the single %s"
msgstr "Mostrando o único %s"

#: woocommerce/loop/result-count.php:28
msgid "Showing the single product"
msgstr "Mostrando o produto único"

#: inc/woocommerce/template-functions/header.php:40 tutor/global/login.php:102
#: tutor/login-form.php:101
msgid "Sign In"
msgstr "Entrar"

#: inc/learnpress/template-functions/profile.php:47
#: inc/tutor/geeks-tutor-template-functions.php:401
#: inc/woocommerce/template-functions/my-account.php:109
#: job_manager/account-signin.php:42 learnpress/checkout/account-login.php:22
#: learnpress/checkout/account-register.php:52
#: learnpress/global/form-login.php:48
#: tutor/dashboard/instructor/registration.php:67
#: tutor/dashboard/registration.php:67 woocommerce/global/form-login.php:82
#: woocommerce/myaccount/form-login.php:71
#: woocommerce/myaccount/form-login.php:89
msgid "Sign in"
msgstr "Entrar"

#: inc/template-functions/header.php:532 inc/template-functions/header.php:546
msgid "Sign Out"
msgstr "Sair"

#: job_manager/account-signin.php:29
msgid "Sign out"
msgstr "Sair"

#: inc/template-functions/header.php:574 inc/template-functions/header.php:590
msgid "Sign Up"
msgstr "Cadastre-Se"

#: inc/learnpress/template-functions/profile.php:33
#: inc/tutor/geeks-tutor-template-functions.php:403
#: learnpress/checkout/account-register.php:18
#: tutor/dashboard/instructor/registration.php:63
#: tutor/dashboard/registration.php:63 woocommerce/global/form-login.php:40
#: woocommerce/myaccount/form-login.php:39
msgid "Sign up"
msgstr "Cadastre-se"

#: inc/woocommerce/template-functions/header.php:41
msgid "Sign Up "
msgstr ""

#: inc/template-functions/single-post.php:468
msgid "Sign up for our Newsletter"
msgstr "Assine nossa Newsletter"

#. translators: $1 and $2 opening and closing emphasis tags respectively
#: woocommerce/checkout/payment.php:44
msgid ""
"Since your browser does not support JavaScript, or it is disabled, please "
"ensure you click the %1$sUpdate Totals%2$s button before placing your order. "
"You may be charged more than the amount stated above if you fail to do so."
msgstr ""
"Como seu navegador não suporta JavaScript ou está desativado, certifique-se "
"de clicar no botão %1$sUpdate Totals%2$s Antes de fazer seu pedido. Você "
"pode ser cobrado mais do que o valor indicado acima, se você não fazê-lo."

#: inc/learnpress/class-geeks-learnpress-customizer.php:75
msgid "Single Course Version"
msgstr "Versão Curso Único"

#: inc/wpjm/class-geeks-wpjm.php:89
msgid "Single Job Listing Sidebar"
msgstr "Barra Lateral De Listagem De Emprego Único"

#: inc/acf/settings/single-post.php:11
msgid "Single Post Options - Geeks"
msgstr "Opções De Postagem Única-Geeks"

#: tutor/dashboard/assignments/review.php:94 tutor/global/attachments.php:34
msgid "Size"
msgstr "Tamanho"

#: tutor/single/assignment/content.php:260
msgid "Size: "
msgstr ""

#: tutor/dashboard/settings/profile.php:148
msgid "Skill/Occupation"
msgstr "Habilidade / Ocupação"

#: tutor/single/quiz/parts/question.php:146
msgid "Skip Question"
msgstr "Pular Pergunta"

#: tutor/single/quiz/top.php:137
msgid "Skip Quiz"
msgstr "Pular Quiz"

#: tutor/single/assignment/content.php:429
#: tutor/single/assignment/content.php:706
msgid "Skip To Next"
msgstr "Pular Para O Próximo"

#: edd_templates/shortcode-receipt.php:128
msgid "SKU"
msgstr "SKU"

#: woocommerce/single-product/meta.php:41
msgid "SKU: "
msgstr ""

#: inc/acf/fields/carousel-control-field.php:245
msgid "Slide Autoplay Time"
msgstr "Slide Autoplay Time"

#: inc/acf/fields/carousel-control-field.php:202
msgid "Slide Axis"
msgstr "Eixo Do Slide"

#: inc/acf/fields/carousel-control-field.php:177
msgid "Slide Mode"
msgstr "Modo Slide"

#: inc/acf/fields/carousel-control-field.php:251
msgid "Slide Speed"
msgstr "Deslize A Velocidade"

#: inc/class-geeks.php:332 inc/customizer/class-geeks-customizer.php:546
msgid "Small"
msgstr "Pequeno"

#: inc/class-geeks.php:224
msgid "Social Media"
msgstr "Mídias Sociais"

#: tutor/dashboard/settings/nav-bar.php:35
msgid "Social Profile"
msgstr "Perfil Social"

#: tutor/dashboard/settings/profile.php:178
msgid "Social Profiles"
msgstr "Perfis Sociais"

#: inc/classes/class-tgm-plugin-activation.php:340
msgid "Something went wrong with the plugin API."
msgstr "Algo deu errado com a API do plugin."

#: content-none.php:18
msgid ""
"Sorry, but nothing matched your search terms. Please try again with some "
"different keywords."
msgstr ""
"Desculpe, mas nada corresponde aos seus termos de pesquisa. Por favor, tente "
"novamente com algumas palavras-chave diferentes."

#: tutor/dashboard/assignments/review.php:20
msgid "Sorry, but you are looking for something that isn't here."
msgstr "Desculpe, mas você está procurando algo que não está aqui."

#: woocommerce/checkout/form-pay.php:79
msgid ""
"Sorry, it seems that there are no available payment methods for your "
"location. Please contact us if you require assistance or wish to make "
"alternate arrangements."
msgstr ""
"Desculpe, parece que não há métodos de pagamento disponíveis para sua "
"localização. Entre em contato conosco se precisar de assistência ou desejar "
"fazer arranjos alternativos."

#: woocommerce/checkout/payment.php:35
msgid ""
"Sorry, it seems that there are no available payment methods for your state. "
"Please contact us if you require assistance or wish to make alternate "
"arrangements."
msgstr ""
"Desculpe, parece que não há métodos de pagamento disponíveis para o seu "
"estado. Entre em contato conosco se precisar de assistência ou desejar fazer "
"arranjos alternativos."

#: woocommerce/single-product/add-to-cart/variation.php:22
msgid ""
"Sorry, this product is unavailable. Please choose a different combination."
msgstr ""
"Desculpe, este produto não está disponível. Por favor, escolha uma "
"combinação diferente."

#: tutor/dashboard/announcements.php:123 tutor/dashboard/assignments.php:57
#: tutor/dashboard/question-answer.php:54
msgid "Sort By"
msgstr "Ordenar Por"

#: inc/learnpress/geeks-learnpress-functions.php:10
#: inc/tutor/geeks-tutor-template-functions.php:381
#: inc/woocommerce/template-functions/product-archive.php:135
msgid "Sort by"
msgstr "Ordenar por"

#: tutor/dashboard/assignments/submitted.php:64
msgid "Sort By:"
msgstr "Ordenar Por:"

#: inc/acf/fields/query-control-field.php:175
#: inc/classes/class-tgm-plugin-activation.php:2669
msgid "Source"
msgstr "Fonte"

#: inc/acf/fields/carousel-control-field.php:227
msgid "Space Between Slides"
msgstr "Espaço Entre Slides"

#: inc/acf/fields/fields.php:370
msgid "Specify the style used to render the selected fields"
msgstr "Especifique o estilo usado para renderizar os campos selecionados"

#: tutor/dashboard/create-course.php:187
msgid "Standard size for the course thumbnail is 700x430."
msgstr "O tamanho padrão da miniatura do curso é 700x430."

#: tutor/single/assignment/content.php:700
msgid "Start Assignment Submit"
msgstr "Iniciar Envio Da Tarefa"

#: templates/courses/single-course-enrolled.php:132
#: tutor/single/course/add-to-cart.php:71
#: tutor/single/course/course-entry-box.php:80
msgid "Start Learning"
msgstr "Comece A Aprender"

#: learnpress/single-course/buttons/enroll.php:28
msgid "Start Now"
msgstr "Comece Agora"

#: tutor/single/quiz/previous-attempts.php:48 tutor/single/quiz/top.php:132
msgid "Start Quiz"
msgstr "Iniciar Quiz"

#: woocommerce/cart/shipping-calculator.php:50
#: woocommerce/cart/shipping-calculator.php:55
#: woocommerce/cart/shipping-calculator.php:67
msgid "State / County"
msgstr "Estado / Município"

#: tutor/dashboard/earning/report.php:45
#: tutor/dashboard/earning/statements.php:43
#: tutor/dashboard/earning/statements.php:127 tutor/dashboard/earning.php:50
msgid "Statements"
msgstr "Declarações"

#: inc/classes/class-tgm-plugin-activation.php:2675
#: learnpress/profile/tabs/orders/list.php:35 tutor/dashboard/dashboard.php:236
#: tutor/dashboard/purchase_history.php:118 tutor/dashboard/withdraw.php:210
msgid "Status"
msgstr "Status"

#: woocommerce/wishlist-view.php:119
msgid "Stock status"
msgstr "Status do estoque"

#: woocommerce/wishlist-view-mobile.php:186
msgid "Stock:"
msgstr "Estoque:"

#: inc/tutor/geeks-tutor-template-functions.php:714
#: woocommerce/myaccount/navigation.php:66
msgid "Store"
msgstr "Loja"

#: tutor/dashboard/assignments/review.php:55
#: tutor/dashboard/assignments/submitted.php:77
#: tutor/dashboard/question-answer.php:46 tutor/public-profile.php:152
#: tutor/student-public-profile.php:144
msgid "Student"
msgstr "Estudante"

#: inc/gke/geeks-gke-template-functions.php:76
#: inc/learnpress/functions/instructor.php:99
#: inc/learnpress/functions/instructor.php:122
#: inc/tutor/functions/single-course.php:68
#: inc/tutor/geeks-tutor-functions.php:41
msgid "Students"
msgstr "Estudantes"

#: inc/learnpress/template-functions/tax.php:22
msgctxt "students enrolled"
msgid "%s student is learning %s."
msgid_plural "%s students are learning %s."
msgstr[0] " %s  aluno está aprendendo %s."
msgstr[1] " %s  os alunos estão aprendendo %s."

#: tutor/dashboard/assignments/submitted.php:45
msgid "Submission Deadline"
msgstr "Prazo Para Envio"

#: comments.php:95 inc/template-functions/posts.php:67
msgid "Submit"
msgstr "Enviar"

#: tutor/single/quiz/parts/question.php:142
msgid "Submit &amp; Next"
msgstr "Submeter & Seguinte"

#: tutor/single/assignment/content.php:387
msgid "Submit Assignment"
msgstr "Enviar Exercício"

#: woocommerce/product-searchform.php:28
msgctxt "submit button"
msgid "Search"
msgstr "Pesquisar"

#: tutor/dashboard/create-course.php:204
msgid "Submit for Review"
msgstr "Enviar para revisão"

#: job_manager/job-preview.php:48
msgid "Submit Listing"
msgstr "Enviar Listagem"

#: tutor/single/quiz/parts/question.php:142
#: tutor/single/quiz/parts/question.php:160
msgid "Submit Quiz"
msgstr "Enviar Quiz"

#: tutor/dashboard/withdraw.php:182
msgid "Submit Request"
msgstr "Enviar Solicitação"

#: templates/single-course/tabs/review.php:158
#: tutor/single/course/review-form.php:42
msgid "Submit Review"
msgstr "Enviar Revisão"

#: tutor/dashboard/assignments/review.php:59
msgid "Submitted Date"
msgstr "Data De Envio"

#: edd_templates/checkout_cart.php:102 edd_templates/shortcode-receipt.php:98
#: learnpress/checkout/review-order.php:89
#: learnpress/order/order-details.php:84 woocommerce/cart/cart-totals.php:31
#: woocommerce/cart/cart-totals.php:32 woocommerce/cart/cart.php:42
#: woocommerce/cart/cart.php:128 woocommerce/checkout/review-order.php:33
#: woocommerce/checkout/review-order.php:65
msgid "Subtotal"
msgstr "Subtotal"

#: tutor/dashboard/announcements/create.php:55
#: tutor/dashboard/announcements/update.php:54
msgid "Summary"
msgstr "Sumário"

#: tutor/dashboard/announcements/create.php:57
#: tutor/dashboard/announcements/update.php:56
msgid "Summary..."
msgstr "Sumário..."

#. translators: %s: layout name
#: inc/learnpress/template-functions/loop.php:28
#: inc/woocommerce/template-functions/product-archive.php:93
msgid "Switch to %s"
msgstr "Mudar para %s"

#: inc/acf/fields/fields.php:376
msgid "Table"
msgstr "Tabela"

#: learnpress/single-course/tabs/curriculum.php:38
#: tutor/single/lesson/lesson_sidebar.php:62
msgid "Table of Contents"
msgstr "Sumário"

#: tutor/course-filter/filters.php:93
msgid "Tag"
msgstr "Tag"

#: woocommerce/single-product/meta.php:47
msgid "Tag:"
msgid_plural "Tags:"
msgstr[0] "Tag:"
msgstr[1] "Tags:"

#: tutor/single/course/tags.php:20
msgid "Tags"
msgstr "Tags"

#: edd_templates/checkout_cart.php:120 edd_templates/shortcode-receipt.php:90
msgid "Tax"
msgstr "Fiscal"

#: inc/acf/settings/blog-tax.php:11
msgid "Taxonomy Options - Geeks"
msgstr "Opções De Taxonomia-Geeks"

#: tutor/dashboard/instructor/apply_for_instructor.php:45
msgid ""
"Tell us your qualifications, show us your passion, and begin\n"
"\t\t\tteaching with us!"
msgstr ""
"Conte - nos suas qualificações, mostre-nos sua paixão e comece a ensinar "
"conosco!"

#: learnpress/single-course/loop-section.php:94
msgctxt "template title empty"
msgid "Untitled"
msgstr "Sem título"

#: inc/acf/fields/query-control-field.php:209
#: inc/acf/fields/query-control-field.php:224
msgid "Term"
msgstr "Termo"

#: tutor/dashboard/instructor/registration.php:167
#: tutor/dashboard/registration.php:169
msgid "Terms and Conditions"
msgstr "Termos e Condições"

#: learnpress/checkout/term-conditions.php:22
msgid "Terms of Service"
msgstr "Termos de Serviço"

#: learnpress/addons/stripe-payment/form.php:60
msgid ""
"Test mode is enabled. You can use the card number **************** with any "
"CVC and a valid expiration date for testing purpose."
msgstr ""
"O modo de teste está ativado. Você pode usar o número do cartão "
"*************** com qualquer CVC e uma data de validade válida para fins de "
"teste."

#. translators: %s: version number
#: inc/classes/class-tgm-plugin-activation.php:2103
msgid "TGMPA v%s"
msgstr "TGMPA v%s"

#: tutor/dashboard/create-course.php:140
msgid "Thank You!"
msgstr "Obrigado!"

#: woocommerce/checkout/thankyou.php:51
msgid "Thank you."
msgstr "Obrigada."

#: learnpress/checkout/order-received.php:54
#: woocommerce/checkout/thankyou.php:105
msgid "Thank you. Your order has been received."
msgstr "Obrigada. Seu pedido foi recebido."

#: tutor/dashboard/settings/profile.php:172
msgid ""
"The display name is shown in all public fields, such as the author name, "
"instructor name, student name, and name that will be printed on the "
"certificate."
msgstr ""
"O nome de exibição é mostrado em todos os campos públicos, como o nome do "
"autor, nome do instrutor, nome do aluno e nome que será impresso no "
"certificado."

#: woocommerce/myaccount/my-address.php:46
msgid "The following addresses will be used on the checkout page by default."
msgstr "Os endereços a seguir serão usados na página de checkout por padrão."

#: inc/classes/class-tgm-plugin-activation.php:395
#: inc/classes/class-tgm-plugin-activation.php:3073
msgid "The following plugin was activated successfully:"
msgstr "O seguinte plugin foi ativado com sucesso:"

#: woocommerce/notices/error.php:32
msgid "The following problems were found:"
msgstr "Foram encontrados os seguintes problemas:"

#: inc/classes/class-tgm-plugin-activation.php:3632
msgid ""
"The installation and activation process is starting. This process may take a "
"while on some hosts, so please be patient."
msgstr ""
"O processo de instalação e ativação está sendo iniciado. Esse processo pode "
"demorar um pouco em alguns hosts, portanto, seja paciente."

#. translators: 1: plugin name.
#: inc/classes/class-tgm-plugin-activation.php:3628
msgid "The installation of %1$s failed."
msgstr "A instalação do %1$s falhou."

#: inc/classes/class-tgm-plugin-activation.php:3640
msgid ""
"The installation process is starting. This process may take a while on some "
"hosts, so please be patient."
msgstr ""
"O processo de instalação está começando. Esse processo pode demorar um pouco "
"em alguns hosts, portanto, seja paciente."

#: tutor/single/course/enrolled/announcements.php:51
msgid ""
"The instructor hasn’t added any announcements to this course yet. "
"Announcements are used to inform you of updates or additions to the course."
msgstr ""
"O instrutor ainda não adicionou nenhum anúncio a este curso. Os anúncios são "
"usados para informá-lo sobre atualizações ou adições ao curso."

#: tutor/dashboard/withdraw.php:106
msgid "The preferred payment method is selected as %s. "
msgstr ""

#: inc/classes/class-tgm-plugin-activation.php:1032
msgid ""
"The remote plugin package consists of more than one file, but the files are "
"not packaged in a folder."
msgstr ""
"O pacote de plug-in remoto consiste em mais de um arquivo, mas os arquivos "
"não são empacotados em uma pasta."

#: inc/classes/class-tgm-plugin-activation.php:1022
msgid ""
"The remote plugin package does not contain a folder with the desired slug "
"and renaming did not work."
msgstr ""
"O pacote de plug-ins remotos não contém uma pasta com o slug desejado e a "
"renomeação não funcionou."

#: edd_templates/shortcode-receipt.php:12
msgid "The specified receipt ID appears to be invalid"
msgstr "O ID do recibo especificado parece ser inválido"

#: job_manager/content-no-jobs-found.php:24
msgid "There are currently no vacancies."
msgstr "Atualmente não há vagas."

#: job_manager/content-no-jobs-found.php:22
msgid "There are no listings matching your search."
msgstr "Não há anúncios correspondentes à sua pesquisa."

#: woocommerce/single-product-reviews.php:62
msgid "There are no reviews yet."
msgstr "Ainda não há comentários."

#: woocommerce/cart/cart-shipping.php:69
msgid ""
"There are no shipping options available. Please ensure that your address has "
"been entered correctly, or contact us if you need any help."
msgstr ""
"Não há opções de envio disponíveis. Certifique-se de que seu endereço foi "
"digitado corretamente ou entre em contato conosco se precisar de alguma "
"ajuda."

#: inc/classes/class-tgm-plugin-activation.php:403
msgid ""
"There are one or more required or recommended plugins to install, update or "
"activate."
msgstr ""
"Há um ou mais plugins necessários ou recomendados para instalar, atualizar "
"ou ativar."

#: woocommerce/checkout/cart-errors.php:21
msgid ""
"There are some issues with the items in your cart. Please go back to the "
"cart page and resolve these issues before checking out."
msgstr ""
"Existem alguns problemas com os itens em seu carrinho. Volte para a página "
"do carrinho e resolva esses problemas antes de finalizar a compra."

#: tutor/dashboard/earning/statement.php:71
#: tutor/dashboard/earning/statements.php:211
msgid "There is not enough sales data to generate a statement"
msgstr "Não há dados de vendas suficientes para gerar um extrato"

#: tutor/dashboard/settings/withdraw-settings.php:153
msgid ""
"There's no Withdrawal method selected yet! To select a Withdraw method, "
"please contact the Site Admin."
msgstr ""
"Ainda não há nenhum método de Saque selecionado! Para selecionar um método "
"de Saque, entre em contato com o administrador do Site."

#: learnpress/profile/tabs/settings/privacy.php:20
msgid ""
"These controls give you the ability to customize what areas of your profile "
"others are able to see."
msgstr ""
"Esses controles permitem que você personalize quais áreas do seu perfil "
"outras pessoas podem ver."

#: inc/template-functions/single-post.php:448
msgid ""
"This content is password protected. To view it please enter your password "
"below:"
msgstr ""
"Este conteúdo é protegido por senha. Para visualizá - lo digite sua senha "
"abaixo:"

#: learnpress/single-course/tabs/tabs.php:48
msgid "This course has been blocked reason by expire"
msgstr "Este curso foi bloqueado motivo por expirar"

#: tutor/single/course/course-entry-box.php:95
msgid ""
"This course is full right now. We limit the number of students to create an "
"optimized and productive group dynamic."
msgstr ""
"Este curso está cheio agora. Limitamos o número de alunos para criar uma "
"dinâmica de grupo otimizada e produtiva."

#. translators: %1$s - The demo name. %2$s - Minutes.
#: inc/ocdi/class-geeks-ocdi.php:265
msgid "This demo will import %1$s. It may take %2$s"
msgstr "Esta demonstração importará %1$s. pode levar %2$s"

#: job_manager/content-single-job_listing.php:22
msgid "This listing has expired."
msgstr "Este anúncio expirou."

#: tutor/dashboard/earning/earning-report-top-menu.php:16
msgid "This Month"
msgstr "Este Mês"

#: inc/classes/class-tgm-plugin-activation.php:607
msgid "This plugin needs to be updated to be compatible with your theme."
msgstr "Este plugin precisa ser atualizado para ser compatível com o seu tema."

#: woocommerce/single-product/add-to-cart/variable.php:34
msgid "This product is currently out of stock and unavailable."
msgstr "Este produto está esgotado e indisponível no momento."

#: inc/customizer/class-geeks-customizer.php:1043
msgid ""
"This section contains settings related to posts listing archives and single "
"post."
msgstr ""
"Esta seção contém configurações relacionadas a postagens listando arquivos e "
"postagem única."

#: inc/learnpress/class-geeks-learnpress-customizer.php:47
msgid "This section contains settings related to single article"
msgstr "Esta seção contém configurações relacionadas ao artigo único"

#: inc/customizer/class-geeks-customizer.php:582
msgid "This setting allows you to add  button css"
msgstr "Essa configuração permite adicionar css de botão"

#: inc/gke/class-geeks-gke-customizer.php:74
#: inc/gke/class-geeks-gke-customizer.php:90
msgid "This setting allows you to add a description"
msgstr "Essa configuração permite adicionar uma descrição"

#: inc/gke/class-geeks-gke-customizer.php:58
msgid "This setting allows you to add a title"
msgstr "Essa configuração permite adicionar um título"

#: inc/customizer/class-geeks-customizer.php:870
msgid "This setting allows you to change button url."
msgstr "Essa configuração permite que você altere a url do botão."

#: inc/customizer/class-geeks-customizer.php:474
msgid "This setting allows you to change the button link"
msgstr "Esta configuração permite que você altere o link do botão"

#: inc/customizer/class-geeks-customizer.php:508
#: inc/customizer/class-geeks-customizer.php:814
msgid "This setting allows you to change the button text"
msgstr "Esta configuração permite que você altere o texto do botão"

#: inc/customizer/class-geeks-customizer.php:615
msgid "This setting allows you to choose your button color"
msgstr "Esta configuração permite que você escolha a cor do seu botão"

#: inc/customizer/class-geeks-customizer.php:542
msgid "This setting allows you to choose your button size"
msgstr "Esta configuração permite que você escolha o tamanho do seu botão"

#: inc/customizer/class-geeks-customizer.php:437
msgid "This setting allows you to choose your button variant"
msgstr "Esta configuração permite que você escolha sua variante de botão"

#: inc/customizer/class-geeks-customizer.php:915
msgid "This setting allows you to choose your footer type."
msgstr "Essa configuração permite que você escolha o tipo de rodapé."

#: inc/customizer/class-geeks-customizer.php:141
msgid "This setting allows you to choose your header type."
msgstr "Essa configuração permite que você escolha seu tipo de cabeçalho."

#: inc/learnpress/class-geeks-learnpress-customizer.php:76
msgid "This setting allows you to choose your single course version."
msgstr "Essa configuração permite que você escolha sua versão de curso único."

#: inc/customizer/class-geeks-customizer.php:169
msgid ""
"This setting allows you to control header width. Default is fluid container."
msgstr ""
"Essa configuração permite controlar a largura do cabeçalho. O padrão é "
"recipiente de fluido."

#: inc/customizer/class-geeks-customizer.php:1209
msgid ""
"This setting allows you to enable / disable newsletter form in Single Post"
msgstr ""
"Esta configuração permite ativar / desativar o formulário de newsletter em "
"postagem única"

#: inc/customizer/class-geeks-customizer.php:1131
msgid "This setting allows you to enable or disable full width sticky post."
msgstr ""
"Essa configuração permite ativar ou desativar a postagem fixa de largura "
"total."

#: inc/customizer/class-geeks-customizer.php:1072
msgid "This setting allows you to enable or disable sidebar widget area."
msgstr ""
"Esta configuração permite ativar ou desativar a área de widget da barra "
"lateral."

#: inc/customizer/class-geeks-customizer.php:668
msgid "This setting allows you to enable or disable sticky header."
msgstr "Essa configuração permite ativar ou desativar o sticky header."

#: inc/customizer/class-geeks-customizer.php:1157
msgid "This setting allows you to enable related posts"
msgstr "Essa configuração permite ativar postagens relacionadas"

#: inc/customizer/class-geeks-customizer.php:1182
msgid "This setting allows you to enable social share"
msgstr "Essa configuração permite ativar o compartilhamento social"

#: inc/wpjm/class-geeks-wpjm-customizer.php:83
msgid "This setting allows you to show or hide application form."
msgstr ""
"Essa configuração permite mostrar ou ocultar o formulário de inscrição."

#: inc/customizer/class-geeks-customizer.php:980
msgid ""
"This setting allows you to show or hide footer white background in Footer."
msgstr ""
"Esta configuração permite mostrar ou ocultar o fundo branco do rodapé no "
"rodapé."

#: inc/customizer/class-geeks-customizer.php:945
msgid "This setting allows you to show or hide footer widgets in Footer."
msgstr ""
"Essa configuração permite mostrar ou ocultar widgets de rodapé no rodapé."

#: inc/customizer/class-geeks-customizer.php:734
msgid "This setting allows you to upload an image for 404 page."
msgstr ""
"Esta configuração permite que você carregue uma imagem para a página 404."

#: inc/customizer/class-geeks-customizer.php:1467
msgid "This settings allow you to apply your custom color option."
msgstr ""
"Essas configurações permitem que você aplique sua opção de cor personalizada."

#: learnpress/pages/profile.php:36
msgid "This user does not public their profile."
msgstr "Este usuário não publica seu perfil."

#: tutor/dashboard/earning/earning-report-top-menu.php:18
msgid "This Week"
msgstr "Esta Semana"

#: woocommerce/myaccount/form-edit-account.php:41
msgid ""
"This will be how your name will be displayed in the account section and in "
"reviews"
msgstr "Será assim que seu nome será exibido na seção Conta e nas avaliações"

#: tutor/dashboard/earning/earning-report-top-menu.php:14
msgid "This Year"
msgstr "Este Ano"

#: tutor/single/quiz/top.php:88
msgid "Time"
msgstr "Tempo"

#: tutor/single/assignment/content.php:172
msgid "Time Duration : "
msgstr ""

#: tutor/single/quiz/parts/meta.php:59
msgid "Time remaining: "
msgstr ""

#: inc/acf/fields/query-control-field.php:187
#: learnpress/addons/course-review/review-form.php:47
msgid "Title"
msgstr "Título"

#: inc/tutor/geeks-tutor-functions.php:23
msgid "Title (A &rarr; Z)"
msgstr "Título (A ❷ Z)"

#: inc/tutor/geeks-tutor-functions.php:24
msgid "Title (Z &rarr; A)"
msgstr "Titulo (Z₃ A)"

#. translators: 1: number of plugins.
#: inc/classes/class-tgm-plugin-activation.php:2521
msgid "To Activate <span class=\"count\">(%s)</span>"
msgid_plural "To Activate <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#. translators: 1: number of plugins.
#: inc/classes/class-tgm-plugin-activation.php:2513
msgid "To Install <span class=\"count\">(%s)</span>"
msgid_plural "To Install <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: tutor/dashboard/purchase_history.php:51
msgid "Today"
msgstr "Hoje"

#: inc/tutor/geeks-tutor-template-functions.php:421
msgid "Toggle navigation"
msgstr "Toggle navigation"

#: edd_templates/checkout_cart.php:129
#: learnpress/checkout/order-received.php:121
#: learnpress/checkout/review-order.php:103
#: learnpress/order/order-details.php:29 learnpress/order/order-details.php:88
#: learnpress/profile/tabs/orders/list.php:34
#: woocommerce/cart/cart-totals.php:99 woocommerce/cart/cart-totals.php:100
#: woocommerce/cart/mini-cart.php:107 woocommerce/checkout/review-order.php:112
#: woocommerce/order/order-details.php:56
msgid "Total"
msgstr "Total"

#: tutor/single/quiz/parts/meta.php:21 tutor/single/quiz/top.php:98
msgid "Total Attempted"
msgstr "Total Tentado"

#: learnpress/profile/tabs/courses/general-statistic.php:63
#: tutor/dashboard/dashboard.php:206
msgid "Total Courses"
msgstr "Total De Cursos"

#: tutor/dashboard/dashboard.php:214
msgid "Total Earnings"
msgstr "Ganhos Totais"

#: tutor/single/course/course-entry-box.php:37
msgid "Total Enrolled"
msgstr "Total Inscritos"

#: tutor/single/assignment/content.php:345
msgid "Total File Size: Max"
msgstr "Tamanho Total Do Arquivo: Max"

#: tutor/dashboard/assignments.php:79 tutor/single/assignment/content.php:462
msgid "Total Marks"
msgstr "Total De Notas"

#: learnpress/addons/course-review/course-rate.php:25
msgctxt "total number of reviews"
msgid "Based on %s review"
msgid_plural "Based on %s reviews"
msgstr[0] "Baseado em  %s  review"
msgstr[1] "Baseado em  %s  reviews"

#: tutor/dashboard/withdraw.php:87
msgid "Total Pending Withdrawal %s"
msgstr "Total de saques Pendentes %s"

#: tutor/dashboard/assignments/submitted.php:49
#: tutor/dashboard/assignments/submitted.php:78
msgid "Total Points"
msgstr "Total De Pontos"

#: tutor/single/assignment/content.php:202
msgid "Total Points : "
msgstr ""

#: edd_templates/shortcode-receipt.php:105
msgid "Total Price"
msgstr "Preço Total"

#: learnpress/profile/tabs/courses/general-statistic.php:71
#: tutor/dashboard/dashboard.php:198 tutor/public-profile.php:152
#: tutor/student-public-profile.php:144
msgid "Total Students"
msgstr "Total De Alunos"

#: tutor/dashboard/assignments.php:80
msgid "Total Submits"
msgstr "Total Submete"

#: woocommerce/checkout/order-receipt.php:33
#: woocommerce/checkout/thankyou.php:78
msgid "Total:"
msgstr "Total:"

#: woocommerce/checkout/form-pay.php:28
msgid "Totals"
msgstr "Totais"

#: inc/tutor/class-geeks-tutor.php:200
msgid "Tumblr"
msgstr "Tumblr"

#: tutor/dashboard/instructor/apply_for_instructor.php:23
msgid ""
"Tutor LMS can be used to edit content built using that extension.\n"
"\t\t\t\tIt cannot edit layouts made before."
msgstr ""
"Tutor LMS pode ser usado para editar conteúdo construído usando essa "
"extensão.  Não é possível editar layouts feitos antes."

#: inc/tutor/class-geeks-tutor.php:176 inc/tutor/class-geeks-tutor.php:198
msgid "Twitter"
msgstr "Twitter"

#: inc/classes/class-tgm-plugin-activation.php:2670
#: tutor/dashboard/earning/statement.php:54
#: tutor/dashboard/earning/statement.php:60
#: tutor/dashboard/earning/statements.php:192
#: tutor/dashboard/earning/statements.php:199
msgid "Type"
msgstr "Tipo"

#: woocommerce/checkout/thankyou.php:37
msgid ""
"Unfortunately your order cannot be processed as the originating "
"bank/merchant has declined your transaction. Please attempt your purchase "
"again."
msgstr ""
"Infelizmente, seu pedido não pode ser processado, pois o banco/comerciante "
"de origem recusou sua transação. Por favor, tente sua compra novamente."

#: woocommerce/wishlist-view.php:101
msgid "Unit price"
msgstr "Preço unitário"

#: inc/classes/class-tgm-plugin-activation.php:2844
#: tutor/dashboard/announcements/update.php:63
#: tutor/dashboard/settings/profile.php:90
#: woocommerce/cart/shipping-calculator.php:86
msgid "Update"
msgstr "Atualização"

#. translators: %2$s: plugin name in screen reader markup
#: inc/classes/class-tgm-plugin-activation.php:2729
msgid "Update %2$s"
msgstr "Atualização %2$s"

#: tutor/dashboard/announcements/update.php:19
msgid "Update Announcement"
msgstr "Anúncio De Atualização"

#. translators: 1: number of plugins.
#: inc/classes/class-tgm-plugin-activation.php:2517
msgid "Update Available <span class=\"count\">(%s)</span>"
msgid_plural "Update Available <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: woocommerce/cart/cart.php:181 woocommerce/cart/cart.php:183
msgid "Update cart"
msgstr "Atualizar carrinho"

#: tutor/dashboard/settings/profile.php:199
msgid "Update Profile"
msgstr "Atualizar Perfil"

#: inc/classes/class-tgm-plugin-activation.php:2454
msgid "Update recommended"
msgstr "Atualização recomendada"

#: inc/classes/class-tgm-plugin-activation.php:608
msgid "Update Required"
msgstr "Atualização Necessária"

#: tutor/dashboard/reviews/edit-review-form.php:29
#: tutor/dashboard/reviews/given-reviews.php:128
msgid "Update Review"
msgstr "Revisão De Atualização"

#: woocommerce/checkout/payment.php:46
msgid "Update totals"
msgstr "Atualizar totais"

#. translators: 1: plugin name, 2: action number 3: total number of actions.
#: inc/classes/class-tgm-plugin-activation.php:3623
msgid "Updating Plugin %1$s (%2$d/%3$d)"
msgstr "Atualizando o Plugin %1$s (%2$d/%3$d)"

#. translators: %s: plugin name.
#: inc/classes/class-tgm-plugin-activation.php:339
msgid "Updating Plugin: %s"
msgstr "Atualizando Plugin: %s"

#: inc/classes/class-tgm-plugin-activation.php:2805
msgid "Upgrade message from the plugin author:"
msgstr "Mensagem de atualização do autor do plugin:"

#: learnpress/profile/tabs/settings/avatar.php:53
msgid "Upload"
msgstr "Upload"

#: inc/customizer/class-geeks-customizer.php:73
msgid "Upload logo for dark header"
msgstr "Carregar logo para Dark header"

#: inc/ocdi/class-geeks-ocdi.php:276 inc/ocdi/class-geeks-ocdi.php:286
#: inc/ocdi/class-geeks-ocdi.php:296 inc/ocdi/class-geeks-ocdi.php:306
#: inc/ocdi/class-geeks-ocdi.php:316 inc/ocdi/class-geeks-ocdi.php:386
#: inc/ocdi/class-geeks-ocdi.php:397 inc/ocdi/class-geeks-ocdi.php:407
#: inc/ocdi/class-geeks-ocdi.php:417 inc/ocdi/class-geeks-ocdi.php:427
#: inc/ocdi/class-geeks-ocdi.php:437 inc/ocdi/class-geeks-ocdi.php:447
#: inc/ocdi/class-geeks-ocdi.php:457
msgid "upto a minute"
msgstr "até um minuto"

#: learnpress/profile/tabs/settings/change-password.php:23
msgid "Use this form to change your account password."
msgstr "Utilize este formulário para alterar a senha da sua conta."

#: inc/learnpress/functions/profile.php:106
msgid "User Avatar"
msgstr "Avatar Do Usuário"

#: tutor/dashboard/settings/profile.php:137
msgid "User Name"
msgstr "Nome De Usuário"

#: inc/woocommerce/template-functions/my-account.php:119
#: inc/woocommerce/template-functions/my-account.php:120
#: learnpress/checkout/account-register.php:31
#: learnpress/checkout/account-register.php:32
#: learnpress/global/form-register.php:34
#: learnpress/global/form-register.php:35
#: tutor/dashboard/instructor/registration.php:103
#: tutor/dashboard/instructor/registration.php:106
#: tutor/dashboard/my-profile.php:57 tutor/dashboard/registration.php:103
#: tutor/dashboard/registration.php:106 tutor/global/login.php:64
#: tutor/login-form.php:60 woocommerce/myaccount/form-login.php:100
#: woocommerce/myaccount/form-login.php:101
msgid "Username"
msgstr "Username"

#: inc/template-functions/global.php:166
msgid "Username already taken"
msgstr "Nome de usuário já utilizado"

#: learnpress/checkout/account-login.php:29 learnpress/global/form-login.php:28
#: learnpress/global/form-lost-password.php:26
#: tutor/template-part/retrieve-password.php:41
#: woocommerce/global/form-login.php:60 woocommerce/myaccount/form-login.php:48
#: woocommerce/myaccount/form-lost-password.php:37
msgid "Username or email"
msgstr "Nome de usuário ou e-mail"

#: tutor/global/login.php:65 tutor/login-form.php:61
msgid "Username or Email Address"
msgstr "Nome de usuário ou endereço de E-mail"

#: tutor/dashboard/settings/profile.php:149
msgid "UX Designer"
msgstr "UX Designer"

#: templates/profile/header-dashboard.php:27 tutor/public-profile.php:95
#: tutor/student-public-profile.php:88
msgid "Verified"
msgstr "Verificado"

#: inc/woocommerce/template-functions/single-product.php:393
msgid "verified owner"
msgstr "proprietário verificado"

#: inc/classes/class-tgm-plugin-activation.php:2674
msgid "Version"
msgstr "Versão"

#: inc/acf/fields/carousel-control-field.php:208
msgid "Vertical"
msgstr "Vertical"

#: woocommerce/single-product-reviews.php:90
msgid "Very Poor"
msgstr "Muito Pobre"

#: tutor/dashboard/create-course.php:188
msgid "Video section controls the course overview video."
msgstr "A seção Vídeo controla o vídeo de visão geral do curso."

#: tutor/single/course/add-to-cart-woocommerce.php:26
msgid "View Cart"
msgstr "Ver Carrinho"

#: templates/courses/curriculum-module.php:45
msgid "View Chapter Details"
msgstr "Ver Detalhes Do Capítulo"

#: tutor/dashboard/create-course.php:39
msgid "View Course"
msgstr "Ver Curso"

#: templates/instructor/list.php:48 templates/single-course/instructor.php:48
msgid "View Details"
msgstr "Ver Detalhes"

#: edd_templates/history-purchases.php:43
msgid "View Details and Downloads"
msgstr "Ver detalhes e Downloads"

#: learnpress/profile/tabs/courses/course-grid.php:42
msgid "View more"
msgstr "Ver mais"

#: inc/learnpress/template-functions/single.php:188
#: templates/courses/single-course-enrolled.php:67
msgid "View Profile"
msgstr "Ver Perfil"

#: inc/learnpress/template-functions/profile.php:18
msgid "Want to become an instructor?"
msgstr "Quer se tornar um instrutor?"

#: woocommerce/checkout/thankyou.php:36
msgid "We are sorry!"
msgstr "Lamentamos!"

#: learnpress/checkout/order-received.php:35
msgid "We are sorry. We could not validate your order."
msgstr "Lamentamos. Não foi possível validar seu pedido."

#: comments.php:84 inc/tutor/class-geeks-tutor.php:161
msgid "Website"
msgstr "Site"

#: tutor/single/quiz/top.php:84
msgid "Week"
msgstr "Semana"

#: tutor/single/quiz/top.php:84
msgid "Weeks"
msgstr "Semanas"

#: tutor/single/course/course-benefits.php:25
msgid "What Will I Learn?"
msgstr "O Que Vou Aprender?"

#: templates/single-course/included.php:13
#: tutor/single/course/material-includes.php:30
msgid "What's included"
msgstr "O que está incluso"

#: woocommerce/single-product/meta.php:32
msgid "What’s included"
msgstr "O que está incluso"

#: learnpress/profile/tabs/orders/recover-order.php:21
msgid ""
"When you checkout with Guest order key will send in email, you can use order "
"key to create order."
msgstr ""
"Quando você finalizar a compra com a chave de pedido do convidado enviará um "
"e-mail, você pode usar a chave de pedido para criar o pedido."

#. translators: 1: column number, 2: row number
#: inc/class-geeks.php:423
msgid "Widgets added here will appear in column %1$d of footer row %2$d."
msgstr ""
"Os Widgets adicionados aqui aparecerão na coluna %1$d da linha de rodapé "
"%2$d."

#. translators: 1: column number
#: inc/class-geeks.php:417
msgid "Widgets added here will appear in column %1$d of the footer."
msgstr "Os Widgets adicionados aqui aparecerão na coluna %1$d do rodapé."

#: inc/wpjm/class-geeks-wpjm.php:91
msgid ""
"Widgets in this area will be shown under your single job listing, before "
"comments."
msgstr ""
"Os Widgets nesta área serão mostrados em sua lista de vagas única, antes dos "
"comentários."

#: inc/tutor/class-geeks-tutor.php:88 inc/woocommerce/integrations.php:17
msgid "Wishlist"
msgstr "Wishlist"

#. translators: 1: first course 2: last course 3: total courses
#: inc/template-functions/posts.php:278
msgctxt "with first and last %4$s"
msgid "Showing %1$d&ndash;%2$d of %3$d %4$d"
msgid_plural "Showing %1$d&ndash;%2$d of %3$d %5$s"
msgstr[0] "Mostrando %1$d - %2$d de %3$d %4$d"
msgstr[1] "Mostrando %1$d - %2$d de %3$d %5$s"

#. translators: 1: first result 2: last result 3: total results
#: woocommerce/loop/result-count.php:36
msgctxt "with first and last product"
msgid "Showing %1$d&ndash;%2$d of %3$d product"
msgid_plural "Showing %1$d&ndash;%2$d of %3$d products"
msgstr[0] "Mostrando %1$d - %2$d de %3$d produto"
msgstr[1] "Mostrando %1$d–%2$d de %3$d produtos"

#: inc/tutor/class-geeks-tutor.php:71 inc/woocommerce/integrations.php:49
#: tutor/dashboard/settings/nav-bar.php:30
msgid "Withdraw"
msgstr "Retirar"

#: tutor/dashboard/withdraw.php:137
msgid "Withdrawable Balance"
msgstr "Saldo Sacável"

#: tutor/dashboard/withdraw.php:60
msgid "Withdrawal"
msgstr "Desistência"

#: tutor/dashboard/withdraw.php:200
msgid "Withdrawal History"
msgstr "Histórico De Saques"

#: tutor/dashboard/withdraw.php:207
msgid "Withdrawal Method"
msgstr "Método De Saque"

#: tutor/dashboard/withdraw.php:94 tutor/dashboard/withdraw.php:132
msgid "Withdrawal Request"
msgstr "Solicitação De Saque"

#: tutor/dashboard/withdraw.php:40
msgid "Withdrawal request is pending for approval, please hold tight."
msgstr ""
"O pedido de retirada está pendente para aprovação, por favor, segure firme."

#: inc/tutor/class-geeks-tutor.php:125 inc/woocommerce/integrations.php:32
msgid "Withdrawals"
msgstr "Saques"

#: inc/classes/class-tgm-plugin-activation.php:2413
msgid "WordPress Repository"
msgstr "Repositório WordPress"

#: tutor/dashboard/assignments/review.php:132
msgid "Write a note"
msgstr "Escreva uma nota"

#: tutor/dashboard/assignments/review.php:136
msgid "Write a note to students about this submission"
msgstr "Escreva uma nota aos alunos sobre este envio"

#: learnpress/addons/course-review/review-form.php:15
#: learnpress/addons/course-review/review-form.php:23
#: templates/single-course/tabs/review.php:109
#: tutor/single/course/review-form.php:19
msgid "Write a review"
msgstr "Escreva um comentário"

#: templates/single-course/tabs/review.php:154
#: tutor/dashboard/reviews/edit-review-form.php:24
#: tutor/dashboard/reviews/given-reviews.php:121
#: tutor/single/course/review-form.php:38
msgid "write a review"
msgstr "Escreva um comentário"

#: tutor/single/quiz/parts/image-answer.php:15
msgid "Write your answer here"
msgstr "Escreva sua resposta aqui"

#: tutor/dashboard/purchase_history.php:61
msgid "Yearly"
msgstr "Anual"

#: inc/customizer/class-geeks-customizer.php:1472
#: inc/wpjm/class-geeks-wpjm-customizer.php:85
#: learnpress/global/lp-modal-overlay.php:24
msgid "Yes"
msgstr "Sim"

#: tutor/dashboard/my-courses.php:203
#: tutor/dashboard/reviews/given-reviews.php:147
msgid "Yes, Delete This"
msgstr "Sim, Delete This"

#: tutor/single/quiz/top.php:147
msgid "Yes, Skip This"
msgstr "Sim, Pule Isso"

#: tutor/dashboard/notifications/profile-completion.php:33
msgid "You are almost done!"
msgstr "Você está quase terminando!"

#: tutor/dashboard/logged-in.php:18
msgid "You are already logged in"
msgstr "Você já está logado"

#. translators: Placeholder %s is the username.
#: job_manager/account-signin.php:26
msgid "You are currently signed in as %s."
msgstr "No momento, você está conectado como  %s ."

#: job_manager/job-submit.php:24
msgid "You are editing an existing job. %s"
msgstr "Você está editando um trabalho existente. %s"

#: tutor/dashboard/withdraw.php:110
msgid "You can change your %1$s withdrawal preference %2$s"
msgstr "Você pode alterar sua preferência de retirada %1$s %2$s"

#: learnpress/profile/tabs/orders/list.php:22
msgid "You can find all of your orders here."
msgstr "Você pode encontrar todos os seus pedidos aqui."

#: tutor/dashboard/instructor/registration.php:28
#: tutor/dashboard/registration.php:28
msgid ""
"You do not have access to this area of the application. Please refer to your "
"system  administrator."
msgstr ""
"Você não tem acesso a esta área do aplicativo. Consulte o administrador do "
"sistema."

#: job_manager/job-dashboard.php:34
msgid "You do not have any active listings."
msgstr "Você não tem nenhuma listagem ativa."

#: tutor/dashboard/wishlist.php:46
msgid "You do not have any course on the wishlist yet."
msgstr "Ainda não tem nenhum curso na wishlist."

#: tutor/permission-denied.php:29
msgid "You don't have permission to access this page"
msgstr "Você não tem permissão para acessar esta página"

#: tutor/dashboard/create-course.php:35
msgid "You don't have the right to edit this course"
msgstr "Você não tem o direito de editar este curso"

#: templates/courses/single-course-enrolled.php:182
msgid "You enrolled this course on %s."
msgstr "Você matriculou este curso em  %s ."

#: learnpress/single-course/sidebar/user-time.php:42
msgid "You finished on:"
msgstr "Você terminou em:"

#: learnpress/single-course/tabs/tabs.php:43
msgid "You finished this course. This course has been blocked"
msgstr "Você terminou este curso. Este curso foi bloqueado"

#: tutor/dashboard/withdraw.php:81
msgid "You have %1$s %2$s %3$s and this is insufficient balance to withdraw"
msgstr "Você tem %1$s %2$s %3$s e isso é saldo insuficiente para sacar"

#: tutor/dashboard/withdraw.php:78
msgid "You have %1$s %2$s %3$s ready to withdraw now"
msgstr "Você tem %1$s %2$s %3$s pronto para sacar agora"

#: learnpress/content-lesson/button-complete.php:31
msgid "You have completed this lesson at "
msgstr ""

#: tutor/dashboard/settings/profile.php:64
msgid "You have full control to manage your own account setting."
msgstr ""
"Você tem controle total para gerenciar sua própria configuração de conta."

#: tutor/single/assignment/content.php:224
#: tutor/single/assignment/content.php:569
msgid ""
"You have missed the submission deadline. Please contact the instructor for "
"more information."
msgstr ""
"Você perdeu o prazo de envio. Entre em contato com o instrutor para mais "
"informações."

#: edd_templates/history-purchases.php:63
msgid "You have not made any purchases"
msgstr "Você não fez nenhuma compra"

#: woocommerce/myaccount/my-address.php:111
msgid "You have not set up this type of address yet."
msgstr "Você ainda não configurou esse tipo de endereço."

#: woocommerce/single-product/up-sells.php:26
msgid "You may also like&hellip;"
msgstr "Você pode gostar também…"

#: woocommerce/cart/cross-sells.php:24
msgid "You may be interested in&hellip;"
msgstr "Você pode estar interessado em…"

#. translators: %s opening and closing link tags respectively
#: woocommerce/single-product-reviews.php:126
msgid "You must be %1$slogged in%2$s to post a review."
msgstr "Você deve ser %1$slogged em%2$s para postar um comentário."

#: woocommerce/checkout/form-checkout.php:31
msgid "You must be logged in to checkout."
msgstr "Você deve estar logado para finalizar a compra."

#: job_manager/account-signin.php:54
msgid "You must sign in to create a new listing."
msgstr "Você deve entrar para criar uma nova listagem."

#: learnpress/single-course/sidebar/user-time.php:25
msgid "You started on:"
msgstr "Você começou em:"

#: job_manager/account-signin.php:21
msgid "Your account"
msgstr "Sua conta"

#: job_manager/account-signin.php:49
msgid "Your account details will be confirmed via email."
msgstr "Os dados da sua conta serão confirmados via e-mail."

#: edd_templates/history-purchases.php:3
msgid "Your account has been successfully verified!"
msgstr "Sua conta foi verificada com sucesso!"

#. translators: %s: date on which application was made
#: inc/tutor/geeks-tutor-functions.php:100
msgid "Your Application is pending from %s"
msgstr "Seu aplicativo está pendente de %s"

#. translators: %s: date on which the application was rejected
#: tutor/dashboard.php:68
msgid "Your application to become an instructor was rejected on %s"
msgstr "Sua inscrição para se tornar um instrutor foi rejeitada em %s"

#: tutor/single/assignment/content.php:582
msgid "Your Assignment"
msgstr "Sua Atribuição"

#: tutor/dashboard/settings/profile.php:80
msgid "Your avatar"
msgstr "Seu avatar"

#: job_manager/job-filters.php:100
msgid ""
"Your browser does not support JavaScript, or it is disabled. JavaScript must "
"be enabled in order to view listings."
msgstr ""
"Seu navegador não suporta JavaScript, ou está desativado. JavaScript deve "
"estar habilitado para visualizar listagens."

#: inc/woocommerce/template-functions/cart.php:11
msgid "Your cart is currently empty."
msgstr "Seu carrinho está vazio no momento."

#: job_manager/job-dashboard.php:19
msgid "Your listings are shown in the table below."
msgstr "Suas listagens são mostradas na tabela abaixo."

#: learnpress/checkout/review-order.php:19
#: woocommerce/checkout/review-order.php:23
msgid "Your order"
msgstr "Seu pedido"

#: woocommerce/checkout/thankyou.php:52
msgid "Your order has been received."
msgstr "Seu pedido foi recebido."

#: tutor/dashboard/assignments/review.php:123
msgid "Your Points"
msgstr "Seus Pontos"

#: tutor/single/assignment/content.php:83 tutor/single/lesson/content.php:76
#: tutor/single-quiz.php:49
msgid "Your Progress:"
msgstr "Seu Progresso:"

#: woocommerce/single-product-reviews.php:93
msgid "Your rating"
msgstr "Sua avaliação"

#: inc/woocommerce/template-functions/single-product.php:420
msgid "Your review is awaiting approval"
msgstr "Sua avaliação está aguardando aprovação"

#: inc/tutor/class-geeks-tutor.php:140
msgid "Zoom"
msgstr "Zoom"

#: inc/customizer/geeks-customizer-functions.php:16
msgid "— Select —"
msgstr "- Selecione —"
