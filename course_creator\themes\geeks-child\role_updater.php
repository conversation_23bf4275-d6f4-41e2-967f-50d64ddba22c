<?php
function adicionar_role_caminho_selecionado() {
    // Função JavaScript para realizar a chamada AJAX ao clicar no botão
    ?>
    <script type="text/javascript">
        function processarCandidatos() {
            if (confirm('Tem certeza que deseja processar todos os candidatos?')) {
                jQuery.post(
                    '<?php echo admin_url('admin-ajax.php'); ?>',
                    {
                        'action': 'processar_candidatos'
                    },
                    function(response) {
                        alert('Processamento concluído: ' + response);
                    }
                );
            }
        }
    </script>
    <?php
    return '<button onclick="processarCandidatos()">Processar Candidatos</button>';
}
add_shortcode('botao_processar_candidatos', 'adicionar_role_caminho_selecionado');

function processar_candidatos_callback() {

    $candidatos = get_users(array(
        'role' => 'candidato'
    ));

    $roles_map = array(
        'programador' => 'aluno_programacao',
        'designer' => 'aluno_design',
        'wordpress' => 'aluno_wordpress',
        'trafego' => 'aluno_marketing',
        'editor' => 'aluno_edio_de_vdeo'
    );

    $processados = 0;

    foreach ($candidatos as $candidato) {
        $caminho_selecionado = get_user_meta($candidato->ID, 'caminho_selecionado', true);
        
        if (isset($roles_map[$caminho_selecionado])) {
            $nova_role = $roles_map[$caminho_selecionado];
            $candidato->add_role($nova_role);
            $processados++;
        }
    }

    echo $processados . ' usuários processados.';
    wp_die();
}
add_action('wp_ajax_processar_candidatos', 'processar_candidatos_callback');

function export_alunos_csv() {
    if (isset($_GET['export_alunos_csv'])) {
        global $wpdb;

        $filename = 'alunos.csv';
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');

        $output = fopen('php://output', 'w');
        fputcsv($output, ['ID', 'Nome', 'Email', 'Matriculou num curso']);

        // Obtém todos os usuários com qualquer role que contenha "aluno_"
        $args = [
            'orderby' => 'ID',
            'order' => 'ASC',
            'meta_query' => [
                [
                    'key'     => $wpdb->prefix . 'capabilities',
                    'value'   => 'aluno_',
                    'compare' => 'LIKE',
                ]
            ]
        ];
        $user_query = new WP_User_Query($args);
        $users = $user_query->get_results();

        foreach ($users as $user) {
            $user_id = $user->ID;
            $user_name = $user->display_name;
            $user_email = $user->user_email;

            // Verifica se o usuário é autor de algum post do tipo 'tutor_enrolled'
            $enrolled = get_posts([
                'post_type' => 'tutor_enrolled',
                'author' => $user_id,
                'post_status' => 'completed',
                'numberposts' => 1,
            ]);

            $matriculou = !empty($enrolled) ? 'Sim' : 'Não';

            fputcsv($output, [$user_id, $user_name, $user_email, $matriculou]);
        }

        fclose($output);
        exit();
    }
}

add_action('init', 'export_alunos_csv');

function download_alunos_button_shortcode() {
    $button = '<a href="' . esc_url(add_query_arg('export_alunos_csv', 'true')) . '" class="button">Baixar Lista de Alunos</a>';
    return $button;
}

add_shortcode('download_alunos_button', 'download_alunos_button_shortcode');

// Função para alterar o nome de usuário
function update_usernames() {
    // Verifica a permissão do usuário atual
    if (!current_user_can('manage_options')) {
        return 'Você não tem permissão para realizar esta ação.';
    }

    // Roles específicas
    $roles = ['aluno_programacao', 'aluno_design', 'aluno_edio_de_vdeo', 'aluno_marketing', 'aluno_wordpress'];

    // Recupera todos os usuários com as roles especificadas
    $users = get_users([
        'role__in' => $roles
    ]);

    // Altera o nome de usuário para "Nome Sobrenome"
    foreach ($users as $user) {
        
        $first_name = get_user_meta($user->ID, 'nome', true);
        $last_name = get_user_meta($user->ID, 'sobrenome', true);
        
        
        if ($first_name && $last_name) {
            $new_display_name = $first_name . ' ' . $last_name;
            wp_update_user([
                'ID' => $user->ID,
                'user_login' => $new_display_name,
                'display_name' => $new_display_name,
                'nickname' => $new_display_name,
                'first_name' => $first_name,
                'last_name' => $last_name
            ]);
        }
    }

    return 'Nomes de usuários atualizados com sucesso!';
}

// Função do Shortcode que exibe o botão
function update_usernames_button_shortcode() {
    // HTML do botão com JavaScript para requisição AJAX
    ob_start(); ?>
    <button id="update_usernames_button">Atualizar Nomes de Usuários</button>
    <div id="update_usernames_result"></div>
    <script type="text/javascript">
        document.getElementById('update_usernames_button').addEventListener('click', function() {
            var resultDiv = document.getElementById('update_usernames_result');
            resultDiv.innerHTML = 'Atualizando...';

            fetch('<?php echo admin_url('admin-ajax.php?action=update_usernames'); ?>', {
                method: 'POST',
                credentials: 'same-origin'
            })
            .then(response => response.text())
            .then(data => resultDiv.innerHTML = data)
            .catch(error => resultDiv.innerHTML = 'Ocorreu um erro: ' + error);
        });
    </script>
    <?php
    return ob_get_clean();
}

// Adiciona o shortcode [update_usernames_button]
add_shortcode('update_usernames_button', 'update_usernames_button_shortcode');

// Adiciona ação para lidar com a requisição AJAX
add_action('wp_ajax_update_usernames', 'update_usernames');
add_action('wp_ajax_nopriv_update_usernames', 'update_usernames');

