<?php
/**
 * Debug Shortcodes Test Page
 * 
 * This page helps debug why shortcodes aren't working
 */

// Load WordPress
require_once('wp-config.php');
require_once(ABSPATH . 'wp-blog-header.php');

?>
<!DOCTYPE html>
<html>
<head>
    <title>Debug Shortcodes</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>Shortcode Debug Information</h1>

    <div class="debug-section">
        <h2>1. WordPress Environment</h2>
        <ul>
            <li><strong>WordPress Loaded:</strong> <span class="<?php echo defined('ABSPATH') ? 'success' : 'error'; ?>"><?php echo defined('ABSPATH') ? 'YES' : 'NO'; ?></span></li>
            <li><strong>Current User ID:</strong> <?php echo get_current_user_id(); ?></li>
            <li><strong>Is Admin:</strong> <?php echo is_admin() ? 'YES' : 'NO'; ?></li>
            <li><strong>Current Theme:</strong> <?php echo get_template(); ?></li>
        </ul>
    </div>

    <div class="debug-section">
        <h2>2. File Existence Check</h2>
        <?php
        $files_to_check = [
            'course-automation-loader.php' => get_stylesheet_directory() . '/tutor/course-automation-loader.php',
            'capabilities.php' => get_stylesheet_directory() . '/tutor/core/capabilities.php',
            'course-shortcodes.php' => get_stylesheet_directory() . '/tutor/shortcodes/course-shortcodes.php',
        ];
        
        echo '<ul>';
        foreach ($files_to_check as $name => $path) {
            $exists = file_exists($path);
            echo '<li><strong>' . $name . ':</strong> <span class="' . ($exists ? 'success' : 'error') . '">' . ($exists ? 'EXISTS' : 'MISSING') . '</span></li>';
            if (!$exists) {
                echo '<li style="margin-left: 20px; color: red;">Path: ' . $path . '</li>';
            }
        }
        echo '</ul>';
        ?>
    </div>

    <div class="debug-section">
        <h2>3. Function Availability</h2>
        <?php
        $functions_to_check = [
            'panapana_csv_course_creator_shortcode',
            'panapana_course_exporter_shortcode', 
            'panapana_hello_world_course_shortcode',
            'panapana_master_debug_shortcode',
            'panapana_user_can_access_automation'
        ];
        
        echo '<ul>';
        foreach ($functions_to_check as $function) {
            $exists = function_exists($function);
            echo '<li><strong>' . $function . ':</strong> <span class="' . ($exists ? 'success' : 'error') . '">' . ($exists ? 'EXISTS' : 'MISSING') . '</span></li>';
        }
        echo '</ul>';
        ?>
    </div>

    <div class="debug-section">
        <h2>4. Registered Shortcodes</h2>
        <?php
        global $shortcode_tags;
        $our_shortcodes = ['tutor_course_exporter', 'panapana_csv_course_creator', 'panapana_hello_world_course', 'panapana_master_debug'];
        
        echo '<ul>';
        foreach ($our_shortcodes as $shortcode) {
            $registered = isset($shortcode_tags[$shortcode]);
            echo '<li><strong>[' . $shortcode . ']:</strong> <span class="' . ($registered ? 'success' : 'error') . '">' . ($registered ? 'REGISTERED' : 'NOT REGISTERED') . '</span></li>';
        }
        echo '</ul>';
        
        echo '<h3>All Registered Shortcodes:</h3>';
        echo '<p class="info">Total: ' . count($shortcode_tags) . ' shortcodes registered</p>';
        ?>
    </div>

    <div class="debug-section">
        <h2>5. Manual Shortcode Test</h2>
        <p>Testing if shortcode functions work when called directly:</p>
        
        <?php if (function_exists('panapana_master_debug_shortcode')): ?>
            <h3>Debug Shortcode Output:</h3>
            <div style="border: 1px solid #ccc; padding: 10px; background: #f9f9f9;">
                <?php echo panapana_master_debug_shortcode(); ?>
            </div>
        <?php else: ?>
            <p class="error">Debug shortcode function not available</p>
        <?php endif; ?>
    </div>

    <div class="debug-section">
        <h2>6. WordPress do_shortcode Test</h2>
        <p>Testing WordPress shortcode processing:</p>
        
        <h3>Test: [panapana_master_debug]</h3>
        <div style="border: 1px solid #ccc; padding: 10px; background: #f9f9f9;">
            <?php echo do_shortcode('[panapana_master_debug]'); ?>
        </div>
    </div>

    <div class="debug-section">
        <h2>7. PHP Error Check</h2>
        <?php
        // Check for PHP errors
        $error_log_path = ini_get('error_log');
        echo '<p><strong>PHP Error Log:</strong> ' . ($error_log_path ? $error_log_path : 'Not configured') . '</p>';
        
        // Check if we can capture any errors
        ob_start();
        error_reporting(E_ALL);
        ini_set('display_errors', 1);
        
        // Try to load our files manually
        echo '<h3>Manual File Loading Test:</h3>';
        try {
            require_once get_stylesheet_directory() . '/tutor/course-automation-loader.php';
            echo '<p class="success">✅ Loader file loaded successfully</p>';
        } catch (Exception $e) {
            echo '<p class="error">❌ Error loading loader: ' . $e->getMessage() . '</p>';
        }
        
        $output = ob_get_clean();
        echo $output;
        ?>
    </div>

</body>
</html>
