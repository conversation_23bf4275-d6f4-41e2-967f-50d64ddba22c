<?php
function custom_signup_form_mulher() {
    
    function validaCPF($cpf) {
        $cpf = preg_replace('/\D/', '', $cpf);

        if (strlen($cpf) != 11) {
            return false;
        }

        if (preg_match('/(\d)\1{10}/', $cpf)) {
            return false;
        }

        for ($t = 9; $t < 11; $t++) {
            for ($d = 0, $c = 0; $c < $t; $c++) {
                $d += $cpf[$c] * (($t + 1) - $c);
            }
            $d = ((10 * $d) % 11) % 10;
            if ($cpf[$c] != $d) {
                return false;
            }
        }

        return true;
    }

    if (isset($_POST['submit'])) {
        $errors = [];

        if (empty($_POST['nome'])) {
            $errors[] = 'Nome é obrigatório.';
        }

        if (empty($_POST['sobrenome'])) {
            $errors[] = 'Sobrenome é obrigatório.';
        }

        if (!filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Email inválido.';
        }

        if (empty($_POST['senha'])) {
            $errors[] = 'Senha é obrigatória.';
        }

        if ($_POST['senha'] !== $_POST['confirmar_senha']) {
            $errors[] = 'As senhas não coincidem.';
        }

        if (!validaCPF($_POST['cpf'])) {
            $errors[] = 'CPF inválido.';
        }

        // Verificação de unicidade do CPF
        $cpf_meta_query = new WP_User_Query([
            'meta_key' => 'cpf',
            'meta_value' => $_POST['cpf'],
        ]);

        if (!empty($cpf_meta_query->get_results())) {
            $errors[] = 'CPF já cadastrado.';
        }

        if (empty($_POST['data_nascimento'])) {
            $errors[] = 'Data de Nascimento é obrigatória.';
        }

        if (empty($_POST['telefone']) || !preg_match('/^\(\d{2}\) \d{5}-\d{4}$/', $_POST['telefone'])) {
            $errors[] = 'Telefone inválido.';
        }

        if (empty($_POST['endereco'])) {
            $errors[] = 'Endereço é obrigatório.';
        }

        if (empty($_POST['bairro'])) {
            $errors[] = 'Bairro é obrigatório.';
        }

        echo '<ul class="errors">';
        if (!empty($errors)) {
            foreach ($errors as $error) {
                echo '<li class="erro">' . $error . '</li>';
            }
            echo '<script>window.location.hash = "#signup-form-mulher";</script>';
        }
        echo '</ul>';

        if (empty($errors)) {
            $user_id = wp_create_user($_POST['email'], $_POST['senha'], $_POST['email']);

            if (!is_wp_error($user_id)) {
                update_user_meta($user_id, 'nome', sanitize_text_field($_POST['nome']));
                update_user_meta($user_id, 'sobrenome', sanitize_text_field($_POST['sobrenome']));
                update_user_meta($user_id, 'cpf', sanitize_text_field($_POST['cpf']));
                update_user_meta($user_id, 'data_nascimento', sanitize_text_field($_POST['data_nascimento']));
                update_user_meta($user_id, 'telefone', sanitize_text_field($_POST['telefone']));
                update_user_meta($user_id, 'endereco', sanitize_text_field($_POST['endereco']));
                update_user_meta($user_id, 'bairro', sanitize_text_field($_POST['bairro']));

                $user = new WP_User($user_id);
                $user->set_role('mulher_vulnervel');
				
                $to = $_POST['email'];
                $subject = 'Seja bem-vinda ao Alagoas Tech! ✨';
                $headers = [
                    'Content-Type: text/html; charset=UTF-8',
                    'From: Instituto Panapaná <<EMAIL>>'
                ];
                $message = '<html><body>';
                $message .= '<table style="width: 100%; max-width: 600px; margin: 0 auto; border-collapse: collapse;">';
                $message .= '<tr><td style="background-color: #f3f3f3; padding: 20px; text-align: center;">';
                $message .= '            <img style="max-width: 150px; height: auto;" src="https://cursos.institutopanapana.org.br/wp-content/uploads/2023/06/LOGO-insituto-panapana-org-hr.png" alt="Instituto Panapaná Logo" data-mce-src="https://cursos.institutopanapana.org.br/wp-content/uploads/2023/06/LOGO-insituto-panapana-org-hr.png" data-mce-style="max-width: 150px; height: auto;" data-mce-selected="1">
';
                $message .= '</td></tr>';
                $message .= '<tr><td style="background-color: #ffffff; padding: 20px; font-family: Arial, sans-serif;">';
                $message .= '<h1 style="font-size: 24px; color: #333333;">Seja bem-vinda ao Alagoas Tech! ✨</h1>';
                $message .= '<p style="font-size: 16px; color: #333333;">Olá, ' . $_POST['nome']. '</p>';
                $message .= '<p style="font-size: 16px; color: #333333;">É com muita alegria que te damos as boas-vindas ao Alagoas Tech! Você deu o primeiro passo para uma jornada incrível no mundo da tecnologia.</p>';
                $message .= '<p style="font-size: 16px; color: #333333;">Para acessar a plataforma e continuar sua inscrição, use os dados cadastrados:</p>';
                $message .= '<p style="font-size: 16px; color: #333333;"><strong>Usuário:</strong> ' . $_POST['email'] . '</p>';
                $message .= '<p style="font-size: 16px; color: #333333;">Se precisar redefinir sua senha, clique aqui: <a href="https://cursos.institutopanapana.org.br/dashboard/retrieve-password" style="color: #ff7300; text-decoration: none;">redefinir senha</a></p>';
                $message .= '<p style="font-size: 16px; color: #333333;">Acesse a plataforma aqui:<br><br><a href="https://cursos.institutopanapana.org.br/login" style="background-color: #ff7300; padding:8px 48px; text-decoration: none;color: white;border-radius: 6px;font-weight: bold; margin: 12px 0px;">acessar</a></p>';
                $message .= '<p style="font-size: 16px; color: #333333;">Lembre-se: Para garantir sua vaga no processo seletivo, complete os formulários dentro da plataforma. É rapidinho!</p>';
                $message .= '<p style="font-size: 16px; color: #333333;">Estamos muito felizes em te receber no Alagoas Tech!</p>';
                $message .= '<p style="font-size: 16px; color: #333333;">Até breve,</p>';
                $message .= '<p style="font-size: 16px; color: #333333;">Equipe Instituto Panapaná</p>';
                $message .= '</td></tr>';
                $message .= '</table>';
                $message .= '</body></html>';

                wp_mail($to, $subject, $message, $headers);
				
                // Login do usuário
                $credentials = array(
                    'user_login' => $_POST['email'],
                    'user_password' => $_POST['senha'],
                    'remember' => true,
                );
				
                $user_signon = wp_signon($credentials, false);

                if (!is_wp_error($user_signon)) {
                    wp_redirect('/formulario-de-inscricao');
                    exit;
                } else {
                    $errors[] = $user_signon->get_error_message();
                    echo '<ul class="errors">';
                    foreach ($errors as $error) {
                        echo '<li class="erro">' . $error . '</li>';
                    }
                    echo '</ul>';
                    echo '<script>window.location.hash = "#signup-form-mulher";</script>';
                }
            } else {
                $errors[] = $user_id->get_error_message();
                echo '<ul class="errors">';
                foreach ($errors as $error) {
                    echo '<li class="erro">' . $error . '</li>';
                }
                echo '</ul>';
                echo '<script>window.location.hash = "#signup-form-mulher";</script>';
            }
        }
    }

    ob_start();
    ?>

    <style>
        .flex-col {
            display: flex;
            flex-direction: column;
        }

        .flex-row {
            display: flex;
            flex-direction: row;
            gap: 10px;
        }

        label {
            display: block;
            font-weight: bold;
            font-size: 1em;
            margin-bottom: 5px;
            color: white;
            margin-top: 14px;
        }

        input, select, select#bairro {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
            background-color: white;
            border: solid 1px white;
            border-radius: 4px;
            color: black;
        }

        option {
            color: black;
        }

        .half-width {
            width: 50%;
        }

        .errors {
            background-color: red;
            color: white;
        }

        .elementor-widget-container{
            background-color: transparent!important;
        }
    </style>

    <form method="POST" class="flex-col" id="signup-form-mulher">
        <div class="flex-row">
            <div class="half-width">
                <label for="nome">Nome</label>
                <input type="text" id="nome" name="nome" required>
            </div>
            <div class="half-width">
                <label for="sobrenome">Sobrenome</label>
                <input type="text" id="sobrenome" name="sobrenome" required>
            </div>
        </div>

        <div class="flex-row">
            <div class="half-width">
                <label for="telefone">Telefone Whatsapp</label>
                <input type="text" id="telefone" name="telefone" required>
            </div>
            <div class="half-width">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" required>
            </div>
        </div>

        <div class="flex-row">
            <div class="half-width">
                <label for="senha">Senha</label>
                <input type="password" id="senha" name="senha" required>
            </div>
            <div class="half-width">
                <label for="confirmar_senha">Confirmação de Senha</label>
                <input type="password" id="confirmar_senha" name="confirmar_senha" required>
            </div>
        </div>

        <div class="flex-row">
            <div class="half-width">
                <label for="cpf">CPF</label>
                <input type="text" id="cpf" name="cpf" required>
            </div>
            <div class="half-width">
                <label for="data_nascimento">Data de Nascimento</label>
                <input type="date" id="data_nascimento" name="data_nascimento" placeholder="dd-mm-yyyy" max="2009-12-31" required>
            </div>
        </div>

        <label for="endereco">Endereço Completo</label>
        <input type="text" id="endereco" name="endereco" required>

        <label for="bairro">Bairro</label>
        <select id="bairro" name="bairro" required>
            <option value="">Selecione um bairro</option>
            <?php
            $bairros = [
                'Antares', 'Barro Duro', 'Bebedouro', 'Benedito Bentes', 'Bom Parto', 'Canaã', 'Centro', 'Chá de Jaqueira',
                'Cidade Universitária', 'Clima Bom', 'Cruz das Almas', 'Farol', 'Feitosa', 'Fernão Velho', 'Garça Torta',
                'Gruta de Lourdes', 'Guaxumá', 'Ipioca', 'Jacarecica', 'Jacintinho', 'Jaraguá', 'Jardim Petrópolis', 'Jatiúca',
                'Levada', 'Magabeiras', 'Mutange', 'Ouro Preto', 'Pajuçara', 'Pescaria', 'Petrópolis', 'Pinheiro', 'Pitanguinha',
                'Poço', 'Ponta da Terra', 'Ponta Grossa', 'Ponta Verde', 'Pontal da Praça', 'Prado', 'Riacho Doce', 'Rio Novo',
                'Santa Amélia', 'Santa Lúcia', 'Santo Amaro', 'Santos Dumont', 'São Jorge', 'Serraria', 'Tabuleiro dos Martins',
                'Trapiche da Barra', 'Vergel do Lago', 'Zona Rural'
            ];

            foreach ($bairros as $bairro) {
                echo '<option value="' . $bairro . '">' . $bairro . '</option>';
            }
            ?>
        </select>
        <input type="submit" name="submit" value="Cadastrar">
    </form>

    <script>
        function formatCPF(cpf) {
            cpf = cpf.replace(/\D/g, '');
            cpf = cpf.substring(0, 11);
            cpf = cpf.replace(/(\d{3})(\d)/, '$1.$2');
            cpf = cpf.replace(/(\d{3})(\d)/, '$1.$2');
            cpf = cpf.replace(/(\d{3})(\d{1,2})$/, '$1-$2');
            return cpf;
        }

        function formatPhone(phone) {
            phone = phone.replace(/\D/g, '');
            phone = phone.substring(0, 11);
            phone = phone.replace(/(\d{2})(\d)/, '($1) $2');
            phone = phone.replace(/(\d{5})(\d{4})$/, '$1-$2');
            return phone;
        }

        function validateCPF(cpf) {
            cpf = cpf.replace(/\D/g, '');
            if (cpf.length !== 11 || /^(\d)\1+$/.test(cpf)) return false;
            let soma = 0, resto;
            for (let i = 1; i <= 9; i++) soma += parseInt(cpf.substring(i-1, i)) * (11 - i);
            resto = (soma * 10) % 11;
            if ((resto === 10) || (resto === 11)) resto = 0;
            if (resto !== parseInt(cpf.substring(9, 10))) return false;
            soma = 0;
            for (let i = 1; i <= 10; i++) soma += parseInt(cpf.substring(i-1, i)) * (12 - i);
            resto = (soma * 10) % 11;
            if ((resto === 10) || (resto === 11)) resto = 0;
            return resto === parseInt(cpf.substring(10, 11));
        }

        function displayError(message) {
            const errorList = document.querySelector('.errors');
            const errorItem = document.createElement('li');
            errorItem.classList.add('erro');
            errorItem.textContent = message;
            errorList.appendChild(errorItem);
        }

        document.getElementById('cpf').addEventListener('input', function (e) {
            e.target.value = formatCPF(e.target.value);
        });

        document.getElementById('telefone').addEventListener('input', function (e) {
            e.target.value = formatPhone(e.target.value);
        });

        document.getElementById('signup-form-mulher').addEventListener('submit', function (e) {
            const cpf = document.getElementById('cpf').value;
            const telefone = document.getElementById('telefone').value;

            const senha = document.getElementById('senha').value;
            const confirmarSenha = document.getElementById('confirmar_senha').value;

            const errorList = document.querySelector('.errors');
            errorList.innerHTML = '';

            if (!validateCPF(cpf)) {
                e.preventDefault();
                displayError('CPF inválido.');
                window.location.hash = '#signup-form-mulher';
            }

            if (!/^\(\d{2}\) \d{5}-\d{4}$/.test(telefone)) {
                e.preventDefault();
                displayError('Telefone inválido.');
                window.location.hash = '#signup-form-mulher';
            }

            if (confirmarSenha != senha){
                e.preventDefault();
                displayError('A confirmação de senha deve ser igual à senha.');
                window.location.hash = '#signup-form-mulher';
            }
        });
    </script>

    <?php
    return ob_get_clean();
}
add_shortcode('custom_signup_mulher', 'custom_signup_form_mulher');