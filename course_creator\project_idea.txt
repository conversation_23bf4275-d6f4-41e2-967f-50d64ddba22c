i have a wordpress video courses platform using <PERSON><PERSON> LMS + <PERSON>ks theme. 
I need a way to automate the course creation process or at least acelerate it.
Today it works with youtube embed link and vtt subtitle shortcode inside tutor LMS.
Here's an example:
[custom_video_with_vtt src="https://youtu.be/FLqi7on3vU4?si=mikR0L5xvmOzdpNm" width="640" height="360" vtt="https://cursos.institutopanapana.org.br/wp-content/uploads/legendas/FLqi7on3vU4.pt.vtt"] 

but as you can see on the images, there is a lot of data to fill all requirements to have a new course as:
- course name
- module names
- classes names
   - class description
   - class shortcode
   - class quiz
   - class attachment
   - class pdf shortcode (when is ebook)
etc.

my idea is to try automate or at least acelerate what i can to create a course giving only the parameters and the system to fill all the database need to create a new course and then i only take care of details as image course and some other stuff.

for the youtube links videos, subtitles download and shortcode generations i already have something using this code on windows:

download legendas:
yt-dlp --skip-download --write-auto-sub --sub-lang "pt.*" --convert-subs vtt --output "legendas/%(id)s.%(ext)s" "https://www.youtube.com/watch?v=4n4I0EqvBk0&list=PL_8vsRIoHA0oxD_cl0QcDBYiLYtdE0CUl"

criar csv shortcodes:
yt-dlp --print "%(title)s,\"[custom_video_with_vtt src=\"http://googleusercontent.com/youtube.com/%(id)s\" width=\"640\" height=\"360\" vtt=\"https://cursos.institutopanapana.org.br/wp-content/uploads/legendas/%(id)s.pt.vtt\"]\"" "https://www.youtube.com/watch?v=4n4I0EqvBk0&list=PL_8vsRIoHA0oxD_cl0QcDBYiLYtdE0CUl" > videos_shortcode.csv 

but for automation would be good to have an interface on my website to do this kind of stuff, where i can input the youtube playlist link or a doc or csv with all youtube links that i want and on the order of the classes

but for that i need to organize all course in modules and classes, and this classes could be shortcode youtube + subtitle as i showed above or shortcode pdf or an iframe like this: <iframe style="width: 100%; height: 700px;" src="https://docs.google.com/gview?url=https://cursos.institutopanapana.org.br/wp-content/uploads/2025/05/E-BOOK-1_FUNDAMENTOS-DO-PROMPT.pdf&amp;embedded=true" frameborder="0"></iframe> 
or the class could be a quiz as well that have an format as well like this:
settings,"Exercícios de fixação","",0,minutes,,0,80,3,,,rand,,200
question,"Qual a importância de se utilizar guias e grades durante o processo de design editorial?","",single_choice,1.00,2,1,1,1,""
answer,"É importante pois facilita a formatação e proporção dos elementos.",text,0,0,,1
answer,"As guias e grades permitem que o design fique mais harmônico e equilibrado, alinhando elementos e definindo proporções de maneira consistente.",text,1,0,,2
answer,"As guias e grades não são importantes no processo de design editorial.",text,0,0,,3
answer,"As guias e grades servem apenas para separar os elementos na página.",text,0,0,,4
question,"O que significa quando uma imagem possui uma linha laranja ao ser selecionada no InDesign?","",single_choice,1.00,2,1,1,1,""
answer,"Significa que a imagem está desproporcional e precisa ser ajustada.",text,0,0,,2
answer,"Indica que apenas a imagem está selecionada e não a caixa que a contém.",text,1,0,,3
answer,"Mostra que a imagem não está bem posicionada na página.",text,0,0,,1
answer,"Não tem nenhum significado especial, é apenas uma questão estética do programa.",text,0,0,,4
question,"Por que é importante definir estilos de parágrafo ao elaborar um livro no InDesign?","",single_choice,1.00,2,1,1,1,""
answer,"Os estilos de parágrafo facilitam a formatação e manutenção da identidade visual do livro.",text,1,0,,1
answer,"Os estilos de parágrafo não são importantes na elaboração de um livro.",text,0,0,,3
answer,"Os estilos de parágrafo servem apenas para destacar títulos e subtítulos.",text,0,0,,2
answer,"Definir estilos de parágrafo é uma opção, não uma necessidade para livros.",text,0,0,,4
question,"Qual a função do comando \'Editar > Colar sem Formatação\' no InDesign?","",single_choice,1.00,2,1,1,1,""
answer,"Permite colar um texto mantendo apenas o conteúdo sem formatações de fontes ou parágrafos.",text,1,0,,4
answer,"Não tem nenhuma função específica nesse contexto.",text,0,0,,2
answer,"Serve para colar uma imagem sem afetar seu posicionamento original.",text,0,0,,3
answer,"Ajuda a padronizar a formatação do texto colado.",text,0,0,,1

the tutor lms offer a way to import the quizz like this, but i'm not sure if this format goona work for the automatic system that fills the database directly, we need to check how the quizz is store on database.

actually we need to check how all course above that i said is store on database and we make some tests to try to create a simple course and then try to add details if works.

keep in mind that all the course need to have the classes in order, so the system need a way to specify that, like i said above 
"- course name
- module names
- classes names
  - class description
  - class shortcode
  - class quiz
  - class attachment
  - class pdf shortcode (when is ebook)
etc. "

but splitting in modules and right order.

idk if could be a csv file specifying all things or differents fields.

first the important is to understand the database and test each part, if i can fill the database with a csv and upload mannually the subtitles, ebooks its good at first.
the interface to automate that could be done later.

i attached a file that i used for another function that track the courses classes youtube links for you start to understand a litttle how the database of tutor lms works.

so we need a implementation plan split into small tasks for we start this project and test all steps