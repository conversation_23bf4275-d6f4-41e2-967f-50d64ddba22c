<?php
// Função que cria o shortcode para gerar o botão de download do CSV
function gerar_csv_candidatos_shortcode() {
    // URL para o arquivo que gera o CSV
    $download_url = add_query_arg('action', 'gerar_csv_candidatos', admin_url('admin-ajax.php'));
    
    // Gera o botão com link para o download
    return '<a href="' . esc_url($download_url) . '" class="btn-csv-download" style="padding: 10px 20px; background-color: #0073aa; color: #fff; text-decoration: none; border-radius: 5px;">Baixar CSV de Candidatos</a>';
}

// Função que cria o arquivo CSV para download
function gerar_csv_candidatos() {
    // Somente usuários com a role 'candidato'
    $args = array(
        'role'    => 'candidato',
        'meta_query' => array(
            array(
                'key'     => 'caminho_selecionado',
                'compare' => 'EXISTS',
            ),
        ),
    );

    // Pega os usuários com os critérios definidos
    $user_query = new WP_User_Query($args);
    $users = $user_query->get_results();

    if (empty($users)) {
        wp_die('Nenhum candidato encontrado.');
    }

    // Definir o cabeçalho para forçar o download do CSV
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename=candidatos.csv');

    // Abrir o "output stream" para gerar o CSV
    $output = fopen('php://output', 'w');

    // Cabeçalhos do CSV
    fputcsv($output, array('Nome', 'Sobrenome', 'Telefone', 'Curso Selecionado'));

    // Loop pelos usuários e escreve os dados no CSV
    foreach ($users as $user) {
        $nome = get_user_meta($user->ID, 'nome', true);
        $sobrenome = get_user_meta($user->ID, 'sobrenome', true);
        $telefone = get_user_meta($user->ID, 'telefone', true);
        $curso_selecionado = get_user_meta($user->ID, 'caminho_selecionado', true);

        fputcsv($output, array($nome, $sobrenome, $telefone, $curso_selecionado));
    }

    // Fechar o output stream
    fclose($output);
    exit();
}

// Registra o AJAX para a função de gerar CSV
add_action('wp_ajax_gerar_csv_candidatos', 'gerar_csv_candidatos');
add_action('wp_ajax_nopriv_gerar_csv_candidatos', 'gerar_csv_candidatos');

// Registra o shortcode
add_shortcode('gerar_csv_candidatos', 'gerar_csv_candidatos_shortcode');