# Backlog Session 2 - CSV Parser Development

**Date:** 2025-07-04  
**Session Duration:** Phase 1 Testing + Task 2.1 Complete + Task 2.2 Partial  
**Status:** Phase 2 In Progress (50% complete)

## Session Overview

This session focused on testing Phase 1 results and beginning Phase 2 development. Successfully tested the Hello World course creator and designed/implemented the CSV parsing system for automated course creation.

## Phase 1 Testing Results ✅

### Hello World Course Creator Test
- **Status:** SUCCESSFUL ✅
- **Shortcode:** `[panapana_hello_world_course]`
- **Result:** Created complete course "Hello World - Curso de Teste" with:
  - ✅ Course structure with proper metadata
  - ✅ Module: "Módulo 1: Introdução"
  - ✅ Video lesson: "Aula 1: Bem-vindos" (3m 32s duration)
  - ✅ E-book lesson: "E-book: Manual de Introdução"
  - ✅ Quiz: "Quiz: Teste seus conhecimentos"

### Permission Fix Applied
- **Issue:** User getting permission errors when testing shortcodes
- **Solution:** Updated all permission checks to allow both:
  - `export_course_data` capability (for gestor role)
  - `manage_options` capability (for admin role)
- **Files Modified:** `public/wp-content/themes/geeks-child/tutor/export_course_videos.php`
- **Result:** Admin role can now access all automation shortcodes

## Task 2.1: Design Unified CSV Input Format ✅

### Deliverables Created
1. **`backlog/csv-format-design.md`** - Complete CSV specification
2. **`backlog/sample-course.csv`** - Working example with 2 modules

### CSV Format Features
- **Row-Based Structure:** Single CSV defines complete courses
- **Row Types:** `course`, `topic`, `lesson`, `quiz`, `question`, `answer`
- **Content Types:** 
  - Video lessons (YouTube + VTT subtitles)
  - E-book lessons (PDF with iframe viewer)
  - Quizzes with multiple choice and true/false questions
- **Hierarchical Organization:** Maintains course → topic → lesson/quiz relationships

### Key Design Decisions
- **Flexible Column Structure:** 17 columns support all content types
- **Order Management:** `order` column for proper sequencing
- **Content-Specific Columns:** `youtube_url`, `vtt_filename`, `pdf_url`, `quiz_settings`
- **Validation Rules:** Required fields vary by row type

### Sample CSV Structure
```csv
row_type,course_title,topic_title,item_title,item_type,content,description,order,youtube_url,vtt_filename,pdf_url,quiz_settings,question_type,question_points,answer_text,is_correct,answer_order
course,"Git - Iniciante ao Avançado","","","","","Aprenda Git do básico ao avançado",1
topic,"Git - Iniciante ao Avançado","Módulo 1: Introdução","","","","Conceitos fundamentais",1
lesson,"Git - Iniciante ao Avançado","Módulo 1: Introdução","O que é Git?",video,"","Introdução aos conceitos",1,"https://www.youtube.com/watch?v=abc123","abc123.pt.vtt"
```

## Task 2.2: Write CSV Parser Function (IN PROGRESS - 50%)

### Implementation Progress

#### ✅ Completed Components

**1. User Interface (`panapana_csv_course_creator_shortcode`)**
- File upload form with CSV validation
- Clear instructions and example format
- Proper WordPress nonce security
- Error/success message display

**2. File Processing (`panapana_process_csv_upload`)**
- File upload validation (CSV extension, upload errors)
- Security checks and file handling
- Integration with main parsing function

**3. CSV Reading (`panapana_read_csv_file`)**
- Robust CSV parsing with header validation
- Required column checking (`row_type`, `course_title`)
- Row type validation (course, topic, lesson, quiz, question, answer)
- Empty row handling and error reporting

**4. Data Organization (`panapana_group_csv_data_by_course`)**
- Groups CSV rows by course for batch processing
- Maintains hierarchical relationships (course → topics → lessons/quizzes)
- Tracks current context (topic, quiz, question) for proper nesting
- Comprehensive validation of data structure

#### 🔄 Partially Implemented

**5. Course Creation (`panapana_create_course_from_data`)**
- **Status:** Function signature created, basic structure outlined
- **Progress:** Course post creation logic started
- **Remaining:** Complete implementation and error handling

#### ❌ Not Started

**6. Topic and Content Creation Functions:**
- `panapana_create_topic_with_content()` - Create topics with lessons/quizzes
- `panapana_create_lesson_from_data()` - Handle video/ebook lesson creation
- `panapana_create_quiz_from_data()` - Quiz creation (Task 2.3 dependency)

### Technical Implementation Details

#### CSV Parsing Logic
```php
// Row type validation
$valid_row_types = array( 'course', 'topic', 'lesson', 'quiz', 'question', 'answer' );

// Data grouping structure
$courses[ $course_title ] = array(
    'course_info' => $course_row,
    'topics' => array(
        $topic_title => array(
            'topic_info' => $topic_row,
            'lessons' => array( $lesson_rows ),
            'quizzes' => array(
                $quiz_title => array(
                    'quiz_info' => $quiz_row,
                    'questions' => array(
                        $question_text => array(
                            'question_info' => $question_row,
                            'answers' => array( $answer_rows )
                        )
                    )
                )
            )
        )
    )
);
```

#### Content Type Handling Strategy
- **Video Lessons:** Generate `[custom_video_with_vtt]` shortcode from YouTube URL + VTT filename
- **E-book Lessons:** Create iframe with Google Docs viewer from PDF URL
- **Quizzes:** Direct database insertion into `wp_tutor_quiz_questions` and `wp_tutor_quiz_question_answers`

## Files Modified This Session

### 1. `public/wp-content/themes/geeks-child/tutor/export_course_videos.php`
- **Lines Added:** ~200 lines of new code
- **New Functions:** 5 new functions for CSV processing
- **New Shortcode:** `[panapana_csv_course_creator]`
- **Permission Updates:** 3 permission checks updated for admin access

### 2. Documentation Files Created
- `backlog/csv-format-design.md` - Complete CSV specification (300 lines)
- `backlog/sample-course.csv` - Working example with 2 modules (25 rows)
- `next-prompt.md` - Continuation prompt for next session (122 lines)

## Current Code Status

### Working Shortcodes
1. `[panapana_hello_world_course]` - ✅ Tested and working
2. `[tutor_course_exporter]` - ✅ Working (not tested this session)
3. `[panapana_master_debug]` - ✅ Working (not tested this session)
4. `[panapana_csv_course_creator]` - 🔄 UI complete, backend 50% done

### Function Implementation Status
```php
// ✅ Complete and tested
panapana_csv_course_creator_shortcode()
panapana_process_csv_upload()
panapana_read_csv_file()
panapana_group_csv_data_by_course()

// 🔄 Started but incomplete
panapana_create_course_from_data()

// ❌ Not implemented
panapana_create_topic_with_content()
panapana_create_lesson_from_data()
panapana_create_quiz_from_data()
```

## Next Session Priorities

### Immediate Tasks (Task 2.2 Completion)
1. **Complete Course Creation Logic:**
   - Finish `panapana_create_course_from_data()` implementation
   - Implement `panapana_create_topic_with_content()`
   - Implement `panapana_create_lesson_from_data()` with video/ebook support

2. **Test CSV Parser:**
   - Use `[panapana_csv_course_creator]` shortcode
   - Upload `backlog/sample-course.csv`
   - Verify complete course creation

### Task 2.3: Quiz Creation Logic
- Implement `panapana_create_quiz_from_data()`
- Direct database insertion for questions and answers
- Quiz settings parsing and application

## Technical Challenges Identified

### 1. Quiz Database Integration
- Need direct SQL insertion into `wp_tutor_quiz_questions` and `wp_tutor_quiz_question_answers`
- WordPress doesn't provide built-in functions for Tutor LMS quiz creation
- Must handle question types (single_choice, true_false) and answer validation

### 2. Error Handling Strategy
- CSV processing can fail at multiple points (file, parsing, course creation)
- Need comprehensive error reporting for user feedback
- Partial failures should be handled gracefully

### 3. Content Validation
- Video lessons require YouTube URL validation
- E-book lessons need PDF URL accessibility checking
- Quiz questions must have proper answer counts and correct answer validation

## Key Learnings This Session

### CSV Design Insights
- Single CSV file can handle complex course structures effectively
- Row-based approach provides flexibility while maintaining relationships
- Content-specific columns allow for specialized handling without complexity

### WordPress Integration Patterns
- Permission checking needs both capability types for full admin access
- File upload handling requires proper validation and security
- Hierarchical post creation follows predictable patterns

### User Experience Considerations
- Clear error messages are crucial for CSV validation
- Example formats help users understand requirements
- Progress feedback important for bulk operations

## Testing Strategy for Next Session

### Phase 2 Testing Plan
1. **Basic CSV Upload:** Test file upload and validation
2. **Simple Course:** Single module with one video lesson
3. **Complex Course:** Multiple modules with mixed content types
4. **Error Scenarios:** Invalid CSV formats, missing data
5. **Quiz Integration:** Complete quiz creation with questions/answers

### Success Criteria
- CSV upload creates complete courses matching sample structure
- All content types render properly (video, ebook, quiz)
- Error handling provides clear user feedback
- Performance acceptable for typical course sizes (10-50 lessons)

This session established the foundation for complete CSV-driven course automation. The next session should focus on completing the course creation logic and implementing quiz functionality.
