function formatPhone(phone) {
    phone = phone.replace(/\D/g, '');
    phone = phone.substring(0, 11);
    phone = phone.replace(/(\d{2})(\d)/, '($1) $2');
    phone = phone.replace(/(\d{5})(\d{4})$/, '$1-$2');
    return phone;
}

document.getElementById('telefone').addEventListener('input', function (e) {
    e.target.value = formatPhone(e.target.value);
});

document.getElementById('edit-profile-form').addEventListener('submit', function (e) {
    const telefone = document.getElementById('telefone').value;

    const errorList = document.querySelector('.errors');
    if (errorList) {
        errorList.innerHTML = '';
    }

    if (!/^\(\d{2}\) \d{5}-\d{4}$/.test(telefone)) {
        e.preventDefault();
        const errorItem = document.createElement('li');
        errorItem.classList.add('erro');
        errorItem.textContent = 'Telefone inválido.';
        errorList.appendChild(errorItem);
        window.location.hash = '#edit-profile-form';
    }
});