"use strict";var EasyWPSMTPDashboardWidget=window.EasyWPSMTPDashboardWidget||function(a){var i={$chart:a("#easy-wp-smtp-dash-widget-chart"),$dismissBtn:a(".easy-wp-smtp-dash-widget-dismiss-chart-upgrade"),$summaryReportEmailBlock:a(".easy-wp-smtp-dash-widget-summary-report-email-block"),$summaryReportEmailDismissBtn:a(".easy-wp-smtp-dash-widget-summary-report-email-dismiss"),$summaryReportEmailEnableInput:a("#easy-wp-smtp-dash-widget-summary-report-email-enable"),$emailAlertsDismissBtn:a("#easy-wp-smtp-dash-widget-dismiss-email-alert-block")},r={instance:null,settings:{chart:{type:"area",toolbar:{show:!1},foreColor:"#50575E",height:303,offsetY:-10,parentHeightOffset:0},stroke:{curve:"smooth",width:2},dataLabels:{enabled:!1},series:[{data:[]},{data:[]}],colors:["#211FA6","#0F8A56"],xaxis:{type:"datetime",categories:[],tickPlacement:"on",labels:{style:{colors:"#6F6F84",fontSize:"13px"},format:"dd",datetimeUTC:!1},axisBorder:{show:!1},axisTicks:{show:!0,colors:"#DADADF",height:9}},yaxis:{labels:{style:{colors:"#6F6F84",fontSize:"13px"},formatter:function(e){return isNaN(e)?"":e.toFixed(0)}},axisTicks:{show:!0,colors:"#DADADF",width:9,offsetY:.5}},grid:{show:!0,borderColor:"#DADADF",xaxis:{lines:{show:!0}},yaxis:{lines:{show:!0}}},legend:{show:!1}},init:function(){i.$chart.length&&(r.updateWithDummyData(),r.instance=new ApexCharts(i.$chart[0],r.settings),r.instance.render())},updateWithDummyData:function(){for(var e,s=moment().startOf("day"),t=[70,75,55,45,34,25,20],a=[25,30,25,20,15,10,5],i=1;i<=7;i++)e=s.clone().subtract(i,"days"),r.settings.xaxis.categories.push(e),r.settings.series[0].data.push({x:e,y:t[i-1]}),r.settings.series[1].data.push({x:e,y:a[i-1]})}},s={chart:r,init:function(){a(s.ready)},ready:function(){i.$dismissBtn.on("click",function(e){e.preventDefault(),s.saveWidgetMeta("hide_graph",1),a(this).closest(".easy-wp-smtp-dash-widget-chart-block-container").remove(),a("#easy-wp-smtp-dash-widget-upgrade-footer").show()}),i.$summaryReportEmailDismissBtn.on("click",function(e){e.preventDefault(),s.saveWidgetMeta("hide_summary_report_email_block",1),i.$summaryReportEmailBlock.slideUp()}),i.$summaryReportEmailEnableInput.on("change",function(e){e.preventDefault();var s=a(this),t=s.next("i"),e=(s.hide(),t.show(),{_wpnonce:easy_wp_smtp_dashboard_widget.nonce,action:"easy_wp_smtp_"+easy_wp_smtp_dashboard_widget.slug+"_enable_summary_report_email"});a.post(ajaxurl,e).done(function(){i.$summaryReportEmailBlock.find(".easy-wp-smtp-dash-widget-summary-report-email-block-setting").addClass("hidden"),i.$summaryReportEmailBlock.find(".easy-wp-smtp-dash-widget-summary-report-email-block-applied").removeClass("hidden")}).fail(function(){s.show(),t.hide()})}),i.$emailAlertsDismissBtn.on("click",function(e){e.preventDefault(),a("#easy-wp-smtp-dash-widget-email-alerts-education").remove(),s.saveWidgetMeta("hide_email_alerts_banner",1)}),r.init(),s.removeOverlay(i.$chart)},saveWidgetMeta:function(e,s){e={_wpnonce:easy_wp_smtp_dashboard_widget.nonce,action:"easy_wp_smtp_"+easy_wp_smtp_dashboard_widget.slug+"_save_widget_meta",meta:e,value:s};a.post(ajaxurl,e)},removeOverlay:function(e){e.siblings(".easy-wp-smtp-dash-widget-overlay").remove()}};return s}((document,window,jQuery));EasyWPSMTPDashboardWidget.init();