<?php
/**
 * Tutor LMS Course Automation - Course Exporter
 *
 * This file handles the original course export functionality,
 * extracting course data and video links for external use.
 *
 * @package TutorLMS_CourseAutomation
 * @subpackage Core
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Exports course data when triggered by a URL parameter.
 *
 * Only users with the 'export_course_data' capability can run this export.
 */
function panapana_export_course_videos_on_trigger() {
	if ( ! isset( $_GET['export_tutor_course'] ) ) {
		return;
	}

	// Security: Check for the custom capability or administrator access.
	if ( ! panapana_user_can_access_automation() ) {
		wp_die( 'Você não tem permissão para acessar esta página.' );
	}

	$course_id = intval( $_GET['export_tutor_course'] );
	if ( ! $course_id ) {
		wp_die( 'ID de curso inválido.' );
	}

	$course = get_post( $course_id );
	if ( ! $course || $course->post_type !== 'courses' ) {
		wp_die( 'Curso não encontrado.' );
	}

	// Export course data
	panapana_export_course_data( $course_id, $course );
}
add_action( 'init', 'panapana_export_course_videos_on_trigger' );

/**
 * Exports complete course data including videos and structure.
 *
 * @param int     $course_id Course ID to export.
 * @param WP_Post $course    Course post object.
 */
function panapana_export_course_data( $course_id, $course ) {
	// Set headers for text output
	header( 'Content-Type: text/plain; charset=utf-8' );
	echo "Exportando dados para o Curso: \"{$course->post_title}\" (ID: {$course_id})\n";
	echo "===================================================================\n\n";

	// Get course topics
	$topics = get_posts( array(
		'post_type'      => 'topics',
		'post_parent'    => $course_id,
		'post_status'    => 'publish',
		'posts_per_page' => -1,
		'orderby'        => 'menu_order',
		'order'          => 'ASC',
	) );

	if ( empty( $topics ) ) {
		echo "Nenhum tópico encontrado para este curso.\n";
		die();
	}

	foreach ( $topics as $topic ) {
		echo "TÓPICO: {$topic->post_title} (ID: {$topic->ID})\n";
		echo str_repeat( '-', 50 ) . "\n";

		// Export lessons for this topic
		panapana_export_topic_lessons( $topic->ID );

		// Export quizzes for this topic
		panapana_export_topic_quizzes( $topic->ID );

		echo "\n";
	}

	die();
}

/**
 * Exports all lessons for a specific topic.
 *
 * @param int $topic_id Topic ID.
 */
function panapana_export_topic_lessons( $topic_id ) {
	$lessons = get_posts( array(
		'post_type'      => 'lesson',
		'post_parent'    => $topic_id,
		'post_status'    => 'publish',
		'posts_per_page' => -1,
		'orderby'        => 'menu_order',
		'order'          => 'ASC',
	) );

	if ( empty( $lessons ) ) {
		echo "  Nenhuma aula encontrada neste tópico.\n";
		return;
	}

	foreach ( $lessons as $lesson ) {
		echo "  AULA: {$lesson->post_title} (ID: {$lesson->ID})\n";

		// Check for video content
		$video_url = get_post_meta( $lesson->ID, '_video_url', true );
		if ( ! empty( $video_url ) ) {
			echo "    Vídeo: {$video_url}\n";
		}

		// Extract video URLs from content
		$video_urls = panapana_extract_video_urls_from_content( $lesson->post_content );
		foreach ( $video_urls as $url ) {
			echo "    Vídeo encontrado no conteúdo: {$url}\n";
		}

		// Check for other media
		$attachments = get_attached_media( '', $lesson->ID );
		foreach ( $attachments as $attachment ) {
			echo "    Anexo: {$attachment->post_title} - " . wp_get_attachment_url( $attachment->ID ) . "\n";
		}

		echo "\n";
	}
}

/**
 * Exports all quizzes for a specific topic.
 *
 * @param int $topic_id Topic ID.
 */
function panapana_export_topic_quizzes( $topic_id ) {
	$quizzes = get_posts( array(
		'post_type'      => 'tutor_quiz',
		'post_parent'    => $topic_id,
		'post_status'    => 'publish',
		'posts_per_page' => -1,
		'orderby'        => 'menu_order',
		'order'          => 'ASC',
	) );

	if ( empty( $quizzes ) ) {
		echo "  Nenhum quiz encontrado neste tópico.\n";
		return;
	}

	foreach ( $quizzes as $quiz ) {
		echo "  QUIZ: {$quiz->post_title} (ID: {$quiz->ID})\n";

		// Export quiz questions
		panapana_export_quiz_questions( $quiz->ID );

		echo "\n";
	}
}

/**
 * Exports questions for a specific quiz.
 *
 * @param int $quiz_id Quiz ID.
 */
function panapana_export_quiz_questions( $quiz_id ) {
	global $wpdb;

	$questions = $wpdb->get_results( $wpdb->prepare(
		"SELECT * FROM {$wpdb->prefix}tutor_quiz_questions WHERE quiz_id = %d ORDER BY question_order ASC",
		$quiz_id
	) );

	if ( empty( $questions ) ) {
		echo "    Nenhuma pergunta encontrada neste quiz.\n";
		return;
	}

	foreach ( $questions as $question ) {
		echo "    PERGUNTA: {$question->question_title}\n";
		echo "    Tipo: {$question->question_type}\n";

		// Export answers for this question
		$answers = $wpdb->get_results( $wpdb->prepare(
			"SELECT * FROM {$wpdb->prefix}tutor_quiz_question_answers WHERE belongs_question_id = %d ORDER BY answer_order ASC",
			$question->question_id
		) );

		foreach ( $answers as $answer ) {
			$correct_indicator = $answer->is_correct ? '[CORRETA]' : '';
			echo "      - {$answer->answer_title} {$correct_indicator}\n";
		}

		echo "\n";
	}
}

/**
 * Extracts video URLs from lesson content.
 *
 * @param string $content Lesson content.
 * @return array Array of video URLs found.
 */
function panapana_extract_video_urls_from_content( $content ) {
	$video_urls = array();

	// Extract YouTube URLs
	preg_match_all( '/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/', $content, $youtube_matches );
	foreach ( $youtube_matches[0] as $match ) {
		$video_urls[] = $match;
	}

	// Extract Vimeo URLs
	preg_match_all( '/vimeo\.com\/(\d+)/', $content, $vimeo_matches );
	foreach ( $vimeo_matches[0] as $match ) {
		$video_urls[] = 'https://' . $match;
	}

	// Extract custom video shortcodes
	preg_match_all( '/\[custom_video_with_vtt[^\]]*src="([^"]+)"[^\]]*\]/', $content, $shortcode_matches );
	foreach ( $shortcode_matches[1] as $match ) {
		$video_urls[] = $match;
	}

	return array_unique( $video_urls );
}

/**
 * Gets a list of all courses for the export interface.
 *
 * @return array Array of course objects.
 */
function panapana_get_courses_for_export() {
	return get_posts( array(
		'post_type'      => 'courses',
		'post_status'    => 'publish',
		'posts_per_page' => -1,
		'orderby'        => 'title',
		'order'          => 'ASC',
	) );
}
