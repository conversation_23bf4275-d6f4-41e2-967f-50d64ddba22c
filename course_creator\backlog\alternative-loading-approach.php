<?php
/**
 * Alternative Loading Approach for functions.php
 * 
 * This shows how you could manually control what gets loaded
 * instead of using the automatic loader.
 */

// Option B: Manual selective loading in functions.php
// Replace the current loader line with this approach:

// Always load capabilities (lightweight, needed for security)
require_once get_stylesheet_directory() . '/tutor/core/capabilities.php';

// Load shortcodes only on frontend or specific admin pages
if ( ! is_admin() || ( is_admin() && in_array( get_current_screen()->base ?? '', ['post', 'page'] ) ) ) {
    require_once get_stylesheet_directory() . '/tutor/shortcodes/course-shortcodes.php';
}

// Load export functionality only when needed
if ( isset( $_GET['export_tutor_course'] ) ) {
    require_once get_stylesheet_directory() . '/tutor/core/course-exporter.php';
}

// Load creation functionality only when needed
if ( isset( $_POST['panapana_csv_upload'] ) || 
     isset( $_POST['create_hello_world'] ) || 
     is_admin() ) {
    require_once get_stylesheet_directory() . '/tutor/core/csv-parser.php';
    require_once get_stylesheet_directory() . '/tutor/core/course-creator.php';
    require_once get_stylesheet_directory() . '/tutor/core/quiz-creator.php';
}

// Option C: Lazy loading with autoloader
function panapana_autoload_on_demand( $function_name ) {
    $function_map = [
        // CSV functions
        'panapana_process_csv_upload' => 'core/csv-parser.php',
        'panapana_read_csv_file' => 'core/csv-parser.php',
        
        // Course creation functions  
        'panapana_create_course_from_data' => 'core/course-creator.php',
        'panapana_create_hello_world_course' => 'core/course-creator.php',
        
        // Quiz functions
        'panapana_create_quiz_from_data' => 'core/quiz-creator.php',
        
        // Export functions
        'panapana_export_course_data' => 'core/course-exporter.php',
        
        // Shortcode functions
        'panapana_csv_course_creator_shortcode' => 'shortcodes/course-shortcodes.php',
    ];
    
    if ( isset( $function_map[ $function_name ] ) && ! function_exists( $function_name ) ) {
        require_once get_stylesheet_directory() . '/tutor/' . $function_map[ $function_name ];
    }
}

// Hook into function calls to load files on demand
// This would require more complex implementation but provides maximum efficiency
