{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 2, "name": "frontend-admin/select-field", "title": "Select Field", "description": "Displays a select field.", "category": "frontend-admin", "textdomain": "frontend-admin", "icon": "list-view", "supports": {"align": ["wide"]}, "attributes": {"name": {"type": "string", "default": ""}, "label": {"type": "string", "default": "Select"}, "hide_label": {"type": "boolean", "default": ""}, "required": {"type": "boolean", "default": ""}, "instructions": {"type": "string", "default": ""}, "layout": {"type": "string", "default": "vertical"}, "choices": {"type": "array", "default": [{"value": "red", "label": "Red"}, {"value": "blue", "label": "Blue"}]}, "default_value": {"type": "string", "default": ""}, "ui": {"type": "boolean", "default": false}, "ajax": {"type": "boolean", "default": false}, "allow_null": {"type": "boolean", "default": false}, "multiple": {"type": "boolean", "default": false}, "return_format": {"type": "string", "default": "value"}}, "editorScript": "file:../../select/index.js"}