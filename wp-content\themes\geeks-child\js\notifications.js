document.addEventListener('DOMContentLoaded', function () {
    const notificationItems = document.querySelectorAll('.notification-item');
    const markAllReadButton = document.getElementById('mark-all-read');
    const dropdownCheckbox = document.getElementById('dropdown');
    const sectionDropdown = document.querySelector('.section-dropdown');

    // Adiciona o evento de clique a cada notificação
    notificationItems.forEach(item => {
        item.addEventListener('click', function (event) {
            event.preventDefault(); // Evita o redirecionamento imediato

            const notificationId = this.getAttribute('data-id');
            const notificationUrl = this.getAttribute('href');

            // Faz a requisição AJAX para marcar a notificação como lida
            fetch('/wp-admin/admin-ajax.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: new URLSearchParams({
                    action: 'mark_as_read',
                    notification_id: notificationId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.open(notificationUrl, '_blank'); // Abre o link em uma nova aba
                } else {
                    console.error('Erro ao marcar a notificação como lida.');
                }
            })
            .catch(error => {
                console.error('Erro na requisição:', error);
            });
        });
    });

    // Marca todas as notificações como lidas quando o botão é clicado
    if(markAllReadButton){
        markAllReadButton.addEventListener('click', function () {
            fetch('/wp-admin/admin-ajax.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: new URLSearchParams({
                    action: 'mark_all_as_read'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload(); // Recarrega a página para atualizar a lista de notificações
                } else {
                    console.error('Erro ao marcar todas as notificações como lidas.');
                }
            })
            .catch(error => {
                console.error('Erro na requisição:', error);
            });
        });
    }

    // Fecha o dropdown de notificações ao clicar fora dele
    document.addEventListener('click', function(event) {
        if (!sectionDropdown.contains(event.target) && !dropdownCheckbox.contains(event.target) && dropdownCheckbox.checked) {
            dropdownCheckbox.checked = false;
        }
    });
});
