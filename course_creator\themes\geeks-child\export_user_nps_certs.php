<?php
/**
 * Functionality to export User Data, NPS Responses, and their existing Certificate IDs (Hashes)
 * FOR A SPECIFIC LIST OF USER IDS.
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
    die;
}

/**
 * Shortcode to display the "Export User NPS with Certificate IDs (Specific Users)" button.
 *
 * Usage: [export_specific_user_nps_certs_button]
 */
function panapana_export_specific_user_nps_certs_button_shortcode() {
    if ( ! current_user_can( 'manage_options' ) ) { // Or a more specific capability
        return '<p>Você não tem permissão para ver este botão.</p>';
    }

    $export_url = wp_nonce_url(
        admin_url( 'admin-post.php?action=export_specific_user_nps_certs_csv' ), // Unique action
        'export_specific_user_nps_certs_nonce',      // Unique nonce action
        'export_sunc_nonce'                          // Unique nonce name
    );

    ob_start();
    ?>
    <div class="panapana-export-section" style="margin: 20px 0; padding: 15px; border: 1px solid #ccd0d4; background-color: #f6f7f7; border-radius: 4px;">
        <h3>Exportar Dados de Usuários Específicos (NPS e Certificados)</h3>
        <p>Clique no botão abaixo para exportar um CSV contendo dados do usuário (Nome, CPF, Endereço), suas respostas NPS, o nome do curso associado à resposta NPS, e o ID do certificado (hash) se existente, para uma lista pré-definida de usuários.</p>
        <p>
            <a href="<?php echo esc_url( $export_url ); ?>" class="button button-primary">
                <span class="dashicons dashicons-media-spreadsheet" style="vertical-align: middle; margin-right: 5px;"></span>
                Exportar Dados de Usuários Específicos (CSV)
            </a>
        </p>
    </div>
    <?php
    return ob_get_clean();
}
// Note the new shortcode name to avoid conflicts if you keep the old one
add_shortcode( 'export_specific_user_nps_certs_button', 'panapana_export_specific_user_nps_certs_button_shortcode' );


/**
 * Handles the CSV export for specific user data, NPS, and certificate IDs.
 * Hooked to admin_post_export_specific_user_nps_certs_csv.
 */
function panapana_handle_export_specific_user_nps_certs_csv() {
    // 1. Verify Nonce & Capability
    if ( ! isset( $_GET['export_sunc_nonce'] ) || ! wp_verify_nonce( sanitize_key( $_GET['export_sunc_nonce'] ), 'export_specific_user_nps_certs_nonce' ) ) {
        wp_die( 'Invalid security token.', 'Security Check Failed', array( 'response' => 403 ) );
    }
    if ( ! current_user_can( 'manage_options' ) ) {
        wp_die( 'Você não tem permissão para realizar esta ação.', 'Permission Denied', array( 'response' => 403 ) );
    }

    global $wpdb;
    $nps_table_name = $wpdb->prefix . 'nps_responses';

    // --- THIS IS YOUR SPECIFIC LIST OF USER IDS ---
    $target_user_ids_string = "299,357,428,445,562,596,604,622,624,625,627,628,629,631,632,633,634,635,637,638,640,658,660";
    $target_user_ids = array_map('intval', explode(',', $target_user_ids_string));
    $target_user_ids = array_unique(array_filter($target_user_ids)); // Clean up

    if ( empty($target_user_ids) ) {
        wp_die('A lista de IDs de usuário alvo está vazia.', 'Nenhum Usuário Especificado');
    }

    // Prepare placeholders for the IN clause
    $placeholders = implode(', ', array_fill(0, count($target_user_ids), '%d'));
    
    // 2. Fetch NPS responses ONLY for the target user IDs
    $sql_query = $wpdb->prepare(
        "SELECT * FROM {$nps_table_name} WHERE user_id IN ({$placeholders}) ORDER BY user_id ASC, submission_date DESC",
        $target_user_ids // Pass the array of IDs as subsequent arguments to prepare
    );
    // Because $target_user_ids is an array, and prepare expects individual args for %d,
    // we need to call it with call_user_func_array for the IN clause
    $nps_results = $wpdb->get_results( call_user_func_array(array($wpdb, 'prepare'), array_merge(array("SELECT * FROM {$nps_table_name} WHERE user_id IN ({$placeholders}) ORDER BY user_id ASC, submission_date DESC"), $target_user_ids)), ARRAY_A );


    if ( empty( $nps_results ) ) {
        wp_die( 'Nenhuma resposta NPS encontrada para os usuários especificados.', 'Nenhuma Resposta' );
    }

    // 3. Prepare CSV Output (same as before)
    $filename = 'usuarios_especificos_nps_certificados_' . date( 'Y-m-d_H-i-s' ) . '.csv';
    header( 'Content-Type: text/csv; charset=utf-8' );
    header( 'Content-Disposition: attachment; filename="' . $filename . '"' );
    $output = fopen( 'php://output', 'w' );
    fprintf( $output, chr(0xEF) . chr(0xBB) . chr(0xBF) ); // UTF-8 BOM

    $csv_headers = array(
        'User ID', 'Nome do Usuário (Display Name)', 'Primeiro Nome (User Meta)', 'Sobrenome (User Meta)',
        'CPF (User Meta)', 'Endereço (User Meta)', 'Telefone (User Meta)', 'Email do Usuário',
        'NPS Course ID', 'Nome do Curso (do NPS)', 'NPS Data Envio',
        'NPS Recomendação (0-10)', 'NPS Qualidade Geral', 'NPS Aprendizado Aplicável',
        'NPS Declaração Aceita', 'ID do Certificado (Hash)'
    );
    fputcsv( $output, $csv_headers );

    // 4. Loop through data and write to CSV (mostly same as before)
    foreach ( $nps_results as $nps_row ) {
        $user_id = (int) $nps_row['user_id'];
        $user_info = get_userdata( $user_id );
        $csv_row_data = array(); // Initialize for each row

        // User Data
        $csv_row_data['user_id'] = $user_id;
        $csv_row_data['user_display_name'] = $user_info ? $user_info->display_name : 'Usuário Desconhecido';
        $csv_row_data['user_meta_first_name'] = $user_info ? get_user_meta($user_id, 'first_name', true) : '';
        $csv_row_data['user_meta_last_name'] = $user_info ? get_user_meta($user_id, 'last_name', true) : '';
        $csv_row_data['user_meta_cpf'] = $user_info ? get_user_meta($user_id, 'cpf', true) : '';
        $csv_row_data['user_meta_endereco'] = $user_info ? get_user_meta($user_id, 'endereco', true) : '';
        $csv_row_data['user_meta_telefone'] = $user_info ? get_user_meta($user_id, 'telefone', true) : '';
        $csv_row_data['user_email'] = $user_info ? $user_info->user_email : '';

        // NPS Data
        $nps_course_id = isset($nps_row['course_id']) ? (int)$nps_row['course_id'] : 0;
        $nps_course_name = 'N/A';
        if ($nps_course_id > 0) {
            $course_title = get_the_title($nps_course_id);
            $nps_course_name = $course_title ? $course_title : 'ID Inválido: ' . $nps_course_id;
        }
        $csv_row_data['nps_course_id'] = $nps_course_id ?: '';
        $csv_row_data['nps_course_name'] = $nps_course_name;
        $csv_row_data['nps_submission_date'] = $nps_row['submission_date'];
        $csv_row_data['nps_recommended'] = $nps_row['recommended'];
        $csv_row_data['nps_qualidade_conteudo'] = $nps_row['qualidade_conteudo'];
        $csv_row_data['nps_utilidade_aulas'] = $nps_row['utilidade_aulas'];
        $csv_row_data['nps_sugestoes'] = $nps_row['sugestoes'];

        // Certificate ID (Hash)
        $certificate_hash_value = '';
        if ( $nps_course_id > 0 && $user_id > 0 ) {
            $cert_hash = $wpdb->get_var( $wpdb->prepare(
                "SELECT comment_content FROM {$wpdb->comments} 
                 WHERE comment_post_ID = %d AND user_id = %d AND comment_type = %s 
                 ORDER BY comment_date_gmt DESC LIMIT 1",
                $nps_course_id, $user_id, 'course_completed'
            ) );
            if ( $cert_hash ) {
                $certificate_hash_value = $cert_hash;
            }
        }
        $csv_row_data['certificate_hash'] = $certificate_hash_value;

        // Prepare ordered row for CSV
        $ordered_row = [];
        foreach ($csv_headers as $header_key_placeholder) {
            // This mapping needs to be robust or keys made consistent
            if ($header_key_placeholder === 'User ID') $ordered_row[] = $csv_row_data['user_id'];
            elseif ($header_key_placeholder === 'Nome do Usuário (Display Name)') $ordered_row[] = $csv_row_data['user_display_name'];
            elseif ($header_key_placeholder === 'Primeiro Nome (User Meta)') $ordered_row[] = $csv_row_data['user_meta_first_name'];
            elseif ($header_key_placeholder === 'Sobrenome (User Meta)') $ordered_row[] = $csv_row_data['user_meta_last_name'];
            elseif ($header_key_placeholder === 'CPF (User Meta)') $ordered_row[] = $csv_row_data['user_meta_cpf'];
            elseif ($header_key_placeholder === 'Endereço (User Meta)') $ordered_row[] = $csv_row_data['user_meta_endereco'];
            elseif ($header_key_placeholder === 'Telefone (User Meta)') $ordered_row[] = $csv_row_data['user_meta_telefone'];
            elseif ($header_key_placeholder === 'Email do Usuário') $ordered_row[] = $csv_row_data['user_email'];
            elseif ($header_key_placeholder === 'NPS Course ID') $ordered_row[] = $csv_row_data['nps_course_id'];
            elseif ($header_key_placeholder === 'Nome do Curso (do NPS)') $ordered_row[] = $csv_row_data['nps_course_name'];
            elseif ($header_key_placeholder === 'NPS Data Envio') $ordered_row[] = $csv_row_data['nps_submission_date'];
            elseif ($header_key_placeholder === 'NPS Recomendação (0-10)') $ordered_row[] = $csv_row_data['nps_recommended'];
            elseif ($header_key_placeholder === 'NPS Qualidade Geral') $ordered_row[] = $csv_row_data['nps_qualidade_conteudo'];
            elseif ($header_key_placeholder === 'NPS Aprendizado Aplicável') $ordered_row[] = $csv_row_data['nps_utilidade_aulas'];
            elseif ($header_key_placeholder === 'NPS Declaração Aceita') $ordered_row[] = $csv_row_data['nps_sugestoes'];
            elseif ($header_key_placeholder === 'ID do Certificado (Hash)') $ordered_row[] = $csv_row_data['certificate_hash'];
            else $ordered_row[] = '';
        }
        fputcsv( $output, $ordered_row );
    }

    // 5. Close stream and exit
    fclose( $output );
    exit();
}
// Note the new action hook name
add_action( 'admin_post_export_specific_user_nps_certs_csv', 'panapana_handle_export_specific_user_nps_certs_csv' );